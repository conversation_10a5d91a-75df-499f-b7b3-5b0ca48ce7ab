"""
<PERSON><PERSON><PERSON> to integrate user manual data into the existing CPMS chatbot system
"""
import os
import sys
import json
from pathlib import Path

# Add parent directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from backend.config import config
from backend.data.enhanced_data_processor import EnhancedCPMSDataProcessor
from backend.data.faiss_vector_store import FAISSVectorStore
from backend.cache.faq_cache import faq_cache

class UserManualIntegrator:
    def __init__(self):
        self.data_processor = EnhancedCPMSDataProcessor()
        self.vector_store = None
        
    def integrate_user_manual_data(self):
        """Complete integration of user manual data"""
        print("🏛️ CPMS User Manual Integration")
        print("="*50)
        
        # Step 1: Process and enhance data
        print("\n📋 Step 1: Processing User Manual Data...")
        combined_data, enhanced_qa = self.data_processor.process_all_enhanced_data()
        
        # Step 2: Update vector store
        print("\n🔍 Step 2: Updating Vector Store...")
        self.update_vector_store(combined_data)
        
        # Step 3: Update FAQ cache with new procedures
        print("\n⚡ Step 3: Updating FAQ Cache...")
        self.update_faq_cache(enhanced_qa)
        
        # Step 4: Generate summary
        print("\n📊 Step 4: Generating Summary...")
        self.data_processor.generate_summary_report(combined_data, enhanced_qa)
        
        print("\n🎉 User Manual Integration Complete!")
        print("Your chatbot now has enhanced step-by-step procedural knowledge!")
        
    def update_vector_store(self, combined_data):
        """Update vector store with new user manual data"""
        try:
            # Initialize vector store
            self.vector_store = FAISSVectorStore()
            
            # Add new user manual data to vector store
            manual_data = [entry for entry in combined_data if entry.get('type') == 'user_manual_procedure']
            
            if manual_data:
                print(f"   Adding {len(manual_data)} user manual procedures to vector store...")
                
                # Prepare documents for vector store
                documents = []
                for entry in manual_data:
                    doc = {
                        'content': entry['content'],
                        'metadata': {
                            'source': entry['source'],
                            'title': entry.get('title', ''),
                            'type': 'user_manual',
                            'procedure_type': entry.get('procedure_type', ''),
                            'chapter': entry.get('metadata', {}).get('chapter', ''),
                            'section': entry.get('metadata', {}).get('section', '')
                        }
                    }
                    documents.append(doc)
                
                # Add to vector store
                self.vector_store.add_documents(documents)
                print("   ✅ Vector store updated successfully")
            else:
                print("   ⚠️ No user manual data found to add")
                
        except Exception as e:
            print(f"   ❌ Error updating vector store: {e}")
    
    def update_faq_cache(self, enhanced_qa):
        """Update FAQ cache with common procedural questions"""
        try:
            # Extract common procedural questions for FAQ cache
            procedural_faqs = []
            
            for qa in enhanced_qa:
                question = qa.get('instruction', '')
                answer = qa.get('output', '')
                proc_type = qa.get('procedure_type', '')
                
                # Add high-priority procedural questions to FAQ cache
                if self._is_high_priority_faq(question, proc_type):
                    faq_entry = {
                        'question': question,
                        'answer': answer,
                        'source': qa.get('source', 'User Manual'),
                        'procedure_type': proc_type,
                        'priority': 'high'
                    }
                    procedural_faqs.append(faq_entry)
            
            if procedural_faqs:
                print(f"   Adding {len(procedural_faqs)} procedural FAQs to cache...")
                
                # Add to FAQ cache
                for faq in procedural_faqs[:20]:  # Limit to top 20 to avoid cache bloat
                    faq_cache.add_faq(
                        question=faq['question'],
                        answer=faq['answer'],
                        source=faq['source']
                    )
                
                print("   ✅ FAQ cache updated with procedural questions")
            else:
                print("   ⚠️ No high-priority procedural FAQs found")
                
        except Exception as e:
            print(f"   ❌ Error updating FAQ cache: {e}")
    
    def _is_high_priority_faq(self, question: str, proc_type: str) -> bool:
        """Determine if a question should be in the FAQ cache"""
        question_lower = question.lower()
        
        # High-priority question patterns
        high_priority_patterns = [
            'how to create ao login',
            'how to create dh login',
            'path to create ao',
            'path to create dh',
            'how to create retiree profile',
            'how to fill pension forms',
            'how to lodge grievance',
            'how to update mobile number',
            'how to update email',
            'how to process pension',
            'how to sanction pension',
            'steps to create',
            'procedure to create'
        ]
        
        # High-priority procedure types
        high_priority_types = [
            'AO_LOGIN_CREATION',
            'DH_CREATION',
            'RETIREE_PROFILE',
            'GRIEVANCE_MANAGEMENT',
            'PROFILE_UPDATE'
        ]
        
        return (any(pattern in question_lower for pattern in high_priority_patterns) or 
                proc_type in high_priority_types)
    
    def verify_integration(self):
        """Verify that the integration was successful"""
        print("\n🔍 Verifying Integration...")
        
        # Check if enhanced data files exist
        enhanced_data_path = config.PROCESSED_DATA_PATH / "enhanced_processed_data.json"
        enhanced_training_path = config.DATA_DIR / "enhanced_training_data.jsonl"
        
        if enhanced_data_path.exists():
            with open(enhanced_data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                manual_entries = [d for d in data if d.get('type') == 'user_manual_procedure']
                print(f"   ✅ Enhanced data file: {len(manual_entries)} user manual procedures")
        else:
            print("   ❌ Enhanced data file not found")
        
        if enhanced_training_path.exists():
            qa_count = 0
            with open(enhanced_training_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        qa_count += 1
            print(f"   ✅ Enhanced training file: {qa_count} Q&A pairs")
        else:
            print("   ❌ Enhanced training file not found")
        
        # Check specialized training sets
        specialized_files = list(config.DATA_DIR.glob("training_*.jsonl"))
        if specialized_files:
            print(f"   ✅ Specialized training sets: {len(specialized_files)} files")
            for file_path in specialized_files:
                print(f"      • {file_path.name}")
        else:
            print("   ⚠️ No specialized training sets found")
    
    def show_sample_procedures(self):
        """Show sample procedures that were integrated"""
        print("\n📋 Sample Integrated Procedures:")
        print("-" * 40)
        
        try:
            enhanced_data_path = config.PROCESSED_DATA_PATH / "enhanced_processed_data.json"
            if enhanced_data_path.exists():
                with open(enhanced_data_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                manual_procedures = [d for d in data if d.get('type') == 'user_manual_procedure']
                
                # Show samples by procedure type
                procedure_types = {}
                for proc in manual_procedures:
                    proc_type = proc.get('procedure_type', 'GENERAL')
                    if proc_type not in procedure_types:
                        procedure_types[proc_type] = []
                    procedure_types[proc_type].append(proc)
                
                for proc_type, procedures in procedure_types.items():
                    print(f"\n🔹 {proc_type}: {len(procedures)} procedures")
                    if procedures:
                        sample = procedures[0]
                        title = sample.get('title', 'No title')
                        print(f"   Sample: {title}")
                        
        except Exception as e:
            print(f"   ❌ Error showing samples: {e}")

def main():
    """Main function to run user manual integration"""
    integrator = UserManualIntegrator()
    
    try:
        # Run the integration
        integrator.integrate_user_manual_data()
        
        # Verify the integration
        integrator.verify_integration()
        
        # Show sample procedures
        integrator.show_sample_procedures()
        
        print("\n" + "="*60)
        print("🎯 NEXT STEPS:")
        print("1. Test the chatbot with procedural questions")
        print("2. Run fine-tuning with enhanced training data")
        print("3. Update vector database if needed")
        print("4. Monitor FAQ cache performance")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n⚠️ Integration interrupted by user")
    except Exception as e:
        print(f"\n❌ Integration failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
