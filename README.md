# CPMS Chatbot - Comprehensive Pension Management System

A complete AI-powered chatbot system for the Comprehensive Pension Management System (CPMS) that provides accurate, context-aware responses to pension-related queries using your local Mistral model and scraped data.

## 🌟 Features

- **RAG (Retrieval-Augmented Generation)**: Combines your local Mistral model with a vector database of CPMS documents
- **Local Model Support**: Works with your `mistral-7b-instruct-v0.2.Q4_K_S.gguf` model
- **Fine-tuning Capability**: QLoRA-based fine-tuning for domain-specific knowledge
- **Web Interface**: User-friendly Gradio-based chat interface
- **Multi-source Data**: Processes both PDF documents and web-scraped content
- **Real-time Responses**: Fast, accurate answers with source citations

## 📁 Project Structure

```
Chatbot_Fine_Tuning/
├── config.py              # Configuration settings
├── data_processor.py      # Data extraction and processing
├── vector_store.py        # Vector database management
├── model_handler.py       # Model loading and inference
├── rag_system.py          # RAG pipeline implementation
├── fine_tuning.py         # QLoRA fine-tuning
├── web_interface.py       # Gradio web interface
├── main.py               # Main entry point
├── requirements.txt      # Python dependencies
├── README.md            # This file
├── pdf_data/            # Your PDF documents
├── web_scraped/         # Your scraped web data
├── data/                # Processed data storage
├── models/              # Fine-tuned models
├── vector_db/           # Vector database
└── output/              # Generated outputs
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Run Complete Setup

```bash
python main.py --mode full
```

This will:
- Process your PDF and web-scraped data
- Build the vector database
- Test the RAG system
- Optionally run fine-tuning
- Launch the web interface

### 3. Launch Web Interface Only

```bash
python main.py --mode web --model-type gguf
```

## 🔧 Configuration

Edit `config.py` to customize:

- **Model paths**: Update `MISTRAL_MODEL_PATH` to your model location
- **Data paths**: Adjust paths for your PDF and web data
- **Training parameters**: Modify LoRA settings, batch size, etc.
- **Generation settings**: Temperature, max tokens, etc.

## 📊 Data Processing

### Automatic Processing
```bash
python main.py --mode process
```

### Manual Processing
```python
from data_processor import CPMSDataProcessor

processor = CPMSDataProcessor()
all_data, qa_pairs = processor.process_all_data()
```

## 🔍 Vector Store

### Build Vector Database
```bash
python main.py --mode vector
```

### Test Search
```python
from vector_store import CPMSVectorStore

vector_store = CPMSVectorStore()
results = vector_store.similarity_search("How to login to CPMS?")
```

## 🤖 Model Usage

### RAG System
```python
from rag_system import CPMSRAGSystem

rag = CPMSRAGSystem(model_type="gguf")
result = rag.query("What is CPMS?")
print(result['response'])
```

### Direct Model Access
```python
from model_handler import CPMSModelHandler

model = CPMSModelHandler(model_type="gguf")
response = model.chat("How can I check my pension status?")
```

## 🎯 Fine-tuning

### Run Fine-tuning
```bash
python main.py --mode finetune
```

### Requirements
- **GPU**: Recommended 12GB+ VRAM
- **Time**: 2-4 hours on GPU
- **Data**: Automatically generated Q&A pairs from your documents

### Fine-tuning Process
1. Loads base Mistral model with 4-bit quantization
2. Applies QLoRA adapters
3. Trains on generated Q&A pairs
4. Saves fine-tuned model for inference

## 🌐 Web Interface

### Features
- **Chat Interface**: Natural conversation with the bot
- **Suggested Questions**: Quick-start prompts
- **Source Citations**: See which documents informed each response
- **System Information**: Monitor system health and statistics
- **Quick Reference**: Important contact info and procedures

### Access
- **Local**: http://localhost:7860
- **Network**: Use `--share` flag for public link

## 📋 Usage Examples

### Common Queries
- "What is CPMS?"
- "How can I login to CPMS portal?"
- "When is my pension credited?"
- "How to lodge a grievance?"
- "What documents are needed for family pension?"
- "How to change my bank account details?"

### System Commands
```bash
# Full setup
python main.py --mode full

# Skip fine-tuning
python main.py --mode full --skip-finetune

# Use different model
python main.py --mode web --model-type huggingface

# Test system
python main.py --mode test
```

## 🔧 Troubleshooting

### Common Issues

1. **GGUF Model Not Found**
   - Update `MISTRAL_MODEL_PATH` in `config.py`
   - Ensure the `.gguf` file exists at the specified path

2. **Out of Memory**
   - Reduce `BATCH_SIZE` in `config.py`
   - Use CPU-only mode by setting `n_gpu_layers=0`

3. **Slow Performance**
   - Install CUDA-compatible PyTorch
   - Use GPU acceleration
   - Reduce `MAX_NEW_TOKENS`

4. **No Data Found**
   - Ensure PDF files are in `pdf_data/` directory
   - Ensure web scraped files are in `web_scraped/` directory

### Performance Optimization

1. **For Better Speed**:
   - Use GPU acceleration
   - Reduce context window size
   - Use quantized models

2. **For Better Accuracy**:
   - Run fine-tuning
   - Increase retrieval context
   - Add more training data

## 📞 CPMS Contact Information

- **Helpline**: 1800-113-5800
- **Email**: <EMAIL>
- **Technical Support**: <EMAIL>
- **Website**: https://dotpension.gov.in

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is designed for educational and internal use with CPMS data. Please ensure compliance with your organization's data usage policies.

## 🙏 Acknowledgments

- **Mistral AI** for the base language model
- **Hugging Face** for the transformers library
- **ChromaDB** for vector storage
- **Gradio** for the web interface
- **Department of Telecommunications** for CPMS documentation

---

**Ready to help pensioners with accurate, AI-powered assistance! 🏛️💬**
