#!/usr/bin/env python3
"""
Start CPMS Frontend on Ubuntu with correct backend IP configuration
"""
import os
import sys
from pathlib import Path

def setup_environment():
    """Setup environment for frontend"""
    print("Setting up frontend environment...")
    
    # Add workspace root to Python path
    workspace_root = Path(__file__).parent
    sys.path.insert(0, str(workspace_root))
    
    # Verify backend configuration
    try:
        from backend.config import config
        print(f"Backend API URL: {config.BACKEND_API_BASE_URL}")
        
        if "*************" in config.BACKEND_API_BASE_URL:
            print("✅ Frontend configured to connect to correct backend IP")
        else:
            print("⚠️ Frontend may not be configured for the correct backend IP")
            
    except ImportError:
        print("⚠️ Could not import backend config, using fallback")

def start_frontend():
    """Start the frontend server"""
    try:
        print("Starting CPMS Frontend Server...")
        print("=" * 40)
        
        # Import and run the frontend
        from frontend.run_frontend import main
        main()
        
    except KeyboardInterrupt:
        print("\nFrontend stopped by user")
    except Exception as e:
        print(f"Error starting frontend: {e}")
        print("\nTroubleshooting:")
        print("1. Check if backend is running at *************:8001")
        print("2. Verify network connectivity")
        print("3. Check firewall settings")
        sys.exit(1)

def show_access_info():
    """Show how to access the frontend"""
    print("\n" + "=" * 50)
    print("🌐 CPMS Frontend Access Information")
    print("=" * 50)
    print("Frontend will be available at:")
    print("  • Enhanced Portal: http://localhost:7861/enhanced_index.html")
    print("  • Basic Portal:    http://localhost:7861/index.html")
    print("")
    print("Backend API (should be running):")
    print("  • API:             http://*************:8001")
    print("  • API Docs:        http://*************:8001/docs")
    print("")
    print("Test Pensioner Accounts:")
    print("  • EPPO: DOT12345678, Mobile: **********")
    print("  • EPPO: DOT87654321, Mobile: **********")
    print("  • EPPO: DOT11223344, Mobile: **********")
    print("=" * 50)

if __name__ == "__main__":
    setup_environment()
    show_access_info()
    start_frontend()
