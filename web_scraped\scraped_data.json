{"About Us": {"url": "https://dotpension.gov.in/Home/AboutUs", "page_name": "About Us", "title": "Comprehensive Pension Management System", "main_content": "", "links": [{"text": "Pensioners' Portal", "url": "http://pensionersportal.gov.in/", "is_internal": false}, {"text": "Department of Telecommunication", "url": "http://dot.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://jeevanpramaan.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://dotpension.gov.in/Home/Feedback ", "is_internal": true}, {"text": "Contact us", "url": "https://dotpension.gov.in/Home/AboutUs", "is_internal": true}, {"text": "Policies", "url": "https://dotpension.gov.in/Home/Policies ", "is_internal": true}], "tables": [], "lists": [{"type": "ol", "items": ["Direct disbursement of pension on timely basis without intermediaries", "Single window system for complete pension process", "Online grievance management for the pensioners reduces paper work", "Tracking of pension status from home encourages transparency and accountability", "Covers 3.5 lakh current pensioners/family pensioners and 1.5 lakh future retirees.", "Faster processing of arrears and revision of pension"]}, {"type": "ul", "items": ["Helpdesk", "Email : support[dot]cpms-dot[at]nic[dot]in", "|", "For Technical Help : 1800-113-5800", "|", "For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in"]}, {"type": "ul", "items": ["Pensioners' Portal", "|", "Department of Telecommunication", "|", "<PERSON><PERSON><PERSON>", "|", "<PERSON><PERSON><PERSON>", "|", "Contact us", "|", "Policies"]}], "metadata": {"scraped_at": "2025-06-10 11:55:20", "status_code": 200, "content_length": 12846, "source_type": "web"}}, "Retiree Corner": {"url": "https://dotpension.gov.in/Home/RetireeCorner", "page_name": "Retiree Corner", "title": "Comprehensive Pension Management System", "main_content": "", "links": [{"text": "link", "url": "https://dotpension.gov.in/WriteReadData/Home/CPMS-FAQForPensioners.pdf", "is_internal": true}, {"text": "Pensioners' Portal", "url": "http://pensionersportal.gov.in/", "is_internal": false}, {"text": "Department of Telecommunication", "url": "http://dot.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://jeevanpramaan.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://dotpension.gov.in/Home/Feedback ", "is_internal": true}, {"text": "Contact us", "url": "https://dotpension.gov.in/Home/RetireeCorner", "is_internal": true}, {"text": "Policies", "url": "https://dotpension.gov.in/Home/Policies ", "is_internal": true}], "tables": [], "lists": [{"type": "ol", "items": ["Direct disbursement of pension on timely basis without intermediaries", "Single window system for complete pension process", "Online grievance management for the pensioners reducing paper work", "Tracking of pension status from home encourages transparency and accountability", "Faster processing of arrears and revision of pension"]}, {"type": "ol", "items": ["Pension Sanction: Pension<PERSON> can view the pension sanctions i.e. ePPO (Digitally signed PPO), Gratuity sanction and sanction of revision of pension on the dashboard, along with details of commutation payment and arrears. It may be noted these sanction will be available for future use.", "Lodge Grievance: Pension<PERSON> can lodge grievance online and check its status also.  Alternately the Pensioner can always call the national Helpline 1800-113-5800 or mail his grievances/query to sampann.cpms-dot[at]nic[dot]in.", "Pension statement: Pensioner can check the pension payments made till now on his/her dashboard.", "Mobile Number, Email ID and Address Update: Re<PERSON>rees will get the feature to update their mobile number, Email ID and address online at any time at their leisure.", "Retiring officer/official can see the progress of his/her application at the top of the dashboard till pension Authorization."]}, {"type": "ul", "items": ["Track pension", "View ePPO", "Lodge and track grievances", "View monthly Statement", "Stay Updated"]}, {"type": "ul", "items": ["All India Toll-free helpline: 1800-113-5800", "Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in"]}, {"type": "ul", "items": ["Helpdesk", "Email : support[dot]cpms-dot[at]nic[dot]in", "|", "For Technical Help : 1800-113-5800", "|", "For Administrative and Training Help :  dycgcabait-dot[at]gov[dot]in"]}, {"type": "ul", "items": ["Pensioners' Portal", "|", "Department of Telecommunication", "|", "<PERSON><PERSON><PERSON>", "|", "<PERSON><PERSON><PERSON>", "|", "Contact us", "|", "Policies"]}], "metadata": {"scraped_at": "2025-06-10 11:55:22", "status_code": 200, "content_length": 13918, "source_type": "web"}}, "Circulars": {"url": "https://dotpension.gov.in/Home/RetireeCornerCir", "page_name": "Circulars", "title": "Comprehensive Pension Management System", "main_content": "", "links": [{"text": "Pensioners' Portal", "url": "http://pensionersportal.gov.in/", "is_internal": false}, {"text": "Department of Telecommunication", "url": "http://dot.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://jeevanpramaan.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://dotpension.gov.in/Home/Feedback ", "is_internal": true}, {"text": "Contact us", "url": "https://dotpension.gov.in/Home/RetireeCornerCir", "is_internal": true}, {"text": "Policies", "url": "https://dotpension.gov.in/Home/Policies ", "is_internal": true}], "tables": [{"headers": ["S.No.", "Order/Circular Name", "Download File", "Dated"], "rows": [["1", "Know Your Pensioner KYP Form", "", "26/12/2022"], ["2", "Submission of LC by Pensioners at any CCA Office", "", "23/06/2021"], ["3", "Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries", "", "22/08/2019"], ["4", "Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries", "", "22/08/2019"], ["5", "Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices", "", "04/03/2021"]]}], "lists": [{"type": "ul", "items": ["Helpdesk", "Email : support[dot]cpms-dot[at]nic[dot]in", "|", "For Technical Help : 1800-113-5800", "|", "For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in"]}, {"type": "ul", "items": ["Pensioners' Portal", "|", "Department of Telecommunication", "|", "<PERSON><PERSON><PERSON>", "|", "<PERSON><PERSON><PERSON>", "|", "Contact us", "|", "Policies"]}], "metadata": {"scraped_at": "2025-06-10 11:55:24", "status_code": 200, "content_length": 14928, "source_type": "web"}}, "Manual": {"url": "https://dotpension.gov.in/Home/DownloadManual", "page_name": "Manual", "title": "Comprehensive Pension Management System", "main_content": "", "links": [{"text": "User Manual", "url": "https://dotpension.gov.in/WriteReadData/Home/CPMS-UserManual.pdf", "is_internal": true}, {"text": "SAMPANN User Manual- BSNL VRS 2019- HoO 1.1", "url": "https://dotpension.gov.in/WriteReadData/Home/SAMPANNUserManual-BSNLVRS2019-HoO1.1.pdf", "is_internal": true}, {"text": "Bank Undertaking", "url": "https://dotpension.gov.in/WriteReadData/Home/Bank%20undertaking.pdf", "is_internal": true}, {"text": "Faq's For Pensioner", "url": "https://dotpension.gov.in/WriteReadData/Home/CPMS-FAQForPensioners.pdf", "is_internal": true}, {"text": "Actual declaration for Investments and Savings", "url": "https://dotpension.gov.in/WriteReadData/Home/ActualDeclaration.pdf", "is_internal": true}, {"text": "Proposed declaration for Investments and Savings", "url": "https://dotpension.gov.in/WriteReadData/Home/ProposedDeclaration.pdf", "is_internal": true}, {"text": "Form 1A to apply for Commutation of Pension", "url": "https://dotpension.gov.in/WriteReadData/Home/Form1A_COP.pdf", "is_internal": true}, {"text": "Know Your Pensioner (KYP) Form", "url": "https://dotpension.gov.in/WriteReadData/Home/KnowYourPensioner(KYP)Form.pdf", "is_internal": true}, {"text": "Pension Forms - Form 4, Form 8, Form 10  Form 12", "url": "https://dotpension.gov.in/WriteReadData/Home/PensionForms_Form4_Form8_Form10_Form12.pdf", "is_internal": true}, {"text": "Pensioners' Portal", "url": "http://pensionersportal.gov.in/", "is_internal": false}, {"text": "Department of Telecommunication", "url": "http://dot.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://jeevanpramaan.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://dotpension.gov.in/Home/Feedback ", "is_internal": true}, {"text": "Contact us", "url": "https://dotpension.gov.in/Home/DownloadManual", "is_internal": true}, {"text": "Policies", "url": "https://dotpension.gov.in/Home/Policies ", "is_internal": true}], "tables": [{"headers": [""], "rows": [["User Manual"], ["SAMPANN User Manual- BSNL VRS 2019- HoO 1.1"], ["Bank Undertaking"], ["Faq's For Pensioner"], ["Actual declaration for Investments and Savings"], ["Proposed declaration for Investments and Savings"], ["Form 1A to apply for Commutation of Pension"], ["Know Your Pensioner (KYP) Form"], ["Pension Forms - Form 4, Form 8, Form 10  Form 12"]]}], "lists": [{"type": "ul", "items": ["Helpdesk", "Email : support[dot]cpms-dot[at]nic[dot]in", "|", "For Technical Help : 1800-113-5800", "|", "For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in"]}, {"type": "ul", "items": ["Pensioners' Portal", "|", "Department of Telecommunication", "|", "<PERSON><PERSON><PERSON>", "|", "<PERSON><PERSON><PERSON>", "|", "Contact us", "|", "Policies"]}], "metadata": {"scraped_at": "2025-06-10 11:55:26", "status_code": 200, "content_length": 12971, "source_type": "web"}}, "FAQ": {"url": "https://dotpension.gov.in/FAQ/Index", "page_name": "FAQ", "title": "Comprehensive Pension Management System", "main_content": "", "links": [{"text": "Pensioners' Portal", "url": "http://pensionersportal.gov.in/", "is_internal": false}, {"text": "Department of Telecommunication", "url": "http://dot.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://jeevanpramaan.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://dotpension.gov.in/Home/Feedback ", "is_internal": true}, {"text": "Contact us", "url": "https://dotpension.gov.in/FAQ/Index", "is_internal": true}, {"text": "Policies", "url": "https://dotpension.gov.in/Home/Policies ", "is_internal": true}], "tables": [{"headers": ["1)", "What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. It covers all activities starting from pension sanction, authorization, and disbursement. It also enables the pensioner to track latest status of pension."], "rows": [["2)", "How to get connected to CPMS portal?\n\n\nAns : \nRetirees can get connected by logging on to www.dotpension.gov.in. Retiring employees will receive user ID and Password on their mobile number."], ["3)", "What changes after CPMS implementation?\n\n\nAns : \nThe pensioner will continue to be credited to the designated bank account of the pensioner after implementation of CPMS. However, Controller of Communication Accounts (CCA) becomes the pension disbursing authority (PDA) in place of Post Offices and CPPC of Banks. Thus, the CCA becomes the single window authority dealing with all pension relating activities of pensioners."], ["4)", "How does the Pensioner benefit from implementation of CPMS?\n\n\nAns : \nCPMS implementation will benefit the pensioner in the following ways:\r\n•\tDirect transfer of pension by the CCA to the pensioner’s bank account through CPMS will eliminate the delay caused by involvement of the CPPC \r\n•\tIt will eliminate the need for Transfer of pension cases from one Circle to another. This in turn will ensure that pensioners drawing pension in a bank located in another state (different from where he retired) start getting pension without any time-lag.\r\n•\tAny revision in pension will get effected without delay. Calculation and payment of arrear will take place automatically once the pension is revised.\r\n•\tThe time-lag between change in Dearness Relief (DR) and its payment will be minimized as latest DR rates will be updated centrally.\r\n•\tAge-linked enhancement of pension will take place on attaining the designated age.\r\n•\tThe pensioner will be able to access and take print-out of details of pension payment by logging into CPMS with the login ID provided to the pensioner.\r\n•\tTDS and Form-16 will be provided by the CCA without any intervention from the pensioner."], ["5)", "When is the pension credited to the pensioner's account by the paying Branch?\n\n\nAns : \nThe pension is credited to the account on the last working day of the month. In case of arrears etc. the amount will be credited Out of Cycle on earliest possible day."], ["6)", "Which Office will the pensioner approach for commencement of his/her pension?\n\n\nAns : \nThe pensioner is not required to visit any office."], ["7)", "Will the pensioner get a PPO in new system?\n\n\nAns : \nA digitally signed PPO shall be sent to Pensioners dashboard. However, a Printed copy shall also be sent via post to pensioner’s address by PDA section."], ["8)", "Can a pensioner open a Joint Account with his/ her spouse?\n\n\nAns : \nYes, the pensioner can open a joint account with his/her spouse. The account details of the same must be communicated to the Pension Section of the CCA office. The same will be updated into the CPMS software, and the pension will flow into the new account."], ["9)", "Can the pensioner login to CPMS?\n\n\nAns : \nYes. Every pensioner will get a login ID and Password after the forms are sent to retiree online for filling them and sending  to the Head of the Office (HoO)."], ["10)", "What are the services which can be accessed by the pensioner by logging into CPMS with his login ID?\n\n\nAns : \nThe Pensioner will be able to access the following services by logging into CPMS:\r\n•\tProgress of his pension case. At any point of time.\r\n•\t Lodge a grievance from the CPMS portal itself. Otherwise he/she can always contact the CCA office over Phone with his query.\r\n•\tView details of pension received and take print-out of pension payment slip.\r\n•\tView PPO and sanction orders"], ["11)", "What is to be done in case the pensioner wants to change the state in which his bank branch is located?\n\n\nAns : \nThe pensioners covered under CPMS needs to submit application in prescribed form along with details of bank account and bank undertaking to the CCA office. Pension will be credited to the new bank account once the PDA changes bank details in CPMS. There will be no need for recalling PPO from Bank and transferring pension case from one state to another."], ["12)", "In case of Family Pension, which office should the dependents approach?\n\n\nAns : \nIn case of unfortunate death of the employee/pensioner, the Death Certificate along with Life certificate (or DLC) of family pensioner may be sent to CCA office."], ["13)", "When does the family pension commence?\n\n\nAns : \nOnce the Death Certificate is received, the pension can either start in the same month or the next.  Family pensioner need not visit CCA office for commencing disbursement of family pension."], ["14)", "How to get details of pension through SMS?\n\n\nAns : \nThe CPMS software is integrated with SMS gateway. At various stages of the pensionary benefits calculation, the SMS and Emails will be triggered by the system to the retiree/dependent provided a working mobile number has been provided."], ["15)", "If the pensioner wants details of pension paid during a financial year or Pension Payment Slip, whom should he/she approach?\n\n\nAns : \nThe pensioner’s dashboard, has a link to Pension Ledger. It can be accessed online, and previous pension paid amounts will be shown."], ["16)", "If the pensioner has a query or requires clarification on the amount of pension paid, what should he/she do?\n\n\nAns : \nPensioner can directly contact the CCA office for the details. In case of any grievance, he/she can lodge the same from his dashboard itself."], ["17)", "Whether Income Tax (TDS) will be deducted at source?\n\n\nAns : \nYes, income tax is deducted at source for all pensioner (not family pensioners), as per provisions of Income Tax Act and instructions issued by the Income Tax Department."], ["18)", "Who is responsible for deduction of Income Tax at source from pension payment?\n\n\nAns : \nPDA section of CCA office is responsible for deducting the TDS on Pension."], ["19)", "Where should a pensioner submit details of investment for claiming relief under Income Tax Act?\n\n\nAns : \nThe investment details need to be sent to the PDA section of CCA offices. On the pensioner’s dashboard, pensioners may log in and feed their details and upload supporting documents. Taking a printout of above, they can send along with document to CCA office by post.\r\nPensioners can also download the income tax proforma available on www.dotpension.gov.in and fill it up and then send the duly filled form along with enclosures to CCA."], ["20)", "Which office will the pensioner approach for grievance resolution?\n\n\nAns : \nGrievance can be lodged from the Pensioner’s dashboard online. The grievance can also be reported to the CCA office directly. A dedicated toll-free helpline 1800-113-5800 is also available. Mails can be sent to sampann[dot]cpms-dot[at]gov[dot]in"]]}], "lists": [{"type": "ul", "items": ["Helpdesk", "Email : support[dot]cpms-dot[at]nic[dot]in", "|", "For Technical Help : 1800-113-5800", "|", "For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in"]}, {"type": "ul", "items": ["Pensioners' Portal", "|", "Department of Telecommunication", "|", "<PERSON><PERSON><PERSON>", "|", "<PERSON><PERSON><PERSON>", "|", "Contact us", "|", "Policies"]}], "metadata": {"scraped_at": "2025-06-10 11:55:29", "status_code": 200, "content_length": 28497, "source_type": "web"}}, "Training": {"url": "https://dotpension.gov.in/Home/Training", "page_name": "Training", "title": "Comprehensive Pension Management System", "main_content": "", "links": [{"text": "HoO Module Training Presentation", "url": "https://dotpension.gov.in/WriteReadData/Home/SAMPANN_HoO_Training_VRS 2019_1.2.pptx", "is_internal": true}, {"text": "HoO Module Training Video", "url": "https://dotpension.gov.in/WriteReadData/Home/SAMPANN_HoO_Video_DoT.mp4", "is_internal": true}, {"text": "Retiree Form Filling Training Presentation", "url": "https://dotpension.gov.in/WriteReadData/Home/SAMPANN_Retiree_Form_Training_1.0.pptx", "is_internal": true}, {"text": "Retiree Form Filling Training Video", "url": "https://dotpension.gov.in/WriteReadData/Home/SAMPANN_Retiree_Video_pptv0.4.mp4", "is_internal": true}, {"text": "Revised Process Flow BSNL VRS", "url": "https://dotpension.gov.in/WriteReadData/Home/RevisedProcessFlow-HoO-BSNLVRS2019v0.2.pdf", "is_internal": true}, {"text": "Manual for Logic of Arrear Calculation on Voucher date", "url": "https://dotpension.gov.in/WriteReadData/Home/Logic_ArrearCalculation.pdf", "is_internal": true}, {"text": "Migration_Family Revision User Manual", "url": "https://dotpension.gov.in/WriteReadData/Home/Migration_FamilyRevision.pdf", "is_internal": true}, {"text": "Migration_Pay Revision User Manual", "url": "https://dotpension.gov.in/WriteReadData/Home/Migration_PayRevisionUser.pdf", "is_internal": true}, {"text": "Minor Corrections in ePPO", "url": "https://dotpension.gov.in/WriteReadData/Home/Minor_Coreectioneppo.pdf", "is_internal": true}, {"text": "Process of Generation of Pension Sleep through SAMPANN mobile App", "url": "https://dotpension.gov.in/WriteReadData/Home/Generation_PensionMobileApp.pdf", "is_internal": true}, {"text": "User Manual failed Cancelled Bill handling in SAMPANN", "url": "https://dotpension.gov.in/WriteReadData/Home/Canceled_BillHandling.pdf", "is_internal": true}, {"text": "User manual for Additional Quantum Pension", "url": "https://dotpension.gov.in/WriteReadData/Home/Addition_QuantumPension.pdf", "is_internal": true}, {"text": "User Manual for Change in logic of calculation of gratuity family pension (AE vs. LPD and Penalty)", "url": "https://dotpension.gov.in/WriteReadData/Home/Logic_CalculationGratuity.pdf", "is_internal": true}, {"text": "User manual for Deactivation module", "url": "https://dotpension.gov.in/WriteReadData/Home/Manual_DeactivationModule.pdf", "is_internal": true}, {"text": "User manual for Enhanced Monthly Bill Enhancements", "url": "https://dotpension.gov.in/WriteReadData/Home/Enhanced_MonthlyBill.pdf", "is_internal": true}, {"text": "User manual for Family and Migrated Pensioner Dashboard", "url": "https://dotpension.gov.in/WriteReadData/Home/Migrated_PensionerDashboard.pdf", "is_internal": true}, {"text": "User manual for FMA Update", "url": "https://dotpension.gov.in/WriteReadData/Home/FmaUpdate.pdf", "is_internal": true}, {"text": "User manual for Income tax related changes for family pensioners", "url": "https://dotpension.gov.in/WriteReadData/Home/Income_TaxPensioners.pdf", "is_internal": true}, {"text": "User manual for logic of Minimum Pension", "url": "https://dotpension.gov.in/WriteReadData/Home/Logic_MinimumPension.pdf", "is_internal": true}, {"text": "User manual for Part IV utility", "url": "https://dotpension.gov.in/WriteReadData/Home/PartIVUtility.pdf", "is_internal": true}, {"text": "User Manual for Pay related revision for BSNL VRS 2019 cases", "url": "https://dotpension.gov.in/WriteReadData/Home/PayRelated_BsnlVrs2019case.pdf", "is_internal": true}, {"text": "User manual for Pensioner Pension Slip Generation", "url": "https://dotpension.gov.in/WriteReadData/Home/Pensioner_PensionSleepGeneration.pdf", "is_internal": true}, {"text": "User Manual for Return Delete feature in Revision Module", "url": "https://dotpension.gov.in/WriteReadData/Home/ReturnDelete_FeatureRevisionModule.pdf", "is_internal": true}, {"text": "User Manual Transfer of Pension", "url": "https://dotpension.gov.in/WriteReadData/Home/User_TransferPension.pdf", "is_internal": true}, {"text": "Pensioners' Portal", "url": "http://pensionersportal.gov.in/", "is_internal": false}, {"text": "Department of Telecommunication", "url": "http://dot.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://jeevanpramaan.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://dotpension.gov.in/Home/Feedback ", "is_internal": true}, {"text": "Contact us", "url": "https://dotpension.gov.in/Home/Training", "is_internal": true}, {"text": "Policies", "url": "https://dotpension.gov.in/Home/Policies ", "is_internal": true}], "tables": [{"headers": [""], "rows": [["HoO Module Training Presentation"], ["HoO Module Training Video"], ["Retiree Form Filling Training Presentation"], ["Retiree Form Filling Training Video"], ["Revised Process Flow BSNL VRS"], ["Manual for Logic of Arrear Calculation on Voucher date"], ["Migration_Family Revision User Manual"], ["Migration_Pay Revision User Manual"], ["Minor Corrections in ePPO"], ["Process of Generation of Pension Sleep through SAMPANN mobile App"], ["User Manual failed Cancelled Bill handling in SAMPANN"], ["User manual for Additional Quantum Pension"], ["User Manual for Change in logic of calculation of gratuity family pension (AE vs. LPD and Penalty)"], ["User manual for Deactivation module"], ["User manual for Enhanced Monthly Bill Enhancements"], ["User manual for Family and Migrated Pensioner Dashboard"], ["User manual for FMA Update"], ["User manual for Income tax related changes for family pensioners"], ["User manual for logic of Minimum Pension"], ["User manual for Part IV utility"], ["User Manual for Pay related revision for BSNL VRS 2019 cases"], ["User manual for Pensioner Pension Slip Generation"], ["User Manual for Return Delete feature in Revision Module"], ["User Manual Transfer of Pension"]]}], "lists": [{"type": "ul", "items": ["Helpdesk", "Email : support[dot]cpms-dot[at]nic[dot]in", "|", "For Technical Help : 1800-113-5800", "|", "For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in"]}, {"type": "ul", "items": ["Pensioners' Portal", "|", "Department of Telecommunication", "|", "<PERSON><PERSON><PERSON>", "|", "<PERSON><PERSON><PERSON>", "|", "Contact us", "|", "Policies"]}], "metadata": {"scraped_at": "2025-06-10 11:55:31", "status_code": 200, "content_length": 21072, "source_type": "web"}}, "Whats New": {"url": "https://dotpension.gov.in/Home/WhatsNew", "page_name": "Whats New", "title": "Comprehensive Pension Management System", "main_content": "", "links": [{"text": "Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi)", "url": "https://youtu.be/cH2ZmMB51C0", "is_internal": false}, {"text": "Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English)", "url": "https://youtu.be/GTe-NfkUkow", "is_internal": false}, {"text": "SAMPANN Login Creation for Family and Migrated Pensioners (Hindi)", "url": "https://youtu.be/hU9899C-5FI", "is_internal": false}, {"text": "SAMPANN Login Creation for Family and Migrated Pensioners (English)", "url": "https://youtu.be/SIYRJNAWlkc", "is_internal": false}, {"text": "Generation of Pension Slip in SAMPANN Hindi", "url": "https://youtu.be/XEpZhuI6-DI", "is_internal": false}, {"text": "Generation of Pension Slip in SAMPANN English", "url": "https://youtu.be/ooBb4n01S_s", "is_internal": false}, {"text": "Process of  Life Certificate/Digital Life certificate generation and submission", "url": "https://youtu.be/tbSLfvYW-nY", "is_internal": false}, {"text": "Updated Digital Life Certificate Submission Process For DOT Pensioners", "url": "https://youtu.be/1LeIgIboS-M", "is_internal": false}, {"text": "Pensioners' Portal", "url": "http://pensionersportal.gov.in/", "is_internal": false}, {"text": "Department of Telecommunication", "url": "http://dot.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://jeevanpramaan.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://dotpension.gov.in/Home/Feedback ", "is_internal": true}, {"text": "Contact us", "url": "https://dotpension.gov.in/Home/WhatsNew", "is_internal": true}, {"text": "Policies", "url": "https://dotpension.gov.in/Home/Policies ", "is_internal": true}], "tables": [{"headers": [""], "rows": [["Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi)", "Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English)", "SAMPANN Login Creation for Family and Migrated Pensioners (Hindi)", "SAMPANN Login Creation for Family and Migrated Pensioners (English)", "Generation of Pension Slip in SAMPANN Hindi", "Generation of Pension Slip in SAMPANN English", "Process of  Life Certificate/Digital Life certificate generation and submission", "Updated Digital Life Certificate Submission Process For DOT Pensioners"]]}], "lists": [{"type": "ul", "items": ["Helpdesk", "Email : support[dot]cpms-dot[at]nic[dot]in", "|", "For Technical Help : 1800-113-5800", "|", "For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in"]}, {"type": "ul", "items": ["Pensioners' Portal", "|", "Department of Telecommunication", "|", "<PERSON><PERSON><PERSON>", "|", "<PERSON><PERSON><PERSON>", "|", "Contact us", "|", "Policies"]}], "metadata": {"scraped_at": "2025-06-10 11:55:33", "status_code": 200, "content_length": 12284, "source_type": "web"}}, "Contact": {"url": "https://dotpension.gov.in/Home/Contact", "page_name": "Contact", "title": "No title found", "main_content": "", "links": [{"text": "Click here to login again.", "url": "https://dotpension.gov.in/", "is_internal": true}], "tables": [], "lists": [], "metadata": {"scraped_at": "2025-06-10 11:55:35", "status_code": 200, "content_length": 2609, "source_type": "web"}}, "Home": {"url": "https://dotpension.gov.in/", "page_name": "Home", "title": "Comprehensive Pension Management System", "main_content": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/ SAMPANN Contact details for access by pensioners.\nCPMS portal at\nwww.dotpension.gov.in\nToll Free number – 1800-113-5800\n(Timings – 9:30 AM to 6:00 PM except on weekends & gazetted  holidays)\nCentralized helpdesk Mail Id – sampann[dot]cpms-dot[at]gov[dot]in\nAll CCA Contact Details -\nClick here\nSAMPANN APP -  Mobile application available on google play store\nFAQ For Pensioners -\nClick here\nClose\nContact us\nEmail : support[dot]cpms-dot[at]nic[dot]in\nFor Technical Help : 1800-113-5800\nFor Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\nClose\nAlert\nTo know your Identification/Life Certificate status\nClick Here\nजीवन प्रमाण पत्र की स्थिति जानने के लिए\nयहां क्लिक करें\nIf your Life Certificate has expired on or before 31st May, 2025, please submit your Life Certificate at the earliest and latest by 20th June, 2025 to avoid stoppage of pension. Also, if your Life Certificate is expiring in upcoming months, please ensure to submit it before expiry for uninterrupted pension payment. यदि आपका जीवन प्रमाण पत्र 31 मई, 2025 को या उससे पहले समाप्त हो गया है, तो कृपया पेंशन रोकने से बचने के लिए 20 जून, 2025 तक या उससे पहले जीवन प्रमाण पत्र जमा करें। इसके अलावा, यदि आपका जीवन प्रमाण पत्र आगामी महीनों में समाप्त हो रहा है, तो कृपया निर्बाध पेंशन भुगतान के लिए समाप्ति से पहले इसे जमा करना सुनिश्चित करें।\nInformation\nContact Us\nKnow Your Pensioner (KYP) Form\nKnow Your PPO No\nHelp line\nSelect Your Tax Regime\nLife Certificate Validity Check\n440931\nPensioners Benefited\n14493495\nTransactions Made\nHelpdesk\nEmail : support[dot]cpms-dot[at]nic[dot]in\nFor Technical Help : 1800-113-5800\nFor Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\nPensioners' Portal\nDepartment of Telecommunication\nJeevan Pramaan\nFeedback\nContact us\nPolicies\n\"© 2023 Designed by Controller General Of Communication Accounts, Department of Telecommunications, Developed and Maintained by M/s Uneecops Technologies Ltd, Hosted by NIC.\"", "links": [{"text": "www.dotpension.gov.in", "url": "https://www.dotpension.gov.in", "is_internal": false}, {"text": "Click here", "url": "https://dotpension.gov.in/Home/AllCCAContactDetails", "is_internal": true}, {"text": "Click here", "url": "https://dotpension.gov.in/WriteReadData/Home/CPMS-FAQForPensioners.pdf", "is_internal": true}, {"text": "Click Here", "url": "https://dotpension.gov.in/Login/LCInformationForm", "is_internal": true}, {"text": "यहां क्लिक करें", "url": "https://dotpension.gov.in/Login/LCInformationForm", "is_internal": true}, {"text": "Information", "url": "https://dotpension.gov.in/WriteReadData/Home/CPMS-Information.pdf", "is_internal": true}, {"text": "Contact Us", "url": "https://dotpension.gov.in/", "is_internal": true}, {"text": "Know Your Pensioner (KYP) Form", "url": "https://dotpension.gov.in/WriteReadData/Home/KnowYourPensioner(KYP)Form.pdf", "is_internal": true}, {"text": "Know Your PPO No", "url": "https://dotpension.gov.in/KnowYourPPO/Index", "is_internal": true}, {"text": "Help line", "url": "https://dotpension.gov.in/", "is_internal": true}, {"text": "Select Your Tax Regime", "url": "https://dotpension.gov.in/TaxRegime/Index", "is_internal": true}, {"text": "Life Certificate Validity Check", "url": "https://dotpension.gov.in/Login/LCInformationForm", "is_internal": true}, {"text": "Pensioners' Portal", "url": "http://pensionersportal.gov.in/", "is_internal": false}, {"text": "Department of Telecommunication", "url": "http://dot.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://jeevanpramaan.gov.in/", "is_internal": false}, {"text": "<PERSON><PERSON><PERSON>", "url": "https://dotpension.gov.in/Home/Feedback ", "is_internal": true}, {"text": "Contact us", "url": "https://dotpension.gov.in/", "is_internal": true}, {"text": "Policies", "url": "https://dotpension.gov.in/Home/Policies ", "is_internal": true}], "tables": [], "lists": [{"type": "ul", "items": ["Information", "Contact Us", "Know Your Pensioner (KYP) Form"]}, {"type": "ul", "items": ["Know Your PPO No", "Help line"]}, {"type": "ul", "items": ["Select Your Tax Regime", "Life Certificate Validity Check"]}, {"type": "ul", "items": ["Helpdesk", "Email : support[dot]cpms-dot[at]nic[dot]in", "|", "For Technical Help : 1800-113-5800", "|", "For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in"]}, {"type": "ul", "items": ["Pensioners' Portal", "|", "Department of Telecommunication", "|", "<PERSON><PERSON><PERSON>", "|", "<PERSON><PERSON><PERSON>", "|", "Contact us", "|", "Policies"]}], "metadata": {"scraped_at": "2025-06-10 11:55:39", "status_code": 200, "content_length": 42494, "source_type": "web"}}}