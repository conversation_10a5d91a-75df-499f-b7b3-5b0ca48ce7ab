"""
Optimized Model handler for CPMS chatbot with caching and async support
Reduces response time from 30-40s to under 10s
"""
import os
import time
import asyncio
import threading
from typing import Optional, Dict, Any, List
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FuturesTimeoutError
import torch
import signal
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    pipeline
)

# Optional imports for quantization
try:
    from transformers import BitsAndBytesConfig
    BITSANDBYTES_AVAILABLE = True
except ImportError:
    BITSANDBYTES_AVAILABLE = False
    print("⚠️ bitsandbytes not available, using CPU-only mode")
from peft import PeftModel, PeftConfig
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from backend.config import config

try:
    from llama_cpp import <PERSON>lama
    LLAMA_CPP_AVAILABLE = True
except ImportError:
    LLAMA_CPP_AVAILABLE = False
    print("llama-cpp-python not available. Install it for GGUF model support.")

# Global model cache to avoid reloading
_MODEL_CACHE = {}
_RESPONSE_CACHE = {}
_CACHE_LOCK = threading.Lock()

class OptimizedCPMSModelHandler:
    def __init__(self, model_type: str = "gguf"):
        """
        Initialize optimized model handler with caching

        Args:
            model_type: "gguf" for local GGUF model, "huggingface" for HF models, "finetuned" for fine-tuned model
        """
        self.model_type = model_type
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.executor = ThreadPoolExecutor(max_workers=2)

        # Check if model is already cached
        cache_key = f"{model_type}_{config.MISTRAL_MODEL_PATH}"

        with _CACHE_LOCK:
            if cache_key in _MODEL_CACHE and config.ENABLE_MODEL_CACHING:
                print(f"Loading {model_type} model from cache...")
                cached_model = _MODEL_CACHE[cache_key]
                self.model = cached_model['model']
                self.tokenizer = cached_model.get('tokenizer')
                print(f"✅ {model_type} model loaded from cache")
            else:
                self._load_model()
                if config.ENABLE_MODEL_CACHING:
                    _MODEL_CACHE[cache_key] = {
                        'model': self.model,
                        'tokenizer': self.tokenizer,
                        'timestamp': time.time()
                    }

    def _load_model(self):
        """Load model based on type"""
        if self.model_type == "gguf":
            self._load_gguf_model_optimized()
        elif self.model_type == "huggingface":
            self._load_huggingface_model_optimized()
        elif self.model_type == "finetuned":
            self._load_finetuned_model_optimized()

    def _load_gguf_model_optimized(self):
        """Load GGUF model with optimized settings"""
        if not LLAMA_CPP_AVAILABLE:
            raise ImportError("llama-cpp-python is required for GGUF models")

        if not os.path.exists(config.MISTRAL_MODEL_PATH):
            raise FileNotFoundError(f"GGUF model not found at {config.MISTRAL_MODEL_PATH}")

        print(f"Loading optimized GGUF model from {config.MISTRAL_MODEL_PATH}")

        # Optimized settings for faster inference
        self.model = Llama(
            model_path=config.MISTRAL_MODEL_PATH,
            n_ctx=1024,  # Reduced context for faster processing
            n_threads=os.cpu_count() or 8,  # Use all available CPU threads
            n_gpu_layers=32 if torch.cuda.is_available() else 0,  # Use GPU if available
            n_batch=512,  # Larger batch for better throughput
            verbose=False,
            use_mmap=True,  # Memory mapping for faster loading
            use_mlock=True,  # Lock memory to prevent swapping
        )

        print("✅ Optimized GGUF model loaded successfully")

    def _load_huggingface_model_optimized(self):
        """Load Hugging Face model with optimizations"""
        model_name = "mistralai/Mistral-7B-Instruct-v0.2"

        print(f"Loading optimized Hugging Face model: {model_name}")

        # Configure quantization if available
        bnb_config = None
        if BITSANDBYTES_AVAILABLE and torch.cuda.is_available():
            print("🔧 Using 4-bit quantization with bitsandbytes")
            bnb_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4",
                bnb_4bit_compute_dtype=torch.float16
            )
        else:
            print("🔧 Using CPU-only mode (no quantization)")

        self.tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            use_fast=True,  # Use fast tokenizer
            padding_side="left"
        )
        self.tokenizer.pad_token = self.tokenizer.eos_token

        # Load model with appropriate configuration
        model_kwargs = {
            "trust_remote_code": True,
            "low_cpu_mem_usage": True
        }

        if bnb_config is not None:
            model_kwargs.update({
                "quantization_config": bnb_config,
                "device_map": "auto",
                "torch_dtype": torch.float16
            })
        else:
            # CPU-only configuration
            model_kwargs.update({
                "torch_dtype": torch.float32,
                "device_map": {"": "cpu"}
            })

        self.model = AutoModelForCausalLM.from_pretrained(
            model_name,
            **model_kwargs
        )

        # Enable optimizations
        if hasattr(self.model, 'eval'):
            self.model.eval()

        print("✅ Optimized Hugging Face model loaded successfully")

    def _load_finetuned_model_optimized(self):
        """Load fine-tuned model with optimizations"""
        if not os.path.exists(config.FINE_TUNED_MODEL_PATH):
            print(f"Fine-tuned model not found at {config.FINE_TUNED_MODEL_PATH}")
            print("Falling back to optimized base Hugging Face model")
            self._load_huggingface_model_optimized()
            return

        print(f"Loading optimized fine-tuned model from {config.FINE_TUNED_MODEL_PATH}")

        # Load base model with optimizations
        base_model_name = "mistralai/Mistral-7B-Instruct-v0.2"

        # Configure quantization if available
        bnb_config = None
        if BITSANDBYTES_AVAILABLE and torch.cuda.is_available():
            bnb_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4",
                bnb_4bit_compute_dtype=torch.float16
            )

        # Load base model with appropriate configuration
        model_kwargs = {
            "trust_remote_code": True,
            "low_cpu_mem_usage": True
        }

        if bnb_config is not None:
            model_kwargs.update({
                "quantization_config": bnb_config,
                "device_map": "auto",
                "torch_dtype": torch.float16
            })
        else:
            model_kwargs.update({
                "torch_dtype": torch.float32,
                "device_map": {"": "cpu"}
            })

        base_model = AutoModelForCausalLM.from_pretrained(
            base_model_name,
            **model_kwargs
        )

        # Load fine-tuned adapter
        self.model = PeftModel.from_pretrained(base_model, config.FINE_TUNED_MODEL_PATH)
        self.tokenizer = AutoTokenizer.from_pretrained(
            base_model_name,
            use_fast=True,
            padding_side="left"
        )
        self.tokenizer.pad_token = self.tokenizer.eos_token

        if hasattr(self.model, 'eval'):
            self.model.eval()

        print("✅ Optimized fine-tuned model loaded successfully")

    @lru_cache(maxsize=config.MAX_CACHE_SIZE if hasattr(config, 'MAX_CACHE_SIZE') else 100)
    def _cached_generate(self, prompt_hash: str, prompt: str, max_tokens: int, temperature: float) -> str:
        """Generate response with caching"""
        if self.model_type == "gguf":
            return self._generate_gguf_optimized(prompt, max_tokens, temperature)
        else:
            return self._generate_hf_optimized(prompt, max_tokens, temperature)

    def generate_response(self, prompt: str, max_tokens: int = None, temperature: float = None) -> str:
        """Generate response with caching and optimization"""
        max_tokens = max_tokens or config.MAX_NEW_TOKENS
        temperature = temperature or config.TEMPERATURE

        # Check cache first
        if config.ENABLE_RESPONSE_CACHING:
            prompt_hash = str(hash(prompt + str(max_tokens) + str(temperature)))

            with _CACHE_LOCK:
                if prompt_hash in _RESPONSE_CACHE:
                    cache_entry = _RESPONSE_CACHE[prompt_hash]
                    if time.time() - cache_entry['timestamp'] < config.CACHE_TTL_SECONDS:
                        print("✅ Response served from cache")
                        return cache_entry['response']

        # Generate new response
        response = self._cached_generate(prompt_hash, prompt, max_tokens, temperature)

        # Cache the response
        if config.ENABLE_RESPONSE_CACHING:
            with _CACHE_LOCK:
                _RESPONSE_CACHE[prompt_hash] = {
                    'response': response,
                    'timestamp': time.time()
                }

                # Clean old cache entries
                if len(_RESPONSE_CACHE) > config.MAX_CACHE_SIZE:
                    oldest_key = min(_RESPONSE_CACHE.keys(),
                                   key=lambda k: _RESPONSE_CACHE[k]['timestamp'])
                    del _RESPONSE_CACHE[oldest_key]

        return response

    def _generate_gguf_optimized(self, prompt: str, max_tokens: int, temperature: float) -> str:
        """Optimized GGUF generation with timeout"""
        def _generate_with_timeout():
            return self.model(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=config.TOP_P,
                top_k=config.TOP_K,
                stop=["</s>", "### User:", "### Human:"],  # Removed \n\n to allow complete responses
                echo=False,
                repeat_penalty=1.1,
                tfs_z=1.0,  # Tail free sampling for better quality
                typical_p=1.0
            )

        try:
            # Use ThreadPoolExecutor with timeout for generation
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(_generate_with_timeout)
                try:
                    response = future.result(timeout=config.MODEL_GENERATION_TIMEOUT)
                    return response['choices'][0]['text'].strip()
                except FuturesTimeoutError:
                    print(f"⚠️ Model generation timed out after {config.MODEL_GENERATION_TIMEOUT} seconds")
                    return "I apologize, but the response is taking too long to generate. Please try a simpler question or try again later."

        except Exception as e:
            print(f"Error generating response with GGUF model: {e}")
            return "I apologize, but I'm having trouble generating a response right now. Please try again."

    def _generate_hf_optimized(self, prompt: str, max_tokens: int, temperature: float) -> str:
        """Optimized Hugging Face generation with timeout"""
        def _generate_with_timeout():
            inputs = self.tokenizer.encode(prompt, return_tensors="pt", truncate=True, max_length=1024)

            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_new_tokens=max_tokens,
                    temperature=temperature,
                    top_p=config.TOP_P,
                    top_k=config.TOP_K,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    repetition_penalty=1.1,
                    use_cache=True,  # Enable KV cache for faster generation
                    num_beams=1,  # Disable beam search for speed
                )

            return self.tokenizer.decode(outputs[0][inputs.shape[1]:], skip_special_tokens=True)

        try:
            # Use ThreadPoolExecutor with timeout for generation
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(_generate_with_timeout)
                try:
                    response = future.result(timeout=config.MODEL_GENERATION_TIMEOUT)
                    return response.strip()
                except FuturesTimeoutError:
                    print(f"⚠️ Model generation timed out after {config.MODEL_GENERATION_TIMEOUT} seconds")
                    return "I apologize, but the response is taking too long to generate. Please try a simpler question or try again later."

        except Exception as e:
            print(f"Error generating response with HF model: {e}")
            return "I apologize, but I'm having trouble generating a response right now. Please try again."

    def format_prompt(self, user_query: str, context: str = "") -> str:
        """Format prompt for the model with length optimization"""
        # Truncate context if too long
        if context and len(context) > config.MAX_CONTEXT_LENGTH:
            context = context[:config.MAX_CONTEXT_LENGTH] + "..."

        if context:
            system_message = f"{config.SYSTEM_PROMPT}\n\nRelevant Information:\n{context}"
        else:
            system_message = config.SYSTEM_PROMPT

        prompt = config.INSTRUCTION_TEMPLATE.format(
            system=system_message,
            user=user_query,
            assistant=""
        )

        return prompt

    def chat(self, user_query: str, context: str = "") -> str:
        """Optimized chat method"""
        prompt = self.format_prompt(user_query, context)
        response = self.generate_response(prompt)

        # Clean up response
        response = self._clean_response(response)

        return response

    def _clean_response(self, response: str) -> str:
        """Clean up model response"""
        # Remove common artifacts
        response = response.replace("### Assistant:", "").strip()
        response = response.replace("### System:", "").strip()
        response = response.replace("### User:", "").strip()

        # Remove repetitive patterns
        lines = response.split('\n')
        cleaned_lines = []
        prev_line = ""

        for line in lines:
            line = line.strip()
            if line and line != prev_line:
                cleaned_lines.append(line)
                prev_line = line

        cleaned_response = '\n'.join(cleaned_lines)

        # Check if response seems incomplete and add completion note if needed
        if self._is_response_incomplete(cleaned_response):
            print("⚠️ Warning: Response may be incomplete due to token limit")

        return cleaned_response

    def _is_response_incomplete(self, response: str) -> bool:
        """Check if response appears to be incomplete"""
        if not response:
            return True

        # Check for common incomplete patterns
        incomplete_patterns = [
            response.endswith('...'),
            response.endswith(','),
            response.endswith('and'),
            response.endswith('or'),
            response.endswith('the'),
            response.endswith('to'),
            response.endswith('for'),
            response.endswith('with'),
            response.endswith('in'),
            response.endswith('on'),
            response.endswith('at'),
            response.endswith('by'),
            len(response.split()) < 10,  # Very short responses might be incomplete
        ]

        return any(incomplete_patterns)

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        info = {
            'model_type': self.model_type,
            'model_path': config.MISTRAL_MODEL_PATH if self.model_type == "gguf" else "huggingface",
            'status': 'loaded' if self.model else 'not_loaded',
            'optimizations_enabled': True,
            'caching_enabled': config.ENABLE_MODEL_CACHING,
            'response_caching_enabled': config.ENABLE_RESPONSE_CACHING
        }

        if self.model_type == "gguf" and self.model:
            info['context_length'] = getattr(self.model, 'n_ctx', 'unknown')

        return info

def clear_caches():
    """Clear all caches"""
    global _MODEL_CACHE, _RESPONSE_CACHE
    with _CACHE_LOCK:
        _MODEL_CACHE.clear()
        _RESPONSE_CACHE.clear()
    print("✅ All caches cleared")
