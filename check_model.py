#!/usr/bin/env python3
"""
Check if the local GGUF model exists and verify configuration
"""
import os
from pathlib import Path

def check_model():
    """Check if the model exists and show details"""
    model_dir = "/home/<USER>/CPMS_CHATBOT/models/mistral-7b-instruct-v0.2.Q4_K_S"
    
    print("🔍 Checking local model...")
    print(f"Directory: {model_dir}")
    
    if not os.path.exists(model_dir):
        print("❌ Model directory does not exist!")
        return False
    
    print("✅ Model directory exists")
    
    # List all files in the directory
    print("\n📁 Files in model directory:")
    files = os.listdir(model_dir)
    for file in files:
        file_path = os.path.join(model_dir, file)
        if os.path.isfile(file_path):
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            print(f"  - {file} ({size_mb:.1f} MB)")
    
    # Check for GGUF files specifically
    gguf_files = [f for f in files if f.endswith('.gguf')]
    
    if not gguf_files:
        print("\n❌ No .gguf files found!")
        print("💡 Expected filename: mistral-7b-instruct-v0.2.Q4_K_S.gguf")
        return False
    
    print(f"\n✅ Found {len(gguf_files)} GGUF file(s):")
    for gguf_file in gguf_files:
        full_path = os.path.join(model_dir, gguf_file)
        size_mb = os.path.getsize(full_path) / (1024 * 1024)
        print(f"  - {gguf_file} ({size_mb:.1f} MB)")
        print(f"    Full path: {full_path}")
    
    # Check the expected filename
    expected_file = "mistral-7b-instruct-v0.2.Q4_K_S.gguf"
    expected_path = os.path.join(model_dir, expected_file)
    
    if os.path.exists(expected_path):
        print(f"\n✅ Expected model file found: {expected_file}")
        size_mb = os.path.getsize(expected_path) / (1024 * 1024)
        print(f"   Size: {size_mb:.1f} MB")
        print(f"   Path: {expected_path}")
        return expected_path
    else:
        print(f"\n⚠️ Expected file '{expected_file}' not found")
        if gguf_files:
            print(f"   But found: {gguf_files[0]}")
            print(f"   You may need to rename it to: {expected_file}")
            return os.path.join(model_dir, gguf_files[0])
        return False

def test_config():
    """Test the configuration"""
    print("\n🔧 Testing configuration...")
    
    try:
        # Add the backend to path
        import sys
        sys.path.insert(0, 'backend')
        
        from config import config
        print(f"✅ Config loaded")
        print(f"   MISTRAL_MODEL_PATH: {config.MISTRAL_MODEL_PATH}")
        
        if os.path.exists(config.MISTRAL_MODEL_PATH):
            print("✅ Model path exists in config")
            return True
        else:
            print("❌ Model path in config does not exist")
            return False
            
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔍 CPMS Model Checker")
    print("=" * 30)
    
    model_path = check_model()
    
    if model_path:
        print(f"\n🎯 Recommended configuration:")
        print(f"MISTRAL_MODEL_PATH = \"{model_path}\"")
        
        # Test current config
        config_ok = test_config()
        
        if not config_ok:
            print(f"\n💡 To fix the configuration:")
            print(f"1. Edit backend/config/config.py")
            print(f"2. Set: MISTRAL_MODEL_PATH = \"{model_path}\"")
            print(f"3. Or set environment variable: export MISTRAL_MODEL_PATH=\"{model_path}\"")
    else:
        print("\n❌ No usable model found")
        print("💡 Download a model or check the path")
