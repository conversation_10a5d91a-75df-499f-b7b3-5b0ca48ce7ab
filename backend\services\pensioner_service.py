"""
Pensioner service for handling pensioner-specific queries
"""
from datetime import datetime, date
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
import logging

from backend.models.pensioner_models import Pensioner, PensionDetails, QueryLog
from backend.database.database import get_system_config

class PensionerService:
    """Service for handling pensioner-specific queries"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def log_query(self, pensioner_id: int, query_text: str, query_type: str, 
                  response_text: str, processing_time: float, session_id: str, 
                  db: Session) -> None:
        """Log pensioner query for audit purposes"""
        try:
            query_log = QueryLog(
                pensioner_id=pensioner_id,
                query_text=query_text,
                query_type=query_type,
                response_text=response_text,
                processing_time=processing_time,
                session_id=session_id
            )
            db.add(query_log)
            db.commit()
        except Exception as e:
            self.logger.error(f"❌ Error logging query: {e}")
    
    def get_lc_expiry_date(self, pensioner: Pensioner, db: Session) -> Dict[str, Any]:
        """Get LC (Life Certificate) expiry date"""
        try:
            pension_details = db.query(PensionDetails).filter(
                PensionDetails.pensioner_id == pensioner.id
            ).first()
            
            if not pension_details or not pension_details.lc_expiry_date:
                return {
                    "success": False,
                    "message": "LC expiry information not available. Please contact CCA office.",
                    "data": None
                }
            
            # Calculate days remaining
            today = date.today()
            days_remaining = (pension_details.lc_expiry_date - today).days
            
            status = "valid"
            if days_remaining < 0:
                status = "expired"
            elif days_remaining <= 30:
                status = "expiring_soon"
            
            return {
                "success": True,
                "message": f"Your Life Certificate expires on {pension_details.lc_expiry_date.strftime('%d-%m-%Y')}",
                "data": {
                    "lc_expiry_date": pension_details.lc_expiry_date.strftime('%d-%m-%Y'),
                    "days_remaining": days_remaining,
                    "status": status,
                    "last_submitted": pension_details.lc_last_submitted.strftime('%d-%m-%Y') if pension_details.lc_last_submitted else None
                }
            }
        except Exception as e:
            self.logger.error(f"❌ Error getting LC expiry: {e}")
            return {
                "success": False,
                "message": "Unable to retrieve LC information. Please try again later.",
                "data": None
            }
    
    def get_pension_breakup(self, pensioner: Pensioner, db: Session) -> Dict[str, Any]:
        """Get last month pension breakup"""
        try:
            pension_details = db.query(PensionDetails).filter(
                PensionDetails.pensioner_id == pensioner.id
            ).first()
            
            if not pension_details:
                return {
                    "success": False,
                    "message": "Pension details not available. Please contact CCA office.",
                    "data": None
                }
            
            return {
                "success": True,
                "message": f"Pension breakup for {pension_details.last_pension_month or 'current month'}",
                "data": {
                    "basic_pension": pension_details.basic_pension,
                    "da_amount": pension_details.da_amount,
                    "medical_allowance": pension_details.medical_allowance,
                    "additional_pension": pension_details.additional_pension,
                    "total_pension": pension_details.total_pension,
                    "month": pension_details.last_pension_month or datetime.now().strftime('%Y-%m')
                }
            }
        except Exception as e:
            self.logger.error(f"❌ Error getting pension breakup: {e}")
            return {
                "success": False,
                "message": "Unable to retrieve pension breakup. Please try again later.",
                "data": None
            }
    
    def get_commutation_restoration_date(self, pensioner: Pensioner, db: Session) -> Dict[str, Any]:
        """Get commutation restoration date"""
        try:
            pension_details = db.query(PensionDetails).filter(
                PensionDetails.pensioner_id == pensioner.id
            ).first()
            
            if not pension_details:
                return {
                    "success": False,
                    "message": "Pension details not available. Please contact CCA office.",
                    "data": None
                }
            
            if not pension_details.commutation_date:
                return {
                    "success": True,
                    "message": "No commutation found in your records.",
                    "data": {
                        "has_commutation": False
                    }
                }
            
            return {
                "success": True,
                "message": "Commutation restoration information",
                "data": {
                    "has_commutation": True,
                    "commutation_date": pension_details.commutation_date.strftime('%d-%m-%Y'),
                    "commutation_amount": pension_details.commutation_amount,
                    "restoration_date": pension_details.commutation_restoration_date.strftime('%d-%m-%Y') if pension_details.commutation_restoration_date else None,
                    "is_restored": pension_details.commutation_restored
                }
            }
        except Exception as e:
            self.logger.error(f"❌ Error getting commutation info: {e}")
            return {
                "success": False,
                "message": "Unable to retrieve commutation information. Please try again later.",
                "data": None
            }
    
    def get_current_da_rate(self, pensioner: Pensioner, db: Session) -> Dict[str, Any]:
        """Get current DA rate"""
        try:
            # Get system-wide DA rate
            system_da_rate = get_system_config("current_da_rate", "50.0")
            
            # Get pensioner's specific DA details
            pension_details = db.query(PensionDetails).filter(
                PensionDetails.pensioner_id == pensioner.id
            ).first()
            
            return {
                "success": True,
                "message": "Current Dearness Allowance information",
                "data": {
                    "current_da_rate": float(system_da_rate),
                    "your_da_amount": pension_details.da_amount if pension_details else 0.0,
                    "effective_date": pension_details.da_effective_date.strftime('%d-%m-%Y') if pension_details and pension_details.da_effective_date else None
                }
            }
        except Exception as e:
            self.logger.error(f"❌ Error getting DA rate: {e}")
            return {
                "success": False,
                "message": "Unable to retrieve DA information. Please try again later.",
                "data": None
            }
    
    def get_additional_pension_rate(self, pensioner: Pensioner, db: Session) -> Dict[str, Any]:
        """Get additional pension rate"""
        try:
            pension_details = db.query(PensionDetails).filter(
                PensionDetails.pensioner_id == pensioner.id
            ).first()
            
            if not pension_details:
                return {
                    "success": False,
                    "message": "Pension details not available. Please contact CCA office.",
                    "data": None
                }
            
            return {
                "success": True,
                "message": "Additional pension information",
                "data": {
                    "additional_pension_rate": pension_details.additional_pension_rate,
                    "additional_pension_amount": pension_details.additional_pension,
                    "effective_date": pension_details.additional_pension_effective_date.strftime('%d-%m-%Y') if pension_details.additional_pension_effective_date else None
                }
            }
        except Exception as e:
            self.logger.error(f"❌ Error getting additional pension: {e}")
            return {
                "success": False,
                "message": "Unable to retrieve additional pension information. Please try again later.",
                "data": None
            }
    
    def get_family_pension_info(self, pensioner: Pensioner, db: Session) -> Dict[str, Any]:
        """Get normal and enhanced family pension information"""
        try:
            pension_details = db.query(PensionDetails).filter(
                PensionDetails.pensioner_id == pensioner.id
            ).first()
            
            if not pension_details:
                return {
                    "success": False,
                    "message": "Pension details not available. Please contact CCA office.",
                    "data": None
                }
            
            return {
                "success": True,
                "message": "Family pension information",
                "data": {
                    "normal_family_pension": pension_details.normal_family_pension,
                    "enhanced_family_pension": pension_details.enhanced_family_pension,
                    "effective_date": pension_details.family_pension_effective_date.strftime('%d-%m-%Y') if pension_details.family_pension_effective_date else None
                }
            }
        except Exception as e:
            self.logger.error(f"❌ Error getting family pension: {e}")
            return {
                "success": False,
                "message": "Unable to retrieve family pension information. Please try again later.",
                "data": None
            }
    
    def get_fma_info(self, pensioner: Pensioner, db: Session) -> Dict[str, Any]:
        """Get FMA (Fixed Medical Allowance) information"""
        try:
            pension_details = db.query(PensionDetails).filter(
                PensionDetails.pensioner_id == pensioner.id
            ).first()
            
            # Get system FMA rate as fallback
            system_fma = get_system_config("fma_rate", "1000.0")
            
            return {
                "success": True,
                "message": "Fixed Medical Allowance information",
                "data": {
                    "fma_amount": pension_details.fma_amount if pension_details else float(system_fma),
                    "effective_date": pension_details.fma_effective_date.strftime('%d-%m-%Y') if pension_details and pension_details.fma_effective_date else None
                }
            }
        except Exception as e:
            self.logger.error(f"❌ Error getting FMA info: {e}")
            return {
                "success": False,
                "message": "Unable to retrieve FMA information. Please try again later.",
                "data": None
            }
    
    def get_mobile_change_info(self, pensioner: Pensioner, db: Session) -> Dict[str, Any]:
        """Get information about changing mobile number"""
        return {
            "success": True,
            "message": "How to change your mobile number",
            "data": {
                "process": [
                    "Visit your nearest CCA office with original documents",
                    "Fill the mobile number change request form",
                    "Submit Aadhaar card copy and mobile number proof",
                    "Get acknowledgment receipt",
                    "Changes will be updated within 7 working days"
                ],
                "required_documents": [
                    "Original Aadhaar Card",
                    "Copy of new mobile number bill/proof",
                    "Pension Payment Order (PPO)",
                    "Identity proof"
                ],
                "contact_info": {
                    "helpline": "1800-113-5800",
                    "email": "<EMAIL>"
                }
            }
        }
    
    def get_sampann_migration_info(self, pensioner: Pensioner, db: Session) -> Dict[str, Any]:
        """Get Sampann migration date and status"""
        try:
            pension_details = db.query(PensionDetails).filter(
                PensionDetails.pensioner_id == pensioner.id
            ).first()
            
            if not pension_details:
                return {
                    "success": False,
                    "message": "Migration details not available. Please contact CCA office.",
                    "data": None
                }
            
            return {
                "success": True,
                "message": "Sampann migration information",
                "data": {
                    "migration_date": pension_details.sampann_migration_date.strftime('%d-%m-%Y') if pension_details.sampann_migration_date else None,
                    "migration_status": pension_details.sampann_migration_status,
                    "is_migrated": pension_details.sampann_migration_status == "completed"
                }
            }
        except Exception as e:
            self.logger.error(f"❌ Error getting migration info: {e}")
            return {
                "success": False,
                "message": "Unable to retrieve migration information. Please try again later.",
                "data": None
            }

# Global pensioner service instance
pensioner_service = PensionerService()
