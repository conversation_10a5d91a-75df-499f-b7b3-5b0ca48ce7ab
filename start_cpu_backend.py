#!/usr/bin/env python3
"""
CPU-only backend starter for Ubuntu server
Handles missing models and dependencies gracefully
"""
import os
import sys
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent / "backend"))

def check_environment():
    """Check if environment is ready"""
    print("🔍 Checking environment...")
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7+ required")
        return False
    
    print(f"✅ Python {sys.version}")
    
    # Check critical imports
    try:
        import torch
        print(f"✅ PyTorch {torch.__version__}")
    except ImportError:
        print("❌ PyTorch not installed")
        return False
    
    try:
        from transformers import AutoTokenizer
        print("✅ Transformers available")
    except ImportError:
        print("❌ Transformers not installed")
        return False
    
    try:
        import fastapi
        print("✅ FastAPI available")
    except ImportError:
        print("❌ FastAPI not installed")
        return False
    
    return True

def setup_cpu_environment():
    """Setup CPU-only environment"""
    print("🔧 Setting up CPU-only environment...")
    
    # Disable CUDA
    os.environ["CUDA_VISIBLE_DEVICES"] = ""
    
    # Set CPU threads
    import torch
    torch.set_num_threads(os.cpu_count() or 4)
    
    print(f"   - CPU threads: {torch.get_num_threads()}")
    print("   - GPU disabled")

def start_backend():
    """Start the backend server"""
    print("🚀 Starting CPMS Backend (CPU-only mode)...")
    
    try:
        # Import and start the server
        from backend.api.server import app
        import uvicorn
        
        # Run with CPU-optimized settings
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8001,
            reload=False,
            log_level="info",
            workers=1,  # Single worker for CPU
            timeout_keep_alive=30,
            limit_concurrency=10,  # Reduced for CPU
            limit_max_requests=100
        )
        
    except Exception as e:
        print(f"❌ Failed to start backend: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Run: pip install -r backend/requirements.txt")
        print("2. Check if all dependencies are installed")
        print("3. Ensure database is initialized")
        return False
    
    return True

def main():
    """Main function"""
    print("🐧 CPMS Backend - CPU-Only Mode")
    print("=" * 40)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed")
        print("Run the setup script first: ./ubuntu_cpu_setup.sh")
        sys.exit(1)
    
    # Setup CPU environment
    setup_cpu_environment()
    
    # Start backend
    print("\n🎯 Backend will be available at:")
    print("   - API: http://your-server-ip:8001")
    print("   - Docs: http://your-server-ip:8001/docs")
    print("\n⚠️  Note: Using CPU-only mode (slower inference)")
    print("=" * 40)
    
    if not start_backend():
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Backend stopped by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
