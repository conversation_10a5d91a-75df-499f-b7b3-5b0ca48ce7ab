{"instruction": "How to create DH login in CPMS?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "How to create DH login in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "What is the procedure to create Dealing Hand login?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "What is the procedure to create Dealing Hand login?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "How to create DH user in pension section?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "How to create DH user in pension section?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "What are the steps for DH creation in CPMS?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "What are the steps for DH creation in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "How to assign rights to DH user?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "How to assign rights to DH user?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "How to create AO login in CPMS?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    The case will now flow into Calculation Sheet at DH (Pension Sanctioning Section) level (Action->Pension Section-> Calculation Sheet).  DH (Pension Sanctioning Section) will select the case and view the calculation sheet and submit it to AAO for further approval. (3.8) 69 3.8 70  Now the case will flow to AAO (Pension Sanctioning Section) (AAO Login Approval Pension Section Calculation Sheet)  The AAO (Pension Sanctioning Section) can either Approve/ Return the case. (3.9)  If AAO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. In case of Approval, it will go to AO (Pension Sanctioning Section).  Now the case will flow to AO (Pension Sanctioning Section) (AO Login Approval Pension Section Calculation Sheet)  The AO (Pension Sanctioning Section) can Approve/ Return the case.  If AO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. 3.9 (a) 71 3.9 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Calculation Sheet", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "How to create AO login in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create an AO (Accounts Officer) login in CPMS:\n\n    The case will now flow into Calculation Sheet at DH (Pension Sanctioning Section) level (Action->Pension Section-> Calculation Sheet).  DH (Pension Sanctioning Section) will select the case and view the calculation sheet and submit it to AAO for further approval. (3.8) 69 3.8 70  Now the case will flow to AAO (Pension Sanctioning Section) (AAO Login Approval Pension Section Calculation Sheet)  The AAO (Pension Sanctioning Section) can either Approve/ Return the case. (3.9)  If AAO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. In case of Approval, it will go to AO (Pension Sanctioning Section).  Now the case will flow to AO (Pension Sanctioning Section) (AO Login Approval Pension Section Calculation Sheet)  The AO (Pension Sanctioning Section) can Approve/ Return the case.  If AO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. 3.9 (a) 71 3.9 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Calculation Sheet", "procedure_type": "AO_LOGIN_CREATION", "enhanced": true}
{"instruction": "What is the path to create AO user login?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    The case will now flow into Calculation Sheet at DH (Pension Sanctioning Section) level (Action->Pension Section-> Calculation Sheet).  DH (Pension Sanctioning Section) will select the case and view the calculation sheet and submit it to AAO for further approval. (3.8) 69 3.8 70  Now the case will flow to AAO (Pension Sanctioning Section) (AAO Login Approval Pension Section Calculation Sheet)  The AAO (Pension Sanctioning Section) can either Approve/ Return the case. (3.9)  If AAO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. In case of Approval, it will go to AO (Pension Sanctioning Section).  Now the case will flow to AO (Pension Sanctioning Section) (AO Login Approval Pension Section Calculation Sheet)  The AO (Pension Sanctioning Section) can Approve/ Return the case.  If AO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. 3.9 (a) 71 3.9 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Calculation Sheet", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "How does CCA create AO login?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    The case will now flow into Calculation Sheet at DH (Pension Sanctioning Section) level (Action->Pension Section-> Calculation Sheet).  DH (Pension Sanctioning Section) will select the case and view the calculation sheet and submit it to AAO for further approval. (3.8) 69 3.8 70  Now the case will flow to AAO (Pension Sanctioning Section) (AAO Login Approval Pension Section Calculation Sheet)  The AAO (Pension Sanctioning Section) can either Approve/ Return the case. (3.9)  If AAO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. In case of Approval, it will go to AO (Pension Sanctioning Section).  Now the case will flow to AO (Pension Sanctioning Section) (AO Login Approval Pension Section Calculation Sheet)  The AO (Pension Sanctioning Section) can Approve/ Return the case.  If AO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. 3.9 (a) 71 3.9 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Calculation Sheet", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "What are the steps to create Accounts Officer login?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    The case will now flow into Calculation Sheet at DH (Pension Sanctioning Section) level (Action->Pension Section-> Calculation Sheet).  DH (Pension Sanctioning Section) will select the case and view the calculation sheet and submit it to AAO for further approval. (3.8) 69 3.8 70  Now the case will flow to AAO (Pension Sanctioning Section) (AAO Login Approval Pension Section Calculation Sheet)  The AAO (Pension Sanctioning Section) can either Approve/ Return the case. (3.9)  If AAO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. In case of Approval, it will go to AO (Pension Sanctioning Section).  Now the case will flow to AO (Pension Sanctioning Section) (AO Login Approval Pension Section Calculation Sheet)  The AO (Pension Sanctioning Section) can Approve/ Return the case.  If AO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. 3.9 (a) 71 3.9 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Calculation Sheet", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "What are the steps to create Accounts Officer login?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create an AO (Accounts Officer) login in CPMS:\n\n    The case will now flow into Calculation Sheet at DH (Pension Sanctioning Section) level (Action->Pension Section-> Calculation Sheet).  DH (Pension Sanctioning Section) will select the case and view the calculation sheet and submit it to AAO for further approval. (3.8) 69 3.8 70  Now the case will flow to AAO (Pension Sanctioning Section) (AAO Login Approval Pension Section Calculation Sheet)  The AAO (Pension Sanctioning Section) can either Approve/ Return the case. (3.9)  If AAO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. In case of Approval, it will go to AO (Pension Sanctioning Section).  Now the case will flow to AO (Pension Sanctioning Section) (AO Login Approval Pension Section Calculation Sheet)  The AO (Pension Sanctioning Section) can Approve/ Return the case.  If AO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. 3.9 (a) 71 3.9 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Calculation Sheet", "procedure_type": "AO_LOGIN_CREATION", "enhanced": true}
{"instruction": "How to assign authorization to AO in CPMS?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    The case will now flow into Calculation Sheet at DH (Pension Sanctioning Section) level (Action->Pension Section-> Calculation Sheet).  DH (Pension Sanctioning Section) will select the case and view the calculation sheet and submit it to AAO for further approval. (3.8) 69 3.8 70  Now the case will flow to AAO (Pension Sanctioning Section) (AAO Login Approval Pension Section Calculation Sheet)  The AAO (Pension Sanctioning Section) can either Approve/ Return the case. (3.9)  If AAO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. In case of Approval, it will go to AO (Pension Sanctioning Section).  Now the case will flow to AO (Pension Sanctioning Section) (AO Login Approval Pension Section Calculation Sheet)  The AO (Pension Sanctioning Section) can Approve/ Return the case.  If AO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. 3.9 (a) 71 3.9 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Calculation Sheet", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "How to assign authorization to AO in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create an AO (Accounts Officer) login in CPMS:\n\n    The case will now flow into Calculation Sheet at DH (Pension Sanctioning Section) level (Action->Pension Section-> Calculation Sheet).  DH (Pension Sanctioning Section) will select the case and view the calculation sheet and submit it to AAO for further approval. (3.8) 69 3.8 70  Now the case will flow to AAO (Pension Sanctioning Section) (AAO Login Approval Pension Section Calculation Sheet)  The AAO (Pension Sanctioning Section) can either Approve/ Return the case. (3.9)  If AAO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. In case of Approval, it will go to AO (Pension Sanctioning Section).  Now the case will flow to AO (Pension Sanctioning Section) (AO Login Approval Pension Section Calculation Sheet)  The AO (Pension Sanctioning Section) can Approve/ Return the case.  If AO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. 3.9 (a) 71 3.9 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Calculation Sheet", "procedure_type": "AO_LOGIN_CREATION", "enhanced": true}
{"instruction": "How to create AO login in CPMS?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    After the Approval of Calculation Sheet by the AO, the case will come to Pension Sanction tab of DH Login.(DH Login Action Pension Section Pension Sanction)  DH can view Sanctions and ePPO. 3.10) (a)  Now DH will send the case to AAO for approval.  AAO can approve the case and send it to AO for further approval. (AAO Login Action Pension Section Pension Sanction)  AO can approve the case (AO Login Action Pension Section Pension Sanction)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pension Sanction", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "How to create AO login in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create an AO (Accounts Officer) login in CPMS:\n\n    After the Approval of Calculation Sheet by the AO, the case will come to Pension Sanction tab of DH Login.(DH Login Action Pension Section Pension Sanction)  DH can view Sanctions and ePPO. 3.10) (a)  Now DH will send the case to AAO for approval.  AAO can approve the case and send it to AO for further approval. (AAO Login Action Pension Section Pension Sanction)  AO can approve the case (AO Login Action Pension Section Pension Sanction)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Pension Sanction", "procedure_type": "AO_LOGIN_CREATION", "enhanced": true}
{"instruction": "What is the path to create AO user login?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    After the Approval of Calculation Sheet by the AO, the case will come to Pension Sanction tab of DH Login.(DH Login Action Pension Section Pension Sanction)  DH can view Sanctions and ePPO. 3.10) (a)  Now DH will send the case to AAO for approval.  AAO can approve the case and send it to AO for further approval. (AAO Login Action Pension Section Pension Sanction)  AO can approve the case (AO Login Action Pension Section Pension Sanction)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pension Sanction", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "How does CCA create AO login?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    After the Approval of Calculation Sheet by the AO, the case will come to Pension Sanction tab of DH Login.(DH Login Action Pension Section Pension Sanction)  DH can view Sanctions and ePPO. 3.10) (a)  Now DH will send the case to AAO for approval.  AAO can approve the case and send it to AO for further approval. (AAO Login Action Pension Section Pension Sanction)  AO can approve the case (AO Login Action Pension Section Pension Sanction)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pension Sanction", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "What are the steps to create Accounts Officer login?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    After the Approval of Calculation Sheet by the AO, the case will come to Pension Sanction tab of DH Login.(DH Login Action Pension Section Pension Sanction)  DH can view Sanctions and ePPO. 3.10) (a)  Now DH will send the case to AAO for approval.  AAO can approve the case and send it to AO for further approval. (AAO Login Action Pension Section Pension Sanction)  AO can approve the case (AO Login Action Pension Section Pension Sanction)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pension Sanction", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "What are the steps to create Accounts Officer login?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create an AO (Accounts Officer) login in CPMS:\n\n    After the Approval of Calculation Sheet by the AO, the case will come to Pension Sanction tab of DH Login.(DH Login Action Pension Section Pension Sanction)  DH can view Sanctions and ePPO. 3.10) (a)  Now DH will send the case to AAO for approval.  AAO can approve the case and send it to AO for further approval. (AAO Login Action Pension Section Pension Sanction)  AO can approve the case (AO Login Action Pension Section Pension Sanction)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Pension Sanction", "procedure_type": "AO_LOGIN_CREATION", "enhanced": true}
{"instruction": "How to assign authorization to AO in CPMS?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    After the Approval of Calculation Sheet by the AO, the case will come to Pension Sanction tab of DH Login.(DH Login Action Pension Section Pension Sanction)  DH can view Sanctions and ePPO. 3.10) (a)  Now DH will send the case to AAO for approval.  AAO can approve the case and send it to AO for further approval. (AAO Login Action Pension Section Pension Sanction)  AO can approve the case (AO Login Action Pension Section Pension Sanction)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pension Sanction", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "How to assign authorization to AO in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create an AO (Accounts Officer) login in CPMS:\n\n    After the Approval of Calculation Sheet by the AO, the case will come to Pension Sanction tab of DH Login.(DH Login Action Pension Section Pension Sanction)  DH can view Sanctions and ePPO. 3.10) (a)  Now DH will send the case to AAO for approval.  AAO can approve the case and send it to AO for further approval. (AAO Login Action Pension Section Pension Sanction)  AO can approve the case (AO Login Action Pension Section Pension Sanction)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Pension Sanction", "procedure_type": "AO_LOGIN_CREATION", "enhanced": true}
{"instruction": "How to create DH login in CPMS?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    Now, from the DH login of PDA Section go to Action then Vendor Verification (DH PDA Login Action Vendor Verification). The case will appear here in the name of pensioners/ claimants. From PFMS the pensioner’s/ claimant’s (Vendor) bank credentials will be verified and the status with error description in the table will be shown as in Fig. 4.3 below. This process takes approximately 5-10 minutes and when the verification is complete, the case will automatically go to Bill Generation Tab. Otherwise failed cases will remain at this stage which may be Resend.  In case the failure is due to wrong Bank account details, the same will be flashed in the Error Description column. The wrong Bank account details can be rectified from the PDA Utility tab in the AO PDA (Go to AO PDA login, open PDA Utility tab, enter PPO no. of the particular case and edit the Bank details as shown in Fig. 4.4(a) and Fig. 4.4(b)). 76 Fig.4.3  If the case has failed because of any reason other than wrong bank details, the case may be Resend. Fig.4.4 77 Fig.4.4(a) Fig.4.4(b) After completion of Vendor Verification in PFMS the bill will move to the next section of DH log-in of PDA Section i.e. Bill Generation.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "How to create DH login in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    Now, from the DH login of PDA Section go to Action then Vendor Verification (DH PDA Login Action Vendor Verification). The case will appear here in the name of pensioners/ claimants. From PFMS the pensioner’s/ claimant’s (Vendor) bank credentials will be verified and the status with error description in the table will be shown as in Fig. 4.3 below. This process takes approximately 5-10 minutes and when the verification is complete, the case will automatically go to Bill Generation Tab. Otherwise failed cases will remain at this stage which may be Resend.  In case the failure is due to wrong Bank account details, the same will be flashed in the Error Description column. The wrong Bank account details can be rectified from the PDA Utility tab in the AO PDA (Go to AO PDA login, open PDA Utility tab, enter PPO no. of the particular case and edit the Bank details as shown in Fig. 4.4(a) and Fig. 4.4(b)). 76 Fig.4.3  If the case has failed because of any reason other than wrong bank details, the case may be Resend. Fig.4.4 77 Fig.4.4(a) Fig.4.4(b) After completion of Vendor Verification in PFMS the bill will move to the next section of DH log-in of PDA Section i.e. Bill Generation.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "What is the procedure to create Dealing Hand login?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    Now, from the DH login of PDA Section go to Action then Vendor Verification (DH PDA Login Action Vendor Verification). The case will appear here in the name of pensioners/ claimants. From PFMS the pensioner’s/ claimant’s (Vendor) bank credentials will be verified and the status with error description in the table will be shown as in Fig. 4.3 below. This process takes approximately 5-10 minutes and when the verification is complete, the case will automatically go to Bill Generation Tab. Otherwise failed cases will remain at this stage which may be Resend.  In case the failure is due to wrong Bank account details, the same will be flashed in the Error Description column. The wrong Bank account details can be rectified from the PDA Utility tab in the AO PDA (Go to AO PDA login, open PDA Utility tab, enter PPO no. of the particular case and edit the Bank details as shown in Fig. 4.4(a) and Fig. 4.4(b)). 76 Fig.4.3  If the case has failed because of any reason other than wrong bank details, the case may be Resend. Fig.4.4 77 Fig.4.4(a) Fig.4.4(b) After completion of Vendor Verification in PFMS the bill will move to the next section of DH log-in of PDA Section i.e. Bill Generation.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "What is the procedure to create Dealing Hand login?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    Now, from the DH login of PDA Section go to Action then Vendor Verification (DH PDA Login Action Vendor Verification). The case will appear here in the name of pensioners/ claimants. From PFMS the pensioner’s/ claimant’s (Vendor) bank credentials will be verified and the status with error description in the table will be shown as in Fig. 4.3 below. This process takes approximately 5-10 minutes and when the verification is complete, the case will automatically go to Bill Generation Tab. Otherwise failed cases will remain at this stage which may be Resend.  In case the failure is due to wrong Bank account details, the same will be flashed in the Error Description column. The wrong Bank account details can be rectified from the PDA Utility tab in the AO PDA (Go to AO PDA login, open PDA Utility tab, enter PPO no. of the particular case and edit the Bank details as shown in Fig. 4.4(a) and Fig. 4.4(b)). 76 Fig.4.3  If the case has failed because of any reason other than wrong bank details, the case may be Resend. Fig.4.4 77 Fig.4.4(a) Fig.4.4(b) After completion of Vendor Verification in PFMS the bill will move to the next section of DH log-in of PDA Section i.e. Bill Generation.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "How to create DH user in pension section?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    Now, from the DH login of PDA Section go to Action then Vendor Verification (DH PDA Login Action Vendor Verification). The case will appear here in the name of pensioners/ claimants. From PFMS the pensioner’s/ claimant’s (Vendor) bank credentials will be verified and the status with error description in the table will be shown as in Fig. 4.3 below. This process takes approximately 5-10 minutes and when the verification is complete, the case will automatically go to Bill Generation Tab. Otherwise failed cases will remain at this stage which may be Resend.  In case the failure is due to wrong Bank account details, the same will be flashed in the Error Description column. The wrong Bank account details can be rectified from the PDA Utility tab in the AO PDA (Go to AO PDA login, open PDA Utility tab, enter PPO no. of the particular case and edit the Bank details as shown in Fig. 4.4(a) and Fig. 4.4(b)). 76 Fig.4.3  If the case has failed because of any reason other than wrong bank details, the case may be Resend. Fig.4.4 77 Fig.4.4(a) Fig.4.4(b) After completion of Vendor Verification in PFMS the bill will move to the next section of DH log-in of PDA Section i.e. Bill Generation.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "How to create DH user in pension section?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    Now, from the DH login of PDA Section go to Action then Vendor Verification (DH PDA Login Action Vendor Verification). The case will appear here in the name of pensioners/ claimants. From PFMS the pensioner’s/ claimant’s (Vendor) bank credentials will be verified and the status with error description in the table will be shown as in Fig. 4.3 below. This process takes approximately 5-10 minutes and when the verification is complete, the case will automatically go to Bill Generation Tab. Otherwise failed cases will remain at this stage which may be Resend.  In case the failure is due to wrong Bank account details, the same will be flashed in the Error Description column. The wrong Bank account details can be rectified from the PDA Utility tab in the AO PDA (Go to AO PDA login, open PDA Utility tab, enter PPO no. of the particular case and edit the Bank details as shown in Fig. 4.4(a) and Fig. 4.4(b)). 76 Fig.4.3  If the case has failed because of any reason other than wrong bank details, the case may be Resend. Fig.4.4 77 Fig.4.4(a) Fig.4.4(b) After completion of Vendor Verification in PFMS the bill will move to the next section of DH log-in of PDA Section i.e. Bill Generation.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "What are the steps for DH creation in CPMS?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    Now, from the DH login of PDA Section go to Action then Vendor Verification (DH PDA Login Action Vendor Verification). The case will appear here in the name of pensioners/ claimants. From PFMS the pensioner’s/ claimant’s (Vendor) bank credentials will be verified and the status with error description in the table will be shown as in Fig. 4.3 below. This process takes approximately 5-10 minutes and when the verification is complete, the case will automatically go to Bill Generation Tab. Otherwise failed cases will remain at this stage which may be Resend.  In case the failure is due to wrong Bank account details, the same will be flashed in the Error Description column. The wrong Bank account details can be rectified from the PDA Utility tab in the AO PDA (Go to AO PDA login, open PDA Utility tab, enter PPO no. of the particular case and edit the Bank details as shown in Fig. 4.4(a) and Fig. 4.4(b)). 76 Fig.4.3  If the case has failed because of any reason other than wrong bank details, the case may be Resend. Fig.4.4 77 Fig.4.4(a) Fig.4.4(b) After completion of Vendor Verification in PFMS the bill will move to the next section of DH log-in of PDA Section i.e. Bill Generation.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "What are the steps for DH creation in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    Now, from the DH login of PDA Section go to Action then Vendor Verification (DH PDA Login Action Vendor Verification). The case will appear here in the name of pensioners/ claimants. From PFMS the pensioner’s/ claimant’s (Vendor) bank credentials will be verified and the status with error description in the table will be shown as in Fig. 4.3 below. This process takes approximately 5-10 minutes and when the verification is complete, the case will automatically go to Bill Generation Tab. Otherwise failed cases will remain at this stage which may be Resend.  In case the failure is due to wrong Bank account details, the same will be flashed in the Error Description column. The wrong Bank account details can be rectified from the PDA Utility tab in the AO PDA (Go to AO PDA login, open PDA Utility tab, enter PPO no. of the particular case and edit the Bank details as shown in Fig. 4.4(a) and Fig. 4.4(b)). 76 Fig.4.3  If the case has failed because of any reason other than wrong bank details, the case may be Resend. Fig.4.4 77 Fig.4.4(a) Fig.4.4(b) After completion of Vendor Verification in PFMS the bill will move to the next section of DH log-in of PDA Section i.e. Bill Generation.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "How to assign rights to DH user?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    Now, from the DH login of PDA Section go to Action then Vendor Verification (DH PDA Login Action Vendor Verification). The case will appear here in the name of pensioners/ claimants. From PFMS the pensioner’s/ claimant’s (Vendor) bank credentials will be verified and the status with error description in the table will be shown as in Fig. 4.3 below. This process takes approximately 5-10 minutes and when the verification is complete, the case will automatically go to Bill Generation Tab. Otherwise failed cases will remain at this stage which may be Resend.  In case the failure is due to wrong Bank account details, the same will be flashed in the Error Description column. The wrong Bank account details can be rectified from the PDA Utility tab in the AO PDA (Go to AO PDA login, open PDA Utility tab, enter PPO no. of the particular case and edit the Bank details as shown in Fig. 4.4(a) and Fig. 4.4(b)). 76 Fig.4.3  If the case has failed because of any reason other than wrong bank details, the case may be Resend. Fig.4.4 77 Fig.4.4(a) Fig.4.4(b) After completion of Vendor Verification in PFMS the bill will move to the next section of DH log-in of PDA Section i.e. Bill Generation.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "How to assign rights to DH user?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    Now, from the DH login of PDA Section go to Action then Vendor Verification (DH PDA Login Action Vendor Verification). The case will appear here in the name of pensioners/ claimants. From PFMS the pensioner’s/ claimant’s (Vendor) bank credentials will be verified and the status with error description in the table will be shown as in Fig. 4.3 below. This process takes approximately 5-10 minutes and when the verification is complete, the case will automatically go to Bill Generation Tab. Otherwise failed cases will remain at this stage which may be Resend.  In case the failure is due to wrong Bank account details, the same will be flashed in the Error Description column. The wrong Bank account details can be rectified from the PDA Utility tab in the AO PDA (Go to AO PDA login, open PDA Utility tab, enter PPO no. of the particular case and edit the Bank details as shown in Fig. 4.4(a) and Fig. 4.4(b)). 76 Fig.4.3  If the case has failed because of any reason other than wrong bank details, the case may be Resend. Fig.4.4 77 Fig.4.4(a) Fig.4.4(b) After completion of Vendor Verification in PFMS the bill will move to the next section of DH log-in of PDA Section i.e. Bill Generation.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "How to create AO login in CPMS?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    After successful payment, login from DH PDA and click the tab LC & DLC Verification (DH PDA Login LC & DLC Verification  LC Verification). 82 Fig.4.12  Now click on Verify link shown in the Action column, a popup window will appear as shown in the figure below. Here we have to select the Life Certificate and Non Employment (FMA also, in case of CDA pensioners) Validity, From and to dates (From date will be next date, from the date of retirement). After filling the details click on Confirm button. (Note-The above certificate date is for non-employment of pensioner) Fig.4.13  After LC & DLC the case will go in Arrears tab (PDA DH login Action Bill Generation  Arrears) 83  Here select the Reason for Arrear and fill the PPO Number of pensioner, then click on the Search button. Now it will show the Arrear calculation. After checking the calculation, printout of the arrear details should be taken and the soft copy of the same saved (by clicking ctrl +P). Now click on the Save & Send for Approval button. Then case will go to AAO of PDA Section for approval. (The above printout will be used as Arrear Sanction for PFMS payment).  AAO PDA will select the case (by clicking the check box corresponding to the case) and click on Approve button (PDA AAO login Action ApprovalPDA Arrears). The case will go to AO of PDA Section for approval. 84  Now, AO PDA (PDA AO login Action ApprovalPDA Arrears) will select the appropriate Account Head Code from the drop down list. Now select the case (by clicking the check box corresponding to the case) and enter Not Payable Before Date and click on Approve button. Now the Bill will automatically go to PFMS for arrear payment.  After Arrears payment, the case will go to monthly bill section (PDA DH login Action Bill Generation Monthly bill (Normal/Family, IDA/CDA)).\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Arrears", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "How to create AO login in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create an AO (Accounts Officer) login in CPMS:\n\n    After successful payment, login from DH PDA and click the tab LC & DLC Verification (DH PDA Login LC & DLC Verification  LC Verification). 82 Fig.4.12  Now click on Verify link shown in the Action column, a popup window will appear as shown in the figure below. Here we have to select the Life Certificate and Non Employment (FMA also, in case of CDA pensioners) Validity, From and to dates (From date will be next date, from the date of retirement). After filling the details click on Confirm button. (Note-The above certificate date is for non-employment of pensioner) Fig.4.13  After LC & DLC the case will go in Arrears tab (PDA DH login Action Bill Generation  Arrears) 83  Here select the Reason for Arrear and fill the PPO Number of pensioner, then click on the Search button. Now it will show the Arrear calculation. After checking the calculation, printout of the arrear details should be taken and the soft copy of the same saved (by clicking ctrl +P). Now click on the Save & Send for Approval button. Then case will go to AAO of PDA Section for approval. (The above printout will be used as Arrear Sanction for PFMS payment).  AAO PDA will select the case (by clicking the check box corresponding to the case) and click on Approve button (PDA AAO login Action ApprovalPDA Arrears). The case will go to AO of PDA Section for approval. 84  Now, AO PDA (PDA AO login Action ApprovalPDA Arrears) will select the appropriate Account Head Code from the drop down list. Now select the case (by clicking the check box corresponding to the case) and enter Not Payable Before Date and click on Approve button. Now the Bill will automatically go to PFMS for arrear payment.  After Arrears payment, the case will go to monthly bill section (PDA DH login Action Bill Generation Monthly bill (Normal/Family, IDA/CDA)).\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Arrears", "procedure_type": "AO_LOGIN_CREATION", "enhanced": true}
{"instruction": "What is the path to create AO user login?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    After successful payment, login from DH PDA and click the tab LC & DLC Verification (DH PDA Login LC & DLC Verification  LC Verification). 82 Fig.4.12  Now click on Verify link shown in the Action column, a popup window will appear as shown in the figure below. Here we have to select the Life Certificate and Non Employment (FMA also, in case of CDA pensioners) Validity, From and to dates (From date will be next date, from the date of retirement). After filling the details click on Confirm button. (Note-The above certificate date is for non-employment of pensioner) Fig.4.13  After LC & DLC the case will go in Arrears tab (PDA DH login Action Bill Generation  Arrears) 83  Here select the Reason for Arrear and fill the PPO Number of pensioner, then click on the Search button. Now it will show the Arrear calculation. After checking the calculation, printout of the arrear details should be taken and the soft copy of the same saved (by clicking ctrl +P). Now click on the Save & Send for Approval button. Then case will go to AAO of PDA Section for approval. (The above printout will be used as Arrear Sanction for PFMS payment).  AAO PDA will select the case (by clicking the check box corresponding to the case) and click on Approve button (PDA AAO login Action ApprovalPDA Arrears). The case will go to AO of PDA Section for approval. 84  Now, AO PDA (PDA AO login Action ApprovalPDA Arrears) will select the appropriate Account Head Code from the drop down list. Now select the case (by clicking the check box corresponding to the case) and enter Not Payable Before Date and click on Approve button. Now the Bill will automatically go to PFMS for arrear payment.  After Arrears payment, the case will go to monthly bill section (PDA DH login Action Bill Generation Monthly bill (Normal/Family, IDA/CDA)).\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Arrears", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "How does CCA create AO login?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    After successful payment, login from DH PDA and click the tab LC & DLC Verification (DH PDA Login LC & DLC Verification  LC Verification). 82 Fig.4.12  Now click on Verify link shown in the Action column, a popup window will appear as shown in the figure below. Here we have to select the Life Certificate and Non Employment (FMA also, in case of CDA pensioners) Validity, From and to dates (From date will be next date, from the date of retirement). After filling the details click on Confirm button. (Note-The above certificate date is for non-employment of pensioner) Fig.4.13  After LC & DLC the case will go in Arrears tab (PDA DH login Action Bill Generation  Arrears) 83  Here select the Reason for Arrear and fill the PPO Number of pensioner, then click on the Search button. Now it will show the Arrear calculation. After checking the calculation, printout of the arrear details should be taken and the soft copy of the same saved (by clicking ctrl +P). Now click on the Save & Send for Approval button. Then case will go to AAO of PDA Section for approval. (The above printout will be used as Arrear Sanction for PFMS payment).  AAO PDA will select the case (by clicking the check box corresponding to the case) and click on Approve button (PDA AAO login Action ApprovalPDA Arrears). The case will go to AO of PDA Section for approval. 84  Now, AO PDA (PDA AO login Action ApprovalPDA Arrears) will select the appropriate Account Head Code from the drop down list. Now select the case (by clicking the check box corresponding to the case) and enter Not Payable Before Date and click on Approve button. Now the Bill will automatically go to PFMS for arrear payment.  After Arrears payment, the case will go to monthly bill section (PDA DH login Action Bill Generation Monthly bill (Normal/Family, IDA/CDA)).\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Arrears", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "What are the steps to create Accounts Officer login?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    After successful payment, login from DH PDA and click the tab LC & DLC Verification (DH PDA Login LC & DLC Verification  LC Verification). 82 Fig.4.12  Now click on Verify link shown in the Action column, a popup window will appear as shown in the figure below. Here we have to select the Life Certificate and Non Employment (FMA also, in case of CDA pensioners) Validity, From and to dates (From date will be next date, from the date of retirement). After filling the details click on Confirm button. (Note-The above certificate date is for non-employment of pensioner) Fig.4.13  After LC & DLC the case will go in Arrears tab (PDA DH login Action Bill Generation  Arrears) 83  Here select the Reason for Arrear and fill the PPO Number of pensioner, then click on the Search button. Now it will show the Arrear calculation. After checking the calculation, printout of the arrear details should be taken and the soft copy of the same saved (by clicking ctrl +P). Now click on the Save & Send for Approval button. Then case will go to AAO of PDA Section for approval. (The above printout will be used as Arrear Sanction for PFMS payment).  AAO PDA will select the case (by clicking the check box corresponding to the case) and click on Approve button (PDA AAO login Action ApprovalPDA Arrears). The case will go to AO of PDA Section for approval. 84  Now, AO PDA (PDA AO login Action ApprovalPDA Arrears) will select the appropriate Account Head Code from the drop down list. Now select the case (by clicking the check box corresponding to the case) and enter Not Payable Before Date and click on Approve button. Now the Bill will automatically go to PFMS for arrear payment.  After Arrears payment, the case will go to monthly bill section (PDA DH login Action Bill Generation Monthly bill (Normal/Family, IDA/CDA)).\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Arrears", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "What are the steps to create Accounts Officer login?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create an AO (Accounts Officer) login in CPMS:\n\n    After successful payment, login from DH PDA and click the tab LC & DLC Verification (DH PDA Login LC & DLC Verification  LC Verification). 82 Fig.4.12  Now click on Verify link shown in the Action column, a popup window will appear as shown in the figure below. Here we have to select the Life Certificate and Non Employment (FMA also, in case of CDA pensioners) Validity, From and to dates (From date will be next date, from the date of retirement). After filling the details click on Confirm button. (Note-The above certificate date is for non-employment of pensioner) Fig.4.13  After LC & DLC the case will go in Arrears tab (PDA DH login Action Bill Generation  Arrears) 83  Here select the Reason for Arrear and fill the PPO Number of pensioner, then click on the Search button. Now it will show the Arrear calculation. After checking the calculation, printout of the arrear details should be taken and the soft copy of the same saved (by clicking ctrl +P). Now click on the Save & Send for Approval button. Then case will go to AAO of PDA Section for approval. (The above printout will be used as Arrear Sanction for PFMS payment).  AAO PDA will select the case (by clicking the check box corresponding to the case) and click on Approve button (PDA AAO login Action ApprovalPDA Arrears). The case will go to AO of PDA Section for approval. 84  Now, AO PDA (PDA AO login Action ApprovalPDA Arrears) will select the appropriate Account Head Code from the drop down list. Now select the case (by clicking the check box corresponding to the case) and enter Not Payable Before Date and click on Approve button. Now the Bill will automatically go to PFMS for arrear payment.  After Arrears payment, the case will go to monthly bill section (PDA DH login Action Bill Generation Monthly bill (Normal/Family, IDA/CDA)).\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Arrears", "procedure_type": "AO_LOGIN_CREATION", "enhanced": true}
{"instruction": "How to assign authorization to AO in CPMS?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    After successful payment, login from DH PDA and click the tab LC & DLC Verification (DH PDA Login LC & DLC Verification  LC Verification). 82 Fig.4.12  Now click on Verify link shown in the Action column, a popup window will appear as shown in the figure below. Here we have to select the Life Certificate and Non Employment (FMA also, in case of CDA pensioners) Validity, From and to dates (From date will be next date, from the date of retirement). After filling the details click on Confirm button. (Note-The above certificate date is for non-employment of pensioner) Fig.4.13  After LC & DLC the case will go in Arrears tab (PDA DH login Action Bill Generation  Arrears) 83  Here select the Reason for Arrear and fill the PPO Number of pensioner, then click on the Search button. Now it will show the Arrear calculation. After checking the calculation, printout of the arrear details should be taken and the soft copy of the same saved (by clicking ctrl +P). Now click on the Save & Send for Approval button. Then case will go to AAO of PDA Section for approval. (The above printout will be used as Arrear Sanction for PFMS payment).  AAO PDA will select the case (by clicking the check box corresponding to the case) and click on Approve button (PDA AAO login Action ApprovalPDA Arrears). The case will go to AO of PDA Section for approval. 84  Now, AO PDA (PDA AO login Action ApprovalPDA Arrears) will select the appropriate Account Head Code from the drop down list. Now select the case (by clicking the check box corresponding to the case) and enter Not Payable Before Date and click on Approve button. Now the Bill will automatically go to PFMS for arrear payment.  After Arrears payment, the case will go to monthly bill section (PDA DH login Action Bill Generation Monthly bill (Normal/Family, IDA/CDA)).\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Arrears", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "How to assign authorization to AO in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create an AO (Accounts Officer) login in CPMS:\n\n    After successful payment, login from DH PDA and click the tab LC & DLC Verification (DH PDA Login LC & DLC Verification  LC Verification). 82 Fig.4.12  Now click on Verify link shown in the Action column, a popup window will appear as shown in the figure below. Here we have to select the Life Certificate and Non Employment (FMA also, in case of CDA pensioners) Validity, From and to dates (From date will be next date, from the date of retirement). After filling the details click on Confirm button. (Note-The above certificate date is for non-employment of pensioner) Fig.4.13  After LC & DLC the case will go in Arrears tab (PDA DH login Action Bill Generation  Arrears) 83  Here select the Reason for Arrear and fill the PPO Number of pensioner, then click on the Search button. Now it will show the Arrear calculation. After checking the calculation, printout of the arrear details should be taken and the soft copy of the same saved (by clicking ctrl +P). Now click on the Save & Send for Approval button. Then case will go to AAO of PDA Section for approval. (The above printout will be used as Arrear Sanction for PFMS payment).  AAO PDA will select the case (by clicking the check box corresponding to the case) and click on Approve button (PDA AAO login Action ApprovalPDA Arrears). The case will go to AO of PDA Section for approval. 84  Now, AO PDA (PDA AO login Action ApprovalPDA Arrears) will select the appropriate Account Head Code from the drop down list. Now select the case (by clicking the check box corresponding to the case) and enter Not Payable Before Date and click on Approve button. Now the Bill will automatically go to PFMS for arrear payment.  After Arrears payment, the case will go to monthly bill section (PDA DH login Action Bill Generation Monthly bill (Normal/Family, IDA/CDA)).\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Arrears", "procedure_type": "AO_LOGIN_CREATION", "enhanced": true}
{"instruction": "How to create AO login in CPMS?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    Once the Grievance is received at AO (Concerned Section) level, he/she will assign that Grievance to the respective DH (Concerned Section). (6.4) 112  AO Login Grievance Management Assign Grievance.  AO (Concerned Section) to select the respective DH (Concerned Section) has to click on Grievance check box and then click on Assign button. (6.4)  After all the aforementioned steps, Grievance will move to respective selected DH (Concerned Section). 6.4\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Grievance to DH", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "What is the path to create AO user login?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    Once the Grievance is received at AO (Concerned Section) level, he/she will assign that Grievance to the respective DH (Concerned Section). (6.4) 112  AO Login Grievance Management Assign Grievance.  AO (Concerned Section) to select the respective DH (Concerned Section) has to click on Grievance check box and then click on Assign button. (6.4)  After all the aforementioned steps, Grievance will move to respective selected DH (Concerned Section). 6.4\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Grievance to DH", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "How does CCA create AO login?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    Once the Grievance is received at AO (Concerned Section) level, he/she will assign that Grievance to the respective DH (Concerned Section). (6.4) 112  AO Login Grievance Management Assign Grievance.  AO (Concerned Section) to select the respective DH (Concerned Section) has to click on Grievance check box and then click on Assign button. (6.4)  After all the aforementioned steps, Grievance will move to respective selected DH (Concerned Section). 6.4\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Grievance to DH", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "What are the steps to create Accounts Officer login?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    Once the Grievance is received at AO (Concerned Section) level, he/she will assign that Grievance to the respective DH (Concerned Section). (6.4) 112  AO Login Grievance Management Assign Grievance.  AO (Concerned Section) to select the respective DH (Concerned Section) has to click on Grievance check box and then click on Assign button. (6.4)  After all the aforementioned steps, Grievance will move to respective selected DH (Concerned Section). 6.4\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Grievance to DH", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "How to assign authorization to AO in CPMS?", "input": "", "output": "To create an AO (Accounts Officer) login in CPMS:\n\n    Once the Grievance is received at AO (Concerned Section) level, he/she will assign that Grievance to the respective DH (Concerned Section). (6.4) 112  AO Login Grievance Management Assign Grievance.  AO (Concerned Section) to select the respective DH (Concerned Section) has to click on Grievance check box and then click on Assign button. (6.4)  After all the aforementioned steps, Grievance will move to respective selected DH (Concerned Section). 6.4\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Grievance to DH", "procedure_type": "AO_LOGIN_CREATION"}
{"instruction": "How to create DH login in CPMS?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    Once the Grievances received at DH (Concerned Section) DH will take the required action on the grievance.  DH Login Action Grievance Management Resolve Grievance. (6.5)  However, before settling the grievance, the DH (Concerned Section) should get approval of competent authority in physical file also.  History of Grievance will be shown in Grievance history with status “Recent” or ‘Settled”.  AO (concerned section) will be able to see it in Settled Grievance. *The Pensioner will get Notification on Pensioner Dashboard once the Grievance is settled or any other action taken on the Grievance. 113 6.5 114\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Grievance Resolution", "procedure_type": "DH_CREATION"}
{"instruction": "How to create DH login in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    Once the Grievances received at DH (Concerned Section) DH will take the required action on the grievance.  DH Login Action Grievance Management Resolve Grievance. (6.5)  However, before settling the grievance, the DH (Concerned Section) should get approval of competent authority in physical file also.  History of Grievance will be shown in Grievance history with status “Recent” or ‘Settled”.  AO (concerned section) will be able to see it in Settled Grievance. *The Pensioner will get Notification on Pensioner Dashboard once the Grievance is settled or any other action taken on the Grievance. 113 6.5 114\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Grievance Resolution", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "What is the procedure to create Dealing Hand login?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    Once the Grievances received at DH (Concerned Section) DH will take the required action on the grievance.  DH Login Action Grievance Management Resolve Grievance. (6.5)  However, before settling the grievance, the DH (Concerned Section) should get approval of competent authority in physical file also.  History of Grievance will be shown in Grievance history with status “Recent” or ‘Settled”.  AO (concerned section) will be able to see it in Settled Grievance. *The Pensioner will get Notification on Pensioner Dashboard once the Grievance is settled or any other action taken on the Grievance. 113 6.5 114\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Grievance Resolution", "procedure_type": "DH_CREATION"}
{"instruction": "What is the procedure to create Dealing Hand login?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    Once the Grievances received at DH (Concerned Section) DH will take the required action on the grievance.  DH Login Action Grievance Management Resolve Grievance. (6.5)  However, before settling the grievance, the DH (Concerned Section) should get approval of competent authority in physical file also.  History of Grievance will be shown in Grievance history with status “Recent” or ‘Settled”.  AO (concerned section) will be able to see it in Settled Grievance. *The Pensioner will get Notification on Pensioner Dashboard once the Grievance is settled or any other action taken on the Grievance. 113 6.5 114\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Grievance Resolution", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "How to create DH user in pension section?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    Once the Grievances received at DH (Concerned Section) DH will take the required action on the grievance.  DH Login Action Grievance Management Resolve Grievance. (6.5)  However, before settling the grievance, the DH (Concerned Section) should get approval of competent authority in physical file also.  History of Grievance will be shown in Grievance history with status “Recent” or ‘Settled”.  AO (concerned section) will be able to see it in Settled Grievance. *The Pensioner will get Notification on Pensioner Dashboard once the Grievance is settled or any other action taken on the Grievance. 113 6.5 114\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Grievance Resolution", "procedure_type": "DH_CREATION"}
{"instruction": "How to create DH user in pension section?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    Once the Grievances received at DH (Concerned Section) DH will take the required action on the grievance.  DH Login Action Grievance Management Resolve Grievance. (6.5)  However, before settling the grievance, the DH (Concerned Section) should get approval of competent authority in physical file also.  History of Grievance will be shown in Grievance history with status “Recent” or ‘Settled”.  AO (concerned section) will be able to see it in Settled Grievance. *The Pensioner will get Notification on Pensioner Dashboard once the Grievance is settled or any other action taken on the Grievance. 113 6.5 114\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Grievance Resolution", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "What are the steps for DH creation in CPMS?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    Once the Grievances received at DH (Concerned Section) DH will take the required action on the grievance.  DH Login Action Grievance Management Resolve Grievance. (6.5)  However, before settling the grievance, the DH (Concerned Section) should get approval of competent authority in physical file also.  History of Grievance will be shown in Grievance history with status “Recent” or ‘Settled”.  AO (concerned section) will be able to see it in Settled Grievance. *The Pensioner will get Notification on Pensioner Dashboard once the Grievance is settled or any other action taken on the Grievance. 113 6.5 114\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Grievance Resolution", "procedure_type": "DH_CREATION"}
{"instruction": "What are the steps for DH creation in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    Once the Grievances received at DH (Concerned Section) DH will take the required action on the grievance.  DH Login Action Grievance Management Resolve Grievance. (6.5)  However, before settling the grievance, the DH (Concerned Section) should get approval of competent authority in physical file also.  History of Grievance will be shown in Grievance history with status “Recent” or ‘Settled”.  AO (concerned section) will be able to see it in Settled Grievance. *The Pensioner will get Notification on Pensioner Dashboard once the Grievance is settled or any other action taken on the Grievance. 113 6.5 114\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Grievance Resolution", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "How to assign rights to DH user?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    Once the Grievances received at DH (Concerned Section) DH will take the required action on the grievance.  DH Login Action Grievance Management Resolve Grievance. (6.5)  However, before settling the grievance, the DH (Concerned Section) should get approval of competent authority in physical file also.  History of Grievance will be shown in Grievance history with status “Recent” or ‘Settled”.  AO (concerned section) will be able to see it in Settled Grievance. *The Pensioner will get Notification on Pensioner Dashboard once the Grievance is settled or any other action taken on the Grievance. 113 6.5 114\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Grievance Resolution", "procedure_type": "DH_CREATION"}
{"instruction": "How to assign rights to DH user?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    Once the Grievances received at DH (Concerned Section) DH will take the required action on the grievance.  DH Login Action Grievance Management Resolve Grievance. (6.5)  However, before settling the grievance, the DH (Concerned Section) should get approval of competent authority in physical file also.  History of Grievance will be shown in Grievance history with status “Recent” or ‘Settled”.  AO (concerned section) will be able to see it in Settled Grievance. *The Pensioner will get Notification on Pensioner Dashboard once the Grievance is settled or any other action taken on the Grievance. 113 6.5 114\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Grievance Resolution", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "How to create DH login in CPMS?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "How to create DH login in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "What is the procedure to create Dealing Hand login?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "What is the procedure to create Dealing Hand login?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "How to create DH user in pension section?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "How to create DH user in pension section?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "What are the steps for DH creation in CPMS?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "What are the steps for DH creation in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}
{"instruction": "How to assign rights to DH user?", "input": "", "output": "To create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION"}
{"instruction": "How to assign rights to DH user?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a DH (Dealing Hand) login in CPMS:\n\n    DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "DH_CREATION", "enhanced": true}

{"instruction": "How to create AAO login in CPMS?", "input": "", "output": "To create an AAO (Assistant Administrative Officer) login in CPMS:\n\n**Path:** Login as AO → Users → User Registration\n\n**Steps:**\n1. AO logs into CPMS\n2. Navigate to Users → User Registration\n3. Select Role Type as \"AAO\"\n4. Fill user details and save\n5. Assign authorization by clicking Lock icon\n6. Set appropriate rights and permissions\n\n**Key Points:**\n• AAO is third level login subordinate to AO\n• Only AO can create AAO login\n• Multiple AAO users can be created\n\n**Reference:** CPMS User Manual Version 6.2 - Section 1.8.2\n\nFor assistance: 1800-113-5800 or <EMAIL>", "source": "CPMS User Manual - Section 1.8.2", "procedure_type": "AAO_CREATION"}
{"instruction": "What is the path to create AAO user login?", "input": "", "output": "**Path to create AAO User Login in CPMS:**\n\n**Login as AO → Users → User Registration**\n\n**Detailed Process:**\n1. AO Login - Access CPMS with AO credentials\n2. Navigate to Users - Click \"Users\" in main menu\n3. User Registration - Select \"User Registration\" option\n4. Configure AAO Role - Select Role Type as \"AAO\"\n5. Authorization Setup - Click Lock icon and assign module rights\n\n**Important:**\n• AAO is third level login subordinate to AO in Pension Section\n• AO creates the login for AAO to work in Pension Section\n• Only AO has authority to create AAO login\n\n**Reference:** CPMS User Manual Version 6.2 - Section 1.8.2\n\nFor support: 1800-113-5800 or <EMAIL>", "source": "CPMS User Manual - Section 1.8.2", "procedure_type": "AAO_CREATION"}
{"instruction": "Path to create AAO login?", "input": "", "output": "**Path to create AAO login in CPMS:**\n\n**Login as AO → Users → User Registration (Select AAO role type)**\n\n**According to CPMS User Manual Version 6.2, Section 1.8.2:**\n\na) AAO is the third level login and subordinate to AO in Pension Section\nb) AO will create the login for AAO to work in Pension Section\nc) Path: Login as AO → Users → User Registration (Select the Role Type as AAO, fill the user detail and save it)\nd) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column\ne) AO will subsequently assign rights\nf) Multiple AAO Users can be created\n\n**Hierarchy:** CCA → AO → AAO\n\nFor technical support: 1800-113-5800 or <EMAIL>", "source": "CPMS User Manual - Section 1.8.2", "procedure_type": "AAO_CREATION"}