#!/usr/bin/env python3
"""
Persistent Cache Management Tool for CPMS Chatbot
Allows viewing and removing cached questions from the persistent answer cache
"""
import shelve
import sys
import os
from pathlib import Path
import json
from typing import Dict, List, Any

# Add workspace root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from backend.config import config

def list_cached_questions():
    """List all questions in the persistent cache"""
    try:
        cache_path = str(config.PERSISTENT_CACHE_PATH)
        print(f"📂 Opening persistent cache: {cache_path}")
        
        with shelve.open(cache_path, flag='r') as cache:
            questions = list(cache.keys())
            
        print(f"\n📊 Found {len(questions)} cached questions:")
        print("=" * 80)
        
        for i, question in enumerate(questions, 1):
            print(f"{i:3d}. {question}")
            
        return questions
        
    except Exception as e:
        print(f"❌ Error reading cache: {e}")
        return []

def view_cached_answer(question: str):
    """View the cached answer for a specific question"""
    try:
        cache_path = str(config.PERSISTENT_CACHE_PATH)
        
        with shelve.open(cache_path, flag='r') as cache:
            if question in cache:
                data = cache[question]
                print(f"\n📋 Cached Answer for: '{question}'")
                print("=" * 80)
                print(f"Response: {data.get('response', 'N/A')[:200]}...")
                print(f"Model Type: {data.get('model_type', 'N/A')}")
                print(f"Original Processing Time: {data.get('original_processing_time', 'N/A')}s")
                print(f"Cached At: {data.get('cached_at', 'N/A')}")
                print(f"Sources: {len(data.get('sources', []))} sources")
                return True
            else:
                print(f"❌ Question not found in cache: {question}")
                return False
                
    except Exception as e:
        print(f"❌ Error reading cache: {e}")
        return False

def remove_cached_questions(questions_to_remove: List[str]):
    """Remove specific questions from the persistent cache"""
    try:
        cache_path = str(config.PERSISTENT_CACHE_PATH)
        print(f"🗑️ Removing {len(questions_to_remove)} questions from cache...")
        
        removed_count = 0
        not_found = []
        
        with shelve.open(cache_path, flag='w') as cache:
            for question in questions_to_remove:
                if question in cache:
                    del cache[question]
                    removed_count += 1
                    print(f"✅ Removed: {question}")
                else:
                    not_found.append(question)
                    print(f"⚠️ Not found: {question}")
        
        print(f"\n📊 Summary:")
        print(f"✅ Successfully removed: {removed_count} questions")
        if not_found:
            print(f"⚠️ Not found: {len(not_found)} questions")
            
        return removed_count, not_found
        
    except Exception as e:
        print(f"❌ Error modifying cache: {e}")
        return 0, []

def search_cached_questions(search_term: str):
    """Search for questions containing a specific term"""
    try:
        cache_path = str(config.PERSISTENT_CACHE_PATH)
        
        with shelve.open(cache_path, flag='r') as cache:
            questions = list(cache.keys())
            
        matching_questions = [q for q in questions if search_term.lower() in q.lower()]
        
        print(f"\n🔍 Found {len(matching_questions)} questions containing '{search_term}':")
        print("=" * 80)
        
        for i, question in enumerate(matching_questions, 1):
            print(f"{i:3d}. {question}")
            
        return matching_questions
        
    except Exception as e:
        print(f"❌ Error searching cache: {e}")
        return []

def backup_cache():
    """Create a backup of the current cache"""
    try:
        cache_path = str(config.PERSISTENT_CACHE_PATH)
        backup_path = cache_path + ".backup"
        
        # Copy all cache files
        import shutil
        for ext in ['.dir', '.dat', '.bak']:
            src = cache_path + ext
            dst = backup_path + ext
            if os.path.exists(src):
                shutil.copy2(src, dst)
                
        print(f"✅ Cache backed up to: {backup_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating backup: {e}")
        return False

def main():
    """Main interactive menu"""
    print("🏛️ CPMS Persistent Cache Management Tool")
    print("=" * 50)
    
    while True:
        print("\n📋 Available Actions:")
        print("1. List all cached questions")
        print("2. Search cached questions")
        print("3. View cached answer")
        print("4. Remove specific questions")
        print("5. Create backup")
        print("6. Exit")
        
        choice = input("\n🔢 Enter your choice (1-6): ").strip()
        
        if choice == '1':
            list_cached_questions()
            
        elif choice == '2':
            search_term = input("🔍 Enter search term: ").strip()
            if search_term:
                search_cached_questions(search_term)
                
        elif choice == '3':
            question = input("❓ Enter exact question: ").strip()
            if question:
                view_cached_answer(question)
                
        elif choice == '4':
            print("\n⚠️ WARNING: This will permanently remove questions from the cache!")
            confirm = input("Type 'YES' to continue: ").strip()
            if confirm == 'YES':
                questions_input = input("📝 Enter questions to remove (one per line, empty line to finish):\n")
                questions_to_remove = []
                
                print("Enter questions (press Enter twice when done):")
                while True:
                    question = input().strip()
                    if not question:
                        break
                    questions_to_remove.append(question)
                
                if questions_to_remove:
                    remove_cached_questions(questions_to_remove)
                else:
                    print("❌ No questions specified")
            else:
                print("❌ Operation cancelled")
                
        elif choice == '5':
            backup_cache()
            
        elif choice == '6':
            print("👋 Goodbye!")
            break
            
        else:
            print("❌ Invalid choice. Please enter 1-6.")

if __name__ == "__main__":
    main()
