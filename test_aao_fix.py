#!/usr/bin/env python3
"""
Test script to verify AAO login creation responses are correct
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from cache.faq_cache import FAQCache

def test_aao_responses():
    """Test AAO-related responses"""

    print("🧪 Testing AAO Login Creation Responses")
    print("=" * 50)

    # Initialize FAQ cache
    faq_cache = FAQCache()

    # Test queries for AAO login creation
    test_queries = [
        "How to create AAO login in CPMS?",
        "What is the path to create AAO user login?",
        "Path to create AAO login?",
        "How does AO create AAO login?",
        "Who creates AAO login?",
        "AAO login creation process"
    ]

    print("Testing AAO-related queries:")
    print("-" * 30)

    for query in test_queries:
        print(f"\n🔍 Query: {query}")

        # Check if query is in precomputed FAQs
        response = faq_cache.get_faq_response(query)

        if response:
            print("✅ Found cached response:")
            print(f"Response: {response['response'][:200]}...")

            # Check for correct information
            response_text = response['response'].lower()

            # Verify correct information
            correct_info = []
            incorrect_info = []

            if "login as ao" in response_text:
                correct_info.append("✅ Correct: Login as AO")
            elif "login as cca" in response_text:
                incorrect_info.append("❌ Incorrect: Login as CCA")

            if "ao can create aao" in response_text or "ao will create" in response_text:
                correct_info.append("✅ Correct: AO creates AAO")
            elif "cca can create aao" in response_text or "cca creates aao" in response_text:
                incorrect_info.append("❌ Incorrect: CCA creates AAO")

            if "third level login" in response_text:
                correct_info.append("✅ Correct: AAO is third level login")

            if "subordinate to ao" in response_text:
                correct_info.append("✅ Correct: AAO subordinate to AO")
            elif "subordinate to cca" in response_text:
                incorrect_info.append("❌ Incorrect: AAO subordinate to CCA")

            # Print verification results
            if correct_info:
                for info in correct_info:
                    print(f"  {info}")
            if incorrect_info:
                for info in incorrect_info:
                    print(f"  {info}")

            if not incorrect_info:
                print("  🎉 Response appears correct!")
            else:
                print("  ⚠️  Response contains incorrect information!")

        else:
            print("❌ No cached response found")

    print("\n" + "=" * 50)
    print("🏁 Test completed!")

if __name__ == "__main__":
    test_aao_responses()
