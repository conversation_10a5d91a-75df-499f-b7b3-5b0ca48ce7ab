#!/usr/bin/env python3
"""
FastAPI Backend Server for CPMS Chatbot
Provides REST API endpoints for the frontend to connect to
"""
import sys
import os
from pathlib import Path

# Add workspace root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from fastapi import FastAPI, HTTPException, Depends, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
import uvicorn
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FuturesTimeoutError

# Import backend components
from backend.config import config
from backend.core.ultra_optimized_rag_system import UltraOptimizedRAGSystem
from backend.database.database import create_tables, init_database, test_connection, get_db
from backend.api.auth_routes import router as auth_router
from backend.api.pensioner_routes import router as pensioner_router
from backend.core.enhanced_query_processor import enhanced_query_processor
from backend.services.auth_service import auth_service
from backend.api.auth_routes import get_current_pensioner

# Initialize FastAPI app
app = FastAPI(
    title="CPMS Chatbot API",
    description="Backend API for CPMS Chatbot with RAG system",
    version="1.0.0"
)

# Add CORS middleware to allow frontend connections
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth_router)
app.include_router(pensioner_router)

# Global RAG system instance
rag_system = None

# Thread pool for async processing
executor = ThreadPoolExecutor(max_workers=4)

# Pydantic models for API requests/responses
class QueryRequest(BaseModel):
    message: str
    include_sources: bool = True
    model_type: Optional[str] = None

class QueryResponse(BaseModel):
    query: str
    response: str
    processing_time: float
    #sources: List[Dict[str, Any]]
    cached: bool
    cache_type: Optional[str] = None
    model_type: str

class HealthResponse(BaseModel):
    status: str
    message: str
    system_info: Dict[str, Any]

class SystemStatsResponse(BaseModel):
    stats: Dict[str, Any]

@app.on_event("startup")
async def startup_event():
    """Initialize the RAG system on startup"""
    global rag_system
    try:
        print("Initializing CPMS Backend API...")
        print(f"Server binding to: {config.API_HOST}:{config.API_PORT}")
        print(f"Frontend should connect to: http://localhost:{config.API_PORT}")

        # Check if local GGUF model exists first
        import os
        print(f"Checking for local GGUF model at: {config.MISTRAL_MODEL_PATH}")

        if os.path.exists(config.MISTRAL_MODEL_PATH):
            print("Local GGUF model found, using it...")
            try:
                rag_system = UltraOptimizedRAGSystem(model_type="gguf")
                print("RAG system initialized with local GGUF model")
            except Exception as e:
                print(f"Failed to load local GGUF model: {e}")
                print("Falling back to HuggingFace model...")
                rag_system = UltraOptimizedRAGSystem(model_type="huggingface")
                print("RAG system initialized with HuggingFace model")
        else:
            print("Local GGUF model not found, using HuggingFace model...")
            print(f"Expected path: {config.MISTRAL_MODEL_PATH}")
            print("To use local model, ensure the .gguf file exists at the above path")
            rag_system = UltraOptimizedRAGSystem(model_type="huggingface")
            print("RAG system initialized with HuggingFace model")

        print("Backend API ready to serve requests!")

    except Exception as e:
        print(f"Failed to initialize RAG system: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    try:
        print("Shutting down CPMS Backend API...")

        # Import here to avoid circular imports
        from backend.cache.faq_cache import faq_cache

        # Close persistent cache gracefully
        faq_cache.close_persistent_cache()

        print("Backend API shutdown complete")
    except Exception as e:
        print(f"Error during shutdown: {e}")

@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint"""
    return {
        "message": "CPMS Chatbot Backend API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }

@app.get("/v1/models")
async def models_endpoint():
    """Handle /v1/models requests (likely from external AI tools)"""
    return {
        "object": "list",
        "data": [
            {
                "id": "cpms-mistral-7b",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "cpms",
                "permission": [],
                "root": "cpms-mistral-7b",
                "parent": None
            }
        ]
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    if not rag_system:
        raise HTTPException(status_code=503, detail="RAG system not initialized")

    try:
        health = rag_system.health_check()
        stats = rag_system.get_system_stats()

        return HealthResponse(
            status=health["status"],
            message="System is healthy" if health["status"] == "healthy" else "System has issues",
            system_info={
                "model_type": stats.get("model_type", "unknown"),
                "total_documents": stats.get("vector_store", {}).get("total_documents", 0),
                "optimizations_active": health.get("optimizations_active", 0),
                "issues": health.get("issues", [])
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@app.post("/query", response_model=QueryResponse)
async def process_query(request: QueryRequest):
    """Process a chat query with timeout handling"""
    if not rag_system:
        raise HTTPException(status_code=503, detail="RAG system not initialized")

    if not request.message.strip():
        raise HTTPException(status_code=400, detail="Message cannot be empty")

    try:
        # Process the query with timeout
        loop = asyncio.get_event_loop()

        # Run the query in a thread pool with timeout
        future = loop.run_in_executor(
            executor,
            rag_system.query,
            request.message,
            request.include_sources
        )

        # Wait for result with timeout
        try:
            result = await asyncio.wait_for(future, timeout=config.API_REQUEST_TIMEOUT)
        except asyncio.TimeoutError:
            # Cancel the future to free resources
            future.cancel()
            raise HTTPException(
                status_code=408,
                detail=f"Query processing timed out after {config.API_REQUEST_TIMEOUT} seconds. Please try a simpler question or try again later."
            )

        return QueryResponse(
            query=result["query"],
            response=result["response"],
            processing_time=result["processing_time"],
            #sources=result.get("sources", []),
            cached=result.get("cached", False),
            cache_type=result.get("cache_type"),
            model_type=result["model_type"]
        )

    except HTTPException:
        # Re-raise HTTP exceptions (like timeout)
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Query processing failed: {str(e)}")

@app.post("/enhanced-query", response_model=QueryResponse)
async def process_enhanced_query(
    request: QueryRequest,
    authorization: Optional[str] = Header(None),
    db: Session = Depends(get_db)
):
    """Process enhanced query with optional pensioner authentication support"""
    if not rag_system:
        raise HTTPException(status_code=503, detail="RAG system not initialized")

    if not request.message.strip():
        raise HTTPException(status_code=400, detail="Message cannot be empty")

    try:
        # Try to get authenticated pensioner if token is provided
        pensioner = None
        if authorization and authorization.startswith("Bearer "):
            try:
                token = authorization.split(" ")[1]
                pensioner = auth_service.get_current_pensioner(token, db)
            except Exception:
                # If authentication fails, continue as unauthenticated user
                pass

        # If user is authenticated, try pensioner-specific query first
        if pensioner:
            pensioner_result = enhanced_query_processor.process_pensioner_query(
                request.message, pensioner, db
            )

            if pensioner_result.get("is_pensioner_query", False):
                # Return pensioner-specific response
                return QueryResponse(
                    query=request.message,
                    response=pensioner_result["message"],
                    processing_time=pensioner_result.get("processing_time", 0.0),
                    cached=False,
                    cache_type=None,
                    model_type="pensioner_service"
                )

        # Fall back to general RAG query
        loop = asyncio.get_event_loop()

        # Run the query in a thread pool with timeout
        future = loop.run_in_executor(
            executor,
            rag_system.query,
            request.message,
            request.include_sources
        )

        # Wait for result with timeout
        try:
            result = await asyncio.wait_for(future, timeout=config.API_REQUEST_TIMEOUT)
        except asyncio.TimeoutError:
            # Cancel the future to free resources
            future.cancel()
            raise HTTPException(
                status_code=408,
                detail=f"Query processing timed out after {config.API_REQUEST_TIMEOUT} seconds. Please try a simpler question or try again later."
            )

        return QueryResponse(
            query=result["query"],
            response=result["response"],
            processing_time=result["processing_time"],
            cached=result.get("cached", False),
            cache_type=result.get("cache_type"),
            model_type=result["model_type"]
        )

    except HTTPException:
        # Re-raise HTTP exceptions (like timeout)
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Enhanced query processing failed: {str(e)}")

@app.get("/suggestions", response_model=List[str])
async def get_suggestions():
    """Get suggested questions"""
    if not rag_system:
        raise HTTPException(status_code=503, detail="RAG system not initialized")

    try:
        suggestions = rag_system.suggest_questions(10)
        return suggestions
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get suggestions: {str(e)}")

@app.get("/stats", response_model=SystemStatsResponse)
async def get_system_stats():
    """Get detailed system statistics"""
    if not rag_system:
        raise HTTPException(status_code=503, detail="RAG system not initialized")

    try:
        stats = rag_system.get_system_stats()
        return SystemStatsResponse(stats=stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")

@app.post("/clear-cache")
async def clear_cache():
    """Clear system caches"""
    if not rag_system:
        raise HTTPException(status_code=503, detail="RAG system not initialized")

    try:
        rag_system.clear_session_cache()
        return {"message": "Cache cleared successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to clear cache: {str(e)}")

def main():
    """Main function to run the server"""
    print("CPMS Chatbot Backend API Server")
    print("=" * 50)

    # Run the server with timeout configurations
    uvicorn.run(
        app,
        host=config.API_HOST,
        port=config.API_PORT,
        reload=False,
        log_level="info",
        access_log=True,
        timeout_keep_alive=30,
        timeout_graceful_shutdown=30,
        limit_concurrency=100,
        limit_max_requests=1000
    )

if __name__ == "__main__":
    main()
