#!/usr/bin/env python3
"""
Test server.py syntax without running it
"""
import ast
import sys

def test_syntax(file_path):
    """Test Python file syntax"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Try to parse the AST
        ast.parse(content)
        print(f"✅ Syntax OK: {file_path}")
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax Error in {file_path}:")
        print(f"   Line {e.lineno}: {e.text}")
        print(f"   Error: {e.msg}")
        return False
        
    except Exception as e:
        print(f"❌ Error reading {file_path}: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing server.py syntax...")
    
    if test_syntax("backend/api/server.py"):
        print("🎉 Server syntax is correct!")
        print("💡 You can now start the backend server")
    else:
        print("🔧 Please fix the syntax errors before starting the server")
        sys.exit(1)
