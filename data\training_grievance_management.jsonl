{"instruction": "How to lodge grievance in CPMS?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiring officer/official can see the progress of his/her application at the top of the dashboard (5.1)  Milestones completed will be shown in GREEN colour dots and the pending ones with RED colour dots.  <PERSON><PERSON><PERSON> can see the pensionary benefits and lodge their grievance, if any, using the dashboard.  <PERSON><PERSON><PERSON> can see his/her pension details by clicking on pension ledger. 87 5.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "How to lodge grievance in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiring officer/official can see the progress of his/her application at the top of the dashboard (5.1)  Milestones completed will be shown in GREEN colour dots and the pending ones with RED colour dots.  <PERSON><PERSON><PERSON> can see the pensionary benefits and lodge their grievance, if any, using the dashboard.  <PERSON><PERSON><PERSON> can see his/her pension details by clicking on pension ledger. 87 5.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
{"instruction": "What is the procedure to raise complaint?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiring officer/official can see the progress of his/her application at the top of the dashboard (5.1)  Milestones completed will be shown in GREEN colour dots and the pending ones with RED colour dots.  Retiree can see the pensionary benefits and lodge their grievance, if any, using the dashboard.  Retiree can see his/her pension details by clicking on pension ledger. 87 5.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "What is the procedure to raise complaint?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiring officer/official can see the progress of his/her application at the top of the dashboard (5.1)  Milestones completed will be shown in GREEN colour dots and the pending ones with RED colour dots.  Retiree can see the pensionary benefits and lodge their grievance, if any, using the dashboard.  Retiree can see his/her pension details by clicking on pension ledger. 87 5.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
{"instruction": "How to track grievance status?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiring officer/official can see the progress of his/her application at the top of the dashboard (5.1)  Milestones completed will be shown in GREEN colour dots and the pending ones with RED colour dots.  Retiree can see the pensionary benefits and lodge their grievance, if any, using the dashboard.  Retiree can see his/her pension details by clicking on pension ledger. 87 5.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "How to track grievance status?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiring officer/official can see the progress of his/her application at the top of the dashboard (5.1)  Milestones completed will be shown in GREEN colour dots and the pending ones with RED colour dots.  Retiree can see the pensionary benefits and lodge their grievance, if any, using the dashboard.  Retiree can see his/her pension details by clicking on pension ledger. 87 5.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
{"instruction": "How to resolve pensioner grievances?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiring officer/official can see the progress of his/her application at the top of the dashboard (5.1)  Milestones completed will be shown in GREEN colour dots and the pending ones with RED colour dots.  Retiree can see the pensionary benefits and lodge their grievance, if any, using the dashboard.  Retiree can see his/her pension details by clicking on pension ledger. 87 5.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "How to resolve pensioner grievances?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiring officer/official can see the progress of his/her application at the top of the dashboard (5.1)  Milestones completed will be shown in GREEN colour dots and the pending ones with RED colour dots.  Retiree can see the pensionary benefits and lodge their grievance, if any, using the dashboard.  Retiree can see his/her pension details by clicking on pension ledger. 87 5.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
{"instruction": "What are the steps to submit grievance?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiring officer/official can see the progress of his/her application at the top of the dashboard (5.1)  Milestones completed will be shown in GREEN colour dots and the pending ones with RED colour dots.  Retiree can see the pensionary benefits and lodge their grievance, if any, using the dashboard.  Retiree can see his/her pension details by clicking on pension ledger. 87 5.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "What are the steps to submit grievance?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiring officer/official can see the progress of his/her application at the top of the dashboard (5.1)  Milestones completed will be shown in GREEN colour dots and the pending ones with RED colour dots.  Retiree can see the pensionary benefits and lodge their grievance, if any, using the dashboard.  Retiree can see his/her pension details by clicking on pension ledger. 87 5.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
{"instruction": "How to lodge grievance in CPMS?", "input": "", "output": "To manage grievances in CPMS:\n\n   To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((5.16)). (5.16) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. 98\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "What is the procedure to raise complaint?", "input": "", "output": "To manage grievances in CPMS:\n\n   To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((5.16)). (5.16) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. 98\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "How to track grievance status?", "input": "", "output": "To manage grievances in CPMS:\n\n   To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((5.16)). (5.16) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. 98\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "How to resolve pensioner grievances?", "input": "", "output": "To manage grievances in CPMS:\n\n   To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((5.16)). (5.16) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. 98\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "What are the steps to submit grievance?", "input": "", "output": "To manage grievances in CPMS:\n\n   To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((5.16)). (5.16) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. 98\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "How to lodge grievance in CPMS?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (5.17) 5.17  Retiree can also upload the attachment related to the Grievance, if any. (5.18)  After filling all the details, Retiree will click on Submit button.(5.19) 99 5.18 5.19 Retiree shall be able track the status of his grievance from grievance history.(5.19)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Lodge Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "How to lodge grievance in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (5.17) 5.17  Retiree can also upload the attachment related to the Grievance, if any. (5.18)  After filling all the details, Retiree will click on Submit button.(5.19) 99 5.18 5.19 Retiree shall be able track the status of his grievance from grievance history.(5.19)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Lodge Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
{"instruction": "What is the procedure to raise complaint?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (5.17) 5.17  Retiree can also upload the attachment related to the Grievance, if any. (5.18)  After filling all the details, Retiree will click on Submit button.(5.19) 99 5.18 5.19 Retiree shall be able track the status of his grievance from grievance history.(5.19)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Lodge Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "What is the procedure to raise complaint?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (5.17) 5.17  Retiree can also upload the attachment related to the Grievance, if any. (5.18)  After filling all the details, Retiree will click on Submit button.(5.19) 99 5.18 5.19 Retiree shall be able track the status of his grievance from grievance history.(5.19)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Lodge Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
{"instruction": "How to track grievance status?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (5.17) 5.17  Retiree can also upload the attachment related to the Grievance, if any. (5.18)  After filling all the details, Retiree will click on Submit button.(5.19) 99 5.18 5.19 Retiree shall be able track the status of his grievance from grievance history.(5.19)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Lodge Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "How to track grievance status?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (5.17) 5.17  Retiree can also upload the attachment related to the Grievance, if any. (5.18)  After filling all the details, Retiree will click on Submit button.(5.19) 99 5.18 5.19 Retiree shall be able track the status of his grievance from grievance history.(5.19)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Lodge Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
{"instruction": "How to resolve pensioner grievances?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (5.17) 5.17  Retiree can also upload the attachment related to the Grievance, if any. (5.18)  After filling all the details, Retiree will click on Submit button.(5.19) 99 5.18 5.19 Retiree shall be able track the status of his grievance from grievance history.(5.19)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Lodge Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "How to resolve pensioner grievances?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (5.17) 5.17  Retiree can also upload the attachment related to the Grievance, if any. (5.18)  After filling all the details, Retiree will click on Submit button.(5.19) 99 5.18 5.19 Retiree shall be able track the status of his grievance from grievance history.(5.19)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Lodge Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
{"instruction": "What are the steps to submit grievance?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (5.17) 5.17  Retiree can also upload the attachment related to the Grievance, if any. (5.18)  After filling all the details, Retiree will click on Submit button.(5.19) 99 5.18 5.19 Retiree shall be able track the status of his grievance from grievance history.(5.19)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Lodge Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "What are the steps to submit grievance?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (5.17) 5.17  Retiree can also upload the attachment related to the Grievance, if any. (5.18)  After filling all the details, Retiree will click on Submit button.(5.19) 99 5.18 5.19 Retiree shall be able track the status of his grievance from grievance history.(5.19)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Lodge Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
{"instruction": "How to lodge grievance in CPMS?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (6.1)  Retiree can also upload the attachment related to the Grievance, if any. (6.2)  After filling all the details, Retiree will click on Submit button.  The concerned section in CCA office/HoO will be displayed as shown at point no. 3. (6.1) *After submission, the Grievance will be received by Account Officer (AO) of concerned section/HoO as applicable and the history will be maintained in Grievance History section. (6.3) point 6. 6.1 111 6.2 6.3\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pensioner Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "How to lodge grievance in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (6.1)  Retiree can also upload the attachment related to the Grievance, if any. (6.2)  After filling all the details, Retiree will click on Submit button.  The concerned section in CCA office/HoO will be displayed as shown at point no. 3. (6.1) *After submission, the Grievance will be received by Account Officer (AO) of concerned section/HoO as applicable and the history will be maintained in Grievance History section. (6.3) point 6. 6.1 111 6.2 6.3\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Pensioner Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
{"instruction": "What is the procedure to raise complaint?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (6.1)  Retiree can also upload the attachment related to the Grievance, if any. (6.2)  After filling all the details, Retiree will click on Submit button.  The concerned section in CCA office/HoO will be displayed as shown at point no. 3. (6.1) *After submission, the Grievance will be received by Account Officer (AO) of concerned section/HoO as applicable and the history will be maintained in Grievance History section. (6.3) point 6. 6.1 111 6.2 6.3\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pensioner Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "What is the procedure to raise complaint?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (6.1)  Retiree can also upload the attachment related to the Grievance, if any. (6.2)  After filling all the details, Retiree will click on Submit button.  The concerned section in CCA office/HoO will be displayed as shown at point no. 3. (6.1) *After submission, the Grievance will be received by Account Officer (AO) of concerned section/HoO as applicable and the history will be maintained in Grievance History section. (6.3) point 6. 6.1 111 6.2 6.3\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Pensioner Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
{"instruction": "How to track grievance status?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (6.1)  Retiree can also upload the attachment related to the Grievance, if any. (6.2)  After filling all the details, Retiree will click on Submit button.  The concerned section in CCA office/HoO will be displayed as shown at point no. 3. (6.1) *After submission, the Grievance will be received by Account Officer (AO) of concerned section/HoO as applicable and the history will be maintained in Grievance History section. (6.3) point 6. 6.1 111 6.2 6.3\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pensioner Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "How to track grievance status?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (6.1)  Retiree can also upload the attachment related to the Grievance, if any. (6.2)  After filling all the details, Retiree will click on Submit button.  The concerned section in CCA office/HoO will be displayed as shown at point no. 3. (6.1) *After submission, the Grievance will be received by Account Officer (AO) of concerned section/HoO as applicable and the history will be maintained in Grievance History section. (6.3) point 6. 6.1 111 6.2 6.3\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Pensioner Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
{"instruction": "How to resolve pensioner grievances?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (6.1)  Retiree can also upload the attachment related to the Grievance, if any. (6.2)  After filling all the details, Retiree will click on Submit button.  The concerned section in CCA office/HoO will be displayed as shown at point no. 3. (6.1) *After submission, the Grievance will be received by Account Officer (AO) of concerned section/HoO as applicable and the history will be maintained in Grievance History section. (6.3) point 6. 6.1 111 6.2 6.3\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pensioner Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "How to resolve pensioner grievances?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (6.1)  Retiree can also upload the attachment related to the Grievance, if any. (6.2)  After filling all the details, Retiree will click on Submit button.  The concerned section in CCA office/HoO will be displayed as shown at point no. 3. (6.1) *After submission, the Grievance will be received by Account Officer (AO) of concerned section/HoO as applicable and the history will be maintained in Grievance History section. (6.3) point 6. 6.1 111 6.2 6.3\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Pensioner Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
{"instruction": "What are the steps to submit grievance?", "input": "", "output": "To manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (6.1)  Retiree can also upload the attachment related to the Grievance, if any. (6.2)  After filling all the details, Retiree will click on Submit button.  The concerned section in CCA office/HoO will be displayed as shown at point no. 3. (6.1) *After submission, the Grievance will be received by Account Officer (AO) of concerned section/HoO as applicable and the history will be maintained in Grievance History section. (6.3) point 6. 6.1 111 6.2 6.3\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pensioner Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT"}
{"instruction": "What are the steps to submit grievance?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo manage grievances in CPMS:\n\n    Retiree can login and raise his/her Grievance related to pension, if any. Retiree Login Pensioners detailGrievance.  Retiree can select the Grievance Type from the dropdown and add the description about it. (6.1)  Retiree can also upload the attachment related to the Grievance, if any. (6.2)  After filling all the details, Retiree will click on Submit button.  The concerned section in CCA office/HoO will be displayed as shown at point no. 3. (6.1) *After submission, the Grievance will be received by Account Officer (AO) of concerned section/HoO as applicable and the history will be maintained in Grievance History section. (6.3) point 6. 6.1 111 6.2 6.3\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Pensioner Grievance", "procedure_type": "GRIEVANCE_MANAGEMENT", "enhanced": true}
