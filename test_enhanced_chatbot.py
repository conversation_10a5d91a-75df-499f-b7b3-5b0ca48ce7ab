#!/usr/bin/env python3
"""
Test script for the Enhanced CPMS Chatbot with Authentication
"""
import requests
import json
import time

# Configuration
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:5000"

# Sample test data (from our populated database)
TEST_PENSIONERS = [
    {
        "eppo_number": "DOT12345678",
        "mobile_number": "**********",
        "name": "<PERSON><PERSON>"
    },
    {
        "eppo_number": "DOT87654321", 
        "mobile_number": "**********",
        "name": "<PERSON><PERSON>"
    },
    {
        "eppo_number": "DOT11223344",
        "mobile_number": "**********", 
        "name": "<PERSON><PERSON>"
    }
]

def test_backend_health():
    """Test if backend is running"""
    try:
        response = requests.get(f"{BACKEND_URL}/health")
        if response.status_code == 200:
            print("✅ Backend is running and healthy")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend. Make sure it's running on port 8000")
        return False

def test_authentication_flow(pensioner):
    """Test the complete authentication flow"""
    print(f"\n🔐 Testing authentication for {pensioner['name']}...")
    
    # Step 1: Send login request
    login_data = {
        "eppo_number": pensioner["eppo_number"],
        "mobile_number": pensioner["mobile_number"]
    }
    
    try:
        response = requests.post(f"{BACKEND_URL}/auth/login", json=login_data)
        result = response.json()
        
        if result.get("success"):
            print(f"✅ OTP sent successfully to {pensioner['mobile_number']}")
            session_id = result["session_id"]
            
            # In a real scenario, user would receive OTP via SMS
            # For testing, we'll simulate entering the OTP
            print("📱 In production, OTP would be sent via SMS")
            print("🧪 For testing, check backend logs for the OTP")
            
            # Simulate OTP verification (you'll need to get the OTP from backend logs)
            otp = input(f"Enter the OTP for {pensioner['name']} (check backend logs): ")
            
            if otp:
                verify_data = {
                    "session_id": session_id,
                    "otp": otp
                }
                
                verify_response = requests.post(f"{BACKEND_URL}/auth/verify-otp", json=verify_data)
                verify_result = verify_response.json()
                
                if verify_result.get("success"):
                    print(f"✅ Authentication successful for {pensioner['name']}")
                    return verify_result["access_token"]
                else:
                    print(f"❌ OTP verification failed: {verify_result.get('message')}")
            else:
                print("⏭️ Skipping OTP verification")
        else:
            print(f"❌ Login failed: {result.get('message')}")
    
    except Exception as e:
        print(f"❌ Authentication error: {e}")
    
    return None

def test_general_queries():
    """Test general CPMS queries (no authentication required)"""
    print("\n💬 Testing general queries...")
    
    general_queries = [
        "What is CPMS?",
        "How to submit Life Certificate?",
        "What is the CPMS helpline number?",
        "How to register on CPMS portal?"
    ]
    
    for query in general_queries:
        try:
            response = requests.post(f"{BACKEND_URL}/query", json={
                "message": query,
                "include_sources": True
            })
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Query: {query}")
                print(f"   Response: {result['response'][:100]}...")
            else:
                print(f"❌ Query failed: {query}")
        
        except Exception as e:
            print(f"❌ Error with query '{query}': {e}")

def test_pensioner_queries(token):
    """Test pensioner-specific queries (authentication required)"""
    print("\n🔐 Testing authenticated pensioner queries...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    pensioner_queries = [
        "When does my LC expire?",
        "Show my pension breakup",
        "What is the current DA rate?",
        "When will my commutation be restored?",
        "What is my family pension amount?",
        "What is my FMA amount?",
        "How to change my mobile number?",
        "When was I migrated to Sampann?"
    ]
    
    for query in pensioner_queries:
        try:
            response = requests.post(f"{BACKEND_URL}/enhanced-query", 
                                   json={"message": query, "include_sources": True},
                                   headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Query: {query}")
                print(f"   Response: {result['response'][:150]}...")
                if result.get('query_type'):
                    print(f"   Type: {result['query_type']}")
            else:
                print(f"❌ Query failed: {query} - {response.status_code}")
        
        except Exception as e:
            print(f"❌ Error with query '{query}': {e}")

def test_api_endpoints():
    """Test individual API endpoints"""
    print("\n🔌 Testing API endpoints...")
    
    endpoints = [
        ("/health", "GET"),
        ("/stats", "GET"),
        ("/query-suggestions", "GET")
    ]
    
    for endpoint, method in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{BACKEND_URL}{endpoint}")
            else:
                response = requests.post(f"{BACKEND_URL}{endpoint}")
            
            if response.status_code == 200:
                print(f"✅ {method} {endpoint} - OK")
            else:
                print(f"❌ {method} {endpoint} - {response.status_code}")
        
        except Exception as e:
            print(f"❌ Error testing {endpoint}: {e}")

def main():
    """Main test function"""
    print("🧪 CPMS Enhanced Chatbot Test Suite")
    print("=" * 50)
    
    # Test backend health
    if not test_backend_health():
        print("\n❌ Backend is not running. Please start it first:")
        print("   cd backend && python run_backend.py")
        return
    
    # Test API endpoints
    test_api_endpoints()
    
    # Test general queries
    test_general_queries()
    
    # Test authentication and pensioner queries
    print(f"\n👥 Available test pensioners:")
    for i, pensioner in enumerate(TEST_PENSIONERS, 1):
        print(f"   {i}. {pensioner['name']} (EPPO: {pensioner['eppo_number']})")
    
    choice = input("\nSelect a pensioner to test authentication (1-3) or press Enter to skip: ")
    
    if choice and choice.isdigit() and 1 <= int(choice) <= 3:
        selected_pensioner = TEST_PENSIONERS[int(choice) - 1]
        token = test_authentication_flow(selected_pensioner)
        
        if token:
            test_pensioner_queries(token)
    
    print("\n" + "=" * 50)
    print("🎉 Test suite completed!")
    print("\n📋 How to test the frontend:")
    print("1. Start the frontend server:")
    print("   cd frontend && python run_frontend.py")
    print("2. Open http://localhost:5000/enhanced_index.html")
    print("3. Try both guest mode and authenticated mode")
    print("4. Use the sample EPPO numbers and mobile numbers above")
    print("5. Check backend logs for OTP codes during testing")

if __name__ == "__main__":
    main()
