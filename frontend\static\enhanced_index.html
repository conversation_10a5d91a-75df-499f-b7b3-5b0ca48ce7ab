<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPMS - Enhanced Pensioner Portal</title>
    <link rel="stylesheet" href="css/styles.css?v=2.0">
    <link rel="stylesheet" href="css/auth.css?v=2.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
    <link rel="icon" type="image/png" href="assets/favicon.png">
    <meta name="description" content="CPMS Enhanced AI-powered assistant with pensioner authentication">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">🏛️</div>
            <div class="loading-text">CPMS</div>
            <div class="loading-spinner"></div>
            <div class="loading-status">Initializing Enhanced Portal...</div>
        </div>
    </div>

    <!-- Authentication Modal -->
    <div id="auth-modal" class="auth-modal hidden">
        <div class="auth-content">
            <div class="auth-header">
                <h2>🔐 Pensioner Login</h2>
                <p>Access your personal pension information</p>
            </div>
            
            <!-- Login Form -->
            <div id="login-form" class="auth-form">
                <div class="form-group">
                    <label for="eppo-number">EPPO Number</label>
                    <input type="text" id="eppo-number" placeholder="Enter your EPPO number" maxlength="20" required>
                </div>
                <div class="form-group">
                    <label for="mobile-number">Mobile Number</label>
                    <input type="tel" id="mobile-number" placeholder="Enter registered mobile number" maxlength="15" required>
                </div>
                <button type="button" id="send-otp-btn" class="auth-btn primary">
                    <i class="fas fa-paper-plane"></i> Send OTP
                </button>
                <div class="auth-footer">
                    <p>Don't have access? Contact CCA office</p>
                    <button type="button" id="guest-mode-btn" class="auth-btn secondary">
                        Continue as Guest
                    </button>
                </div>
            </div>

            <!-- OTP Verification Form -->
            <div id="otp-form" class="auth-form hidden">
                <div class="otp-info">
                    <p>OTP sent to <span id="otp-mobile"></span></p>
                    <p class="otp-timer">Expires in: <span id="otp-countdown">10:00</span></p>
                </div>
                <div class="form-group">
                    <label for="otp-code">Enter 6-digit OTP</label>
                    <input type="text" id="otp-code" placeholder="000000" maxlength="6" required>
                </div>
                <button type="button" id="verify-otp-btn" class="auth-btn primary">
                    <i class="fas fa-check"></i> Verify & Login
                </button>
                <div class="auth-footer">
                    <button type="button" id="resend-otp-btn" class="auth-btn secondary" disabled>
                        Resend OTP
                    </button>
                    <button type="button" id="back-to-login-btn" class="auth-btn secondary">
                        Back to Login
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app" class="app hidden">
        <!-- Enhanced Header with User Info -->
        <header class="header">
            <div class="header-content">
                <div class="header-left">
                    <div class="logo">
                        <span class="logo-icon">🏛️</span>
                        <div class="logo-text">
                            <h1>CPMS Enhanced</h1>
                            <p>Comprehensive Pension Management System</p>
                        </div>
                    </div>
                </div>
                <div class="header-right">
                    <!-- User Info (shown when authenticated) -->
                    <div id="user-info" class="user-info hidden">
                        <div class="user-details">
                            <span class="user-name" id="user-name">Guest User</span>
                            <span class="user-eppo" id="user-eppo"></span>
                        </div>
                        <button class="header-btn" id="logout-btn" title="Logout" aria-label="Logout">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </div>
                    
                    <!-- Login Button (shown when not authenticated) -->
                    <button id="login-btn" class="header-btn login-btn" title="Login" aria-label="Login">
                        <i class="fas fa-user"></i> Login
                    </button>
                    
                    <div class="connection-status" id="connection-status">
                        <div class="status-indicator" id="status-indicator"></div>
                        <span id="status-text">Connecting...</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Quick Actions Panel (for authenticated users) -->
        <div id="quick-actions" class="quick-actions hidden">
            <div class="quick-actions-content">
                <h3>Quick Actions</h3>
                <div class="action-buttons">
                    <button class="action-btn" data-query="When does my LC expire?">
                        <i class="fas fa-calendar-alt"></i> LC Expiry
                    </button>
                    <button class="action-btn" data-query="Show my pension breakup">
                        <i class="fas fa-chart-pie"></i> Pension Breakup
                    </button>
                    <button class="action-btn" data-query="What is the current DA rate?">
                        <i class="fas fa-percentage"></i> DA Rate
                    </button>
                    <button class="action-btn" data-query="When will my commutation be restored?">
                        <i class="fas fa-undo"></i> Commutation
                    </button>
                    <button class="action-btn" data-query="What is my family pension amount?">
                        <i class="fas fa-users"></i> Family Pension
                    </button>
                    <button class="action-btn" data-query="How to change my mobile number?">
                        <i class="fas fa-mobile-alt"></i> Change Mobile
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Container -->
        <main class="main-content">
            <div class="chat-container">
                <div class="chat-messages" id="chat-messages">
                    <div class="message bot-message">
                        <div class="message-avatar">🏛️</div>
                        <div class="message-content">
                            <div class="message-text">
                                <p>Welcome to CPMS Enhanced Portal! 👋</p>
                                <p>I can help you with:</p>
                                <ul>
                                    <li><strong>General CPMS queries</strong> - Available to all users</li>
                                    <li><strong>Personal pension information</strong> - Login required</li>
                                </ul>
                                <p>Please login to access your personal pension details, or continue as guest for general information.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat Input -->
                <div class="chat-input-container">
                    <div class="chat-input-wrapper">
                        <textarea 
                            id="chat-input" 
                            placeholder="Ask about CPMS procedures, your pension details (login required), or general queries..."
                            rows="1"
                            maxlength="1000"
                        ></textarea>
                        <button id="send-btn" class="send-btn" disabled>
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <div class="input-footer">
                        <div class="input-info">
                            <span id="char-count">0/1000</span>
                            <span class="auth-status" id="auth-status">Guest Mode</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="js/auth.js?v=2.0"></script>
    <script src="js/enhanced_chat.js?v=2.0"></script>
    <script>
        // Force CSS refresh function
        function refreshCSS() {
            const links = document.querySelectorAll('link[rel="stylesheet"]');
            links.forEach(link => {
                const href = link.href;
                const newHref = href.includes('?') ? href.split('?')[0] + '?v=' + Date.now() : href + '?v=' + Date.now();
                link.href = newHref;
            });
        }

        // Initialize the enhanced application
        document.addEventListener('DOMContentLoaded', function() {
            // Force CSS refresh on load
            refreshCSS();

            // Initialize authentication system
            if (typeof AuthSystem !== 'undefined') {
                window.authSystem = new AuthSystem();
            }

            // Initialize enhanced chat
            if (typeof EnhancedChat !== 'undefined') {
                window.enhancedChat = new EnhancedChat();
            }

            // Hide loading screen after initialization
            setTimeout(() => {
                document.getElementById('loading-screen').classList.add('hidden');
                document.getElementById('app').classList.remove('hidden');
            }, 1500);
        });
    </script>
</body>
</html>
