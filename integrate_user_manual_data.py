#!/usr/bin/env python3
"""
CPMS User Manual Integration Script
Integrates structured user manual data into the chatbot training system
"""
import os
import sys
from pathlib import Path

# Add backend to path
sys.path.append(str(Path(__file__).parent / "backend"))

def main():
    """Main integration function"""
    print("🏛️ CPMS User Manual Integration")
    print("=" * 50)
    
    try:
        # Import the integration script
        from backend.scripts.integrate_user_manual import UserManualIntegrator
        
        # Run the integration
        integrator = UserManualIntegrator()
        integrator.integrate_user_manual_data()
        
        print("\n🎉 Integration completed successfully!")
        print("\n📋 What was integrated:")
        print("• Step-by-step procedures for AO login creation")
        print("• DH pension creation procedures")
        print("• Retiree profile creation steps")
        print("• Form processing workflows")
        print("• Pension sanctioning procedures")
        print("• Grievance management processes")
        print("• Payment processing steps")
        print("• Profile update procedures")
        
        print("\n🚀 Your chatbot now has enhanced procedural knowledge!")
        print("Test it with questions like:")
        print("• 'How to create AO login in CPMS?'")
        print("• 'What is the path to create DH login?'")
        print("• 'How to create retiree profile?'")
        print("• 'Steps to fill pension forms'")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running this from the project root directory")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Integration failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
