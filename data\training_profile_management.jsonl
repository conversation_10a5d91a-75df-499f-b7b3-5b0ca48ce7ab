{"instruction": "How to create retiree profile in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    Instructions for AO pension section (after DSC) amended, relating to enclosures to be forwarded to AO PDA (72, sub heading 3.7).  Instructions in Vendor verification of PDA section amended, relating to PDA Utility tab (74, sub heading 4.2).  Arrear Section (82, sub heading 4.5)  Manual for DSC installation (97 onwards). What’s New in Version 4.0  Reference for Side Channel creation in Annexure 1 (17, sub heading 1.7.1.1).  Instructions for filling Retiree Profile (32, sub heading 2.1.1).  Instructions for correction needed in Form 7 and Form 8(42, sub heading 2.1.7).  Instructions for filling Form 12 (51,52, sub heading 2.2.4).  Instructions while allot case to DH by AO Pension (56, sub heading 3.1).  Instructions for AE Generated in Pension Section (61, sub heading 3.4).  Instructions for filling Pension Sanction (68, sub heading 3.7).  Instructions for filling Monthly Bill (82, sub heading 4.5).  Annexure 1 for Side Channel User creation (117 onwards). What’s New in Version 5.0  Reference for ID Card Generation for CDA Pensioners (142, sub heading 9.1)  Instructions for taking print of ID Card (144, sub heading 9.2) What’s New in Version 6.0  Addition of 10. Revision of Pension Module (146, sub heading 10)  Addition of 11. Profile Authentication (34 , 124 and 176 ) 10  Addition of chapter 12. Upload utility to chapter 15. Other Pension Types  Addition of 5.3 to 5.5 in Retiree Module What’s New in Version 6.2  Addition in chapter 5- 5.6 Income Tax Module  Addition in 16. Income Tax Processing 11\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to create retiree profile in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n    Instructions for AO pension section (after DSC) amended, relating to enclosures to be forwarded to AO PDA (72, sub heading 3.7).  Instructions in Vendor verification of PDA section amended, relating to PDA Utility tab (74, sub heading 4.2).  Arrear Section (82, sub heading 4.5)  Manual for DSC installation (97 onwards). What’s New in Version 4.0  Reference for Side Channel creation in Annexure 1 (17, sub heading 1.7.1.1).  Instructions for filling Retiree Profile (32, sub heading 2.1.1).  Instructions for correction needed in Form 7 and Form 8(42, sub heading 2.1.7).  Instructions for filling Form 12 (51,52, sub heading 2.2.4).  Instructions while allot case to DH by AO Pension (56, sub heading 3.1).  Instructions for AE Generated in Pension Section (61, sub heading 3.4).  Instructions for filling Pension Sanction (68, sub heading 3.7).  Instructions for filling Monthly Bill (82, sub heading 4.5).  Annexure 1 for Side Channel User creation (117 onwards). What’s New in Version 5.0  Reference for ID Card Generation for CDA Pensioners (142, sub heading 9.1)  Instructions for taking print of ID Card (144, sub heading 9.2) What’s New in Version 6.0  Addition of 10. Revision of Pension Module (146, sub heading 10)  Addition of 11. Profile Authentication (34 , 124 and 176 ) 10  Addition of chapter 12. Upload utility to chapter 15. Other Pension Types  Addition of 5.3 to 5.5 in Retiree Module What’s New in Version 6.2  Addition in chapter 5- 5.6 Income Tax Module  Addition in 16. Income Tax Processing 11\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "What are the steps to create pensioner profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    Instructions for AO pension section (after DSC) amended, relating to enclosures to be forwarded to AO PDA (72, sub heading 3.7).  Instructions in Vendor verification of PDA section amended, relating to PDA Utility tab (74, sub heading 4.2).  Arrear Section (82, sub heading 4.5)  Manual for DSC installation (97 onwards). What’s New in Version 4.0  Reference for Side Channel creation in Annexure 1 (17, sub heading 1.7.1.1).  Instructions for filling Retiree Profile (32, sub heading 2.1.1).  Instructions for correction needed in Form 7 and Form 8(42, sub heading 2.1.7).  Instructions for filling Form 12 (51,52, sub heading 2.2.4).  Instructions while allot case to DH by AO Pension (56, sub heading 3.1).  Instructions for AE Generated in Pension Section (61, sub heading 3.4).  Instructions for filling Pension Sanction (68, sub heading 3.7).  Instructions for filling Monthly Bill (82, sub heading 4.5).  Annexure 1 for Side Channel User creation (117 onwards). What’s New in Version 5.0  Reference for ID Card Generation for CDA Pensioners (142, sub heading 9.1)  Instructions for taking print of ID Card (144, sub heading 9.2) What’s New in Version 6.0  Addition of 10. Revision of Pension Module (146, sub heading 10)  Addition of 11. Profile Authentication (34 , 124 and 176 ) 10  Addition of chapter 12. Upload utility to chapter 15. Other Pension Types  Addition of 5.3 to 5.5 in Retiree Module What’s New in Version 6.2  Addition in chapter 5- 5.6 Income Tax Module  Addition in 16. Income Tax Processing 11\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "What are the steps to create pensioner profile?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n    Instructions for AO pension section (after DSC) amended, relating to enclosures to be forwarded to AO PDA (72, sub heading 3.7).  Instructions in Vendor verification of PDA section amended, relating to PDA Utility tab (74, sub heading 4.2).  Arrear Section (82, sub heading 4.5)  Manual for DSC installation (97 onwards). What’s New in Version 4.0  Reference for Side Channel creation in Annexure 1 (17, sub heading 1.7.1.1).  Instructions for filling Retiree Profile (32, sub heading 2.1.1).  Instructions for correction needed in Form 7 and Form 8(42, sub heading 2.1.7).  Instructions for filling Form 12 (51,52, sub heading 2.2.4).  Instructions while allot case to DH by AO Pension (56, sub heading 3.1).  Instructions for AE Generated in Pension Section (61, sub heading 3.4).  Instructions for filling Pension Sanction (68, sub heading 3.7).  Instructions for filling Monthly Bill (82, sub heading 4.5).  Annexure 1 for Side Channel User creation (117 onwards). What’s New in Version 5.0  Reference for ID Card Generation for CDA Pensioners (142, sub heading 9.1)  Instructions for taking print of ID Card (144, sub heading 9.2) What’s New in Version 6.0  Addition of 10. Revision of Pension Module (146, sub heading 10)  Addition of 11. Profile Authentication (34 , 124 and 176 ) 10  Addition of chapter 12. Upload utility to chapter 15. Other Pension Types  Addition of 5.3 to 5.5 in Retiree Module What’s New in Version 6.2  Addition in chapter 5- 5.6 Income Tax Module  Addition in 16. Income Tax Processing 11\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "How to fill retiree details in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    Instructions for AO pension section (after DSC) amended, relating to enclosures to be forwarded to AO PDA (72, sub heading 3.7).  Instructions in Vendor verification of PDA section amended, relating to PDA Utility tab (74, sub heading 4.2).  Arrear Section (82, sub heading 4.5)  Manual for DSC installation (97 onwards). What’s New in Version 4.0  Reference for Side Channel creation in Annexure 1 (17, sub heading 1.7.1.1).  Instructions for filling Retiree Profile (32, sub heading 2.1.1).  Instructions for correction needed in Form 7 and Form 8(42, sub heading 2.1.7).  Instructions for filling Form 12 (51,52, sub heading 2.2.4).  Instructions while allot case to DH by AO Pension (56, sub heading 3.1).  Instructions for AE Generated in Pension Section (61, sub heading 3.4).  Instructions for filling Pension Sanction (68, sub heading 3.7).  Instructions for filling Monthly Bill (82, sub heading 4.5).  Annexure 1 for Side Channel User creation (117 onwards). What’s New in Version 5.0  Reference for ID Card Generation for CDA Pensioners (142, sub heading 9.1)  Instructions for taking print of ID Card (144, sub heading 9.2) What’s New in Version 6.0  Addition of 10. Revision of Pension Module (146, sub heading 10)  Addition of 11. Profile Authentication (34 , 124 and 176 ) 10  Addition of chapter 12. Upload utility to chapter 15. Other Pension Types  Addition of 5.3 to 5.5 in Retiree Module What’s New in Version 6.2  Addition in chapter 5- 5.6 Income Tax Module  Addition in 16. Income Tax Processing 11\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to fill retiree details in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n    Instructions for AO pension section (after DSC) amended, relating to enclosures to be forwarded to AO PDA (72, sub heading 3.7).  Instructions in Vendor verification of PDA section amended, relating to PDA Utility tab (74, sub heading 4.2).  Arrear Section (82, sub heading 4.5)  Manual for DSC installation (97 onwards). What’s New in Version 4.0  Reference for Side Channel creation in Annexure 1 (17, sub heading 1.7.1.1).  Instructions for filling Retiree Profile (32, sub heading 2.1.1).  Instructions for correction needed in Form 7 and Form 8(42, sub heading 2.1.7).  Instructions for filling Form 12 (51,52, sub heading 2.2.4).  Instructions while allot case to DH by AO Pension (56, sub heading 3.1).  Instructions for AE Generated in Pension Section (61, sub heading 3.4).  Instructions for filling Pension Sanction (68, sub heading 3.7).  Instructions for filling Monthly Bill (82, sub heading 4.5).  Annexure 1 for Side Channel User creation (117 onwards). What’s New in Version 5.0  Reference for ID Card Generation for CDA Pensioners (142, sub heading 9.1)  Instructions for taking print of ID Card (144, sub heading 9.2) What’s New in Version 6.0  Addition of 10. Revision of Pension Module (146, sub heading 10)  Addition of 11. Profile Authentication (34 , 124 and 176 ) 10  Addition of chapter 12. Upload utility to chapter 15. Other Pension Types  Addition of 5.3 to 5.5 in Retiree Module What’s New in Version 6.2  Addition in chapter 5- 5.6 Income Tax Module  Addition in 16. Income Tax Processing 11\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "What information is required for retiree profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    Instructions for AO pension section (after DSC) amended, relating to enclosures to be forwarded to AO PDA (72, sub heading 3.7).  Instructions in Vendor verification of PDA section amended, relating to PDA Utility tab (74, sub heading 4.2).  Arrear Section (82, sub heading 4.5)  Manual for DSC installation (97 onwards). What’s New in Version 4.0  Reference for Side Channel creation in Annexure 1 (17, sub heading 1.7.1.1).  Instructions for filling Retiree Profile (32, sub heading 2.1.1).  Instructions for correction needed in Form 7 and Form 8(42, sub heading 2.1.7).  Instructions for filling Form 12 (51,52, sub heading 2.2.4).  Instructions while allot case to DH by AO Pension (56, sub heading 3.1).  Instructions for AE Generated in Pension Section (61, sub heading 3.4).  Instructions for filling Pension Sanction (68, sub heading 3.7).  Instructions for filling Monthly Bill (82, sub heading 4.5).  Annexure 1 for Side Channel User creation (117 onwards). What’s New in Version 5.0  Reference for ID Card Generation for CDA Pensioners (142, sub heading 9.1)  Instructions for taking print of ID Card (144, sub heading 9.2) What’s New in Version 6.0  Addition of 10. Revision of Pension Module (146, sub heading 10)  Addition of 11. Profile Authentication (34 , 124 and 176 ) 10  Addition of chapter 12. Upload utility to chapter 15. Other Pension Types  Addition of 5.3 to 5.5 in Retiree Module What’s New in Version 6.2  Addition in chapter 5- 5.6 Income Tax Module  Addition in 16. Income Tax Processing 11\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to add new retiree in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    Instructions for AO pension section (after DSC) amended, relating to enclosures to be forwarded to AO PDA (72, sub heading 3.7).  Instructions in Vendor verification of PDA section amended, relating to PDA Utility tab (74, sub heading 4.2).  Arrear Section (82, sub heading 4.5)  Manual for DSC installation (97 onwards). What’s New in Version 4.0  Reference for Side Channel creation in Annexure 1 (17, sub heading 1.7.1.1).  Instructions for filling Retiree Profile (32, sub heading 2.1.1).  Instructions for correction needed in Form 7 and Form 8(42, sub heading 2.1.7).  Instructions for filling Form 12 (51,52, sub heading 2.2.4).  Instructions while allot case to DH by AO Pension (56, sub heading 3.1).  Instructions for AE Generated in Pension Section (61, sub heading 3.4).  Instructions for filling Pension Sanction (68, sub heading 3.7).  Instructions for filling Monthly Bill (82, sub heading 4.5).  Annexure 1 for Side Channel User creation (117 onwards). What’s New in Version 5.0  Reference for ID Card Generation for CDA Pensioners (142, sub heading 9.1)  Instructions for taking print of ID Card (144, sub heading 9.2) What’s New in Version 6.0  Addition of 10. Revision of Pension Module (146, sub heading 10)  Addition of 11. Profile Authentication (34 , 124 and 176 ) 10  Addition of chapter 12. Upload utility to chapter 15. Other Pension Types  Addition of 5.3 to 5.5 in Retiree Module What’s New in Version 6.2  Addition in chapter 5- 5.6 Income Tax Module  Addition in 16. Income Tax Processing 11\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to add new retiree in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n    Instructions for AO pension section (after DSC) amended, relating to enclosures to be forwarded to AO PDA (72, sub heading 3.7).  Instructions in Vendor verification of PDA section amended, relating to PDA Utility tab (74, sub heading 4.2).  Arrear Section (82, sub heading 4.5)  Manual for DSC installation (97 onwards). What’s New in Version 4.0  Reference for Side Channel creation in Annexure 1 (17, sub heading 1.7.1.1).  Instructions for filling Retiree Profile (32, sub heading 2.1.1).  Instructions for correction needed in Form 7 and Form 8(42, sub heading 2.1.7).  Instructions for filling Form 12 (51,52, sub heading 2.2.4).  Instructions while allot case to DH by AO Pension (56, sub heading 3.1).  Instructions for AE Generated in Pension Section (61, sub heading 3.4).  Instructions for filling Pension Sanction (68, sub heading 3.7).  Instructions for filling Monthly Bill (82, sub heading 4.5).  Annexure 1 for Side Channel User creation (117 onwards). What’s New in Version 5.0  Reference for ID Card Generation for CDA Pensioners (142, sub heading 9.1)  Instructions for taking print of ID Card (144, sub heading 9.2) What’s New in Version 6.0  Addition of 10. Revision of Pension Module (146, sub heading 10)  Addition of 11. Profile Authentication (34 , 124 and 176 ) 10  Addition of chapter 12. Upload utility to chapter 15. Other Pension Types  Addition of 5.3 to 5.5 in Retiree Module What’s New in Version 6.2  Addition in chapter 5- 5.6 Income Tax Module  Addition in 16. Income Tax Processing 11\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "How to create retiree profile in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    DH to login to create the retiree profile.  Select Retiree details on the Menu options available on the left pane.  Click on “Add New” button at the right top corner of Retiree Profile page to get the form to fill.  Enter the following information on retiree detail form (underlined parameters are the mandatory fields): a. Title b. First Name c. Middle Name d. Last Name e. Type of Retirement f. Height g. Father’s/ Husband’s Name. h. Mother’s Name i. Date of Birth j. Gender k. Aadhaar Number l. PAN Number m. Mobile Number (This should be an Active Number) n. Email ID o. Identification Mark 1 p. Identification Mark 2  Following details to be filled in other details section (All are mandatory fields): a. Designation b. Group c. Date of Joining d. Date of Retirement e. Date of Superannuation f. Govt. Accommodation provided by Directorate of Estates/BSNL at any time of service: YES/ NO (to select from the dropdown) 35 g. Pay Commission (this should be the Pay Commission corresponding to the last pay drawn) *After filling the aforementioned information, DH to click on Save button. By using the Clear button, User can also refresh the form and fill in fresh information. (2.1) IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore, due diligence should be exercised while creating the Retiree Profile. In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk. 2.1 Creation of the Retiree Profile is the most important step in the process, so it should be ensured entries made therein are correct in all respects. After saving the information, it is recommended that a printout of the page be taken from the screen and Vetted by HoO level User before proceeding ahead in Service Verification. If any error is detected, then retiree profile be edited before initiating 2.1.2(Further updates in chapter 11) 36\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "What are the steps to create pensioner profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    DH to login to create the retiree profile.  Select Retiree details on the Menu options available on the left pane.  Click on “Add New” button at the right top corner of Retiree Profile page to get the form to fill.  Enter the following information on retiree detail form (underlined parameters are the mandatory fields): a. Title b. First Name c. Middle Name d. Last Name e. Type of Retirement f. Height g. Father’s/ Husband’s Name. h. Mother’s Name i. Date of Birth j. Gender k. Aadhaar Number l. PAN Number m. Mobile Number (This should be an Active Number) n. Email ID o. Identification Mark 1 p. Identification Mark 2  Following details to be filled in other details section (All are mandatory fields): a. Designation b. Group c. Date of Joining d. Date of Retirement e. Date of Superannuation f. Govt. Accommodation provided by Directorate of Estates/BSNL at any time of service: YES/ NO (to select from the dropdown) 35 g. Pay Commission (this should be the Pay Commission corresponding to the last pay drawn) *After filling the aforementioned information, DH to click on Save button. By using the Clear button, User can also refresh the form and fill in fresh information. (2.1) IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore, due diligence should be exercised while creating the Retiree Profile. In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk. 2.1 Creation of the Retiree Profile is the most important step in the process, so it should be ensured entries made therein are correct in all respects. After saving the information, it is recommended that a printout of the page be taken from the screen and Vetted by HoO level User before proceeding ahead in Service Verification. If any error is detected, then retiree profile be edited before initiating 2.1.2(Further updates in chapter 11) 36\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to fill retiree details in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    DH to login to create the retiree profile.  Select Retiree details on the Menu options available on the left pane.  Click on “Add New” button at the right top corner of Retiree Profile page to get the form to fill.  Enter the following information on retiree detail form (underlined parameters are the mandatory fields): a. Title b. First Name c. Middle Name d. Last Name e. Type of Retirement f. Height g. Father’s/ Husband’s Name. h. Mother’s Name i. Date of Birth j. Gender k. Aadhaar Number l. PAN Number m. Mobile Number (This should be an Active Number) n. Email ID o. Identification Mark 1 p. Identification Mark 2  Following details to be filled in other details section (All are mandatory fields): a. Designation b. Group c. Date of Joining d. Date of Retirement e. Date of Superannuation f. Govt. Accommodation provided by Directorate of Estates/BSNL at any time of service: YES/ NO (to select from the dropdown) 35 g. Pay Commission (this should be the Pay Commission corresponding to the last pay drawn) *After filling the aforementioned information, DH to click on Save button. By using the Clear button, User can also refresh the form and fill in fresh information. (2.1) IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore, due diligence should be exercised while creating the Retiree Profile. In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk. 2.1 Creation of the Retiree Profile is the most important step in the process, so it should be ensured entries made therein are correct in all respects. After saving the information, it is recommended that a printout of the page be taken from the screen and Vetted by HoO level User before proceeding ahead in Service Verification. If any error is detected, then retiree profile be edited before initiating 2.1.2(Further updates in chapter 11) 36\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "What information is required for retiree profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    DH to login to create the retiree profile.  Select Retiree details on the Menu options available on the left pane.  Click on “Add New” button at the right top corner of Retiree Profile page to get the form to fill.  Enter the following information on retiree detail form (underlined parameters are the mandatory fields): a. Title b. First Name c. Middle Name d. Last Name e. Type of Retirement f. Height g. Father’s/ Husband’s Name. h. Mother’s Name i. Date of Birth j. Gender k. Aadhaar Number l. PAN Number m. Mobile Number (This should be an Active Number) n. Email ID o. Identification Mark 1 p. Identification Mark 2  Following details to be filled in other details section (All are mandatory fields): a. Designation b. Group c. Date of Joining d. Date of Retirement e. Date of Superannuation f. Govt. Accommodation provided by Directorate of Estates/BSNL at any time of service: YES/ NO (to select from the dropdown) 35 g. Pay Commission (this should be the Pay Commission corresponding to the last pay drawn) *After filling the aforementioned information, DH to click on Save button. By using the Clear button, User can also refresh the form and fill in fresh information. (2.1) IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore, due diligence should be exercised while creating the Retiree Profile. In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk. 2.1 Creation of the Retiree Profile is the most important step in the process, so it should be ensured entries made therein are correct in all respects. After saving the information, it is recommended that a printout of the page be taken from the screen and Vetted by HoO level User before proceeding ahead in Service Verification. If any error is detected, then retiree profile be edited before initiating 2.1.2(Further updates in chapter 11) 36\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to add new retiree in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    DH to login to create the retiree profile.  Select Retiree details on the Menu options available on the left pane.  Click on “Add New” button at the right top corner of Retiree Profile page to get the form to fill.  Enter the following information on retiree detail form (underlined parameters are the mandatory fields): a. Title b. First Name c. Middle Name d. Last Name e. Type of Retirement f. Height g. Father’s/ Husband’s Name. h. Mother’s Name i. Date of Birth j. Gender k. Aadhaar Number l. PAN Number m. Mobile Number (This should be an Active Number) n. Email ID o. Identification Mark 1 p. Identification Mark 2  Following details to be filled in other details section (All are mandatory fields): a. Designation b. Group c. Date of Joining d. Date of Retirement e. Date of Superannuation f. Govt. Accommodation provided by Directorate of Estates/BSNL at any time of service: YES/ NO (to select from the dropdown) 35 g. Pay Commission (this should be the Pay Commission corresponding to the last pay drawn) *After filling the aforementioned information, DH to click on Save button. By using the Clear button, User can also refresh the form and fill in fresh information. (2.1) IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore, due diligence should be exercised while creating the Retiree Profile. In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk. 2.1 Creation of the Retiree Profile is the most important step in the process, so it should be ensured entries made therein are correct in all respects. After saving the information, it is recommended that a printout of the page be taken from the screen and Vetted by HoO level User before proceeding ahead in Service Verification. If any error is detected, then retiree profile be edited before initiating 2.1.2(Further updates in chapter 11) 36\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to create retiree profile in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    Pensioner/Retiree should login into CPMS using PAN and Password received on SMS (NOTE: In case of Family Pension the Retiree Profile will be created by the SSA office itself). Click on Pensioners Details >> Profile. 5.2 88 5.2  Few details will be pre-populated in the profile of the retiree and rest of the details will be filled by the retiree (In case there is any discrepancy in the pre-populated details of the retiree, he/she may write to SSA unit and get the same rectified). Personal Details Tab:  Click on Personal Details Tab and fill the same. After filling the same click on Save button and move on to next tab ‘Commutation and FMA’. 5.3 89 5.3 Commutation and FMA Tab: 90 In this tab user will fill the FMA and Commutation details. The commutation percentage can be maximum 40%. 5.4 In case FMA is applicable, retiree needs to select his/her area of residence as CGHS or non- CGHS. Other requirements may also be filled in. 5.4 Family Details Tab:  Retiree will fill information about his/her family members in this tab.  Then the Retiree should fill the nominations, alternate nominees etc. and keep clicking save button to move on to the next tabs. 5.5  The Retiree should NOT fill his/her own details in this tab. 91 5.5 Nomination Form 1: 5.6 5.6 92 Nomination Form A 5.7 5.7 Bank Details Tab: Retiree will fill the bank details in this tab. He/she should be careful while entering these details as this is important information for the pension disbursement. 5.8  After filling the bank details, bank undertaking will be printed (button provided on screen as Print Bank Undertaking). Pensioner will himself/herself sign the same and also get signed from bank authorities. Retiree will then upload the signed Bank Undertaking on CPMS (upload a file). 93 5.8 IMPORTANT: -Pensioner profile prompts the pensioner to enter bank details. The pension and pensionary benefits of the pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Pensioner, and then uploaded on CPMS. Check List Tab: The Retiree will fill the Check List as per the criteria and scenarios. SL. No. 2, 7, 9 cannot be NO. Retiree need to fill them carefully. 5.9 5.9 *Click on ‘Final Submission ‘tab whereupon the case will be finally saved. Once finally submitted, the retiree will not be able to change the data. Hence the details should be thoroughly checked before submission. 5.9 94 The Retiree shall take print out of the forms-form 5, form 3, Nomination forms and Form 1A-and after signing duly submit them with enclosures as mentioned in the checklist of form 5 to HoO.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to create retiree profile in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n    Pensioner/Retiree should login into CPMS using PAN and Password received on SMS (NOTE: In case of Family Pension the Retiree Profile will be created by the SSA office itself). Click on Pensioners Details >> Profile. 5.2 88 5.2  Few details will be pre-populated in the profile of the retiree and rest of the details will be filled by the retiree (In case there is any discrepancy in the pre-populated details of the retiree, he/she may write to SSA unit and get the same rectified). Personal Details Tab:  Click on Personal Details Tab and fill the same. After filling the same click on Save button and move on to next tab ‘Commutation and FMA’. 5.3 89 5.3 Commutation and FMA Tab: 90 In this tab user will fill the FMA and Commutation details. The commutation percentage can be maximum 40%. 5.4 In case FMA is applicable, retiree needs to select his/her area of residence as CGHS or non- CGHS. Other requirements may also be filled in. 5.4 Family Details Tab:  Retiree will fill information about his/her family members in this tab.  Then the Retiree should fill the nominations, alternate nominees etc. and keep clicking save button to move on to the next tabs. 5.5  The Retiree should NOT fill his/her own details in this tab. 91 5.5 Nomination Form 1: 5.6 5.6 92 Nomination Form A 5.7 5.7 Bank Details Tab: Retiree will fill the bank details in this tab. He/she should be careful while entering these details as this is important information for the pension disbursement. 5.8  After filling the bank details, bank undertaking will be printed (button provided on screen as Print Bank Undertaking). Pensioner will himself/herself sign the same and also get signed from bank authorities. Retiree will then upload the signed Bank Undertaking on CPMS (upload a file). 93 5.8 IMPORTANT: -Pensioner profile prompts the pensioner to enter bank details. The pension and pensionary benefits of the pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Pensioner, and then uploaded on CPMS. Check List Tab: The Retiree will fill the Check List as per the criteria and scenarios. SL. No. 2, 7, 9 cannot be NO. Retiree need to fill them carefully. 5.9 5.9 *Click on ‘Final Submission ‘tab whereupon the case will be finally saved. Once finally submitted, the retiree will not be able to change the data. Hence the details should be thoroughly checked before submission. 5.9 94 The Retiree shall take print out of the forms-form 5, form 3, Nomination forms and Form 1A-and after signing duly submit them with enclosures as mentioned in the checklist of form 5 to HoO.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "What are the steps to create pensioner profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    Pensioner/Retiree should login into CPMS using PAN and Password received on SMS (NOTE: In case of Family Pension the Retiree Profile will be created by the SSA office itself). Click on Pensioners Details >> Profile. 5.2 88 5.2  Few details will be pre-populated in the profile of the retiree and rest of the details will be filled by the retiree (In case there is any discrepancy in the pre-populated details of the retiree, he/she may write to SSA unit and get the same rectified). Personal Details Tab:  Click on Personal Details Tab and fill the same. After filling the same click on Save button and move on to next tab ‘Commutation and FMA’. 5.3 89 5.3 Commutation and FMA Tab: 90 In this tab user will fill the FMA and Commutation details. The commutation percentage can be maximum 40%. 5.4 In case FMA is applicable, retiree needs to select his/her area of residence as CGHS or non- CGHS. Other requirements may also be filled in. 5.4 Family Details Tab:  Retiree will fill information about his/her family members in this tab.  Then the Retiree should fill the nominations, alternate nominees etc. and keep clicking save button to move on to the next tabs. 5.5  The Retiree should NOT fill his/her own details in this tab. 91 5.5 Nomination Form 1: 5.6 5.6 92 Nomination Form A 5.7 5.7 Bank Details Tab: Retiree will fill the bank details in this tab. He/she should be careful while entering these details as this is important information for the pension disbursement. 5.8  After filling the bank details, bank undertaking will be printed (button provided on screen as Print Bank Undertaking). Pensioner will himself/herself sign the same and also get signed from bank authorities. Retiree will then upload the signed Bank Undertaking on CPMS (upload a file). 93 5.8 IMPORTANT: -Pensioner profile prompts the pensioner to enter bank details. The pension and pensionary benefits of the pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Pensioner, and then uploaded on CPMS. Check List Tab: The Retiree will fill the Check List as per the criteria and scenarios. SL. No. 2, 7, 9 cannot be NO. Retiree need to fill them carefully. 5.9 5.9 *Click on ‘Final Submission ‘tab whereupon the case will be finally saved. Once finally submitted, the retiree will not be able to change the data. Hence the details should be thoroughly checked before submission. 5.9 94 The Retiree shall take print out of the forms-form 5, form 3, Nomination forms and Form 1A-and after signing duly submit them with enclosures as mentioned in the checklist of form 5 to HoO.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "What are the steps to create pensioner profile?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n    Pensioner/Retiree should login into CPMS using PAN and Password received on SMS (NOTE: In case of Family Pension the Retiree Profile will be created by the SSA office itself). Click on Pensioners Details >> Profile. 5.2 88 5.2  Few details will be pre-populated in the profile of the retiree and rest of the details will be filled by the retiree (In case there is any discrepancy in the pre-populated details of the retiree, he/she may write to SSA unit and get the same rectified). Personal Details Tab:  Click on Personal Details Tab and fill the same. After filling the same click on Save button and move on to next tab ‘Commutation and FMA’. 5.3 89 5.3 Commutation and FMA Tab: 90 In this tab user will fill the FMA and Commutation details. The commutation percentage can be maximum 40%. 5.4 In case FMA is applicable, retiree needs to select his/her area of residence as CGHS or non- CGHS. Other requirements may also be filled in. 5.4 Family Details Tab:  Retiree will fill information about his/her family members in this tab.  Then the Retiree should fill the nominations, alternate nominees etc. and keep clicking save button to move on to the next tabs. 5.5  The Retiree should NOT fill his/her own details in this tab. 91 5.5 Nomination Form 1: 5.6 5.6 92 Nomination Form A 5.7 5.7 Bank Details Tab: Retiree will fill the bank details in this tab. He/she should be careful while entering these details as this is important information for the pension disbursement. 5.8  After filling the bank details, bank undertaking will be printed (button provided on screen as Print Bank Undertaking). Pensioner will himself/herself sign the same and also get signed from bank authorities. Retiree will then upload the signed Bank Undertaking on CPMS (upload a file). 93 5.8 IMPORTANT: -Pensioner profile prompts the pensioner to enter bank details. The pension and pensionary benefits of the pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Pensioner, and then uploaded on CPMS. Check List Tab: The Retiree will fill the Check List as per the criteria and scenarios. SL. No. 2, 7, 9 cannot be NO. Retiree need to fill them carefully. 5.9 5.9 *Click on ‘Final Submission ‘tab whereupon the case will be finally saved. Once finally submitted, the retiree will not be able to change the data. Hence the details should be thoroughly checked before submission. 5.9 94 The Retiree shall take print out of the forms-form 5, form 3, Nomination forms and Form 1A-and after signing duly submit them with enclosures as mentioned in the checklist of form 5 to HoO.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "How to fill retiree details in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    Pensioner/Retiree should login into CPMS using PAN and Password received on SMS (NOTE: In case of Family Pension the Retiree Profile will be created by the SSA office itself). Click on Pensioners Details >> Profile. 5.2 88 5.2  Few details will be pre-populated in the profile of the retiree and rest of the details will be filled by the retiree (In case there is any discrepancy in the pre-populated details of the retiree, he/she may write to SSA unit and get the same rectified). Personal Details Tab:  Click on Personal Details Tab and fill the same. After filling the same click on Save button and move on to next tab ‘Commutation and FMA’. 5.3 89 5.3 Commutation and FMA Tab: 90 In this tab user will fill the FMA and Commutation details. The commutation percentage can be maximum 40%. 5.4 In case FMA is applicable, retiree needs to select his/her area of residence as CGHS or non- CGHS. Other requirements may also be filled in. 5.4 Family Details Tab:  Retiree will fill information about his/her family members in this tab.  Then the Retiree should fill the nominations, alternate nominees etc. and keep clicking save button to move on to the next tabs. 5.5  The Retiree should NOT fill his/her own details in this tab. 91 5.5 Nomination Form 1: 5.6 5.6 92 Nomination Form A 5.7 5.7 Bank Details Tab: Retiree will fill the bank details in this tab. He/she should be careful while entering these details as this is important information for the pension disbursement. 5.8  After filling the bank details, bank undertaking will be printed (button provided on screen as Print Bank Undertaking). Pensioner will himself/herself sign the same and also get signed from bank authorities. Retiree will then upload the signed Bank Undertaking on CPMS (upload a file). 93 5.8 IMPORTANT: -Pensioner profile prompts the pensioner to enter bank details. The pension and pensionary benefits of the pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Pensioner, and then uploaded on CPMS. Check List Tab: The Retiree will fill the Check List as per the criteria and scenarios. SL. No. 2, 7, 9 cannot be NO. Retiree need to fill them carefully. 5.9 5.9 *Click on ‘Final Submission ‘tab whereupon the case will be finally saved. Once finally submitted, the retiree will not be able to change the data. Hence the details should be thoroughly checked before submission. 5.9 94 The Retiree shall take print out of the forms-form 5, form 3, Nomination forms and Form 1A-and after signing duly submit them with enclosures as mentioned in the checklist of form 5 to HoO.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to fill retiree details in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n    Pensioner/Retiree should login into CPMS using PAN and Password received on SMS (NOTE: In case of Family Pension the Retiree Profile will be created by the SSA office itself). Click on Pensioners Details >> Profile. 5.2 88 5.2  Few details will be pre-populated in the profile of the retiree and rest of the details will be filled by the retiree (In case there is any discrepancy in the pre-populated details of the retiree, he/she may write to SSA unit and get the same rectified). Personal Details Tab:  Click on Personal Details Tab and fill the same. After filling the same click on Save button and move on to next tab ‘Commutation and FMA’. 5.3 89 5.3 Commutation and FMA Tab: 90 In this tab user will fill the FMA and Commutation details. The commutation percentage can be maximum 40%. 5.4 In case FMA is applicable, retiree needs to select his/her area of residence as CGHS or non- CGHS. Other requirements may also be filled in. 5.4 Family Details Tab:  Retiree will fill information about his/her family members in this tab.  Then the Retiree should fill the nominations, alternate nominees etc. and keep clicking save button to move on to the next tabs. 5.5  The Retiree should NOT fill his/her own details in this tab. 91 5.5 Nomination Form 1: 5.6 5.6 92 Nomination Form A 5.7 5.7 Bank Details Tab: Retiree will fill the bank details in this tab. He/she should be careful while entering these details as this is important information for the pension disbursement. 5.8  After filling the bank details, bank undertaking will be printed (button provided on screen as Print Bank Undertaking). Pensioner will himself/herself sign the same and also get signed from bank authorities. Retiree will then upload the signed Bank Undertaking on CPMS (upload a file). 93 5.8 IMPORTANT: -Pensioner profile prompts the pensioner to enter bank details. The pension and pensionary benefits of the pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Pensioner, and then uploaded on CPMS. Check List Tab: The Retiree will fill the Check List as per the criteria and scenarios. SL. No. 2, 7, 9 cannot be NO. Retiree need to fill them carefully. 5.9 5.9 *Click on ‘Final Submission ‘tab whereupon the case will be finally saved. Once finally submitted, the retiree will not be able to change the data. Hence the details should be thoroughly checked before submission. 5.9 94 The Retiree shall take print out of the forms-form 5, form 3, Nomination forms and Form 1A-and after signing duly submit them with enclosures as mentioned in the checklist of form 5 to HoO.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "What information is required for retiree profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    Pensioner/Retiree should login into CPMS using PAN and Password received on SMS (NOTE: In case of Family Pension the Retiree Profile will be created by the SSA office itself). Click on Pensioners Details >> Profile. 5.2 88 5.2  Few details will be pre-populated in the profile of the retiree and rest of the details will be filled by the retiree (In case there is any discrepancy in the pre-populated details of the retiree, he/she may write to SSA unit and get the same rectified). Personal Details Tab:  Click on Personal Details Tab and fill the same. After filling the same click on Save button and move on to next tab ‘Commutation and FMA’. 5.3 89 5.3 Commutation and FMA Tab: 90 In this tab user will fill the FMA and Commutation details. The commutation percentage can be maximum 40%. 5.4 In case FMA is applicable, retiree needs to select his/her area of residence as CGHS or non- CGHS. Other requirements may also be filled in. 5.4 Family Details Tab:  Retiree will fill information about his/her family members in this tab.  Then the Retiree should fill the nominations, alternate nominees etc. and keep clicking save button to move on to the next tabs. 5.5  The Retiree should NOT fill his/her own details in this tab. 91 5.5 Nomination Form 1: 5.6 5.6 92 Nomination Form A 5.7 5.7 Bank Details Tab: Retiree will fill the bank details in this tab. He/she should be careful while entering these details as this is important information for the pension disbursement. 5.8  After filling the bank details, bank undertaking will be printed (button provided on screen as Print Bank Undertaking). Pensioner will himself/herself sign the same and also get signed from bank authorities. Retiree will then upload the signed Bank Undertaking on CPMS (upload a file). 93 5.8 IMPORTANT: -Pensioner profile prompts the pensioner to enter bank details. The pension and pensionary benefits of the pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Pensioner, and then uploaded on CPMS. Check List Tab: The Retiree will fill the Check List as per the criteria and scenarios. SL. No. 2, 7, 9 cannot be NO. Retiree need to fill them carefully. 5.9 5.9 *Click on ‘Final Submission ‘tab whereupon the case will be finally saved. Once finally submitted, the retiree will not be able to change the data. Hence the details should be thoroughly checked before submission. 5.9 94 The Retiree shall take print out of the forms-form 5, form 3, Nomination forms and Form 1A-and after signing duly submit them with enclosures as mentioned in the checklist of form 5 to HoO.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to add new retiree in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    Pensioner/Retiree should login into CPMS using PAN and Password received on SMS (NOTE: In case of Family Pension the Retiree Profile will be created by the SSA office itself). Click on Pensioners Details >> Profile. 5.2 88 5.2  Few details will be pre-populated in the profile of the retiree and rest of the details will be filled by the retiree (In case there is any discrepancy in the pre-populated details of the retiree, he/she may write to SSA unit and get the same rectified). Personal Details Tab:  Click on Personal Details Tab and fill the same. After filling the same click on Save button and move on to next tab ‘Commutation and FMA’. 5.3 89 5.3 Commutation and FMA Tab: 90 In this tab user will fill the FMA and Commutation details. The commutation percentage can be maximum 40%. 5.4 In case FMA is applicable, retiree needs to select his/her area of residence as CGHS or non- CGHS. Other requirements may also be filled in. 5.4 Family Details Tab:  Retiree will fill information about his/her family members in this tab.  Then the Retiree should fill the nominations, alternate nominees etc. and keep clicking save button to move on to the next tabs. 5.5  The Retiree should NOT fill his/her own details in this tab. 91 5.5 Nomination Form 1: 5.6 5.6 92 Nomination Form A 5.7 5.7 Bank Details Tab: Retiree will fill the bank details in this tab. He/she should be careful while entering these details as this is important information for the pension disbursement. 5.8  After filling the bank details, bank undertaking will be printed (button provided on screen as Print Bank Undertaking). Pensioner will himself/herself sign the same and also get signed from bank authorities. Retiree will then upload the signed Bank Undertaking on CPMS (upload a file). 93 5.8 IMPORTANT: -Pensioner profile prompts the pensioner to enter bank details. The pension and pensionary benefits of the pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Pensioner, and then uploaded on CPMS. Check List Tab: The Retiree will fill the Check List as per the criteria and scenarios. SL. No. 2, 7, 9 cannot be NO. Retiree need to fill them carefully. 5.9 5.9 *Click on ‘Final Submission ‘tab whereupon the case will be finally saved. Once finally submitted, the retiree will not be able to change the data. Hence the details should be thoroughly checked before submission. 5.9 94 The Retiree shall take print out of the forms-form 5, form 3, Nomination forms and Form 1A-and after signing duly submit them with enclosures as mentioned in the checklist of form 5 to HoO.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to add new retiree in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n    Pensioner/Retiree should login into CPMS using PAN and Password received on SMS (NOTE: In case of Family Pension the Retiree Profile will be created by the SSA office itself). Click on Pensioners Details >> Profile. 5.2 88 5.2  Few details will be pre-populated in the profile of the retiree and rest of the details will be filled by the retiree (In case there is any discrepancy in the pre-populated details of the retiree, he/she may write to SSA unit and get the same rectified). Personal Details Tab:  Click on Personal Details Tab and fill the same. After filling the same click on Save button and move on to next tab ‘Commutation and FMA’. 5.3 89 5.3 Commutation and FMA Tab: 90 In this tab user will fill the FMA and Commutation details. The commutation percentage can be maximum 40%. 5.4 In case FMA is applicable, retiree needs to select his/her area of residence as CGHS or non- CGHS. Other requirements may also be filled in. 5.4 Family Details Tab:  Retiree will fill information about his/her family members in this tab.  Then the Retiree should fill the nominations, alternate nominees etc. and keep clicking save button to move on to the next tabs. 5.5  The Retiree should NOT fill his/her own details in this tab. 91 5.5 Nomination Form 1: 5.6 5.6 92 Nomination Form A 5.7 5.7 Bank Details Tab: Retiree will fill the bank details in this tab. He/she should be careful while entering these details as this is important information for the pension disbursement. 5.8  After filling the bank details, bank undertaking will be printed (button provided on screen as Print Bank Undertaking). Pensioner will himself/herself sign the same and also get signed from bank authorities. Retiree will then upload the signed Bank Undertaking on CPMS (upload a file). 93 5.8 IMPORTANT: -Pensioner profile prompts the pensioner to enter bank details. The pension and pensionary benefits of the pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Pensioner, and then uploaded on CPMS. Check List Tab: The Retiree will fill the Check List as per the criteria and scenarios. SL. No. 2, 7, 9 cannot be NO. Retiree need to fill them carefully. 5.9 5.9 *Click on ‘Final Submission ‘tab whereupon the case will be finally saved. Once finally submitted, the retiree will not be able to change the data. Hence the details should be thoroughly checked before submission. 5.9 94 The Retiree shall take print out of the forms-form 5, form 3, Nomination forms and Form 1A-and after signing duly submit them with enclosures as mentioned in the checklist of form 5 to HoO.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "How to update mobile number in CPMS?", "input": "", "output": "To update profile information in CPMS:\n\n   For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (5.10)). (5.10) A Pop-up window (5.11)) will be displayed with the option to choose the following details which the user wants to edit or change: - 95 (5.11) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Updation of Mobile, Email and address", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to update mobile number in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (5.10)). (5.10) A Pop-up window (5.11)) will be displayed with the option to choose the following details which the user wants to edit or change: - 95 (5.11) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Updation of Mobile, Email and address", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to change email address?", "input": "", "output": "To update profile information in CPMS:\n\n   For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (5.10)). (5.10) A Pop-up window (5.11)) will be displayed with the option to choose the following details which the user wants to edit or change: - 95 (5.11) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Updation of Mobile, Email and address", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to change email address?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (5.10)). (5.10) A Pop-up window (5.11)) will be displayed with the option to choose the following details which the user wants to edit or change: - 95 (5.11) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Updation of Mobile, Email and address", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "What is the procedure to update address?", "input": "", "output": "To update profile information in CPMS:\n\n   For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (5.10)). (5.10) A Pop-up window (5.11)) will be displayed with the option to choose the following details which the user wants to edit or change: - 95 (5.11) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Updation of Mobile, Email and address", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "What is the procedure to update address?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (5.10)). (5.10) A Pop-up window (5.11)) will be displayed with the option to choose the following details which the user wants to edit or change: - 95 (5.11) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Updation of Mobile, Email and address", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to modify pensioner details?", "input": "", "output": "To update profile information in CPMS:\n\n   For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (5.10)). (5.10) A Pop-up window (5.11)) will be displayed with the option to choose the following details which the user wants to edit or change: - 95 (5.11) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Updation of Mobile, Email and address", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to modify pensioner details?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (5.10)). (5.10) A Pop-up window (5.11)) will be displayed with the option to choose the following details which the user wants to edit or change: - 95 (5.11) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Updation of Mobile, Email and address", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to update contact information?", "input": "", "output": "To update profile information in CPMS:\n\n   For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (5.10)). (5.10) A Pop-up window (5.11)) will be displayed with the option to choose the following details which the user wants to edit or change: - 95 (5.11) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Updation of Mobile, Email and address", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to update contact information?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (5.10)). (5.10) A Pop-up window (5.11)) will be displayed with the option to choose the following details which the user wants to edit or change: - 95 (5.11) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Updation of Mobile, Email and address", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to update mobile number in CPMS?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (5.12)) Fig(5.12) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((5.12)). Upon receiving the OTP, retiree should enter the OTP ((5.13)) and save which will then update the mobile number. 96 (5.13)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to update mobile number in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (5.12)) Fig(5.12) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((5.12)). Upon receiving the OTP, retiree should enter the OTP ((5.13)) and save which will then update the mobile number. 96 (5.13)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to change email address?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (5.12)) Fig(5.12) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((5.12)). Upon receiving the OTP, retiree should enter the OTP ((5.13)) and save which will then update the mobile number. 96 (5.13)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to change email address?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (5.12)) Fig(5.12) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((5.12)). Upon receiving the OTP, retiree should enter the OTP ((5.13)) and save which will then update the mobile number. 96 (5.13)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "What is the procedure to update address?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (5.12)) Fig(5.12) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((5.12)). Upon receiving the OTP, retiree should enter the OTP ((5.13)) and save which will then update the mobile number. 96 (5.13)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "What is the procedure to update address?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (5.12)) Fig(5.12) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((5.12)). Upon receiving the OTP, retiree should enter the OTP ((5.13)) and save which will then update the mobile number. 96 (5.13)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to modify pensioner details?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (5.12)) Fig(5.12) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((5.12)). Upon receiving the OTP, retiree should enter the OTP ((5.13)) and save which will then update the mobile number. 96 (5.13)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to modify pensioner details?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (5.12)) Fig(5.12) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((5.12)). Upon receiving the OTP, retiree should enter the OTP ((5.13)) and save which will then update the mobile number. 96 (5.13)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to update contact information?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (5.12)) Fig(5.12) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((5.12)). Upon receiving the OTP, retiree should enter the OTP ((5.13)) and save which will then update the mobile number. 96 (5.13)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to update contact information?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (5.12)) Fig(5.12) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((5.12)). Upon receiving the OTP, retiree should enter the OTP ((5.13)) and save which will then update the mobile number. 96 (5.13)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to update mobile number in CPMS?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (5.14)) (5.14) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((5.14)). Retiree should then enter the OTP received and click on save ((5.15)) which would then update the email ID. 97 (5.15)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to update mobile number in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (5.14)) (5.14) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((5.14)). Retiree should then enter the OTP received and click on save ((5.15)) which would then update the email ID. 97 (5.15)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to change email address?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (5.14)) (5.14) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((5.14)). Retiree should then enter the OTP received and click on save ((5.15)) which would then update the email ID. 97 (5.15)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to change email address?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (5.14)) (5.14) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((5.14)). Retiree should then enter the OTP received and click on save ((5.15)) which would then update the email ID. 97 (5.15)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "What is the procedure to update address?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (5.14)) (5.14) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((5.14)). Retiree should then enter the OTP received and click on save ((5.15)) which would then update the email ID. 97 (5.15)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "What is the procedure to update address?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (5.14)) (5.14) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((5.14)). Retiree should then enter the OTP received and click on save ((5.15)) which would then update the email ID. 97 (5.15)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to modify pensioner details?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (5.14)) (5.14) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((5.14)). Retiree should then enter the OTP received and click on save ((5.15)) which would then update the email ID. 97 (5.15)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to modify pensioner details?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (5.14)) (5.14) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((5.14)). Retiree should then enter the OTP received and click on save ((5.15)) which would then update the email ID. 97 (5.15)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to update contact information?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (5.14)) (5.14) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((5.14)). Retiree should then enter the OTP received and click on save ((5.15)) which would then update the email ID. 97 (5.15)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to update contact information?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (5.14)) (5.14) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((5.14)). Retiree should then enter the OTP received and click on save ((5.15)) which would then update the email ID. 97 (5.15)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to create retiree profile in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    DH has to login to create the retiree profile.  Select Retiree details on the Menu options available on the left panel.  Click on “Add New” button at the right top corner of Retiree Profile page to get the form to fill.  Enter the following information on retiree detail form (underlined parameters are the mandatory fields): q. Title r. First Name s. Middle Name t. Last Name u. Type of Retirement v. Height w. Father’s/ Husband’s Name. x. Mother’s Name y. Date of Birth z. Gender aa. Aadhaar Number bb. PAN Number cc. Mobile Number (This should be an Active Number) dd. Email ID ee. Identification Mark 1 ff. Identification Mark 2  Following details to be filled in other details section (All are mandatory fields): h. Designation i. Group j. Date of Joining k. Date of Retirement l. Date of Superannuation m. Govt. Accommodation provided by Directorate of Estates/BSNL at any time of service: YES/ NO (to select from the dropdown) n. Pay Commission (this should be the Pay Commission corresponding to the last pay drawn) *After filling the aforementioned information, DH to click on Save button. By using the Clear button, User can also refresh the form and fill in fresh information. (8.3) 138 IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore, due diligence should be exercised while creating the Retiree Profile. In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk. 8.3 Creation of the Retiree Profile is the most important step in the process, so it should be ensured entries made therein are correct in all respects. After saving the information, it is recommended a printout of the page be taken from the screen and Vetted by HoO level User before proceeding ahead in Service Verification. If any error is detected, then retiree profile be edited before initiating 8.2.1.2. (further updates in chapter 11)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "What are the steps to create pensioner profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    DH has to login to create the retiree profile.  Select Retiree details on the Menu options available on the left panel.  Click on “Add New” button at the right top corner of Retiree Profile page to get the form to fill.  Enter the following information on retiree detail form (underlined parameters are the mandatory fields): q. Title r. First Name s. Middle Name t. Last Name u. Type of Retirement v. Height w. Father’s/ Husband’s Name. x. Mother’s Name y. Date of Birth z. Gender aa. Aadhaar Number bb. PAN Number cc. Mobile Number (This should be an Active Number) dd. Email ID ee. Identification Mark 1 ff. Identification Mark 2  Following details to be filled in other details section (All are mandatory fields): h. Designation i. Group j. Date of Joining k. Date of Retirement l. Date of Superannuation m. Govt. Accommodation provided by Directorate of Estates/BSNL at any time of service: YES/ NO (to select from the dropdown) n. Pay Commission (this should be the Pay Commission corresponding to the last pay drawn) *After filling the aforementioned information, DH to click on Save button. By using the Clear button, User can also refresh the form and fill in fresh information. (8.3) 138 IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore, due diligence should be exercised while creating the Retiree Profile. In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk. 8.3 Creation of the Retiree Profile is the most important step in the process, so it should be ensured entries made therein are correct in all respects. After saving the information, it is recommended a printout of the page be taken from the screen and Vetted by HoO level User before proceeding ahead in Service Verification. If any error is detected, then retiree profile be edited before initiating 8.2.1.2. (further updates in chapter 11)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to fill retiree details in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    DH has to login to create the retiree profile.  Select Retiree details on the Menu options available on the left panel.  Click on “Add New” button at the right top corner of Retiree Profile page to get the form to fill.  Enter the following information on retiree detail form (underlined parameters are the mandatory fields): q. Title r. First Name s. Middle Name t. Last Name u. Type of Retirement v. Height w. Father’s/ Husband’s Name. x. Mother’s Name y. Date of Birth z. Gender aa. Aadhaar Number bb. PAN Number cc. Mobile Number (This should be an Active Number) dd. Email ID ee. Identification Mark 1 ff. Identification Mark 2  Following details to be filled in other details section (All are mandatory fields): h. Designation i. Group j. Date of Joining k. Date of Retirement l. Date of Superannuation m. Govt. Accommodation provided by Directorate of Estates/BSNL at any time of service: YES/ NO (to select from the dropdown) n. Pay Commission (this should be the Pay Commission corresponding to the last pay drawn) *After filling the aforementioned information, DH to click on Save button. By using the Clear button, User can also refresh the form and fill in fresh information. (8.3) 138 IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore, due diligence should be exercised while creating the Retiree Profile. In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk. 8.3 Creation of the Retiree Profile is the most important step in the process, so it should be ensured entries made therein are correct in all respects. After saving the information, it is recommended a printout of the page be taken from the screen and Vetted by HoO level User before proceeding ahead in Service Verification. If any error is detected, then retiree profile be edited before initiating 8.2.1.2. (further updates in chapter 11)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "What information is required for retiree profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    DH has to login to create the retiree profile.  Select Retiree details on the Menu options available on the left panel.  Click on “Add New” button at the right top corner of Retiree Profile page to get the form to fill.  Enter the following information on retiree detail form (underlined parameters are the mandatory fields): q. Title r. First Name s. Middle Name t. Last Name u. Type of Retirement v. Height w. Father’s/ Husband’s Name. x. Mother’s Name y. Date of Birth z. Gender aa. Aadhaar Number bb. PAN Number cc. Mobile Number (This should be an Active Number) dd. Email ID ee. Identification Mark 1 ff. Identification Mark 2  Following details to be filled in other details section (All are mandatory fields): h. Designation i. Group j. Date of Joining k. Date of Retirement l. Date of Superannuation m. Govt. Accommodation provided by Directorate of Estates/BSNL at any time of service: YES/ NO (to select from the dropdown) n. Pay Commission (this should be the Pay Commission corresponding to the last pay drawn) *After filling the aforementioned information, DH to click on Save button. By using the Clear button, User can also refresh the form and fill in fresh information. (8.3) 138 IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore, due diligence should be exercised while creating the Retiree Profile. In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk. 8.3 Creation of the Retiree Profile is the most important step in the process, so it should be ensured entries made therein are correct in all respects. After saving the information, it is recommended a printout of the page be taken from the screen and Vetted by HoO level User before proceeding ahead in Service Verification. If any error is detected, then retiree profile be edited before initiating 8.2.1.2. (further updates in chapter 11)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to add new retiree in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n    DH has to login to create the retiree profile.  Select Retiree details on the Menu options available on the left panel.  Click on “Add New” button at the right top corner of Retiree Profile page to get the form to fill.  Enter the following information on retiree detail form (underlined parameters are the mandatory fields): q. Title r. First Name s. Middle Name t. Last Name u. Type of Retirement v. Height w. Father’s/ Husband’s Name. x. Mother’s Name y. Date of Birth z. Gender aa. Aadhaar Number bb. PAN Number cc. Mobile Number (This should be an Active Number) dd. Email ID ee. Identification Mark 1 ff. Identification Mark 2  Following details to be filled in other details section (All are mandatory fields): h. Designation i. Group j. Date of Joining k. Date of Retirement l. Date of Superannuation m. Govt. Accommodation provided by Directorate of Estates/BSNL at any time of service: YES/ NO (to select from the dropdown) n. Pay Commission (this should be the Pay Commission corresponding to the last pay drawn) *After filling the aforementioned information, DH to click on Save button. By using the Clear button, User can also refresh the form and fill in fresh information. (8.3) 138 IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore, due diligence should be exercised while creating the Retiree Profile. In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk. 8.3 Creation of the Retiree Profile is the most important step in the process, so it should be ensured entries made therein are correct in all respects. After saving the information, it is recommended a printout of the page be taken from the screen and Vetted by HoO level User before proceeding ahead in Service Verification. If any error is detected, then retiree profile be edited before initiating 8.2.1.2. (further updates in chapter 11)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to update mobile number in CPMS?", "input": "", "output": "To update profile information in CPMS:\n\n    Login with AO Pension go to the Masters Issuing Authority Signature.  Click on the “upload a file” button and upload the signature of the Issuing Authority. (9.1) (9.2)  After uploading the image, click on the Update button (9.3). (9.1) 159 (9.2) (9.3)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Upload AO Pension signature", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to update mobile number in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n    Login with AO Pension go to the Masters Issuing Authority Signature.  Click on the “upload a file” button and upload the signature of the Issuing Authority. (9.1) (9.2)  After uploading the image, click on the Update button (9.3). (9.1) 159 (9.2) (9.3)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Upload AO Pension signature", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to change email address?", "input": "", "output": "To update profile information in CPMS:\n\n    Login with AO Pension go to the Masters Issuing Authority Signature.  Click on the “upload a file” button and upload the signature of the Issuing Authority. (9.1) (9.2)  After uploading the image, click on the Update button (9.3). (9.1) 159 (9.2) (9.3)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Upload AO Pension signature", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to change email address?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n    Login with AO Pension go to the Masters Issuing Authority Signature.  Click on the “upload a file” button and upload the signature of the Issuing Authority. (9.1) (9.2)  After uploading the image, click on the Update button (9.3). (9.1) 159 (9.2) (9.3)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Upload AO Pension signature", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "What is the procedure to update address?", "input": "", "output": "To update profile information in CPMS:\n\n    Login with AO Pension go to the Masters Issuing Authority Signature.  Click on the “upload a file” button and upload the signature of the Issuing Authority. (9.1) (9.2)  After uploading the image, click on the Update button (9.3). (9.1) 159 (9.2) (9.3)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Upload AO Pension signature", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "What is the procedure to update address?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n    Login with AO Pension go to the Masters Issuing Authority Signature.  Click on the “upload a file” button and upload the signature of the Issuing Authority. (9.1) (9.2)  After uploading the image, click on the Update button (9.3). (9.1) 159 (9.2) (9.3)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Upload AO Pension signature", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to modify pensioner details?", "input": "", "output": "To update profile information in CPMS:\n\n    Login with AO Pension go to the Masters Issuing Authority Signature.  Click on the “upload a file” button and upload the signature of the Issuing Authority. (9.1) (9.2)  After uploading the image, click on the Update button (9.3). (9.1) 159 (9.2) (9.3)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Upload AO Pension signature", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to modify pensioner details?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n    Login with AO Pension go to the Masters Issuing Authority Signature.  Click on the “upload a file” button and upload the signature of the Issuing Authority. (9.1) (9.2)  After uploading the image, click on the Update button (9.3). (9.1) 159 (9.2) (9.3)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Upload AO Pension signature", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to update contact information?", "input": "", "output": "To update profile information in CPMS:\n\n    Login with AO Pension go to the Masters Issuing Authority Signature.  Click on the “upload a file” button and upload the signature of the Issuing Authority. (9.1) (9.2)  After uploading the image, click on the Update button (9.3). (9.1) 159 (9.2) (9.3)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Upload AO Pension signature", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to update contact information?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n    Login with AO Pension go to the Masters Issuing Authority Signature.  Click on the “upload a file” button and upload the signature of the Issuing Authority. (9.1) (9.2)  After uploading the image, click on the Update button (9.3). (9.1) 159 (9.2) (9.3)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Upload AO Pension signature", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to create retiree profile in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   In order to ensure that the data filled on retiree profile is correct and has been authenticated by AO Pension (in case of side channel) / HOO user (in case of BSNL), retiree profile upload utility has been developed. On service book verification page (11.1)) DH user shall view the retiree profile and if satisfied then a printout of retiree profile shall be taken (11.2)). Fig(11.1) 196 (11.2) AO Pension (in case of side channel) / HOO user (in case of BSNL) will match the retiree details against physical records and the scanned copy is to be uploaded. Before signing, the retiree profile details shall be matched against physical records and in case of error necessary corrections be made. The scanned copy shall be uploaded (11.3)) only after it has been checked as mentioned above. 197 (11.3) The uploaded verification form can be viewed by clicking eye button (11.3), Fig(11.4)). (11.4) The form may then be sent to HOO(SSA Unit) for approval (11.5)). 198 (11.5) 199 12 12. Upload utility There are scenarios wherein either orders have been issued in continuation of PPO or corrigendum have been issued. It is important that these orders – being critical in nature- are not just sent to the pensioner but are also uploaded to the pensioners’ dashboard. For this purpose, upload utility has been created. Some of the scenarios in which upload utility shall be used: 1. Issue of corrigendum of PPO for inclusion of permanently disabled children by PPO. 2. Sanction and payment of arrears of pension in cases where manual calculation has been done In such cases AO(pension)/AO(PDA) shall upload the documents which shall be visible on the pensioners’ dashboard. (12.1) After entering the PPO number and clicking on the “Search” button (12.1)), the below stated screen will get displayed (12.2)). The file to be sent to Pensioner’s Dashboard shall be selected and then uploaded. The description of file shall be added (12.3)). It may be noted that multiple files may be sent to pensioner’s Dashboard. Files can be sent by clicking the Submit button (12.3)). 200 (12.2) (12.3) The AO(pension)/AO(PDA) can view the history or date wise records for the information being shared with the pensioner on its dashboard Fig(12.4). 201 (12.4) Similarly, the pensioner can also view the information shared by the Department on his/her dashboard by clicking on the tab “Shared Documents” (12.5), Fig(12.6)). (12.5) 202 (12.6) 13 13. Bill / Payment slip generation For reconciliation sanction sent from PDA section with the bills appearing on PFMS and to ensure smooth processing, PDF for the bills sent to PFMS for payment can be generated. For bills sent on a day, a pdf can be generated. The date selected for generation of PDF is the date on which DH created the bills. The option of Bill Type and Status Type is to be selected as per the below stated criteria (13.1), Fig(13.2), Fig(13.3))according to user needs:- 203 (13.1) (13.2) (13.3) A PDF of the bills generated in the particular time frame will be displayed/downloaded on your desired location which can be sent to cash as sanction. The PDF (13.4)) can be generated for the files sent on a day and handed over to Cash for DSC. 204 (13.4) 14 14. Updation of Mobile, Email and address For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (14.1). 205 (14.1) A Pop-up window (14.2)) will be displayed with the option to choose the following details which the user wants to edit or change: - (14.2) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Retiree Profile Authentication", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to create retiree profile in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   In order to ensure that the data filled on retiree profile is correct and has been authenticated by AO Pension (in case of side channel) / HOO user (in case of BSNL), retiree profile upload utility has been developed. On service book verification page (11.1)) DH user shall view the retiree profile and if satisfied then a printout of retiree profile shall be taken (11.2)). Fig(11.1) 196 (11.2) AO Pension (in case of side channel) / HOO user (in case of BSNL) will match the retiree details against physical records and the scanned copy is to be uploaded. Before signing, the retiree profile details shall be matched against physical records and in case of error necessary corrections be made. The scanned copy shall be uploaded (11.3)) only after it has been checked as mentioned above. 197 (11.3) The uploaded verification form can be viewed by clicking eye button (11.3), Fig(11.4)). (11.4) The form may then be sent to HOO(SSA Unit) for approval (11.5)). 198 (11.5) 199 12 12. Upload utility There are scenarios wherein either orders have been issued in continuation of PPO or corrigendum have been issued. It is important that these orders – being critical in nature- are not just sent to the pensioner but are also uploaded to the pensioners’ dashboard. For this purpose, upload utility has been created. Some of the scenarios in which upload utility shall be used: 1. Issue of corrigendum of PPO for inclusion of permanently disabled children by PPO. 2. Sanction and payment of arrears of pension in cases where manual calculation has been done In such cases AO(pension)/AO(PDA) shall upload the documents which shall be visible on the pensioners’ dashboard. (12.1) After entering the PPO number and clicking on the “Search” button (12.1)), the below stated screen will get displayed (12.2)). The file to be sent to Pensioner’s Dashboard shall be selected and then uploaded. The description of file shall be added (12.3)). It may be noted that multiple files may be sent to pensioner’s Dashboard. Files can be sent by clicking the Submit button (12.3)). 200 (12.2) (12.3) The AO(pension)/AO(PDA) can view the history or date wise records for the information being shared with the pensioner on its dashboard Fig(12.4). 201 (12.4) Similarly, the pensioner can also view the information shared by the Department on his/her dashboard by clicking on the tab “Shared Documents” (12.5), Fig(12.6)). (12.5) 202 (12.6) 13 13. Bill / Payment slip generation For reconciliation sanction sent from PDA section with the bills appearing on PFMS and to ensure smooth processing, PDF for the bills sent to PFMS for payment can be generated. For bills sent on a day, a pdf can be generated. The date selected for generation of PDF is the date on which DH created the bills. The option of Bill Type and Status Type is to be selected as per the below stated criteria (13.1), Fig(13.2), Fig(13.3))according to user needs:- 203 (13.1) (13.2) (13.3) A PDF of the bills generated in the particular time frame will be displayed/downloaded on your desired location which can be sent to cash as sanction. The PDF (13.4)) can be generated for the files sent on a day and handed over to Cash for DSC. 204 (13.4) 14 14. Updation of Mobile, Email and address For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (14.1). 205 (14.1) A Pop-up window (14.2)) will be displayed with the option to choose the following details which the user wants to edit or change: - (14.2) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Retiree Profile Authentication", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "What are the steps to create pensioner profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   In order to ensure that the data filled on retiree profile is correct and has been authenticated by AO Pension (in case of side channel) / HOO user (in case of BSNL), retiree profile upload utility has been developed. On service book verification page (11.1)) DH user shall view the retiree profile and if satisfied then a printout of retiree profile shall be taken (11.2)). Fig(11.1) 196 (11.2) AO Pension (in case of side channel) / HOO user (in case of BSNL) will match the retiree details against physical records and the scanned copy is to be uploaded. Before signing, the retiree profile details shall be matched against physical records and in case of error necessary corrections be made. The scanned copy shall be uploaded (11.3)) only after it has been checked as mentioned above. 197 (11.3) The uploaded verification form can be viewed by clicking eye button (11.3), Fig(11.4)). (11.4) The form may then be sent to HOO(SSA Unit) for approval (11.5)). 198 (11.5) 199 12 12. Upload utility There are scenarios wherein either orders have been issued in continuation of PPO or corrigendum have been issued. It is important that these orders – being critical in nature- are not just sent to the pensioner but are also uploaded to the pensioners’ dashboard. For this purpose, upload utility has been created. Some of the scenarios in which upload utility shall be used: 1. Issue of corrigendum of PPO for inclusion of permanently disabled children by PPO. 2. Sanction and payment of arrears of pension in cases where manual calculation has been done In such cases AO(pension)/AO(PDA) shall upload the documents which shall be visible on the pensioners’ dashboard. (12.1) After entering the PPO number and clicking on the “Search” button (12.1)), the below stated screen will get displayed (12.2)). The file to be sent to Pensioner’s Dashboard shall be selected and then uploaded. The description of file shall be added (12.3)). It may be noted that multiple files may be sent to pensioner’s Dashboard. Files can be sent by clicking the Submit button (12.3)). 200 (12.2) (12.3) The AO(pension)/AO(PDA) can view the history or date wise records for the information being shared with the pensioner on its dashboard Fig(12.4). 201 (12.4) Similarly, the pensioner can also view the information shared by the Department on his/her dashboard by clicking on the tab “Shared Documents” (12.5), Fig(12.6)). (12.5) 202 (12.6) 13 13. Bill / Payment slip generation For reconciliation sanction sent from PDA section with the bills appearing on PFMS and to ensure smooth processing, PDF for the bills sent to PFMS for payment can be generated. For bills sent on a day, a pdf can be generated. The date selected for generation of PDF is the date on which DH created the bills. The option of Bill Type and Status Type is to be selected as per the below stated criteria (13.1), Fig(13.2), Fig(13.3))according to user needs:- 203 (13.1) (13.2) (13.3) A PDF of the bills generated in the particular time frame will be displayed/downloaded on your desired location which can be sent to cash as sanction. The PDF (13.4)) can be generated for the files sent on a day and handed over to Cash for DSC. 204 (13.4) 14 14. Updation of Mobile, Email and address For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (14.1). 205 (14.1) A Pop-up window (14.2)) will be displayed with the option to choose the following details which the user wants to edit or change: - (14.2) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Retiree Profile Authentication", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "What are the steps to create pensioner profile?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   In order to ensure that the data filled on retiree profile is correct and has been authenticated by AO Pension (in case of side channel) / HOO user (in case of BSNL), retiree profile upload utility has been developed. On service book verification page (11.1)) DH user shall view the retiree profile and if satisfied then a printout of retiree profile shall be taken (11.2)). Fig(11.1) 196 (11.2) AO Pension (in case of side channel) / HOO user (in case of BSNL) will match the retiree details against physical records and the scanned copy is to be uploaded. Before signing, the retiree profile details shall be matched against physical records and in case of error necessary corrections be made. The scanned copy shall be uploaded (11.3)) only after it has been checked as mentioned above. 197 (11.3) The uploaded verification form can be viewed by clicking eye button (11.3), Fig(11.4)). (11.4) The form may then be sent to HOO(SSA Unit) for approval (11.5)). 198 (11.5) 199 12 12. Upload utility There are scenarios wherein either orders have been issued in continuation of PPO or corrigendum have been issued. It is important that these orders – being critical in nature- are not just sent to the pensioner but are also uploaded to the pensioners’ dashboard. For this purpose, upload utility has been created. Some of the scenarios in which upload utility shall be used: 1. Issue of corrigendum of PPO for inclusion of permanently disabled children by PPO. 2. Sanction and payment of arrears of pension in cases where manual calculation has been done In such cases AO(pension)/AO(PDA) shall upload the documents which shall be visible on the pensioners’ dashboard. (12.1) After entering the PPO number and clicking on the “Search” button (12.1)), the below stated screen will get displayed (12.2)). The file to be sent to Pensioner’s Dashboard shall be selected and then uploaded. The description of file shall be added (12.3)). It may be noted that multiple files may be sent to pensioner’s Dashboard. Files can be sent by clicking the Submit button (12.3)). 200 (12.2) (12.3) The AO(pension)/AO(PDA) can view the history or date wise records for the information being shared with the pensioner on its dashboard Fig(12.4). 201 (12.4) Similarly, the pensioner can also view the information shared by the Department on his/her dashboard by clicking on the tab “Shared Documents” (12.5), Fig(12.6)). (12.5) 202 (12.6) 13 13. Bill / Payment slip generation For reconciliation sanction sent from PDA section with the bills appearing on PFMS and to ensure smooth processing, PDF for the bills sent to PFMS for payment can be generated. For bills sent on a day, a pdf can be generated. The date selected for generation of PDF is the date on which DH created the bills. The option of Bill Type and Status Type is to be selected as per the below stated criteria (13.1), Fig(13.2), Fig(13.3))according to user needs:- 203 (13.1) (13.2) (13.3) A PDF of the bills generated in the particular time frame will be displayed/downloaded on your desired location which can be sent to cash as sanction. The PDF (13.4)) can be generated for the files sent on a day and handed over to Cash for DSC. 204 (13.4) 14 14. Updation of Mobile, Email and address For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (14.1). 205 (14.1) A Pop-up window (14.2)) will be displayed with the option to choose the following details which the user wants to edit or change: - (14.2) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Retiree Profile Authentication", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "How to fill retiree details in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   In order to ensure that the data filled on retiree profile is correct and has been authenticated by AO Pension (in case of side channel) / HOO user (in case of BSNL), retiree profile upload utility has been developed. On service book verification page (11.1)) DH user shall view the retiree profile and if satisfied then a printout of retiree profile shall be taken (11.2)). Fig(11.1) 196 (11.2) AO Pension (in case of side channel) / HOO user (in case of BSNL) will match the retiree details against physical records and the scanned copy is to be uploaded. Before signing, the retiree profile details shall be matched against physical records and in case of error necessary corrections be made. The scanned copy shall be uploaded (11.3)) only after it has been checked as mentioned above. 197 (11.3) The uploaded verification form can be viewed by clicking eye button (11.3), Fig(11.4)). (11.4) The form may then be sent to HOO(SSA Unit) for approval (11.5)). 198 (11.5) 199 12 12. Upload utility There are scenarios wherein either orders have been issued in continuation of PPO or corrigendum have been issued. It is important that these orders – being critical in nature- are not just sent to the pensioner but are also uploaded to the pensioners’ dashboard. For this purpose, upload utility has been created. Some of the scenarios in which upload utility shall be used: 1. Issue of corrigendum of PPO for inclusion of permanently disabled children by PPO. 2. Sanction and payment of arrears of pension in cases where manual calculation has been done In such cases AO(pension)/AO(PDA) shall upload the documents which shall be visible on the pensioners’ dashboard. (12.1) After entering the PPO number and clicking on the “Search” button (12.1)), the below stated screen will get displayed (12.2)). The file to be sent to Pensioner’s Dashboard shall be selected and then uploaded. The description of file shall be added (12.3)). It may be noted that multiple files may be sent to pensioner’s Dashboard. Files can be sent by clicking the Submit button (12.3)). 200 (12.2) (12.3) The AO(pension)/AO(PDA) can view the history or date wise records for the information being shared with the pensioner on its dashboard Fig(12.4). 201 (12.4) Similarly, the pensioner can also view the information shared by the Department on his/her dashboard by clicking on the tab “Shared Documents” (12.5), Fig(12.6)). (12.5) 202 (12.6) 13 13. Bill / Payment slip generation For reconciliation sanction sent from PDA section with the bills appearing on PFMS and to ensure smooth processing, PDF for the bills sent to PFMS for payment can be generated. For bills sent on a day, a pdf can be generated. The date selected for generation of PDF is the date on which DH created the bills. The option of Bill Type and Status Type is to be selected as per the below stated criteria (13.1), Fig(13.2), Fig(13.3))according to user needs:- 203 (13.1) (13.2) (13.3) A PDF of the bills generated in the particular time frame will be displayed/downloaded on your desired location which can be sent to cash as sanction. The PDF (13.4)) can be generated for the files sent on a day and handed over to Cash for DSC. 204 (13.4) 14 14. Updation of Mobile, Email and address For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (14.1). 205 (14.1) A Pop-up window (14.2)) will be displayed with the option to choose the following details which the user wants to edit or change: - (14.2) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Retiree Profile Authentication", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to fill retiree details in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   In order to ensure that the data filled on retiree profile is correct and has been authenticated by AO Pension (in case of side channel) / HOO user (in case of BSNL), retiree profile upload utility has been developed. On service book verification page (11.1)) DH user shall view the retiree profile and if satisfied then a printout of retiree profile shall be taken (11.2)). Fig(11.1) 196 (11.2) AO Pension (in case of side channel) / HOO user (in case of BSNL) will match the retiree details against physical records and the scanned copy is to be uploaded. Before signing, the retiree profile details shall be matched against physical records and in case of error necessary corrections be made. The scanned copy shall be uploaded (11.3)) only after it has been checked as mentioned above. 197 (11.3) The uploaded verification form can be viewed by clicking eye button (11.3), Fig(11.4)). (11.4) The form may then be sent to HOO(SSA Unit) for approval (11.5)). 198 (11.5) 199 12 12. Upload utility There are scenarios wherein either orders have been issued in continuation of PPO or corrigendum have been issued. It is important that these orders – being critical in nature- are not just sent to the pensioner but are also uploaded to the pensioners’ dashboard. For this purpose, upload utility has been created. Some of the scenarios in which upload utility shall be used: 1. Issue of corrigendum of PPO for inclusion of permanently disabled children by PPO. 2. Sanction and payment of arrears of pension in cases where manual calculation has been done In such cases AO(pension)/AO(PDA) shall upload the documents which shall be visible on the pensioners’ dashboard. (12.1) After entering the PPO number and clicking on the “Search” button (12.1)), the below stated screen will get displayed (12.2)). The file to be sent to Pensioner’s Dashboard shall be selected and then uploaded. The description of file shall be added (12.3)). It may be noted that multiple files may be sent to pensioner’s Dashboard. Files can be sent by clicking the Submit button (12.3)). 200 (12.2) (12.3) The AO(pension)/AO(PDA) can view the history or date wise records for the information being shared with the pensioner on its dashboard Fig(12.4). 201 (12.4) Similarly, the pensioner can also view the information shared by the Department on his/her dashboard by clicking on the tab “Shared Documents” (12.5), Fig(12.6)). (12.5) 202 (12.6) 13 13. Bill / Payment slip generation For reconciliation sanction sent from PDA section with the bills appearing on PFMS and to ensure smooth processing, PDF for the bills sent to PFMS for payment can be generated. For bills sent on a day, a pdf can be generated. The date selected for generation of PDF is the date on which DH created the bills. The option of Bill Type and Status Type is to be selected as per the below stated criteria (13.1), Fig(13.2), Fig(13.3))according to user needs:- 203 (13.1) (13.2) (13.3) A PDF of the bills generated in the particular time frame will be displayed/downloaded on your desired location which can be sent to cash as sanction. The PDF (13.4)) can be generated for the files sent on a day and handed over to Cash for DSC. 204 (13.4) 14 14. Updation of Mobile, Email and address For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (14.1). 205 (14.1) A Pop-up window (14.2)) will be displayed with the option to choose the following details which the user wants to edit or change: - (14.2) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Retiree Profile Authentication", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "What information is required for retiree profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   In order to ensure that the data filled on retiree profile is correct and has been authenticated by AO Pension (in case of side channel) / HOO user (in case of BSNL), retiree profile upload utility has been developed. On service book verification page (11.1)) DH user shall view the retiree profile and if satisfied then a printout of retiree profile shall be taken (11.2)). Fig(11.1) 196 (11.2) AO Pension (in case of side channel) / HOO user (in case of BSNL) will match the retiree details against physical records and the scanned copy is to be uploaded. Before signing, the retiree profile details shall be matched against physical records and in case of error necessary corrections be made. The scanned copy shall be uploaded (11.3)) only after it has been checked as mentioned above. 197 (11.3) The uploaded verification form can be viewed by clicking eye button (11.3), Fig(11.4)). (11.4) The form may then be sent to HOO(SSA Unit) for approval (11.5)). 198 (11.5) 199 12 12. Upload utility There are scenarios wherein either orders have been issued in continuation of PPO or corrigendum have been issued. It is important that these orders – being critical in nature- are not just sent to the pensioner but are also uploaded to the pensioners’ dashboard. For this purpose, upload utility has been created. Some of the scenarios in which upload utility shall be used: 1. Issue of corrigendum of PPO for inclusion of permanently disabled children by PPO. 2. Sanction and payment of arrears of pension in cases where manual calculation has been done In such cases AO(pension)/AO(PDA) shall upload the documents which shall be visible on the pensioners’ dashboard. (12.1) After entering the PPO number and clicking on the “Search” button (12.1)), the below stated screen will get displayed (12.2)). The file to be sent to Pensioner’s Dashboard shall be selected and then uploaded. The description of file shall be added (12.3)). It may be noted that multiple files may be sent to pensioner’s Dashboard. Files can be sent by clicking the Submit button (12.3)). 200 (12.2) (12.3) The AO(pension)/AO(PDA) can view the history or date wise records for the information being shared with the pensioner on its dashboard Fig(12.4). 201 (12.4) Similarly, the pensioner can also view the information shared by the Department on his/her dashboard by clicking on the tab “Shared Documents” (12.5), Fig(12.6)). (12.5) 202 (12.6) 13 13. Bill / Payment slip generation For reconciliation sanction sent from PDA section with the bills appearing on PFMS and to ensure smooth processing, PDF for the bills sent to PFMS for payment can be generated. For bills sent on a day, a pdf can be generated. The date selected for generation of PDF is the date on which DH created the bills. The option of Bill Type and Status Type is to be selected as per the below stated criteria (13.1), Fig(13.2), Fig(13.3))according to user needs:- 203 (13.1) (13.2) (13.3) A PDF of the bills generated in the particular time frame will be displayed/downloaded on your desired location which can be sent to cash as sanction. The PDF (13.4)) can be generated for the files sent on a day and handed over to Cash for DSC. 204 (13.4) 14 14. Updation of Mobile, Email and address For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (14.1). 205 (14.1) A Pop-up window (14.2)) will be displayed with the option to choose the following details which the user wants to edit or change: - (14.2) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Retiree Profile Authentication", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to add new retiree in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   In order to ensure that the data filled on retiree profile is correct and has been authenticated by AO Pension (in case of side channel) / HOO user (in case of BSNL), retiree profile upload utility has been developed. On service book verification page (11.1)) DH user shall view the retiree profile and if satisfied then a printout of retiree profile shall be taken (11.2)). Fig(11.1) 196 (11.2) AO Pension (in case of side channel) / HOO user (in case of BSNL) will match the retiree details against physical records and the scanned copy is to be uploaded. Before signing, the retiree profile details shall be matched against physical records and in case of error necessary corrections be made. The scanned copy shall be uploaded (11.3)) only after it has been checked as mentioned above. 197 (11.3) The uploaded verification form can be viewed by clicking eye button (11.3), Fig(11.4)). (11.4) The form may then be sent to HOO(SSA Unit) for approval (11.5)). 198 (11.5) 199 12 12. Upload utility There are scenarios wherein either orders have been issued in continuation of PPO or corrigendum have been issued. It is important that these orders – being critical in nature- are not just sent to the pensioner but are also uploaded to the pensioners’ dashboard. For this purpose, upload utility has been created. Some of the scenarios in which upload utility shall be used: 1. Issue of corrigendum of PPO for inclusion of permanently disabled children by PPO. 2. Sanction and payment of arrears of pension in cases where manual calculation has been done In such cases AO(pension)/AO(PDA) shall upload the documents which shall be visible on the pensioners’ dashboard. (12.1) After entering the PPO number and clicking on the “Search” button (12.1)), the below stated screen will get displayed (12.2)). The file to be sent to Pensioner’s Dashboard shall be selected and then uploaded. The description of file shall be added (12.3)). It may be noted that multiple files may be sent to pensioner’s Dashboard. Files can be sent by clicking the Submit button (12.3)). 200 (12.2) (12.3) The AO(pension)/AO(PDA) can view the history or date wise records for the information being shared with the pensioner on its dashboard Fig(12.4). 201 (12.4) Similarly, the pensioner can also view the information shared by the Department on his/her dashboard by clicking on the tab “Shared Documents” (12.5), Fig(12.6)). (12.5) 202 (12.6) 13 13. Bill / Payment slip generation For reconciliation sanction sent from PDA section with the bills appearing on PFMS and to ensure smooth processing, PDF for the bills sent to PFMS for payment can be generated. For bills sent on a day, a pdf can be generated. The date selected for generation of PDF is the date on which DH created the bills. The option of Bill Type and Status Type is to be selected as per the below stated criteria (13.1), Fig(13.2), Fig(13.3))according to user needs:- 203 (13.1) (13.2) (13.3) A PDF of the bills generated in the particular time frame will be displayed/downloaded on your desired location which can be sent to cash as sanction. The PDF (13.4)) can be generated for the files sent on a day and handed over to Cash for DSC. 204 (13.4) 14 14. Updation of Mobile, Email and address For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (14.1). 205 (14.1) A Pop-up window (14.2)) will be displayed with the option to choose the following details which the user wants to edit or change: - (14.2) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Retiree Profile Authentication", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to add new retiree in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   In order to ensure that the data filled on retiree profile is correct and has been authenticated by AO Pension (in case of side channel) / HOO user (in case of BSNL), retiree profile upload utility has been developed. On service book verification page (11.1)) DH user shall view the retiree profile and if satisfied then a printout of retiree profile shall be taken (11.2)). Fig(11.1) 196 (11.2) AO Pension (in case of side channel) / HOO user (in case of BSNL) will match the retiree details against physical records and the scanned copy is to be uploaded. Before signing, the retiree profile details shall be matched against physical records and in case of error necessary corrections be made. The scanned copy shall be uploaded (11.3)) only after it has been checked as mentioned above. 197 (11.3) The uploaded verification form can be viewed by clicking eye button (11.3), Fig(11.4)). (11.4) The form may then be sent to HOO(SSA Unit) for approval (11.5)). 198 (11.5) 199 12 12. Upload utility There are scenarios wherein either orders have been issued in continuation of PPO or corrigendum have been issued. It is important that these orders – being critical in nature- are not just sent to the pensioner but are also uploaded to the pensioners’ dashboard. For this purpose, upload utility has been created. Some of the scenarios in which upload utility shall be used: 1. Issue of corrigendum of PPO for inclusion of permanently disabled children by PPO. 2. Sanction and payment of arrears of pension in cases where manual calculation has been done In such cases AO(pension)/AO(PDA) shall upload the documents which shall be visible on the pensioners’ dashboard. (12.1) After entering the PPO number and clicking on the “Search” button (12.1)), the below stated screen will get displayed (12.2)). The file to be sent to Pensioner’s Dashboard shall be selected and then uploaded. The description of file shall be added (12.3)). It may be noted that multiple files may be sent to pensioner’s Dashboard. Files can be sent by clicking the Submit button (12.3)). 200 (12.2) (12.3) The AO(pension)/AO(PDA) can view the history or date wise records for the information being shared with the pensioner on its dashboard Fig(12.4). 201 (12.4) Similarly, the pensioner can also view the information shared by the Department on his/her dashboard by clicking on the tab “Shared Documents” (12.5), Fig(12.6)). (12.5) 202 (12.6) 13 13. Bill / Payment slip generation For reconciliation sanction sent from PDA section with the bills appearing on PFMS and to ensure smooth processing, PDF for the bills sent to PFMS for payment can be generated. For bills sent on a day, a pdf can be generated. The date selected for generation of PDF is the date on which DH created the bills. The option of Bill Type and Status Type is to be selected as per the below stated criteria (13.1), Fig(13.2), Fig(13.3))according to user needs:- 203 (13.1) (13.2) (13.3) A PDF of the bills generated in the particular time frame will be displayed/downloaded on your desired location which can be sent to cash as sanction. The PDF (13.4)) can be generated for the files sent on a day and handed over to Cash for DSC. 204 (13.4) 14 14. Updation of Mobile, Email and address For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (14.1). 205 (14.1) A Pop-up window (14.2)) will be displayed with the option to choose the following details which the user wants to edit or change: - (14.2) The user has to select the option which he/she wants to change or modify.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Retiree Profile Authentication", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "How to update mobile number in CPMS?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (14.3)) 206 Fig(14.3) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((14.3)). Upon receiving the OTP, retiree should enter the OTP ((14.4)) and save which will then update the mobile number. (14.4)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Mobile Number Update", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to update mobile number in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (14.3)) 206 Fig(14.3) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((14.3)). Upon receiving the OTP, retiree should enter the OTP ((14.4)) and save which will then update the mobile number. (14.4)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Mobile Number Update", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to change email address?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (14.3)) 206 Fig(14.3) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((14.3)). Upon receiving the OTP, retiree should enter the OTP ((14.4)) and save which will then update the mobile number. (14.4)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Mobile Number Update", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to change email address?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (14.3)) 206 Fig(14.3) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((14.3)). Upon receiving the OTP, retiree should enter the OTP ((14.4)) and save which will then update the mobile number. (14.4)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Mobile Number Update", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "What is the procedure to update address?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (14.3)) 206 Fig(14.3) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((14.3)). Upon receiving the OTP, retiree should enter the OTP ((14.4)) and save which will then update the mobile number. (14.4)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Mobile Number Update", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "What is the procedure to update address?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (14.3)) 206 Fig(14.3) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((14.3)). Upon receiving the OTP, retiree should enter the OTP ((14.4)) and save which will then update the mobile number. (14.4)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Mobile Number Update", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to modify pensioner details?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (14.3)) 206 Fig(14.3) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((14.3)). Upon receiving the OTP, retiree should enter the OTP ((14.4)) and save which will then update the mobile number. (14.4)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Mobile Number Update", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to modify pensioner details?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (14.3)) 206 Fig(14.3) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((14.3)). Upon receiving the OTP, retiree should enter the OTP ((14.4)) and save which will then update the mobile number. (14.4)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Mobile Number Update", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to update contact information?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (14.3)) 206 Fig(14.3) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((14.3)). Upon receiving the OTP, retiree should enter the OTP ((14.4)) and save which will then update the mobile number. (14.4)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Mobile Number Update", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to update contact information?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Mobile Number’, the following screen will be displayed (14.3)) 206 Fig(14.3) Retiree can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((14.3)). Upon receiving the OTP, retiree should enter the OTP ((14.4)) and save which will then update the mobile number. (14.4)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Mobile Number Update", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to update mobile number in CPMS?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (14.5)) 207 (14.5) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((14.5)). Retiree should then enter the OTP received and click on save ((14.6)) which would then update the email ID. (14.6)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Email ID Update", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to update mobile number in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (14.5)) 207 (14.5) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((14.5)). Retiree should then enter the OTP received and click on save ((14.6)) which would then update the email ID. (14.6)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Email ID Update", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to change email address?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (14.5)) 207 (14.5) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((14.5)). Retiree should then enter the OTP received and click on save ((14.6)) which would then update the email ID. (14.6)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Email ID Update", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to change email address?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (14.5)) 207 (14.5) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((14.5)). Retiree should then enter the OTP received and click on save ((14.6)) which would then update the email ID. (14.6)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Email ID Update", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "What is the procedure to update address?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (14.5)) 207 (14.5) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((14.5)). Retiree should then enter the OTP received and click on save ((14.6)) which would then update the email ID. (14.6)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Email ID Update", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "What is the procedure to update address?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (14.5)) 207 (14.5) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((14.5)). Retiree should then enter the OTP received and click on save ((14.6)) which would then update the email ID. (14.6)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Email ID Update", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to modify pensioner details?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (14.5)) 207 (14.5) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((14.5)). Retiree should then enter the OTP received and click on save ((14.6)) which would then update the email ID. (14.6)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Email ID Update", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to modify pensioner details?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (14.5)) 207 (14.5) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((14.5)). Retiree should then enter the OTP received and click on save ((14.6)) which would then update the email ID. (14.6)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Email ID Update", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to update contact information?", "input": "", "output": "To update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (14.5)) 207 (14.5) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((14.5)). Retiree should then enter the OTP received and click on save ((14.6)) which would then update the email ID. (14.6)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Email ID Update", "procedure_type": "PROFILE_UPDATE"}
{"instruction": "How to update contact information?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo update profile information in CPMS:\n\n   Upon selecting ‘Email ID’, the following screen will be displayed (14.5)) 207 (14.5) Retiree can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((14.5)). Retiree should then enter the OTP received and click on save ((14.6)) which would then update the email ID. (14.6)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Email ID Update", "procedure_type": "PROFILE_UPDATE", "enhanced": true}
{"instruction": "How to create retiree profile in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((14.7)). 208 (14.7) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. For more details on Grievance Management please refer 6. 15 15. Other pension Types The pension for VRS/superannuation pension/Pro-rata/compensation pension shall be as per above user manual. However, following changes have to be kept in mind while processing the pension under Compulsory retirement/Invalid pension/Compassionate allowance. For these three categories of retirement, the application of applicant is eligible for commutation only after medical examination. So, the date on which the medical authority signs the medical report in Part III of Form 4 shall be required to be entered in retiree profile as in 15.1. 209 (15.1) Based on the date entered, the commutation will get calculated as per CCS (Commutation of Pension) Rules, 1981 and get displayed in pension papers. This shall be the only change in case of Invalid pension. Additionally, form 7 shall be provided with additional input fields in case of other two retirement types detailed as under.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Address Update", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to create retiree profile in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((14.7)). 208 (14.7) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. For more details on Grievance Management please refer 6. 15 15. Other pension Types The pension for VRS/superannuation pension/Pro-rata/compensation pension shall be as per above user manual. However, following changes have to be kept in mind while processing the pension under Compulsory retirement/Invalid pension/Compassionate allowance. For these three categories of retirement, the application of applicant is eligible for commutation only after medical examination. So, the date on which the medical authority signs the medical report in Part III of Form 4 shall be required to be entered in retiree profile as in 15.1. 209 (15.1) Based on the date entered, the commutation will get calculated as per CCS (Commutation of Pension) Rules, 1981 and get displayed in pension papers. This shall be the only change in case of Invalid pension. Additionally, form 7 shall be provided with additional input fields in case of other two retirement types detailed as under.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Address Update", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "What are the steps to create pensioner profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((14.7)). 208 (14.7) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. For more details on Grievance Management please refer 6. 15 15. Other pension Types The pension for VRS/superannuation pension/Pro-rata/compensation pension shall be as per above user manual. However, following changes have to be kept in mind while processing the pension under Compulsory retirement/Invalid pension/Compassionate allowance. For these three categories of retirement, the application of applicant is eligible for commutation only after medical examination. So, the date on which the medical authority signs the medical report in Part III of Form 4 shall be required to be entered in retiree profile as in 15.1. 209 (15.1) Based on the date entered, the commutation will get calculated as per CCS (Commutation of Pension) Rules, 1981 and get displayed in pension papers. This shall be the only change in case of Invalid pension. Additionally, form 7 shall be provided with additional input fields in case of other two retirement types detailed as under.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Address Update", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "What are the steps to create pensioner profile?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((14.7)). 208 (14.7) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. For more details on Grievance Management please refer 6. 15 15. Other pension Types The pension for VRS/superannuation pension/Pro-rata/compensation pension shall be as per above user manual. However, following changes have to be kept in mind while processing the pension under Compulsory retirement/Invalid pension/Compassionate allowance. For these three categories of retirement, the application of applicant is eligible for commutation only after medical examination. So, the date on which the medical authority signs the medical report in Part III of Form 4 shall be required to be entered in retiree profile as in 15.1. 209 (15.1) Based on the date entered, the commutation will get calculated as per CCS (Commutation of Pension) Rules, 1981 and get displayed in pension papers. This shall be the only change in case of Invalid pension. Additionally, form 7 shall be provided with additional input fields in case of other two retirement types detailed as under.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Address Update", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "How to fill retiree details in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((14.7)). 208 (14.7) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. For more details on Grievance Management please refer 6. 15 15. Other pension Types The pension for VRS/superannuation pension/Pro-rata/compensation pension shall be as per above user manual. However, following changes have to be kept in mind while processing the pension under Compulsory retirement/Invalid pension/Compassionate allowance. For these three categories of retirement, the application of applicant is eligible for commutation only after medical examination. So, the date on which the medical authority signs the medical report in Part III of Form 4 shall be required to be entered in retiree profile as in 15.1. 209 (15.1) Based on the date entered, the commutation will get calculated as per CCS (Commutation of Pension) Rules, 1981 and get displayed in pension papers. This shall be the only change in case of Invalid pension. Additionally, form 7 shall be provided with additional input fields in case of other two retirement types detailed as under.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Address Update", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to fill retiree details in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((14.7)). 208 (14.7) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. For more details on Grievance Management please refer 6. 15 15. Other pension Types The pension for VRS/superannuation pension/Pro-rata/compensation pension shall be as per above user manual. However, following changes have to be kept in mind while processing the pension under Compulsory retirement/Invalid pension/Compassionate allowance. For these three categories of retirement, the application of applicant is eligible for commutation only after medical examination. So, the date on which the medical authority signs the medical report in Part III of Form 4 shall be required to be entered in retiree profile as in 15.1. 209 (15.1) Based on the date entered, the commutation will get calculated as per CCS (Commutation of Pension) Rules, 1981 and get displayed in pension papers. This shall be the only change in case of Invalid pension. Additionally, form 7 shall be provided with additional input fields in case of other two retirement types detailed as under.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Address Update", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "What information is required for retiree profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((14.7)). 208 (14.7) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. For more details on Grievance Management please refer 6. 15 15. Other pension Types The pension for VRS/superannuation pension/Pro-rata/compensation pension shall be as per above user manual. However, following changes have to be kept in mind while processing the pension under Compulsory retirement/Invalid pension/Compassionate allowance. For these three categories of retirement, the application of applicant is eligible for commutation only after medical examination. So, the date on which the medical authority signs the medical report in Part III of Form 4 shall be required to be entered in retiree profile as in 15.1. 209 (15.1) Based on the date entered, the commutation will get calculated as per CCS (Commutation of Pension) Rules, 1981 and get displayed in pension papers. This shall be the only change in case of Invalid pension. Additionally, form 7 shall be provided with additional input fields in case of other two retirement types detailed as under.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Address Update", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to add new retiree in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((14.7)). 208 (14.7) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. For more details on Grievance Management please refer 6. 15 15. Other pension Types The pension for VRS/superannuation pension/Pro-rata/compensation pension shall be as per above user manual. However, following changes have to be kept in mind while processing the pension under Compulsory retirement/Invalid pension/Compassionate allowance. For these three categories of retirement, the application of applicant is eligible for commutation only after medical examination. So, the date on which the medical authority signs the medical report in Part III of Form 4 shall be required to be entered in retiree profile as in 15.1. 209 (15.1) Based on the date entered, the commutation will get calculated as per CCS (Commutation of Pension) Rules, 1981 and get displayed in pension papers. This shall be the only change in case of Invalid pension. Additionally, form 7 shall be provided with additional input fields in case of other two retirement types detailed as under.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Address Update", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to add new retiree in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((14.7)). 208 (14.7) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. For more details on Grievance Management please refer 6. 15 15. Other pension Types The pension for VRS/superannuation pension/Pro-rata/compensation pension shall be as per above user manual. However, following changes have to be kept in mind while processing the pension under Compulsory retirement/Invalid pension/Compassionate allowance. For these three categories of retirement, the application of applicant is eligible for commutation only after medical examination. So, the date on which the medical authority signs the medical report in Part III of Form 4 shall be required to be entered in retiree profile as in 15.1. 209 (15.1) Based on the date entered, the commutation will get calculated as per CCS (Commutation of Pension) Rules, 1981 and get displayed in pension papers. This shall be the only change in case of Invalid pension. Additionally, form 7 shall be provided with additional input fields in case of other two retirement types detailed as under.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Address Update", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "How to create retiree profile in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   In form 7 in case of compulsory retirement, at the time of creating the retiree profile, the Date of Medical Report is to be provided Fig(15.1.1). 210 (15.1.1) After processing the case just like for the Normal Pension case, when the user reached at the Form 7 level, careful assessment is required at the time of entering the Percentage (%) at which the pension and DCRG is to be reduced Fig(15.1.2). (15.1.2) The impact of which can be seen at the Form 7 which the system calculates automatically (15.1.3), Fig(15.1.4), Fig(15.1.5)). 211 (15.1.3) (15.1.4) 212 (15.1.5)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Compulsory retirement", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to create retiree profile in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   In form 7 in case of compulsory retirement, at the time of creating the retiree profile, the Date of Medical Report is to be provided Fig(15.1.1). 210 (15.1.1) After processing the case just like for the Normal Pension case, when the user reached at the Form 7 level, careful assessment is required at the time of entering the Percentage (%) at which the pension and DCRG is to be reduced Fig(15.1.2). (15.1.2) The impact of which can be seen at the Form 7 which the system calculates automatically (15.1.3), Fig(15.1.4), Fig(15.1.5)). 211 (15.1.3) (15.1.4) 212 (15.1.5)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Compulsory retirement", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "What are the steps to create pensioner profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   In form 7 in case of compulsory retirement, at the time of creating the retiree profile, the Date of Medical Report is to be provided Fig(15.1.1). 210 (15.1.1) After processing the case just like for the Normal Pension case, when the user reached at the Form 7 level, careful assessment is required at the time of entering the Percentage (%) at which the pension and DCRG is to be reduced Fig(15.1.2). (15.1.2) The impact of which can be seen at the Form 7 which the system calculates automatically (15.1.3), Fig(15.1.4), Fig(15.1.5)). 211 (15.1.3) (15.1.4) 212 (15.1.5)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Compulsory retirement", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "What are the steps to create pensioner profile?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   In form 7 in case of compulsory retirement, at the time of creating the retiree profile, the Date of Medical Report is to be provided Fig(15.1.1). 210 (15.1.1) After processing the case just like for the Normal Pension case, when the user reached at the Form 7 level, careful assessment is required at the time of entering the Percentage (%) at which the pension and DCRG is to be reduced Fig(15.1.2). (15.1.2) The impact of which can be seen at the Form 7 which the system calculates automatically (15.1.3), Fig(15.1.4), Fig(15.1.5)). 211 (15.1.3) (15.1.4) 212 (15.1.5)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Compulsory retirement", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "How to fill retiree details in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   In form 7 in case of compulsory retirement, at the time of creating the retiree profile, the Date of Medical Report is to be provided Fig(15.1.1). 210 (15.1.1) After processing the case just like for the Normal Pension case, when the user reached at the Form 7 level, careful assessment is required at the time of entering the Percentage (%) at which the pension and DCRG is to be reduced Fig(15.1.2). (15.1.2) The impact of which can be seen at the Form 7 which the system calculates automatically (15.1.3), Fig(15.1.4), Fig(15.1.5)). 211 (15.1.3) (15.1.4) 212 (15.1.5)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Compulsory retirement", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to fill retiree details in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   In form 7 in case of compulsory retirement, at the time of creating the retiree profile, the Date of Medical Report is to be provided Fig(15.1.1). 210 (15.1.1) After processing the case just like for the Normal Pension case, when the user reached at the Form 7 level, careful assessment is required at the time of entering the Percentage (%) at which the pension and DCRG is to be reduced Fig(15.1.2). (15.1.2) The impact of which can be seen at the Form 7 which the system calculates automatically (15.1.3), Fig(15.1.4), Fig(15.1.5)). 211 (15.1.3) (15.1.4) 212 (15.1.5)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Compulsory retirement", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "What information is required for retiree profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   In form 7 in case of compulsory retirement, at the time of creating the retiree profile, the Date of Medical Report is to be provided Fig(15.1.1). 210 (15.1.1) After processing the case just like for the Normal Pension case, when the user reached at the Form 7 level, careful assessment is required at the time of entering the Percentage (%) at which the pension and DCRG is to be reduced Fig(15.1.2). (15.1.2) The impact of which can be seen at the Form 7 which the system calculates automatically (15.1.3), Fig(15.1.4), Fig(15.1.5)). 211 (15.1.3) (15.1.4) 212 (15.1.5)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Compulsory retirement", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to add new retiree in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   In form 7 in case of compulsory retirement, at the time of creating the retiree profile, the Date of Medical Report is to be provided Fig(15.1.1). 210 (15.1.1) After processing the case just like for the Normal Pension case, when the user reached at the Form 7 level, careful assessment is required at the time of entering the Percentage (%) at which the pension and DCRG is to be reduced Fig(15.1.2). (15.1.2) The impact of which can be seen at the Form 7 which the system calculates automatically (15.1.3), Fig(15.1.4), Fig(15.1.5)). 211 (15.1.3) (15.1.4) 212 (15.1.5)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Compulsory retirement", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to add new retiree in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   In form 7 in case of compulsory retirement, at the time of creating the retiree profile, the Date of Medical Report is to be provided Fig(15.1.1). 210 (15.1.1) After processing the case just like for the Normal Pension case, when the user reached at the Form 7 level, careful assessment is required at the time of entering the Percentage (%) at which the pension and DCRG is to be reduced Fig(15.1.2). (15.1.2) The impact of which can be seen at the Form 7 which the system calculates automatically (15.1.3), Fig(15.1.4), Fig(15.1.5)). 211 (15.1.3) (15.1.4) 212 (15.1.5)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Compulsory retirement", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "How to create retiree profile in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   The DH at the time of creating the retiree profile will exercise the option of “Removal/Dismissal from service (Rule 24 and 41)” (15.2.1)) and the rest of the process flow will continue just like the same we process for the Normal Pension cases. However, at the time of the filling of Form 7, the DH must ensure to enter the correct and verified amount for the field wherein case of removal/dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41). (15.2.1) 213 The impact of which reflects in the Form 7 (15.2.2), Fig(15.2.3)). (15.2.2) (15.2.3) 214\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Compassionate Allowance", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to create retiree profile in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   The DH at the time of creating the retiree profile will exercise the option of “Removal/Dismissal from service (Rule 24 and 41)” (15.2.1)) and the rest of the process flow will continue just like the same we process for the Normal Pension cases. However, at the time of the filling of Form 7, the DH must ensure to enter the correct and verified amount for the field wherein case of removal/dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41). (15.2.1) 213 The impact of which reflects in the Form 7 (15.2.2), Fig(15.2.3)). (15.2.2) (15.2.3) 214\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Compassionate Allowance", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "What are the steps to create pensioner profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   The DH at the time of creating the retiree profile will exercise the option of “Removal/Dismissal from service (Rule 24 and 41)” (15.2.1)) and the rest of the process flow will continue just like the same we process for the Normal Pension cases. However, at the time of the filling of Form 7, the DH must ensure to enter the correct and verified amount for the field wherein case of removal/dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41). (15.2.1) 213 The impact of which reflects in the Form 7 (15.2.2), Fig(15.2.3)). (15.2.2) (15.2.3) 214\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Compassionate Allowance", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "What are the steps to create pensioner profile?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   The DH at the time of creating the retiree profile will exercise the option of “Removal/Dismissal from service (Rule 24 and 41)” (15.2.1)) and the rest of the process flow will continue just like the same we process for the Normal Pension cases. However, at the time of the filling of Form 7, the DH must ensure to enter the correct and verified amount for the field wherein case of removal/dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41). (15.2.1) 213 The impact of which reflects in the Form 7 (15.2.2), Fig(15.2.3)). (15.2.2) (15.2.3) 214\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Compassionate Allowance", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "How to fill retiree details in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   The DH at the time of creating the retiree profile will exercise the option of “Removal/Dismissal from service (Rule 24 and 41)” (15.2.1)) and the rest of the process flow will continue just like the same we process for the Normal Pension cases. However, at the time of the filling of Form 7, the DH must ensure to enter the correct and verified amount for the field wherein case of removal/dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41). (15.2.1) 213 The impact of which reflects in the Form 7 (15.2.2), Fig(15.2.3)). (15.2.2) (15.2.3) 214\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Compassionate Allowance", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to fill retiree details in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   The DH at the time of creating the retiree profile will exercise the option of “Removal/Dismissal from service (Rule 24 and 41)” (15.2.1)) and the rest of the process flow will continue just like the same we process for the Normal Pension cases. However, at the time of the filling of Form 7, the DH must ensure to enter the correct and verified amount for the field wherein case of removal/dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41). (15.2.1) 213 The impact of which reflects in the Form 7 (15.2.2), Fig(15.2.3)). (15.2.2) (15.2.3) 214\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Compassionate Allowance", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
{"instruction": "What information is required for retiree profile?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   The DH at the time of creating the retiree profile will exercise the option of “Removal/Dismissal from service (Rule 24 and 41)” (15.2.1)) and the rest of the process flow will continue just like the same we process for the Normal Pension cases. However, at the time of the filling of Form 7, the DH must ensure to enter the correct and verified amount for the field wherein case of removal/dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41). (15.2.1) 213 The impact of which reflects in the Form 7 (15.2.2), Fig(15.2.3)). (15.2.2) (15.2.3) 214\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Compassionate Allowance", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to add new retiree in CPMS?", "input": "", "output": "To create a retiree profile in CPMS:\n\n   The DH at the time of creating the retiree profile will exercise the option of “Removal/Dismissal from service (Rule 24 and 41)” (15.2.1)) and the rest of the process flow will continue just like the same we process for the Normal Pension cases. However, at the time of the filling of Form 7, the DH must ensure to enter the correct and verified amount for the field wherein case of removal/dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41). (15.2.1) 213 The impact of which reflects in the Form 7 (15.2.2), Fig(15.2.3)). (15.2.2) (15.2.3) 214\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Compassionate Allowance", "procedure_type": "RETIREE_PROFILE"}
{"instruction": "How to add new retiree in CPMS?", "input": "", "output": "Here are the step-by-step instructions:\n\nTo create a retiree profile in CPMS:\n\n   The DH at the time of creating the retiree profile will exercise the option of “Removal/Dismissal from service (Rule 24 and 41)” (15.2.1)) and the rest of the process flow will continue just like the same we process for the Normal Pension cases. However, at the time of the filling of Form 7, the DH must ensure to enter the correct and verified amount for the field wherein case of removal/dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41). (15.2.1) 213 The impact of which reflects in the Form 7 (15.2.2), Fig(15.2.3)). (15.2.2) (15.2.3) 214\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Compassionate Allowance", "procedure_type": "RETIREE_PROFILE", "enhanced": true}
