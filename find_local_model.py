#!/usr/bin/env python3
"""
Find and configure local GGUF model for Ubuntu
"""
import os
import sys
from pathlib import Path

def find_gguf_models(search_paths):
    """Find GGUF model files in common locations"""
    found_models = []
    
    for search_path in search_paths:
        if os.path.exists(search_path):
            print(f"🔍 Searching in: {search_path}")
            for root, dirs, files in os.walk(search_path):
                for file in files:
                    if file.endswith('.gguf'):
                        full_path = os.path.join(root, file)
                        size_mb = os.path.getsize(full_path) / (1024 * 1024)
                        found_models.append({
                            'path': full_path,
                            'name': file,
                            'size_mb': round(size_mb, 1)
                        })
                        print(f"   ✅ Found: {file} ({size_mb:.1f} MB)")
    
    return found_models

def main():
    """Main function to find and configure local model"""
    print("🔍 Searching for local GGUF models...")
    print("=" * 50)
    
    # Common search paths
    current_dir = Path.cwd()
    search_paths = [
        current_dir,
        current_dir / "models",
        Path.home() / "models",
        "/home/<USER>/CPMS_CHATBOT",
        "/home/<USER>/CPMS_CHATBOT/models",
        "/opt/models",
        "/usr/local/models"
    ]
    
    # Add any additional paths from environment
    if "MODEL_PATH" in os.environ:
        search_paths.append(Path(os.environ["MODEL_PATH"]))
    
    print("Search paths:")
    for path in search_paths:
        print(f"  - {path}")
    print()
    
    # Find models
    found_models = find_gguf_models(search_paths)
    
    if not found_models:
        print("❌ No GGUF models found!")
        print("\n💡 Solutions:")
        print("1. Download a GGUF model:")
        print("   wget https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.2-GGUF/resolve/main/mistral-7b-instruct-v0.2.Q4_K_S.gguf")
        print("2. Or use HuggingFace model (will download automatically)")
        return False
    
    print(f"\n✅ Found {len(found_models)} GGUF model(s):")
    print("=" * 50)
    
    for i, model in enumerate(found_models, 1):
        print(f"{i}. {model['name']}")
        print(f"   Path: {model['path']}")
        print(f"   Size: {model['size_mb']} MB")
        print()
    
    # Select the best model (largest, likely most complete)
    best_model = max(found_models, key=lambda x: x['size_mb'])
    
    print(f"🎯 Recommended model: {best_model['name']}")
    print(f"   Path: {best_model['path']}")
    
    # Generate configuration
    config_content = f'''
# Add this to your backend/config/config.py

# Model paths (Ubuntu)
MISTRAL_MODEL_PATH = "{best_model['path']}"

# Or set as environment variable:
# export MISTRAL_MODEL_PATH="{best_model['path']}"
'''
    
    print("\n📝 Configuration:")
    print("=" * 30)
    print(config_content)
    
    # Write to file
    with open("local_model_config.txt", "w") as f:
        f.write(config_content)
    
    print("✅ Configuration saved to: local_model_config.txt")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Search cancelled")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)
