#!/usr/bin/env python3
"""
Ultra-Optimized CPMS Chatbot Launcher
"""
import sys
import os

# Add workspace root to path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..'))

def main():
    print("🚀 Launching Ultra-Optimized CPMS Chatbot...")

    try:
        from frontend.interface.web_interface import main as launch_web
        launch_web()
    except KeyboardInterrupt:
        print("\n👋 Chatbot stopped by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
