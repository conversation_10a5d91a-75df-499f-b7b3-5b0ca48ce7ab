/**
 * Enhanced Chat System with Authentication Integration
 */
class EnhancedChat {
    constructor() {
        this.baseURL = 'http://*************:8001' || 'http://localhost:8001';
        this.isAuthenticated = false;
        this.user = null;
        
        this.initializeElements();
        this.bindEvents();
        this.checkBackendConnection();
    }
    
    initializeElements() {
        this.chatMessages = document.getElementById('chat-messages');
        this.chatInput = document.getElementById('chat-input');
        this.sendBtn = document.getElementById('send-btn');
        this.charCount = document.getElementById('char-count');
        this.connectionStatus = document.getElementById('connection-status');
        this.statusIndicator = document.getElementById('status-indicator');
        this.statusText = document.getElementById('status-text');
        
        // Quick action buttons
        this.quickActions = document.querySelectorAll('.action-btn');
    }
    
    bindEvents() {
        // Send button
        this.sendBtn?.addEventListener('click', () => this.sendMessage());
        
        // Enter key in chat input
        this.chatInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // Input validation and character count
        this.chatInput?.addEventListener('input', () => {
            this.updateCharCount();
            this.validateInput();
        });
        
        // Quick action buttons
        this.quickActions.forEach(btn => {
            btn.addEventListener('click', () => {
                const query = btn.getAttribute('data-query');
                if (query) {
                    this.chatInput.value = query;
                    this.sendMessage();
                }
            });
        });
        
        // Auto-resize textarea
        this.chatInput?.addEventListener('input', () => {
            this.autoResizeTextarea();
        });
    }
    
    async checkBackendConnection() {
        try {
            const response = await fetch(`${this.baseURL}/health`);
            if (response.ok) {
                this.updateConnectionStatus('connected', 'Connected');
            } else {
                this.updateConnectionStatus('error', 'Backend Error');
            }
        } catch (error) {
            this.updateConnectionStatus('disconnected', 'Disconnected');
            console.error('Backend connection failed:', error);
        }
    }
    
    updateConnectionStatus(status, text) {
        if (this.statusIndicator && this.statusText) {
            this.statusIndicator.className = `status-indicator ${status}`;
            this.statusText.textContent = text;
        }
    }
    
    async sendMessage() {
        const message = this.chatInput?.value.trim();
        if (!message) return;
        
        // Add user message to chat
        this.addMessage(message, 'user');
        
        // Clear input and disable send button
        this.chatInput.value = '';
        this.updateCharCount();
        this.validateInput();
        this.autoResizeTextarea();
        
        // Show typing indicator
        const typingId = this.showTypingIndicator();
        
        try {
            // Determine which endpoint to use
            const endpoint = this.isAuthenticated ? '/enhanced-query' : '/query';
            const headers = window.authSystem ? window.authSystem.getAuthHeaders() : {
                'Content-Type': 'application/json'
            };
            
            const response = await fetch(`${this.baseURL}${endpoint}`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    message: message,
                    include_sources: true
                })
            });
            
            const result = await response.json();
            
            // Remove typing indicator
            this.removeTypingIndicator(typingId);
            
            if (response.ok) {
                this.addMessage(result.response, 'bot', {
                    sources: result.sources,
                    processingTime: result.processing_time,
                    modelUsed: result.model_used,
                    cached: result.cached,
                    queryType: result.query_type
                });
            } else {
                this.addMessage(
                    result.detail || 'Sorry, I encountered an error processing your request.',
                    'bot',
                    { isError: true }
                );
            }
        } catch (error) {
            console.error('Chat error:', error);
            this.removeTypingIndicator(typingId);
            this.addMessage(
                'Sorry, I\'m having trouble connecting to the server. Please try again.',
                'bot',
                { isError: true }
            );
        }
    }
    
    addMessage(content, sender, metadata = {}) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        if (metadata.isError) {
            messageDiv.classList.add('error-message');
        }
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = sender === 'user' ? '👤' : '🏛️';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        const messageText = document.createElement('div');
        messageText.className = 'message-text';
        
        // Format message content
        if (sender === 'bot') {
            messageText.innerHTML = this.formatBotMessage(content);
        } else {
            messageText.textContent = content;
        }
        
        messageContent.appendChild(messageText);
        
        // Add metadata for bot messages
        if (sender === 'bot' && !metadata.isError) {
            const metadataDiv = this.createMessageMetadata(metadata);
            if (metadataDiv) {
                messageContent.appendChild(metadataDiv);
            }
        }
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        this.chatMessages?.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    formatBotMessage(content) {
        // Convert markdown-like formatting to HTML
        let formatted = content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
        
        // Format lists
        formatted = formatted.replace(/^• (.+)$/gm, '<li>$1</li>');
        formatted = formatted.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
        
        return formatted;
    }
    
    createMessageMetadata(metadata) {
        if (!metadata.processingTime && !metadata.sources?.length && !metadata.queryType) {
            return null;
        }
        
        const metadataDiv = document.createElement('div');
        metadataDiv.className = 'message-metadata';
        
        let metadataHTML = '<div class="metadata-items">';
        
        if (metadata.queryType) {
            const typeLabel = this.getQueryTypeLabel(metadata.queryType);
            metadataHTML += `<span class="metadata-item query-type">${typeLabel}</span>`;
        }
        
        if (metadata.processingTime) {
            metadataHTML += `<span class="metadata-item">⏱️ ${metadata.processingTime.toFixed(2)}s</span>`;
        }
        
        if (metadata.modelUsed) {
            metadataHTML += `<span class="metadata-item">🤖 ${metadata.modelUsed}</span>`;
        }
        
        if (metadata.cached) {
            metadataHTML += `<span class="metadata-item cached">💾 Cached</span>`;
        }
        
        metadataHTML += '</div>';
        
        // Add sources if available
        if (metadata.sources?.length > 0) {
            metadataHTML += '<div class="sources">';
            metadataHTML += '<strong>Sources:</strong>';
            metadataHTML += '<ul>';
            metadata.sources.forEach(source => {
                metadataHTML += `<li>${source}</li>`;
            });
            metadataHTML += '</ul>';
            metadataHTML += '</div>';
        }
        
        metadataDiv.innerHTML = metadataHTML;
        return metadataDiv;
    }
    
    getQueryTypeLabel(queryType) {
        const labels = {
            'lc_expiry': '📅 LC Expiry',
            'pension_breakup': '💰 Pension Breakup',
            'commutation_restoration': '🔄 Commutation',
            'da_rate': '📊 DA Rate',
            'additional_pension': '➕ Additional Pension',
            'family_pension': '👨‍👩‍👧‍👦 Family Pension',
            'fma': '🏥 FMA',
            'mobile_change': '📱 Mobile Change',
            'sampann_migration': '🔄 Sampann Migration',
            'pensioner_specific': '🔐 Personal Query',
            'general': '💬 General Query'
        };
        
        return labels[queryType] || '💬 Query';
    }
    
    showTypingIndicator() {
        const typingId = Date.now();
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message bot-message typing-message';
        typingDiv.id = `typing-${typingId}`;
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = '🏛️';
        
        const content = document.createElement('div');
        content.className = 'message-content';
        
        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'typing-indicator';
        typingIndicator.innerHTML = '<span></span><span></span><span></span>';
        
        content.appendChild(typingIndicator);
        typingDiv.appendChild(avatar);
        typingDiv.appendChild(content);
        
        this.chatMessages?.appendChild(typingDiv);
        this.scrollToBottom();
        
        return typingId;
    }
    
    removeTypingIndicator(typingId) {
        const typingElement = document.getElementById(`typing-${typingId}`);
        if (typingElement) {
            typingElement.remove();
        }
    }
    
    updateCharCount() {
        if (this.chatInput && this.charCount) {
            const count = this.chatInput.value.length;
            this.charCount.textContent = `${count}/1000`;
            
            if (count > 900) {
                this.charCount.style.color = '#dc2626';
            } else if (count > 800) {
                this.charCount.style.color = '#d97706';
            } else {
                this.charCount.style.color = '#6b7280';
            }
        }
    }
    
    validateInput() {
        if (this.chatInput && this.sendBtn) {
            const hasText = this.chatInput.value.trim().length > 0;
            const withinLimit = this.chatInput.value.length <= 1000;
            this.sendBtn.disabled = !hasText || !withinLimit;
        }
    }
    
    autoResizeTextarea() {
        if (this.chatInput) {
            this.chatInput.style.height = 'auto';
            this.chatInput.style.height = Math.min(this.chatInput.scrollHeight, 120) + 'px';
        }
    }
    
    scrollToBottom() {
        if (this.chatMessages) {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }
    }
    
    onAuthenticationChange(isAuthenticated, user) {
        this.isAuthenticated = isAuthenticated;
        this.user = user;
        
        // Add system message about authentication change
        if (isAuthenticated && user) {
            this.addMessage(
                `🔐 You are now logged in as ${user.name}. You can now ask about your personal pension details!`,
                'bot'
            );
        } else {
            this.addMessage(
                '👋 You are now in guest mode. You can ask general CPMS questions, but login is required for personal pension details.',
                'bot'
            );
        }
    }
    
    // Method to handle quick suggestions
    async getQuerySuggestions() {
        try {
            const headers = window.authSystem ? window.authSystem.getAuthHeaders() : {
                'Content-Type': 'application/json'
            };
            
            const response = await fetch(`${this.baseURL}/query-suggestions`, {
                headers: headers
            });
            
            if (response.ok) {
                const result = await response.json();
                return result.suggestions;
            }
        } catch (error) {
            console.error('Failed to get suggestions:', error);
        }
        
        return null;
    }
}

// Export for use in other scripts
window.EnhancedChat = EnhancedChat;
