[{"source": "PDF - ActualDeclaration", "content": "Comprehensive Pension Management System (CPMS) ACTUAL DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 10th October every year) Name of the pensioner : P.P.O No : PAN : Assessment Year : Name of the circle : Mobile No. : Income Amount (Rs.) (i) Income from Previous Other Employer(s) : (ii) Tax Deducted by Previous Other Employer(s) : (iii) Income from Other Sources : (iv) Income from House Property (Income Loss): Total : B. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under the Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank ( 5 years) : NSC interest reinvested : Housing Loan principal repaid : Life Insurance premium paid : ULIP investment approved for tax rebate : 2019 -Comprehensive Pension Management System (CPMS) Comprehensive Pension Management System (CPMS) Other deduction U S 80C : Others : U S 80CCC -- Investment in any approved Pension scheme : NPS under Section 80CCD(1) : Section 80CCD(1B) : Section 80CCD(2) : A. OTHER DEDUCTIONS UNDER CHAPTER VI A Amount of deduction claimed (Rs.) U S 80D Medical Insurance U S 80D Medical insurance for Senior Citizen U S 80DD maintenance of Handicap Dependent U S 80DDB Medical treatment on specified disease for super senior citizen U S 80DDB Medical treatment on specified disease for senior citizen U S 80DDB Medical treatment on specified disease U S 80E Higher education U S 80EE Interest on housing loan Donation U S 80G 100 scheme (Only for donation to PM CM LG Relief Fund) Donation U S 80G 50 scheme U S 80CCG- deduction in r o investment in ESS U S 80GG- deduction in respect of rent U S 80QQB-deduction in respect of royalty income U S 80 TTA Interest on deposit in Savings account 2019 -Comprehensive Pension Management System (CPMS) Comprehensive Pension Management System (CPMS) A. OTHER DEDUCTIONS UNDER CHAPTER VI A Amount of deduction claimed (Rs.) U S 80 TTB Interest on deposit senior citizen U S 80U Self Handicap (severe disability and other disability) Enclosed proof of Investment as declared above Date : Signature of Pensioner : 2019 -Comprehensive Pension Management System (CPMS)", "type": "pdf"}, {"source": "PDF - Bank undertaking", "content": "ANNEXURE 1 Pensioner s letter of Authority and Undertaking To, Date Pr.CCA CCA .. Sir, I hereby opt to draw my Pension through a Bank Account under the direct disbursement of telecom pension by DOT through SAMPANN. I hereby authorize the bank to receive my monthly Pension on my behalf and credit the same to my account as per particulars given as follows: a. Name of the Bank b. Branch c. Account No. d. IFSC Code 2) I hereby undertake that any amount of excess wrong payment of pension, if credited to my Bank Account will be refunded on your instructions. 3) I undertake and agree to bind myself and my heirs, successors, executors and administrators to indemnify the Bank PDA in so crediting my pension to my account under the scheme and to forthwith refund pay any amount due from me to the Bank PDA and also irrevocably authorize the Bank PDA to recover, any amount due from me by debit to my said account or any other accounts deposits belonging to me in possession of the Bank. ---------------------------------------------------- Signature of Pensioner Witnesses :- (1) Signature : (2) Signature : Name : Name : Address : Address : 1. Personal details:- 1. Name of the Pensioner : 2. Designation : 3. Date of retirement : 4. Address of the Pensioner : Family Pensioners only:- 5. Relationship with deceased : 6. Name of the Family Pensioner : 2. Bank Details :- 1. Saving Current Account No. : 2. Name of the Bank : 3. Name of the Branch : 3. (a) Certified that the Bank details (2 above) are correct. The account of pensioner and his her signature given overleaf agrees with the specimen signature held in our records. (b) Any excess amount credited in the account of the pensioner and due refundable to the PDA will be refunded immediately as and when called for by the PDA. Notwithstanding anything contained in this clause 3(b), the Bank and the PDA agree and understand that the obligation cost on the Bank by medium of this clause[3(b)], shall be subject to:- i. The rights conferred and the duties imposed on the Bank by Law and or norm and or regulations. Place : Signature of the Bank Manager Date: (Bank Accounts Seal) Note Part 1 2 to be filled in by the pensioner and Part 3 by Bank. MANDATE FORM 1. Beneficiary Name 2. Beneficiary Address Telephone No. 3. Beneficiary Account No. 4. Account type ( Saving Current for Cash Credit) Nine digit code number of the Bank branch 5. appearing on the MICR Cheque issued by the bank (if available) 6. Bank Name 7. Branch Name Address with Telephone No. 8. IFSC (Indian Financial Services Code) Photo copy of the cancelled cheque to confirm 9. correctness of IFSC code and Account No. given I, hereby declare that the particulars given above are correct and complete. If the transaction is delayed or not effected at all for reasons of incompleteness or incorrectness of information given by me as above, I would not hold the user institution responsible. Dated .. (Signature of Spouse) (Signature of the Beneficiary) Certified that the particulars furnished above are Correct as per the record. Bank Stamp (Signature of the Authorised officer ) Dated", "type": "pdf"}, {"source": "PDF - CPMS-FAQForPensioners", "content": "An initiative under Department of Telecommunications(DoT), and developed by the Controller General of Communication Accounts(CGCA), the Comprehensive Pension Management System or SAMPANN is an integrated Pension application for telecom pensioners. This will provide an integrated approach with online facilities to its pensioners to make pension management more convenient. 1. What are the contact points available to the Pensioner? Reply: The following contact points will be available to the pensioners in case he she faces any trouble or has any grievance. - All India Toll-Free Helpline no. - 1800-113-5800 (Timings 9:30 AM to 6:00 PM except on weekends gazetted holidays) Centralized helpdesk dedicated Mail Id sampann.cpms-dot gov.in Sampann Mobile app Online grievance redressal facility through the pensioners account in dotpension.gov.in. CCA offices or Pensioners Lounge Facility at all the Pr. CCAs CCAs offices. Pensioners Service Centre in BSNL units where available. CSCs banks for Life certification only 2. How can a pensioner submit the Life Certificate Digital Life Certificate? Reply: Pensioners can continue to submit Digital Life Certificates at the Banks Post Offices who have Jeevan Pramaan Facility. In addition, the Pensioners Service Centre at SSAs, Pensioners Lounge at CCA Offices, banks and CSCs will facilitate online submission of Digital Life Certificate for the pensioner. For submission of DLC, the pensioner requires to be physically present to facilitate Biometric verification, and provide the PPO Mobile number Aadhaar Number at the CCAs Pensioners Service Centre Banks HPOs CSCs Aadhaar centers for filling online in the following format. Step 1 Step 2 Step 3 Important details for pensioners while filling in information for online DLC: For pensioners who are receiving pension via CPPCs: Sanction authority is TELECOM Disbursing Agency is Bank and Agency is the Bank name. When pension is directly disbursed by CCA office: sanction authority will be TELECOM, Disbursing Agency will be Department of Telecommunication, Agency is the CCA ABC. (Name of CCA which has issued the PPO) Step 4 Modes for submission of Life certificates: Digitally- The Digital Life Certificate will travel online and is not required to be physically submitted. Manually- Alternatively, Life Certificates issued by any of the agencies mentioned in Rule 343 of Central Treasury Rules (CTR) Para 15.2(I), can be dispatched to the CCA offices via Speed post registered post etc. wherein, personal appearance of the pensioner is exempted. Life certificate can also be issued by Banks, digitally or manually 3. How will Pensioner get the Pension Slip and Gratuity? Reply: Pensioners will continue to receive the Pension Slip on their registered mobile number and e-mail ID indicating basic pension, commuted pension, IDA, Income Tax deduction and pension credited in the registered Bank Account. The Pension Slip with password protection will be provided to the pensioners in on his her registered email id. Moreover, the pensioner can also view the monthly statements for pension and gratuity slip in his account dashboard in the CPMS website at dotpension.gov.in. However, if the pensioner still faces any issue, the pensioner can contact the All India Toll free helpline and Local Toll free helplines at each CCA Offices. 4. How will pensioner receive Form 16? Reply: The Form 16 will be sent to the pensioner s address by Post and can also be downloaded from the pensioner s account in the dotpension.gov.in website at the end of FY 5. In the case of Family Pension and in the event of death of the Family Pensioner Pensioner which office should be the dependant approach? Reply: In the event of the death of the Pensioner Family pensioner, if the name of claimant is mentioned in the PPO or any revised Pension authority issued by the CCA office, then the claimant need not present himself herself to the O o Pr. CCAs CCAs. The claimant requires to send the following to the concerned Pr. CCAs CCA 1. Death certificate of pensioner 2. Account details Bank undertaking and mandate form duly filled- if there is change in account details 3. Proof of life certificate- for this either he may present himself or herself before a CCA office or send a Life certificate by Post or generate a digital Life certificate as mentioned in FAQ S No 2 above. Steps followed in DLC generation are reiterated below. STEP 1 Here, the claimant shall fill his her Mobile number and Aadhaar Number. STEP 2 Step 3: Name of pensioner: Name of claimant PPO Number: same as earlier PPO number Type of Pension: Family Pension Other details and steps shall remain the same. In the event of the death of the Pensioner Family pensioner, if the name of claimant is not mentioned in PPO or any revised authority issued by the CCA office, then claimant may submit the Following to the office from which the pensioner retired: 1. Duly filled form 14 2. Bank Undertaking and Mandate form 3. Photograph and specimen signature finger print 4. Death Certificate of pensioner However, if the pensioner still faces any issue, the pensioner can contact All India toll free helpline and Local Toll free helplines at each CCA Offices. 6. Does pensioner have to be identified for the first time at the time of taking pension? Reply: No, there will be no need for the identification of the pensioner for the first time at the time of taking pension. 7. What if the pensioner has made any error at the time of filling up his Form? Reply: The pensioner has to intimate the same to helpdesk or their Head of the office at the earliest about such error occurred and forms shall be returned for the rectification of such error and resubmission by the pensioner. 8. What will be requirements of uploading the scanned photographs and signature in the CPMS? Reply: The following Do s and Don ts must be fulfilled by the pensioner in order to upload the scanned photographs and pictures on the CPMS portal which is: - Dos DON Ts Paste upload joint colour photograph (or Do not paste or upload black and white separate photographs) in the box meant for photograph. affixing uploading the photograph and signature Dimensions of photographs should not be with following dimensions: smaller than the box provided in the Single photograph: 4.5 cm height x 3.5 cm width application form. Digital photograph: File size should be less than Photograph should not be in the form of a 70kb in JPG JPEG selfie. Do not upload attested photographs Joint photograph: 4.5 cm height x 7 cm width Digital photograph: File size should be less than 70kb in JPG JPEG Signature: Should be in blue black ink and clearly visible and size will be 2 cm height x 6 cm width. Digital Signature : File size should be less than 70kb in JPG JPEG The background of the photograph should be a Photograph with dark background or in plain light colour background. uniform, or with eyes hidden under coloured or dark glasses will not be The photo should capture full face, front view, accepted. with eyes open. The head should be in the centre of the frame. Photograph is NOT to be signed. Photograph should fit within the given box. Photograph in computer print will not be accepted. There should not be any distracting shadows on Eyes must not be covered by hair. Glares the face or on the background. on eyeglasses should be avoided with a slight upward or downward tilt of the head. 9. What if the pensioner loses the copy of the PPO? Reply: The Pensioner can download the same from his her Dashboard in the CPMS portal. 10. What if the pensioner forgets his her password? Reply: The Pensioner can any time reset his her password whenever he has forgotten the same by clicking on the reset password utility and then the pensioner will get an SMS on his her registered mobile no. along with the OTP which he she has to furnish in order to reset the same. After doing the said process the pensioner can reset his her password. 11. What are the alternatives in case the Helpline is not responding? Reply: In case the Helpline is not responding then the pensioner can raise a grievance by logging in the CPMS Portal with his User ID or he she can reach the authorities by mailing the grievances to sampann.cpms-dot nic.in or He She can call the local Helpline Nos for the early resolution of his her issues. 12. What will be the Grievance Redressal Mechanism in CPMS? Reply: The pensioner can lodge the complaint grievance from his her dashboard in the CPMS portal by clicking on the tab Grievance Management . After selecting the grievance type and submitting the details of grievance, the grievance will be sent digitally to the concerned official. A Ticket number Complaint number will be generated and the pensioner will get an SMS Email confirmation of the grievance raised by him. The pensioner can simultaneously track the grievance and get the updated status from his her dashboard. However, if the pensioner still faces any issue, the pensioner can contact All India Toll free helpline and Local Toll free helplines at each CCA Offices. 13. When will monthly pension gets processed? Reply: The pensioner will get an alert via SMS Email facility reflecting the updated status of his her pension case and he she can also track the pension case on his her dashboard account on dotpension.gov.in the CPMS portal. 14. If the pensioner has changed his her Bank account details, how can he update the same? Reply: The pensioner can submit send by post, the updated Bank Account details in Bank undertaking form and Mandate form duly signed to the O o Pr. CCAs CCAs. The account details will be updated in the next month. 15. I am an existing pensioner taking pension via CPPCs, how will I get my pension in the new system? Reply: The existing pensioners will be migrated to the new system in the new financial year followed by announcements and proper intimations, the pensioner will continue receiving pension in the same account in which pension was credited earlier. The new system will provide additional benefits of online services for monitoring DLC, Tax functions, grievances, information etc. for greater transparency for the pensioner which can be accessed from anywhere at a click. 16. Is furnishing of Aadhaar is mandatory at the time of creating the profile of the pensioner? Reply: No, it is not mandatory to furnish the Aadhaar at the time of creating the profile of the pensioner. 17. What will be the official timings of Helpdesk? Reply: The helpdesk is open during the official working hours as per the work timings of the DoT(HQ) and O o Pr. CCAs CCAs. However, if the issue still persist or gets unresolved the matter may be escalated to the Nodal officers by emailing at sampann.cpms-dot nic.in or lodging the grievance on the CPMS portal. 18. Can a pensioner personally visit the O o Pr. CCAs CCAs for the resolution of his her grievances? Reply: The pensioner can visit the O o Pr. CCAs CCAs for the resolution of his her grievances or in order to clarify the queries related to its pension during the office hours. 19. How will the pensioner get the PPO in the new system? Reply: A digitally signed PPO shall be sent to the Pensioners account dashboard in the CPMS Portal. However, a Printed copy shall also be sent via post to the pensioner s address by PDA section. 20. Does the Bank undertaking needs to be submitted by the family members of the pensioner in case of the Family Pension Case? Reply: Yes, the members eligible for the Family Pension need to submit the Bank undertaking which will be duly signed and stamped by the authorized Nodal official of the Bank. However, a Blank format of Bank Undertaking can be downloaded from the Downloads on https: dotpension.gov.in. 21. What are the facilities available on the App? Reply: Using the APP, a pensioner can: Track pension View ePPO Lodge and track of grievances View monthly Statement Stay Updated", "type": "pdf"}, {"source": "PDF - CPMS-UserManual", "content": "Comprehensive Pension Management System User Manual (Version 6.2) System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 6.2 Wednesday, August 7, 2019 0 Comprehensive Pension Management System User Manual (Version 6.2) Document Version Sl. No. Version Release Date Remarks First version of CPMS User Manual. 1 1.0 11-10-2018 Does not include details for Arrears. Second Version of CPMS user Manual 2 2.0 29-10-2018 Updation of PDA module Third Version of CPMS user Manual 3 3.0 22-11-2018 Updated in What s New In Version 3.0 Section Fourth Version of CPMS user Manual 4 4.0 27-12-2018 Updated in What s New In Version 4.0 Section Fifth Version of CPMS user Manual 5 5.0 22-04-2019 Updated in What s New In Version 5.0 Section Six Version of CPMS user Manual 6 6.0 19-05-2019 Updated in What s New In Version 6.0 Section 6 6.1 18-06-2019 Minor modification in chapter on Revision 7 6.2 02-08-2019 Income Tax Module 1 Comprehensive Pension Management System User Manual (Version 6.2) Executive Summary Sanchar <PERSON>sion, <PERSON><PERSON>ran The Government of India under the leadership of the Honorable Prime Minister has been actively pursuing the objective of Minimum Government, Maximum Governance . This envisages providing Paperless, Cashless and Faceless services across the country, especially in rural and remote parts of India. The Finance wing of Department of Telecom (DoT) has taken several initiatives to digitalize and provide e-solutions for ease of governance. These include digital payments, online receipts, direct payment of GPF for BSNL employees, registration of pensioners through Jeevan Pramaan Portal for Life Certification, etc. In the current system of pension payments for telecom pensioners, pension for 3.5 lakh DoT and BSNL retirees is being sanctioned and authorized by the Controller of Communication Accounts (CCA) Offices. The pension is thereafter disbursed on commission basis by intermediary Agents-Banks and Post Offices to the pensioners. The amount of pension paid is to the tune of Rs. 10500 crores per annum. Another 1.5 lakh retirees are expected to be added in the near future. There were, however, several shortcomings in the current system. These includes delays in disbursement of the first pension due to time taken in physical movement of PPO from sanctioning authorities to banks or to Post Offices, delay in payment of arrears of pension after revisions, wrong disbursement of pension, difficulty and delay in redressal of pensioners grievances, non-refund of excess payment by Banks Post Offices to the Telecom Department, paper based ineffective audit, etc. In order to mitigate the problems faced by pensioners, the Department has decided to introduce a seamless pension processing system through integrated software, which would bring the processing, sanctioning, authorization and payment of pensions under a common platform. The pensioners, who are the most important stakeholders in this initiative, will share a common touch point with the offices of the Pr. CCAs CCAs (in the field) for looking after their interests and settling any queries grievance they may have related to their pensions. These offices will be a single window for all issues of the telecom pensioners thereby affording a great relief to the senior citizens who served the department with dedication. 2 Comprehensive Pension Management System User Manual (Version 6.2) Table of Contents Executive Summary .................................................................................................................... 2 Table of Contents ....................................................................................................................... 3 Definitions and Acronyms .......................................................................................................... 8 What s New ................................................................................................................................ 9 What s New in Version 3.0 ................................................................................................................. 9 What s New in Version 4.0 ................................................................................................................. 9 What s New in Version 5.0 ................................................................................................................. 9 What s New in Version 6.0 ................................................................................................................. 9 What s New in Version 6.2 ............................................................................................................... 10 CHAPTER 1................................................................................................................................ 11 1.1 SAMPANN .......................................................................................................................... 11 1.2 Users .................................................................................................................................. 11 1.3 Launching Logging into CPMS ......................................................................................... 11 1.4 CPMS Dashboard and Administrator Role ......................................................................... 12 1.4.1 Administrator ........................................................................................................................... 13 1.5 User Creation Management ............................................................................................ 18 1.5.1 Unit wise Login Verticals creation in CPMS ............................................................................ 18 1.6 System Administrator ........................................................................................................ 18 1.6.1 Administrator and CCA login .................................................................................................. 18 1.7 Collection Submission of Pension Papers ....................................................................... 20 1.7.1 For BSNL and DoT offices ....................................................................................................... 20 1.7.1.1 HoO Creation .................................................................................................................... 20 1.7.1.2 DH Creation ...................................................................................................................... 21 1.8 Processing and Sanction of Pension .................................................................................. 23 1.8.1 AO Creation ............................................................................................................................. 23 1.8.2 AAO Creation ........................................................................................................................... 25 1.8.3 DH Creation ............................................................................................................................. 26 1.9 Disbursement of Pensionary Benefits and Pension ........................................................... 28 1.9.1 AO Creation ............................................................................................................................. 28 1.9.2 AAO Creation ........................................................................................................................... 29 1.9.3 DH Creation ............................................................................................................................. 31 CHAPTER 2................................................................................................................................ 34 3 Comprehensive Pension Management System User Manual (Version 6.2) 2. HoO Unit .............................................................................................................................. 34 2.1 Normal Pension Case ......................................................................................................... 34 2.1.1 Creation of Retiree Profile ...................................................................................................... 34 2.1.2 Service Book Verification (12M BDR) ..................................................................................... 36 2.1.3 Send Form to Retiree (8M BDR) ............................................................................................. 36 2.1.4 Form Received (6M BDR) ........................................................................................................ 38 2.1.5 Form Verification (4M BDR) .................................................................................................... 39 2.1.6 Form 7 ...................................................................................................................................... 40 2.1.7 Form 8 ...................................................................................................................................... 44 2.1.8 Send to PAO ............................................................................................................................. 45 2.1.9 View Forms .............................................................................................................................. 45 2.2 Family Pension Case ........................................................................................................... 46 2.2.1 Personal Detail ........................................................................................................................ 46 2.2.2 Service Verification (Family Pension case) ............................................................................. 50 2.2.3 Form 14 .................................................................................................................................... 51 2.2.4 Form 12 .................................................................................................................................... 54 2.2.5 Form 18 .................................................................................................................................... 55 2.2.6 Send to PAO ............................................................................................................................. 58 2.2.7 View Forms .............................................................................................................................. 58 CHAPTER 3................................................................................................................................ 59 3. Pension Sanctioning Section. ............................................................................................... 59 3.1 Allotment of Pension Case to DH ....................................................................................... 59 3.2 Form Received ................................................................................................................... 59 3.3 Pay Regulation ................................................................................................................... 61 3.4 Account Enfacement .......................................................................................................... 62 3.5 Revise Form List ................................................................................................................. 64 3.6 Calculation Sheet ............................................................................................................... 68 3.7 Pension Sanction ................................................................................................................ 71 CHAPTER 4................................................................................................................................ 74 4. PDA Section .......................................................................................................................... 74 4.1 Allotment of Pension Case ................................................................................................. 74 4.2Vendor Verification in PFMS ............................................................................................... 75 4.3Bill Generation .................................................................................................................... 77 4.4PFMS Payment .................................................................................................................... 79 4 Comprehensive Pension Management System User Manual (Version 6.2) 4.5 Arrears ................................................................................................................................ 81 4.6 Monthly Bill ........................................................................................................................ 84 CHAPTER 5................................................................................................................................ 86 5. Retiree Module .................................................................................................................... 86 5.1Retiree Dashboard .............................................................................................................. 86 5.2 Fill Submit Forms ............................................................................................................ 87 5.2.1Pensioners Details .................................................................................................................... 87 5.3 Updation of Mobile, Email and address ............................................................................ 94 5.3.1 Mobile Number Update .......................................................................................................... 95 5.3.2 Email ID Update ....................................................................................................................... 96 5.3.3 Address Update ....................................................................................................................... 97 5.4 Lodge Grievance ................................................................................................................ 98 5.5 View documents and Ledger ............................................................................................. 99 5.6. Income Tax Module ........................................................................................................ 100 5.6.1 Proposed Investment Declaration ........................................................................................ 100 5.6.2 Actual Investment Declaration ............................................................................................. 105 CHAPTER 6.............................................................................................................................. 110 6.Grievance Management ..................................................................................................... 110 6.1 Pensioner Grievance ........................................................................................................ 110 6.2 Allotment of Grievance to DH .......................................................................................... 111 6.3 Grievance Resolution ....................................................................................................... 112 CHAPTER 7.............................................................................................................................. 114 7. DSC Registration ................................................................................................................. 114 CHAPTER 8.............................................................................................................................. 133 8.1 Annexure 1 (Side Channel User Creation) ....................................................................... 133 8.2 HoO Unit- Side channel (Steps for Processing a Case) .................................................... 136 8.2.1Normal Pension Case .............................................................................................................. 137 8.2.1.1 Creation of Retiree Profile ................................................................................................. 137 8.2.1.2 Service Book Verification ................................................................................................... 138 8.2.1.3 Send Form to Retiree ......................................................................................................... 139 8.2.1.4 Form Received .................................................................................................................... 140 ******* Form Verification ............................................................................................................... 141 ******* Form 7 ................................................................................................................................. 141 ******* Form 8 ................................................................................................................................. 144 5 Comprehensive Pension Management System User Manual (Version 6.2) ******* Send to PAO........................................................................................................................ 144 ******* View Forms ......................................................................................................................... 145 8.2.2 Family Pension Case .............................................................................................................. 145 8.2.2.1 Personal Detail ................................................................................................................... 145 8.2.2.2 Service Verification (Family Pension case) ........................................................................ 149 8.2.2.3 Form 14 ............................................................................................................................... 150 8.2.2.4 Form 12 ............................................................................................................................... 153 8.2.2.5 Form 18 ............................................................................................................................... 154 8.2.2.6 Send to PAO........................................................................................................................ 157 8.2.2.7 View Forms ......................................................................................................................... 157 Chapter 9 ................................................................................................................................ 158 9. ID Card Generation ............................................................................................................ 158 9.1 Upload AO Pension signature .......................................................................................... 158 9.2 Generate ID Card ............................................................................................................. 159 Chapter 10 .............................................................................................................................. 161 10. Revision ............................................................................................................................ 161 10.1 Revision in the rate of DA .............................................................................................. 161 10.2 Revision Due to Withheld Amount ................................................................................ 167 10.3 Revision on account of Pay revision Court Order ......................................................... 173 10.4 Revision of Pension to FP (No eligible family member mentioned in PPO) .................. 180 10.5 Revision of Pension to FP (Eligible family member mentioned in PPO) ....................... 188 Chapter 11 .............................................................................................................................. 195 11.Profile Authentication ....................................................................................................... 195 11.1 Retiree Profile Authentication ....................................................................................... 195 Chapter 12 .............................................................................................................................. 199 12. Upload utility .................................................................................................................... 199 Chapter 13 .............................................................................................................................. 202 13. Bill Payment slip generation .......................................................................................... 202 Chapter 14 .............................................................................................................................. 204 14. Updation of Mobile, Email and address .......................................................................... 204 14.1 Mobile Number Update ................................................................................................. 205 14.2 Email ID Update ............................................................................................................. 206 14.3 Address Update .............................................................................................................. 207 6 Comprehensive Pension Management System User Manual (Version 6.2) Chapter 15 .............................................................................................................................. 208 15. Other pension Types ........................................................................................................ 208 15.1 Compulsory retirement.................................................................................................. 209 15.2 Compassionate Allowance ............................................................................................. 212 15.3 Miscellaneous issues ...................................................................................................... 214 Chapter 16 .............................................................................................................................. 215 16. Income Tax Processing ..................................................................................................... 215 7 Comprehensive Pension Management System User Manual (Version 6.2) Definitions and Acronyms Abbreviation Term Description AAO Assistant Account officer ACCA Assistant Controller of Communication Accounts AE Account Enfacement AO Accounts Officer CCA Controller of Communication Accounts CGCA Controller General of Communication Accounts DH Dealing Hand DSC Digital Signature Certificate HoO Head of Office LPD Last Pay Drawn M BDR Month Before Date of Retirement PAO Pay Accounts Office PDA Pension Disbursement Authority PFMS Public Financial Management System 8 Comprehensive Pension Management System User Manual (Version 6.2) What s New What s New in Version 3.0 Instruction for DH PDA amended in PDA module, relating to sending EPPO and Sanction hard copies to different sections (Page 73, sub heading 4.1). Instructions for AO Cash PFMS amended in PDA module, relating to payment from PFMS (Page 80, sub heading 4.4). Instructions for creation of HoO and DH amended, relating to role assignment to officers (Page 15, sub heading 1.5.1). Instructions for filling Form 7 (Page 43, sub heading 2.1.6), Form 18 (Page 56, sub heading 2.2.5) and editing calculation sheet on (Page 67, sub heading 3.5). Instructions for filling bank details in Form 14 amended (Page 52, sub heading 2.2.3). Instructions for filling bank details in Form 12 amended (Page 53, sub heading 2.2.4). Instructions for filling bank details in Pensioner profile amended (Page 91, sub heading 5.2.1). Instructions for AO pension section (after DSC) amended, relating to enclosures to be forwarded to AO PDA (Page 72, sub heading 3.7). Instructions in Vendor verification of PDA section amended, relating to PDA Utility tab (Page 74, sub heading 4.2). Arrear Section (Page 82, sub heading 4.5) Manual for DSC installation (Page 97 onwards). What s New in Version 4.0 Reference for Side Channel creation in Annexure 1 (Page 17, sub heading 1.7.1.1). Instructions for filling Retiree Profile (Page 32, sub heading 2.1.1). Instructions for correction needed in Form 7 and Form 8(Page 42, sub heading 2.1.7). Instructions for filling Form 12 (Page 51,52, sub heading 2.2.4). Instructions while allot case to DH by AO Pension (Page 56, sub heading 3.1). Instructions for AE Generated in Pension Section (Page 61, sub heading 3.4). Instructions for filling Pension Sanction (Page 68, sub heading 3.7). Instructions for filling Monthly Bill (Page 82, sub heading 4.5). Annexure 1 for Side Channel User creation (Page 117 onwards). What s New in Version 5.0 Reference for ID Card Generation for CDA Pensioners (Page 142, sub heading 9.1) Instructions for taking print of ID Card (Page 144, sub heading 9.2) What s New in Version 6.0 Addition of 10. Revision of Pension Module (Page 146, sub heading 10) Addition of 11. Profile Authentication (Page 34 , 124 and 176 ) 9 Comprehensive Pension Management System User Manual (Version 6.2) Addition of chapter 12. Upload utility to chapter 15. Other Pension Types Addition of 5.3 to 5.5 in Retiree Module What s New in Version 6.2 Addition in chapter 5- 5.6 Income Tax Module Addition in Chapter 16. Income Tax Processing 10 Comprehensive Pension Management System User Manual (Version 6.2) CHAPTER 1 1.1 SAMPANN SAMPANN(System for Accounting and Management of Pension) is brand Name for Comprehensive Pension Management System( CPMS) ,a web portal for Pension Processing, Direct Disbursement, Accounting and Auditing of Pension and Pensionary Benefits to Pensioners of Department of Telecommunication. It has been designed with the following objectives: Direct Credit of pensionary benefits and pension to Pensioners Bank Account. Bringing greater transparency in processing of pensions. Reducing delays in disbursement of pension and arrears of pension. Digitization of forms and streamlining in HoO and CCA offices to reduce time and effort. Optimum utilization of resources in processing and disbursement of pension. Providing timely updates and alerts to the Pensioners. Creating a platform for direct interaction with pensioners for serving them better. Improving the quality of service through consistent and standardized work programs. Creating a faster and more responsive online grievance registration and redressal system. Providing real time MIS to CCA and senior officers of DoT and Controller General of Communication Accounts (CGCA). 1.2 Users This User Manual has been designed for the needs of different users of the application. The target users are listed below CCA offices Other DoT field units BSNL field units Retiring officers officials Pensioner (including Family Pensioners) MIS User 1.3 Launching Logging into CPMS Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpension.gov.in in web browser to go to login screen. ( the preferred web browser for CPMS use is Google Chrome and for DSC, it is Internet Explorer Version 9 or 11). 2. Enter User Name. 11 Comprehensive Pension Management System User Manual (Version 6.2) 3. Enter Password. 4. Enter Captcha. 5. Click on Login button. 1.4 CPMS Dashboard and Administrator Role Upon successful logging into CPMS, user will land on the home screen. For consistency, the panel on the left shows the Menu options for selection depending upon the work involved. Menu is arranged as per the sequence of operation and the frequency of usage depending upon the type of user logged in. (Fig 1.0, 1.1) Once the selection is made, the information is displayed in tabular form. The top right corner of the screen will show the User (logged in) profile and photograph, if available. The logout option is next to the user detail at the top right. 12 Comprehensive Pension Management System User Manual (Version 6.2) Fig 1.0 Fig 1.1 1.4.1 Administrator Administrator logins screen will show the following options to work with. (Fig 1.1.1) 13 Comprehensive Pension Management System User Manual (Version 6.2) Fig 1.1.1 Masters: This allows the Administrator User to manage the master data of different modules of CPMS. (Fig 1.1.2) Fig 1.1.2 Users: User section consists of user roles, access rights, user registration and DSC approval. It allows the Administrator User to assign and modify roles, access rights and grant DSC approval to different users. (Fig 1.1.3) 14 Comprehensive Pension Management System User Manual (Version 6.2) Fig 1.1.3 Role Master: Administrator User can see the different roles available in the Role Master and can also add a new role (Fig 1.1.4) Administrator Users Role Master Fig 1.1.4 Role Rights: Administrator User can assign the access rights to the available roles (created by admin) (Fig 1.1.5) Administrator Users Role Rights Click on Action icon of the respective roles to give the access rights. Administrator User can select the respective role from the dropdown and assign the access of modules to it by clicking on check boxes next to the module name. Fig 1.1.5(a) Administrator User will click on Submit button. 15 Comprehensive Pension Management System User Manual (Version 6.2) Fig 1.1.5 Fig 1.1.5 (a) User Registration: It shows the list of all the existing users of the application along with the details. Administrator User can also add new user from this screen. (Fig 1.1.6) 16 Comprehensive Pension Management System User Manual (Version 6.2) Fig 1.1.6 Reports: This allows the Administrator User to generate the reports as per requirements. (Fig 1.1.7) Fig 1.1.7 Audit Trail: This allows the Administrator User to see the workflows and audit trail as per the activities performed. (Fig 1.1.8) 17 Comprehensive Pension Management System User Manual (Version 6.2) Fig 1.1.8 1.5 User Creation Management 1.5.1 Unit wise Login Verticals creation in CPMS The CCA roles will be created by the System Administrator beforehand. It may be noted that the entire work under CPMS has been segmented into four broad parts as follows: 1. System Administrator. 2. Collection and submission of Pension papers. The HoO and DH terms are used for CCA offices BSNL offices Other DOT units for Head of that particular office and Dealing hand. It is recommended HoO should be any officer of the rank of AAO or AO and DH should be officer of the rank below AAO (Sr. Acct. Jr. Acct.). 3. Processing and sanction of Pension. 4. Disbursement of pensionary benefits and monthly pension. 1.6 System Administrator 1.6.1 Administrator and CCA login a) Administrator User will create CCA user name password for login. b) CCA or any officer nominated by CCA shall be the highest authority in CCA office for login in CPMS. c) Path to create the CCA user: Login as Administrator Users User Registration Click on ADD NEW (fill the user detail and save it).(Fig 1.3) 18 Comprehensive Pension Management System User Manual (Version 6.2) Fig 1.3 d) In next step Administrator User will assign roles to CCA. e) Path to assign the CCA Role and Rights: Login as Administrator Users Role Master Click on ADD NEW (fill the user detail and save it).(Fig. 1.4 1.5) Fig 1.4 19 Comprehensive Pension Management System User Manual (Version 6.2) Fig 1.5 1.7 Collection Submission of Pension Papers 1.7.1 For BSNL and DoT offices 1.7.1.1 HoO Creation a) CCA will create the login of HoO. b) HoO is the second level login after CCA. c) Path to create the HoO User Login: Login as CCA Users User Registration (Select the Role Type as HoO, fill the user detail and save it).(Fig 1.6 (A)) d) For side channel (Telecom Circle) HoO creation, please refer Annexure 1. e) After creating the login, CCA will assign the authorization of modules to HoO by clicking on the Lock icon in the authorization column. (Fig 1.6 (i) (A)) f) CCA will subsequently assign rights. (Fig. 1.6 (i)(A)) g) CCA will enter edit details of the HoO. (Fig 1.7 (A)) Fig 1.6 (A) 20 Comprehensive Pension Management System User Manual (Version 6.2) Assign Access Rights Fig 1.6 (i) (A) Edit user details Fig 1.7 (A) 1.7.1.2 DH Creation a) DH is the third level login and subordinate to HoO. b) HoO will create the login for DH. 21 Comprehensive Pension Management System User Manual (Version 6.2) c) Path to create the DH User Login: Login as HoO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (Fig 1.8 (A) and 1.9 (A)) d) After creating the login, HoO will assign the authorization of modules to DH by clicking on the Lock icon in the authorization column. Fig 1.9 (i) (A) Fig 1.8 (A) Edit user details Fig 1.9 (A) 22 Comprehensive Pension Management System User Manual (Version 6.2) Assign Access Rights Fig 1.9 (i) (A) 1.8 Processing and Sanction of Pension 1.8.1 AO Creation a) AO is the second level login and subordinate to CCA in Pension Section. b) CCA will create the login for AO to work in Pension Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (Fig 1.10 and 1.11) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. Fig 1.11 (a) e) CCA will subsequently assign rights. (Fig 1.11 (a)) f) Multiple AO Users can be created. 23 Comprehensive Pension Management System User Manual (Version 6.2) Fig 1.10 Edit user details Fig 1.11 24 Comprehensive Pension Management System User Manual (Version 6.2) Fig 1.11 (a) 1.8.2 AAO Creation a) AAO is the third level login and subordinate to AO in Pension Section. b) AO will create the login for AAO to work in Pension Section. (Fig 1.12 and 1.13) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (Fig 1.12 and 1.13) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. Fig 1.13 (a) e) AO will subsequently assign rights. (Fig 1.13 (a)) f) Multiple AAO Users can be created. Fig 1.12 25 Comprehensive Pension Management System User Manual (Version 6.2) Edit user details (Fig 1.13) Assign Access Rights Fig 1.13 (a) 1.8.3 DH Creation a) DH is the third level login and subordinate to AAO. b) AAO will create the login for DH. c) Path to create the DH User Login: Login as AAO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (Fig 1.14 and 1.15) d) After creating the login, AAO will assign the authorization of modules to DH by clicking on Lock icon in the authorization column. Fig 1.15 (i) e) Multiple DH Users can be created. 26 Comprehensive Pension Management System User Manual (Version 6.2) Fig 1.14 Edit user details Fig 1.15 27 Comprehensive Pension Management System User Manual (Version 6.2) Assign Access Rights Fig 1.15 (i) 1.9 Disbursement of Pensionary Benefits and Pension 1.9.1 AO Creation a) AO is the second level login and subordinate to CCA in Pension Disbursement Section. b) CCA will create the login for AO to work in Pension Disbursement Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (Fig 1.16 and 1.17) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. Fig 1.17 (i) e) CCA will subsequently assign rights. (Fig 1.17 (i)) f) Multiple AO Users can be created. Fig 1.16 28 Comprehensive Pension Management System User Manual (Version 6.2) Edit user details Fig (1.17) Assign Access Rights Fig 1.17 (i) 1.9.2 AAO Creation a) AAO is the third level login and subordinate to AO in Pension Disbursement Section. b) AO will create the login for AAO to work in Pension Disbursement Section. (Fig 1.18 and 1.19) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (Fig 1.18 and 1.19) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. Fig 1.19 (i) e) AO will subsequently assign rights. (Fig 1.19 (i)) 29 Comprehensive Pension Management System User Manual (Version 6.2) f) Multiple AAO Users can be created. (Fig 1.18) Edit user details (Fig 1.19) 30 Comprehensive Pension Management System User Manual (Version 6.2) Assign Access Rights Fig 1.19 (i) 1.9.3 DH Creation a) DH is the third level login and subordinate to AAO in Pension Disbursement Section. b) AAO will create the login for DH. c) Path to create the DH User Login: Login as AAO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (Fig 1.20 and 1.21) d) After creating the login, AAO will assign the authorization of modules to DH by clicking on lock icon in authorization column. Fig 1.21 (i) e) Multiple DH Users can be created. (Fig 1.20) 31 Comprehensive Pension Management System User Manual (Version 6.2) Edit user details (Fig 1.21) Assign Access Rights Fig (1.21) (i) Important Points to Remember Administrator cannot create the login for any other User except CCA. CCA cannot create login for any other user except HoO, AO(Pen) and AO(PDA) HoO cannot create login for any other user except DH. AO cannot create login for any other user except AAO. AAO cannot create login for any other user except DH. In CPMS, each user is uniquely identified with a username, password, role and rights alongside other settings. The role and rights determine which tasks a user can perform, what data user can see, and how to perform the required activities using the available data. 32 Comprehensive Pension Management System User Manual (Version 6.2) Additional Key Points 1. On first login, all Users shall be prompted to update their profiles and change passwords. 2. In case of transfer of personnel or for any updating of profile information, the new details can be incorporated by writing to the Authority who created the user. 3. It is important that user creation is immediately followed by grant of authorisation rights, otherwise the user will find an empty screen. 4. CCA user must check the list of HoO available for creation. If he finds any error in name, it should be immediately brought to the notice of Administrator by writing to CPMS helpdesk support.cpms-dot nic.in. 33 Comprehensive Pension Management System User Manual (Version 6.2) CHAPTER 2 2. HoO Unit This chapter deals with flow of pension papers in the Head of Office (The term HoO is used for BSNL other DoT offices Admin section in CCA offices. 2.1 Normal Pension Case 2.1.1 Creation of Retiree Profile DH to login to create the retiree profile. Select Retiree details on the Menu options available on the left pane. Click on Add New button at the right top corner of Retiree Profile page to get the form to fill. Enter the following information on retiree detail form (underlined parameters are the mandatory fields): a. Title b. First Name c. Middle Name d. Last Name e. Type of Retirement f. Height g. Father s Husband s Name. h. Mother s Name i. Date of Birth j. Gender k. Aadhaar Number l. PAN Number m. Mobile Number (This should be an Active Number) n. Email ID o. Identification Mark 1 p. Identification Mark 2 Following details to be filled in other details section (All are mandatory fields): a. Designation b. Group c. Date of Joining d. Date of Retirement e. Date of Superannuation f. Govt. Accommodation provided by Directorate of Estates BSNL at any time of service: YES NO (to select from the dropdown) 34 Comprehensive Pension Management System User Manual (Version 6.2) g. Pay Commission (this should be the Pay Commission corresponding to the last pay drawn) After filling the aforementioned information, DH to click on Save button. By using the Clear button, User can also refresh the form and fill in fresh information. (Fig 2.1) IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore, due diligence should be exercised while creating the Retiree Profile. In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk. Fig 2.1 Creation of the Retiree Profile is the most important step in the process, so it should be ensured entries made therein are correct in all respects. After saving the information, it is recommended that a printout of the page be taken from the screen and Vetted by HoO level User before proceeding ahead in Service Verification. If any error is detected, then retiree profile be edited before initiating 2.1.2(Further updates in chapter 11) 35 Comprehensive Pension Management System User Manual (Version 6.2) 2.1.2 Service Book Verification (12M BDR) DH to re-check the form and send it for approval to HoO for service book verification. HoO to login and approve the service book verification. After HoO approves the form, the form will appear in Send Form to Retiree tab. HoO can also return the form to DH in case any discrepancy is found. Then DH will have to again verify and send the form to HoO for approval. (Fig 2.2) Before feeding Service Book information and updating, it has to be ensured that the Service Book is actually verified for the period mentioned. If any period remains unverified, please mention it in Remarks and keep a printout in the pension file. Service Book verification should be completed at the earliest. This activity has to be completed 12 months before date of retirement. Fig 2.2 2.1.3 Send Form to Retiree (8M BDR) DH to send the form for approval to HoO. (Fig 2.3) HoO to login and approve the form. HoO can also return the form to DH by clicking on Return button in case any discrepancy has been found. (Fig 2.4) After the approval form HoO, form will be sent to Retiree to fill the required information. 36 Comprehensive Pension Management System User Manual (Version 6.2) This activity has to be completed by 8 months before date of retirement. Fig 2.3 Fig 2.4 37 Comprehensive Pension Management System User Manual (Version 6.2) 2.1.4 Form Received (6M BDR) DH to receive the form after retiree fills and submits the form from his her end (Fig 2.5). DH to check that the form received is correctly filled and that all scanned documents uploaded are of good quality. Then on receipt of hard copy (duly signed by the Retiree), DH may cross verify the details and also check whether all enclosures (as per checklist) have been duly attached. Then DH shall send it to HoO. HoO to Approve Return the form as applicable. (Fig 2.6) If HoO approves the form, it will appear in Form Verification tab of DH User. In case HoO returns the form, it goes back to DH for re-verification. If now, any error is detected by DH, then file has to be returned to retiree with remarks. Retiree shall correct the error and resubmit the papers. Fig 2.5 38 Comprehensive Pension Management System User Manual (Version 6.2) Fig 2.6 2.1.5 Form Verification (4M BDR) This process has been especially incorporated to put another check on correctness of the forms submitted by Retiree. DH to check and verify the form and send it for the approval to HoO by clicking on Verify Button (Fig 2.7) HoO to Approve Return the form as applicable. (Fig 2.8) If HoO approves the form, it will appear in Form 7 tab of DH. In case the HoO returns the form, it goes back to DH for re-verification. Simultaneously, the forms papers will be countersigned by the competent authority in the physical file and process for preparation of Form 7 initiated. 39 Comprehensive Pension Management System User Manual (Version 6.2) Fig2.7 Fig 2.8 2.1.6 Form 7 DH to verify and fill the required information in Form 7. Some information in Form 7 is auto populated. Others have to be entered. (Fig 2.9) a) Name of the retiring Government Employee 40 Comprehensive Pension Management System User Manual (Version 6.2) b) Father s Husband s Name c) PAN NO. d) Height Marks of identification e) Date of Birth f) Service to which he she belongs (indicate name of Organised service, if any, otherwise say General Central Service) g) Particulars of post held at the time of retirement h) Name of the office i) Post held j) Scale of Pay Pay Band Grade pay of the post k) Basic Pay Pay in the pay band Grade pay. l) Basic Pay Pay in the pay band Grade pay m) Whether the appointment mentioned above was under Government or outside the Government on foreign service terms? n) If on foreign service, scale of pay pay band, pay in the pay band and grade pay of the post in the parent department. o) Whether declared substantive in any Post under the Central Govt.? p) Date of beginning of service q) Date of ending service r) Cause of ending service s) In case of compulsory retirement, the orders of the competent authority whether pension may be allowed at full rates or at reduced rates and in case of reduced rates, the percentage at which it is to be allowed (Please See Rule 41) t) In case of removal dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41) u) Particulars relating to military service, if any. v) Particulars relating to the service in autonomous body, if any. w) Whether any Departmental or judicial proceedings in terms of rule 9 of the CCS(Pension) Rules, 1972 are pending against the retiring employee. (lf yes, in terms of Rule 69, provisional pension will be admissible and gratuity will be withheld till the conclusion of departmental or judicial proceedings and issue of final orders) x) Length of service i. Details of omission, imperfection or deficiencies in the Service Book which have been ignored [under rules 59(1) (b) (ii)] ii. Period not counting as qualifying service. iii. Additions to qualifying Service. iv. Whether any leave without pay. v. Net Qualifying service. 41 Comprehensive Pension Management System User Manual (Version 6.2) vi. Qualifying service expressed in terms of complete six monthly periods (Period of three months and above is to be treated as completed six monthly periods (Rule 49) y) Emoluments a. Emoluments in terms of Rule33. b. Emoluments drawn during ten months pending retirement. z) Others: Details of Govt. dues recoverable out of gratuity aa) Licence fee for Govt. accommodation see sub-rules (2), (3) and (4) of rule 2) bb) Dues referred to in Rule 73 cc) Amount indicated by Directorate of Estates to be withheld under Sub rule(S) of Rule 72 dd) Post-retirement address of the retiree 42 Comprehensive Pension Management System User Manual (Version 6.2) Fig 2.9 After the verification, DH will click on tab Submit and Calculate to calculate the pensionary benefits. DH will then send form 7 to HoO by clicking on save calculation and submit HoO will Approve Return the form. If HoO approves the form it will be processed further. In case of Return, form will be sent back to DH for reverification and the same process followed for approval. IMPORTANT: -Form 7 calculates the pensionary benefits and pension as applicable to the pensioner. Once this form is filled and next stage initiated it cannot be edited in HoO section. Therefore, due diligence 43 Comprehensive Pension Management System User Manual (Version 6.2) should be exercised while filling in all important fields like Pay Band, Pay level, Qualifying Non Qualifying service etc. In case Wrong or incorrect information is saved in the system, please immediately inform the Helpdesk. 2.1.7 Form 8 DH to verify and fill the minimum required information in Form 8. Some part of information is auto populated in Form 8. The details of recovery under various heads has to be filled up in this Form. It may be ensured that the total matches with the details filled in Form 7 (Fig 2.10). If Nil recovery is due, then just click Save and Send for Approval . HoO will approve Return the form. If HoO approves the form it will be processed further. In case of Return, Form will be sent back to DH for reverification and the same process followed for approval. The printouts of Form 7 and 8 can be taken from View Forms tab. Approval of competent authority may be taken on the physical Form 7 and 8. 44 Comprehensive Pension Management System User Manual (Version 6.2) Fig 2.10 Form 7 and Form 8 will be processed roughly four months before the date of retirement (Superannuation). HOO will again send the final papers like retirement order, LPC, No dues and Vigilance clearance. If there is any change in the pay etc. before retirement, then based on final papers received Form 7 shall be digitally revised in Pension section. After processing the form 7 from DH level, if HoO user is satisfied then a printout may be taken and along with details of recovery which will be fed in form 8, approval and signature of competent authority may be taken. If any error is detected in the process, then HoO may return to DH the form 7 for necessary correction. DH shall correct it and send it to HoO user again. The approval of competent authority shall be taken. Afterwards, form 8 shall also be filled as already approved and approval taken. 2.1.8 Send to PAO After all the aforementioned steps, DH will submit the form to Pension Section by clicking on Send to PAO . While sending this please ensure that all the documents mentioned in checklist are being sent. The papers may then be dispatched by post. 2.1.9 View Forms All users can view the list of all the retirees and their generated forms. Printout of Form7 and Form 8 can be taken by clicking on Print and sent to Pension Section of concerned CCA office. (Fig 2.11) 45 Comprehensive Pension Management System User Manual (Version 6.2) Fig 2.11 2.2 Family Pension Case 2.2.1 Personal Detail DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH Login Action Family Pension Personal Details)(Fig 2.13) Following information need to be entered on personal detail page: a. Title b. First Name c. Middle Name d. Last Name e. Type of retirement. f. Height g. Father s Husband s Name. h. Mothers Name i. Date of Birth j. Gender k. Aadhaar Number l. PAN Number m. FP Mobile Number n. FP Email ID o. FP Identification Mark 1 p. FP Identification Mark 2 q. Employee Code 46 Comprehensive Pension Management System User Manual (Version 6.2) r. Office Following details to be filled in Other Details section (All are mandatory fields): a. Designation at the time of death b. Group c. Date of Joining d. Date of Death e. Date of Superannuation f. Govt. Accommodation provided by Directorate of Estates BSNL at any time of service: YES NO (to select from the dropdown) g. Pay Commission After filling the aforementioned information, DH to click on Save button. Once filled the details cannot be changed so due diligence should be exercised before saving. (Fig 2.13) To expedite the disbursement of pensionary benefits, Family Pensioner should immediately be advised to submit the required information along with relevant documents to the HoO. Family Pensioners may also be advised to provide mobile phone number (mandatory) so that they can get updates on the pension process and pension disbursement. Fig 2.13 47 Comprehensive Pension Management System User Manual (Version 6.2) After saving the details, an instance will be created in the Personal Details tab, as shown in the Fig 2.14. Fig 2.14 Then DH has to click on Govt. Emp Details edit tab, and proceeds to the subsequent screen. The next screen will show three tabs viz. Personal details, Family Details and Nomination Form1 (Fig 2.15) Fig 2.15 48 Comprehensive Pension Management System User Manual (Version 6.2) Now the DH has to fill in details of Family and Nominations (Fig 2.16, Fig 2.17, Fig 2.18) Fig 2.16 Fig 2.17 49 Comprehensive Pension Management System User Manual (Version 6.2) Fig 2.18 After filling all the details, the DH will submit the form by clicking submit button. 2.2.2 Service Verification (Family Pension case) DH to select Service Verification tab from the Menu options. DH to check the form and send it for approval to HoO for service verification. Before this is done, physical service book verification needs to be done as well. Any unverified portion of the service book should be noted and attached in the file being sent to Pension Section (CCA Office). (Fig 2.14) HoO to login and approve the service book verification. HoO also to verify the service book physically. HoO can also return the form to DH in case any discrepancy is found. Then DH will again have to verify and send the form to HoO for approval. Fig 2.14 50 Comprehensive Pension Management System User Manual (Version 6.2) 2.2.3 Form 14 DH to verify and fill the required information in Form 14. DH may select the claimant who is eligible for pension. This is to be done as per CCS Pension Rules,1972. The Form shows the claimant details, the bank details, the documents to be uploaded like photograph, signature and death certificate etc. The Bank Undertaking Form also needs to be uploaded duly signed by bank authorities as well the Family Pensioner. FMA option has to be chosen (if applicable and desired by the Family Pensioner) (Fig 2.15 (a), Fig 2.15 (b) and Fig 2.15 (c)). It should be seen that the photograph and signature are clear and visible. DH clicks on Form 12 tab. 51 Comprehensive Pension Management System User Manual (Version 6.2) Fig 2.15 (a) 52 Comprehensive Pension Management System User Manual (Version 6.2) Fig 2.15 (b) Fig 2.15 (c) IMPORTANT: -Form 14 prompts the user to enter bank details. The pension of the family pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Family Pensioner, and then uploaded on CPMS. 53 Comprehensive Pension Management System User Manual (Version 6.2) 2.2.4 Form 12 DH to verify and fill the required information in Form 12. For each claimant who has been nominated for gratuity in the nomination form filled earlier. Here DH has to click on the Edit button against the name of the nominee, and his her details will be populated in the Form 12 (Fig 2.16). Again Bank Account details and claimant s details, signature needs to be uploaded. After updation of Form 12 and 14, the case will appear in Form 18 tab of DH. Fig 2.16 IMPORTANT: - Form 12 prompts the user to enter bank details. The DCRG of the family pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Family Pensioner, and then uploaded on CPMS. In case of claimant who is claimant for only Gratuity and not pension, only Mandate form, as generated, needs to be filled by the pensioner and uploaded in portal. In case where there is no nomination, DCRG has to be divided in equal shares among the family members. Accordingly, the nomination form and Form 12s will be filled. 54 Comprehensive Pension Management System User Manual (Version 6.2) 2.2.5 Form 18 DH to verify and fill the required information in Form 18. Some information is auto populated in Form 18. DH should be careful while filling the important details like qualifying service, non-qualifying service period, last month s pay details etc. Pensionary benefits will be calculated on basis of these figures and hence figures must be cross-verified from service book. (Fig 2.17) DH will send Form 18 for approval of HoO. HoO will approve Return the form. HoO should verify the Pensionary benefit amounts. If the amounts are correct, he she can approve it. If incorrect, HoO can return the case back to DH for rectification. If HoO approves the form it will be processed further. In case of Return, form will be sent back to DH for reverification and thereafter process followed for approval. 55 Comprehensive Pension Management System User Manual (Version 6.2) 56 Comprehensive Pension Management System User Manual (Version 6.2) Fig 2.17 Form 18 and 19 to be put up in physical file for approval of competent authority. IMPORTANT: -Form 18 calculates the pensionary benefits and pension as applicable to the Family pensioner. Once this form is filled and next stage initiated it cannot be edited in HoO section. Therefore, due diligence should be exercised while filling in all important fields like Pay Band, pay level, Qualifying Non Qualifying service etc. In case Wrong or incorrect information is saved in the system, please immediately inform the Helpdesk. After processing the form 18 from DH level, if HoO user is satisfied then a printout may be taken and approval and signature of competent authority may be taken. If any error is detected in the process, then HOO may return to DH the form 18 for necessary correction. DH shall correct it and send it to HoO user. The approval of competent authority shall be taken. 57 Comprehensive Pension Management System User Manual (Version 6.2) 2.2.6 Send to PAO After all the aforementioned steps, DH will submit the form to Pension Section (CCA Office) by clicking on Send to PAO . 2.2.7 View Forms All users can view the list of all the retirees and their generated forms. (Fig 2.18) Fig 2.18 58 Comprehensive Pension Management System User Manual (Version 6.2) CHAPTER 3 3. Pension Sanctioning Section. This chapter deals with pension case flow in the Pension Sanctioning Section of the CCA office. 3.1 Allotment of Pension Case to DH The pension case coming from HoO will appear in Allotment tab of the Accounts Officer (AO) of the Pension Sanctioning Section. AO pension sanctioning section will allot the case to the Dealing Hand of the section. (Fig 3.1) The current practice based on which cases are allotted to DH by AOs may be continued. This will be done by the AO who has been authorized to allot the case (It is to be noted that only 1 AO is authorised for allotment). Fig 3.1 3.2 Form Received DH (Pension Sanctioning Section) will receive the case allotted by AO (Action- Pension Section- Form Received). DH (Pension Sanctioning Section) will select on the particular pension case, and click on Receive Form tab (Fig 3.2). 59 Comprehensive Pension Management System User Manual (Version 6.2) DH (Pension Sanctioning Section) will then select the forms submitted by the HoO ACCA. DH will then enter the actual date of receipt of physical forms and other documents. DH will then save the case (Fig 3.3). A physical file has to be opened by the DH upon receipt of forms and documents by the DH. Fig 3.2 Fig 3.3 60 Comprehensive Pension Management System User Manual (Version 6.2) 3.3 Pay Regulation The case will now flow into the Pay Regulation tab of DH (Pension Sanctioning Section) level (Action- Pension Section- Pay Regulation). DH (Pension Sanctioning Section) will select the case and click on Pay Regulation Sheet (Fig 3.4). DH (Pension Sanctioning Section) will then add Pay Scale Data for the particular pension case and save the same. Fig 3.4 61 Comprehensive Pension Management System User Manual (Version 6.2) Fig 3.5 (a) Fig 3.5 (b) 3.4 Account Enfacement The above case will now flow into Account Enfacement (AE) at DH (Pension Sanctioning Section) level (Action- Pension Section- Account Enfacement). 62 Comprehensive Pension Management System User Manual (Version 6.2) The DH (Pension Sanctioning Section) will then select the particular pension case. The DH (Pension Sanctioning Section) can edit the Pay Details by clicking on Pay Regulation tab, if the same was not correctly done in 3.3 above. DH (Pension Sanctioning Section) shall analyse if the case is fit for finalisation of pension or not. If some documents have not been received, then he she will generate AE by clicking on Generate AE tab. The same shall be put up for approval of competent authority along with pay regulation sheet. The Approved AE shall be uploaded by in the software. The physical copy of the AE will be sent by mail dak to concerned HoO. Reminders can be sent using facility for regenerating AE. However, when necessary papers have been received, the Resolve AE tab may be clicked. Fig 3.6 (a) 63 Comprehensive Pension Management System User Manual (Version 6.2) Fig 3.6 (b) A Pension case in which all the papers have not been received or some matter remains unaddressed shall remain pending at AE stage itself. Only when all issues relating to pensioner etc are finalized then case should move beyond AE tab. Before going to Revise Form Stage, it may be checked that Pay regulation has been correctly filled. 3.5 Revise Form List The case will now flow from Account Enfacement at DH (Pension Sanctioning Section) level to Revise Form List Tab(Action- Pension Section- Revise Form List). The DH (Pension Sanctioning Section) will then select the particular pension case. The DH (Pension Sanctioning Section) can view Pay Regulation Form. The calculation sheet can be reviewed and edited, if required. It should be noted that if there is any discrepancy in calculation of pension or pensionary benefits, the same can be rectified here. After this the figures cannot be rectified. Hence due diligence need to be exercise here. Fig (3.7) DH (Pension Sanctioning Section) will, if required, edit FORM 7 (Family Pension) or FORM 18 (Normal Pension) and calculate the Retiree s pensionary benefits. (Fig 3.7) DH (Pension Sanctioning Section) will then enter the following information in FORM 7 FORM18 (some information is auto populated): 64 Comprehensive Pension Management System User Manual (Version 6.2) a) Service to which he she belongs. b) Scale of Pay Pay Band Grade pay of the post. c) Whether the appointment mentioned above was under Government or outside the Government on foreign service terms d) Particulars relating to military service, if any e) Particulars relating to the service in autonomous body, if any f) Period not counting as qualifying service? g) Additions to qualifying Service? h) Whether any leave without pay? Points d, e, f, g and h to be filled in carefully, as they will impact the calculation of net qualifying service. i) Emoluments drawn during 1 10 month before retirement. Point i will calculate Average Emolument, which will be compared against LPD to arrive at pensionary benefits. j) Govt. dues recoverable out of gratuity. After filling the details, the DH will click on submit and calculate button. The amount of gratuity, commutation and monthly pension will be calculated and displayed. Revise Form action can be performed if there is a need to carry out correction in Form 7 received from HoO. Fig 3.7 (a) 65 Comprehensive Pension Management System User Manual (Version 6.2) Fig 3.7 (b) 66 Comprehensive Pension Management System User Manual (Version 6.2) Fig 3.7 (c) 67 Comprehensive Pension Management System User Manual (Version 6.2) IMPORTANT: -Calculation Sheet shows the pensionary benefits and pension as applicable to the pensioner. Once this form is reviewed and next stage initiated, it cannot be edited. Therefore, due diligence should be exercised while reviewing and correcting, if required, in all important fields like Pay Band, pay level, Qualifying Non Qualifying service etc. In case Wrong or incorrect information is saved in the system, please immediately inform the Helpdesk. 3.6 Calculation Sheet The case will now flow into Calculation Sheet at DH (Pension Sanctioning Section) level (Action- Pension Section- Calculation Sheet). DH (Pension Sanctioning Section) will select the case and view the calculation sheet and submit it to AAO for further approval. (Fig 3.8) 68 Comprehensive Pension Management System User Manual (Version 6.2) Fig 3.8 69 Comprehensive Pension Management System User Manual (Version 6.2) Now the case will flow to AAO (Pension Sanctioning Section) (AAO Login Approval Pension Section Calculation Sheet) The AAO (Pension Sanctioning Section) can either Approve Return the case. (Fig 3.9) If AAO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in Revise Form tab. In case of Approval, it will go to AO (Pension Sanctioning Section). Now the case will flow to AO (Pension Sanctioning Section) (AO Login Approval Pension Section Calculation Sheet) The AO (Pension Sanctioning Section) can Approve Return the case. If AO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in Revise Form tab. Fig 3.9 (a) 70 Comprehensive Pension Management System User Manual (Version 6.2) Fig 3.9 (b) 3.7 Pension Sanction After the Approval of Calculation Sheet by the AO, the case will come to Pension Sanction tab of DH Login.(DH Login Action Pension Section Pension Sanction) DH can view Sanctions and ePPO. Fig 3.10) (a) Now DH will send the case to AAO for approval. AAO can approve the case and send it to AO for further approval. (AAO Login Action Pension Section Pension Sanction) AO can approve the case (AO Login Action Pension Section Pension Sanction)(Fig 3.10)(b) This approval can be done only on Internet Explorer version 9 or 11 and by using the Digital Signature Certificate (DSC) of AO Pension Section. Post approval by AO, case will move to the AO- PDA section. DSC installation and signature process is dealt separately in the Chapter on DSC Installation. 71 Comprehensive Pension Management System User Manual (Version 6.2) Fig 3.10 (a) Fig 3.10 (b) 72 Comprehensive Pension Management System User Manual (Version 6.2) Fig 3.10 (b) Important: - AO Pension section will send the physical copy of following documents to AO PDA, after Digital signing of EPPO and Sanctions are done (the same is also mentioned in the enclosures section of PPO Authority Model). 1. Physically signed copy of Pension authority 2. Photo Copy of CDA IDA DR List. 3. Photocopy of PAN no and Aadhaar no. 4. Bank undertaking and Mandate form of pensioner, indicating name and full address of the Authorised Public Sector Bank. 73 Comprehensive Pension Management System User Manual (Version 6.2) CHAPTER 4 4. PDA Section This chapter deals with pension case flow in the PDA Section of CCA Offices. 4.1 Allotment of Pension Case The pension case coming from Pension Sanctioning Section will appear in the Allotment tab of the AO PDA Section. (AO PDA Login Allotment Allocate to PDA- DH) AO PDA will allot the case to the Dealing Hand of the PDA section. (Fig.4.1) Fig.4.1 Now Case will flow to DH PDA Section. (DH PDA Login Action PDA Section Sanction Order Received). DH should take the printouts of digitally signed EPPO(2 copies) Commutation Sanction, Gratuity Sanction, PPO Authority Model in 5 sets from the links available on the page, as shown in the screenshot below. After AO PDA approves and sends the case to PFMS for payment, the above hardcopies (1 set each) needs to be physically sent to HoO section, Pension section, Cash section and Pensioner. One copy will remain with PDA section. Now the DH (PDA Section) will select the case and click on Receive Sanction Send to Bill button. At this stage the name of the employee will appear in the name column of the table as shown below. 74 Comprehensive Pension Management System User Manual (Version 6.2) Fig.4.2 4.2Vendor Verification in PFMS Now, from the DH login of PDA Section go to Action then Vendor Verification (DH PDA Login Action Vendor Verification). The case will appear here in the name of pensioners claimants. From PFMS the pensioner s claimant s (Vendor) bank credentials will be verified and the status with error description in the table will be shown as in Fig. 4.3 below. This process takes approximately 5-10 minutes and when the verification is complete, the case will automatically go to Bill Generation Tab. Otherwise failed cases will remain at this stage which may be Resend. In case the failure is due to wrong Bank account details, the same will be flashed in the Error Description column. The wrong Bank account details can be rectified from the PDA Utility tab in the AO PDA (Go to AO PDA login, open PDA Utility tab, enter PPO no. of the particular case and edit the Bank details as shown in Fig. 4.4(a) and Fig. 4.4(b)). 75 Comprehensive Pension Management System User Manual (Version 6.2) Fig.4.3 If the case has failed because of any reason other than wrong bank details, the case may be Resend. Fig.4.4 76 Comprehensive Pension Management System User Manual (Version 6.2) Fig.4.4(a) Fig.4.4(b) After completion of Vendor Verification in PFMS the bill will move to the next section of DH log-in of PDA Section i.e. Bill Generation. 4.3Bill Generation From the DH log-in of PDA Section go to Others Bill Generate (DH PDA Login Bill Generation Others Bill Generation). Bill other than monthly bills like Commutation, DCRG, etc. will appear at this Tab. Here the bills are appearing in the name of claimants who will get the payment through PFMS. Now select the case from the check box and click Generate Bill Button. The bill will be generated and it will go to AAO of PDA Section. 77 Comprehensive Pension Management System User Manual (Version 6.2) Fig.4.5 AAO of PDA Section will log-in and select the case by clicking the check box corresponding to the case and click on Approve button (AAO PDA Login Approval Others Bills Approve). The case will go to AO of PDA Section for approval. Fig.4.6 Now, AO of PDA Section will log-in and go to approval then other bills (AO PDA Login Approval Others Bills). The AO will then select the appropriate Account Head Code from the drop down list on the top. After entering the Not Payable Before Date click on Approve button. Now the Bill will automatically go to PFMS for payment. After this, the 5 copies of digitally signed Sanctions (taken earlier by DH) should be sent to the HoO section, Pension Section, Cash Section, Pensioner and 1 copy retained in PDA section (as mentioned on Page 71, sub heading 4.1 of this manual). 78 Comprehensive Pension Management System User Manual (Version 6.2) Fig.4.7 4.4PFMS Payment Now go to PFMS and log-in with PAO credential. Select the Bill(s), Generate Payment Batch File and sign Digitally with the DSC. If the amount of the payment is more than 10 lakhs, signatory 2 is required for making the payment. The Payment screens are given below. Fig.4.8 79 Comprehensive Pension Management System User Manual (Version 6.2) Fig.4.9 Fig.4.10 80 Comprehensive Pension Management System User Manual (Version 6.2) Fig.4.11 It is recommended that above payment from PFMS should be done on the same day, when AO PDA sends the case to PFMS. After successful payment from PFMS the cases will appear LC DLC Verification tab of PDA DH. PDA AO can view, if required, the PFMS transaction report of pensionary benefits of the pensioner claimant, in the Reports Tab Other Bill Report. Thereafter the cases will be processed as per the arrear section, below. 4.5 Arrears After successful payment, login from DH PDA and click the tab LC DLC Verification (DH PDA Login LC DLC Verification LC Verification). 81 Comprehensive Pension Management System User Manual (Version 6.2) Fig.4.12 Now click on Verify link shown in the Action column, a popup window will appear as shown in the figure below. Here we have to select the Life Certificate and Non Employment (FMA also, in case of CDA pensioners) Validity, From and to dates (From date will be next date, from the date of retirement). After filling the details click on Confirm button. (Note-The above certificate date is for non-employment of pensioner) Fig.4.13 After LC DLC the case will go in Arrears tab (PDA DH login Action Bill Generation Arrears) 82 Comprehensive Pension Management System User Manual (Version 6.2) Here select the Reason for Arrear and fill the PPO Number of pensioner, then click on the Search button. Now it will show the Arrear calculation. After checking the calculation, printout of the arrear details should be taken and the soft copy of the same saved (by clicking ctrl P). Now click on the Save Send for Approval button. Then case will go to AAO of PDA Section for approval. (The above printout will be used as Arrear Sanction for PFMS payment). AAO PDA will select the case (by clicking the check box corresponding to the case) and click on Approve button (PDA AAO login Action Approval PDA Arrears). The case will go to AO of PDA Section for approval. 83 Comprehensive Pension Management System User Manual (Version 6.2) Now, AO PDA (PDA AO login Action Approval PDA Arrears) will select the appropriate Account Head Code from the drop down list. Now select the case (by clicking the check box corresponding to the case) and enter Not Payable Before Date and click on Approve button. Now the Bill will automatically go to PFMS for arrear payment. After Arrears payment, the case will go to monthly bill section (PDA DH login Action Bill Generation Monthly bill (Normal Family, IDA CDA)). 4.6 Monthly Bill While processing the case for monthly bill the income tax deducted shall be filled and accordingly the bill shall be processed. The income tax deducted shall be used for feeding income tax on monthly and quarterly basis. A printout of the monthly bill shall be taken and handed over to AO Cash conveying the sanction. Thereafter the cases in DH can be passed to AAO and further to AO for approval. Then AO will send the case for monthly bill pension payment, through PFMS. This cycle will be repeated for every month. 84 Comprehensive Pension Management System User Manual (Version 6.2) Fig.4.14 Fig.4.15 85 Comprehensive Pension Management System User Manual (Version 6.2) PDA AO can view the PFMS arrear transaction report of the pensioner claimant, in the Reports Tab Other Bill Report. (Fig 4.16) Fig.4.16 CHAPTER 5 5. Retiree Module This chapter deals with Pensioner s login and the steps to fill and submit the form. 5.1Retiree Dashboard Retiring officer official can see the progress of his her application at the top of the dashboard (Fig 5.1) Milestones completed will be shown in GREEN colour dots and the pending ones with RED colour dots. Retiree can see the pensionary benefits and lodge their grievance, if any, using the dashboard. Retiree can see his her pension details by clicking on pension ledger. 86 Comprehensive Pension Management System User Manual (Version 6.2) Fig 5.1 5.2 Fill Submit Forms Retiree can fill the forms by following the following steps: 5.2.1Pensioners Details Pensioner Retiree should login into CPMS using PAN and Password received on SMS (NOTE: In case of Family Pension the Retiree Profile will be created by the SSA office itself). Click on Pensioners Details Profile. Fig 5.2 87 Comprehensive Pension Management System User Manual (Version 6.2) Fig 5.2 Few details will be pre-populated in the profile of the retiree and rest of the details will be filled by the retiree (In case there is any discrepancy in the pre-populated details of the retiree, he she may write to SSA unit and get the same rectified). Personal Details Tab: Click on Personal Details Tab and fill the same. After filling the same click on Save button and move on to next tab Commutation and FMA . Fig 5.3 88 Comprehensive Pension Management System User Manual (Version 6.2) Fig 5.3 Commutation and FMA Tab: 89 Comprehensive Pension Management System User Manual (Version 6.2) In this tab user will fill the FMA and Commutation details. The commutation percentage can be maximum 40 . Fig 5.4 In case FMA is applicable, retiree needs to select his her area of residence as CGHS or non- CGHS. Other requirements may also be filled in. Fig 5.4 Family Details Tab: Retiree will fill information about his her family members in this tab. Then the Retiree should fill the nominations, alternate nominees etc. and keep clicking save button to move on to the next tabs. Fig 5.5 The Retiree should NOT fill his her own details in this tab. 90 Comprehensive Pension Management System User Manual (Version 6.2) Fig 5.5 Nomination Form 1: Fig 5.6 Fig 5.6 91 Comprehensive Pension Management System User Manual (Version 6.2) Nomination Form A Fig 5.7 Fig 5.7 Bank Details Tab: Retiree will fill the bank details in this tab. He she should be careful while entering these details as this is important information for the pension disbursement. Fig 5.8 After filling the bank details, bank undertaking will be printed (button provided on screen as Print Bank Undertaking). Pensioner will himself herself sign the same and also get signed from bank authorities. Retiree will then upload the signed Bank Undertaking on CPMS (upload a file). 92 Comprehensive Pension Management System User Manual (Version 6.2) Fig 5.8 IMPORTANT: -Pensioner profile prompts the pensioner to enter bank details. The pension and pensionary benefits of the pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Pensioner, and then uploaded on CPMS. Check List Tab: The Retiree will fill the Check List as per the criteria and scenarios. SL. No. 2, 7, 9 cannot be NO. Retiree need to fill them carefully. Fig 5.9 Fig 5.9 Click on Final Submission tab whereupon the case will be finally saved. Once finally submitted, the retiree will not be able to change the data. Hence the details should be thoroughly checked before submission. Fig 5.9 93 Comprehensive Pension Management System User Manual (Version 6.2) The Retiree shall take print out of the forms-form 5, form 3, Nomination forms and Form 1A-and after signing duly submit them with enclosures as mentioned in the checklist of form 5 to HoO. 5.3 Updation of Mobile, Email and address For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option Edit Profile (Fig(5.10)). Fig (5.10) A Pop-up window (Fig(5.11)) will be displayed with the option to choose the following details which the user wants to edit or change: - 94 Comprehensive Pension Management System User Manual (Version 6.2) Fig (5.11) The user has to select the option which he she wants to change or modify. 5.3.1 Mobile Number Update Upon selecting Mobile Number , the following screen will be displayed (Fig(5.12)) Fig(5.12) Retiree can enter his her mobile number and then select either his her registered email ID or the entered mobile number to receive an OTP to verify the number (Fig (5.12)). Upon receiving the OTP, retiree should enter the OTP (Fig (5.13)) and save which will then update the mobile number. 95 Comprehensive Pension Management System User Manual (Version 6.2) Fig (5.13) 5.3.2 Email ID Update Upon selecting Email ID , the following screen will be displayed (Fig(5.14)) Fig (5.14) Retiree can enter his her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number (Fig (5.14)). Retiree should then enter the OTP received and click on save (Fig (5.15)) which would then update the email ID. 96 Comprehensive Pension Management System User Manual (Version 6.2) Fig (5.15) 5.3.3 Address Update To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered (Fig (5.16)). Fig (5.16) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. 97 Comprehensive Pension Management System User Manual (Version 6.2) 5.4 Lodge Grievance Retiree can login and raise his her Grievance related to pension, if any. Retiree Login Pensioners detail Grievance. Retiree can select the Grievance Type from the dropdown and add the description about it. (Fig 5.17) Fig 5.17 Retiree can also upload the attachment related to the Grievance, if any. (Fig 5.18) After filling all the details, Retiree will click on Submit button.(Fig 5.19) 98 Comprehensive Pension Management System User Manual (Version 6.2) Fig 5.18 Fig 5.19 Retiree shall be able track the status of his grievance from grievance history.(Fig 5.19) 5.5 View documents and Ledger Retiree can access ePPO and DCRG sanction along with ledger at any point of time. Also, he can access any corrigendum revision authority on his dashboard as well. (Fig 5.20) 99 Comprehensive Pension Management System User Manual (Version 6.2) Fig 5.20 5.6. Income Tax Module 5.6.1 Proposed Investment Declaration Proposed Declaration: All the pensioners drawing pension via SAMPANN are to submit the proposed declaration forms of investment and other information for availing income tax rebate to the respective pension paying branch by 15th April each year. They can submit by filling the online form from their Dashboard or fill a physical copy and send to Concerned CCA office. In case of online filling of proposed form, no physical copy needs to be sent to the CCA office. The pensioners who fail to submit the proposed declaration in time schedule mentioned above but submit subsequently on a later date, the tax deduction shall get impacted after receipt of the declaration. No proposed declaration would be entertained however after 9th October. After that Actual declaration shall submitted by the pensioner. Pensioner has to take following steps in order to fill the income tax declaration in SAMPANN application: 100 Comprehensive Pension Management System User Manual (Version 6.2) 1. Pensioner shall open SAMPANN website- www.dotpension.gov.in and login using his her credentials as screen shown in the below Fig(5.21). A B C D Fig (5.21) Now Pensioner has to click on the Pensioner Details- Investment Declaration link shown in below Fig (5.22) Pensioner will have the option to submit proposed declaration or actual declaration. 101 Comprehensive Pension Management System User Manual (Version 6.2) Fig(5.22) User has to click on the 1st Fill Proposed Investment button shown in Fig (5.23) and fill his proposed investment declarations details. Fig (5.23) 102 Comprehensive Pension Management System User Manual (Version 6.2) Document upload is not mandatory in case of Proposed Investment Declaration. Also proposed declaration can be submitted by the pensioner before 15 April. In case multiple declarations are submitted, latest one shall be taken into account. Fig (5.25) 103 Comprehensive Pension Management System User Manual (Version 6.2) Fig (5.25) After entering the Proposed Investment Declarations, the below screen will be shown. Here Retiree can see his filled information by clicking on the View link shown in grid under Submitted Declaration title shown in Fig (5.26). Fig (5.26) 104 Comprehensive Pension Management System User Manual (Version 6.2) After click on View link below screen will shown Fig (5.27). From this screen Retiree can take the print of this page. Fig (5.27) 5.6.2 Actual Investment Declaration Actual declaration: All the pensioners drawing pension via SAMPANN are to submit the actual declaration forms of investment and other information for availing income tax rebate to the respective pension paying branch by 10th October each year. They can submit by filling the online form from their Dashboard. However, they can also fill a physical copy available on Pensioners Dashboard and send to Concerned CCA office. If no actual declaration is received by 10th October then the proposed declaration, if received, shall stand nullified. Also, Actual declaration submitted later will be taken into cognizance in the monthly bills remaining to be processed. Hence, no actual declaration shall be accepted beyond 15th Feb . Fig (5.28) Pensioners should fill Actual declaration of investment as indicated below: - Fig (5.28) 105 Comprehensive Pension Management System User Manual (Version 6.2) Fig (5.28) After selecting the Actual Investment Declaration option the following form will appear: - Fig (5.29) 106 Comprehensive Pension Management System User Manual (Version 6.2) Fig (5.29) 107 Comprehensive Pension Management System User Manual (Version 6.2) Fig (5.30) In this Actual investment declaration form, Pensioner may upload proof of investment savings. All documents required as proof of saving (Investment) should be uploaded. They can be uploaded against each entry or as one file against any one of the fields. After entering the Actual Investment Declarations, the below screen will be shown. Here Retiree can see his filled information by clicking on the View link shown in grid under Submitted Declaration title shown in Fig (5.30) . 108 Comprehensive Pension Management System User Manual (Version 6.2) Fig (5.30) The proposed Actual declaration shall be received by CCA Office and there after pensioner may view the admitted declaration. In case of any issue, fresh declaration may be submitted. The reason for change in submitted Admitted document shall be available in remarks. It may be noted that fresh declaration can be submitted only when pensioner s last declaration has been admitted. Proposed Actual declaration submitted by the pensioner shall be available for view accounting to PDA section. Now Retiree can see the Approved Calculation sheet on Dashboardby click on the View link shown in the grid under Admitted Declaration title shown in Fig 5.30. 109 Comprehensive Pension Management System User Manual (Version 6.2) CHAPTER 6 6.Grievance Management This chapter deals with the Grievance Management module, where the Retiree can file his her Grievance related to pension. 6.1 Pensioner Grievance Retiree can login and raise his her Grievance related to pension, if any. Retiree Login Pensioners detail Grievance. Retiree can select the Grievance Type from the dropdown and add the description about it. (Fig 6.1) Retiree can also upload the attachment related to the Grievance, if any. (Fig 6.2) After filling all the details, Retiree will click on Submit button. The concerned section in CCA office HoO will be displayed as shown at point no. 3. (Fig 6.1) After submission, the Grievance will be received by Account Officer (AO) of concerned section HoO as applicable and the history will be maintained in Grievance History section. Fig (6.3) point 6. Fig 6.1 110 Comprehensive Pension Management System User Manual (Version 6.2) Fig 6.2 Fig 6.3 6.2 Allotment of Grievance to DH Once the Grievance is received at AO (Concerned Section) level, he she will assign that Grievance to the respective DH (Concerned Section). (Fig 6.4) 111 Comprehensive Pension Management System User Manual (Version 6.2) AO Login Grievance Management Assign Grievance. AO (Concerned Section) to select the respective DH (Concerned Section) has to click on Grievance check box and then click on Assign button. (Fig 6.4) After all the aforementioned steps, Grievance will move to respective selected DH (Concerned Section). Fig 6.4 6.3 Grievance Resolution Once the Grievances received at DH (Concerned Section) DH will take the required action on the grievance. DH Login Action Grievance Management Resolve Grievance. (Fig 6.5) However, before settling the grievance, the DH (Concerned Section) should get approval of competent authority in physical file also. History of Grievance will be shown in Grievance history with status Recent or Settled . AO (concerned section) will be able to see it in Settled Grievance. The Pensioner will get Notification on Pensioner Dashboard once the Grievance is settled or any other action taken on the Grievance. 112 Comprehensive Pension Management System User Manual (Version 6.2) Fig 6.5 113 Comprehensive Pension Management System User Manual (Version 6.2) CHAPTER 7 7. DSC Registration Following steps need to be follow for registering DSC on the System: - Note: DSC is required only by AO(Pension) in CPMS software. As this configuration is specific to usage of Digital signature in CPMS software only it is advisable to do this configuration only in exclusive computer in which other software using DSC is not used. Step 1: Check whether Java (any version) is installed in the System or not. In order to check the same, follow the below screenshots. Go to the control panel. Step 2: Now click on the Uninstall a Program link, shown below the program link (highlighted in the below screen) 114 Comprehensive Pension Management System User Manual (Version 6.2) Step 3: If java is already installed in the system, then 2 (or more) icons will be seen as highlighted in the screenshot below. If Java is not installed, then go to Step 5. Step 4: Now right click on the icon and you see the Uninstall link, click on the link and uninstall the same. Then you see the below screen. 115 Comprehensive Pension Management System User Manual (Version 6.2) 116 Comprehensive Pension Management System User Manual (Version 6.2) Now after Uninstall, icons are removed and you see the below screen. 117 Comprehensive Pension Management System User Manual (Version 6.2) Step 5: Now we have to install the latest version of java in the system. For this click on the below link. Under Java SE Development Kit 8u191 download following 2 files jdk-8u191-windows-i586.exe (32 bit) (197.34 MB) and jdk- 8u191-windows-x64.exe (64 bit)(207.22 MB) installer file. (https: www.oracle.com technetwork java javase downloads jdk8-downloads- 2133151.html) 118 Comprehensive Pension Management System User Manual (Version 6.2) Step 6: Select Accept License Agreement in the dialogue box shown and download the both file mention above and shown in the above screen. Step 7: Run both the .exe files, one by one, and install the downloaded versions. 119 Comprehensive Pension Management System User Manual (Version 6.2) 120 Comprehensive Pension Management System User Manual (Version 6.2) Step 8: After successfully installation of 2 Java versions, confirmation screens will appear. Thereafter close the screen. Step 9: After installation, you will see the newly installed icons of Java in the control panel, as shown below. Step 10: Now open control panel. 121 Comprehensive Pension Management System User Manual (Version 6.2) Step 11: Then click on System and Security link in Control Panel as shown in the below screen. Then click on the Windows Defender Firewall link on the right hand side. 122 Comprehensive Pension Management System User Manual (Version 6.2) Step 12: Now click on the Turn Windows Defender Firewall on or off link shown in the left hand side. Step 13: Now select the Turn Off options (in both rows) shown in the below screen. After this save the settings. 123 Comprehensive Pension Management System User Manual (Version 6.2) Step 14:Open Control Panel Programs. Then click on Java link. Now following popup window will appear. Step 15: Now click on the Security tab. Step 16:Under Security tab, click on Edit Site List button and add CPMS url (https: dotpension.gov.in) in the Exception Site List. Step 17: Now go to C: Program Files (x86) - Right Click on Java folder - Click Properties 124 Comprehensive Pension Management System User Manual (Version 6.2) Step 18: Now a popup window will appear. Now go to security tab and click on the Edit button. Step 19: The following popup window will appear. Now select the Users (in the Group or User Names Box on the top of popup window). Select Allow checkbox in front of the Full Control (in the permission for Users box) and click Ok button. 125 Comprehensive Pension Management System User Manual (Version 6.2) Step 20: Now go to C: Program Files. Repeat step 17 to step 19 for this folder as well. Step 21: Now, close all windows. Go to My Computer This PC - Local Disk (C: Program Files (x86) java jre1.8.0_191 lib security java (Policy File) 126 Comprehensive Pension Management System User Manual (Version 6.2) Then open java.policy file in notepad and copy the following text and paste it before sign }; in the last line of the file and save the file before close it. permission java.security.AllPermission; permission java.security.SecurityPermission\"putProviderProperty.SunMSCAPI\"; permission java.security.SecurityPermission \"authProvider.SunMSCAPI\"; permission java.net.SocketPermssion \" \",\"accept,connect,resolve\"; NOTE: After opening the file in notepad, please click on the format menu. If the word wrap is OFF, then make it ON by clicking on it. As appear in the following screenshot. 127 Comprehensive Pension Management System User Manual (Version 6.2) Step 22: Now, close all windows. Go to My Computer This PC - Local Disk C: Program Files (x86) java jdk1.8.0_191 jre lib security java (policy file) in notepad and copy the following text and paste it before sign }; in the last line of the file and save the file before close it. permission java.security.AllPermission; permission java.security.SecurityPermission\"putProviderProperty.SunMSCAPI\"; 128 Comprehensive Pension Management System User Manual (Version 6.2) permission java.security.SecurityPermission \"authProvider.SunMSCAPI\"; permission java.net.SocketPermssion \" \",\"accept,connect,resolve\"; NOTE: After opening the file in notepad, please click on the format menu. If the word wrap is OFF, then make it ON by clicking on it. (As done previously) Go to My Computer This PC - Local Disk C: Program Files Java. Repeat steps 21 and 22 for the two folders jre1.8.0_191 and jdk1.8.0_191 (path is as follows C: Program Files Java jre1.8.0_191 lib security java.policy and C: Program Files Java jdk1.8.0_191 jre lib security java.policy). Step 23: Now after completion of the above steps, Java installation on the System is complete. Step 24:Open www.dotpensio.gov.in and go to Browser security settings internet options security tab click on green color Trusted sites icon click on Sites button click on add button and close the windows. 129 Comprehensive Pension Management System User Manual (Version 6.2) 130 Comprehensive Pension Management System User Manual (Version 6.2) Step 25: Now login on CPMS Portal (www.dotpension.gov.in) with AO credentials on Internet Explorer browser, insert DSC in the PC and then click on User - DSC Registration menu. Click on Run this time on the popup button at the bottom of the screen. 131 Comprehensive Pension Management System User Manual (Version 6.2) Step 26: Now click on the View Certificate Detail button. It shows the details of the respective DSC. Enter Enrollment valid from Enrollment valid upto date (if actual dates are known then same should be entered. If actual dates are not known then dates between Certificate from upto should be entered). After that click on Enroll Button. 132 Comprehensive Pension Management System User Manual (Version 6.2) After clicking on the Enroll button the Enrolled DSC details will appear in the DSC Signatory Details box with the status - created. Step 27: After the above steps DSC installation at the user end is completed. Now request has to be sent to support.cpms-dot gov.in (or call on CPMS Helpline Nos) for activating DSC. Step 28: In case the officer, whose DSC is enrolled, is temporarily or otherwise not available then the new officers DSC has to be register. For this the previous DSC has to be deactivated. Steps for deactivating the old DSC and registering the new DSC are as follows: Request support.cpms-dot gov.in (or call on CPMS Helpline Nos) for deactivating old DSC. CHAPTER 8 8.1 Annexure 1 (Side Channel User Creation) 133 Comprehensive Pension Management System User Manual (Version 6.2) Login from CCA User-id and go to Users- User Registration tab. Click on the Add New button shown there. Then a new window will open shown in Fig 8.0. From the drop down menu, Select the Head of Office (SSA Unit) for side channel user. Fig 8.0 134 Comprehensive Pension Management System User Manual (Version 6.2) Fig 8.1 After selection you see the list as shown in above screen. From that list, select the option which ends with Telecom Circle and having the SSA Unit Code starting with 9 which is the unique identifier for the side channel user shown in Fig 8.1. Now enter the user-id of HoO as per the format given (hooccaXXXsc gov.in) in Email Id box (this is a user name only and no need to fill valid email ID) ,Mobile No. and select the Menu List Options give below and save the record. Now, once the user is created, login from that particular user id, fill the mandatory information. Also you can change the default password from there and save the record again. Once the HoO id for Side channel is created, then login from the same user-id, go to Users - User Registration , click on the ADD NEW button and create the next hierarchy i.e. DH for the Side channel of SSA unit. Once the DH user is created, click on the Authorization lock icon shown in the grid and assign the necessary menu rights to the user shown in Fig 8.3. 135 Comprehensive Pension Management System User Manual (Version 6.2) Fig 8.2a Fig 8.2b After giving the rights, login from the DH user-id and default password and fill the necessary info and save the record. Now user will be able to process case from side channel. 8.2 HoO Unit- Side channel (Steps for Processing a Case) This chapter deals with flow of pension papers in the CCA office when the case is processed through Side channel on behalf of the HOO unit and the pensioner to enable creation of digital profile of pensioner. 136 Comprehensive Pension Management System User Manual (Version 6.2) 8.2.1Normal Pension Case 8.2.1.1 Creation of Retiree Profile DH has to login to create the retiree profile. Select Retiree details on the Menu options available on the left panel. Click on Add New button at the right top corner of Retiree Profile page to get the form to fill. Enter the following information on retiree detail form (underlined parameters are the mandatory fields): q. Title r. First Name s. Middle Name t. Last Name u. Type of Retirement v. Height w. Father s Husband s Name. x. Mother s Name y. Date of Birth z. Gender aa. Aadhaar Number bb. PAN Number cc. Mobile Number (This should be an Active Number) dd. Email ID ee. Identification Mark 1 ff. Identification Mark 2 Following details to be filled in other details section (All are mandatory fields): h. Designation i. Group j. Date of Joining k. Date of Retirement l. Date of Superannuation m. Govt. Accommodation provided by Directorate of Estates BSNL at any time of service: YES NO (to select from the dropdown) n. Pay Commission (this should be the Pay Commission corresponding to the last pay drawn) After filling the aforementioned information, DH to click on Save button. By using the Clear button, User can also refresh the form and fill in fresh information. (Fig 8.3) 137 Comprehensive Pension Management System User Manual (Version 6.2) IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore, due diligence should be exercised while creating the Retiree Profile. In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk. Fig 8.3 Creation of the Retiree Profile is the most important step in the process, so it should be ensured entries made therein are correct in all respects. After saving the information, it is recommended a printout of the page be taken from the screen and Vetted by HoO level User before proceeding ahead in Service Verification. If any error is detected, then retiree profile be edited before initiating 8.2.1.2. (further updates in chapter 11) 8.2.1.2 Service Book Verification It may be noted while the case is processed via side channel, the timelines related to lose their relevance. Based on the S B received in office, same may be filled in the module by DH. If some portion remains unverified it may be filled in remarks column. There shall be only one level passing. 138 Comprehensive Pension Management System User Manual (Version 6.2) 8.2.1.3 Send Form to Retiree At this stage after DH sends form to retiree, the digital profile of pensioner will be created. There shall be only one level passing. 139 Comprehensive Pension Management System User Manual (Version 6.2) 8.2.1.4 Form Received Prior to Form received stage, DH shall be required to fill up the forms as per documents submitted by the pensioner. Before submission the final submit button, it may be checked and vetted that the details are correctly filled. If any error is detected however at the form received stage, then the case may be returned for refilling. It may be noted that retiree will not be required to fill the forms. This activity shall be completed by DH. For filling the form go to Form Received , where you find the record under status Cases pending at the Pensioner level which is default selected. Now click on the Edit button (Pencil Icon) shown in the last Action column and fill all the detail of Pensioner very carefully. Fig 8.4 Fig 8.4 After filling up the profile, on the same Form Received page select the option Cases submitted without Physical Copy from Status dropdown list shown in Fig 8.5, select the record click on send for approval button. The case will directly land to Form 7 for processing by skipping the step for Form Verification . There shall be only one level passing. 140 Comprehensive Pension Management System User Manual (Version 6.2) Fig 8.5 ******* Form Verification THIS STEP SHALL NOT BE AVAILABLE IN SIDECHANNEL i.e. SKIPPED ******* Form 7 DH to verify and fill the required information in Form 7. Some information in Form 7 is auto populated. Others have to be entered. (Fig 8.6) ee) Name of the retiring Government Employee ff) Father s Husband s Name gg) PAN NO. hh) Height Marks of identification ii) Date of Birth jj) Service to which he she belongs (indicate name of Organised service, if any, otherwise say General Central Service) kk) Particulars of post held at the time of retirement ll) Name of the office mm) Post held nn) Scale of Pay Pay Band Grade pay of the post oo) Basic Pay Pay in the pay band Grade pay. pp) Basic Pay Pay in the pay band Grade pay qq) Whether the appointment mentioned above was under Government or outside the Government on foreign service terms? rr) If on foreign service, scale of pay pay band, pay in the pay band and grade pay of the post in the parent department. 141 Comprehensive Pension Management System User Manual (Version 6.2) ss) Whether declared substantive in any Post under the Central Govt.? tt) Date of beginning of service uu) Date of ending service vv) Cause of ending service ww) In case of compulsory retirement, the orders of the competent authority whether pension may be allowed at full rates or at reduced rates and in case of reduced rates, the percentage at which it is to be allowed (Please See Rule 41) xx) In case of removal dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41) yy) Particulars relating to military service, if any. zz) Particulars relating to the service in autonomous body, if any. aaa) Whether any Departmental or judicial proceedings in terms of rule 9 of the CCS(Pension) Rules, 1972 are pending against the retiring employee. (lf yes, in terms of Rule 69, provisional pension will be admissible and gratuity will be withheld till the conclusion of departmental or judicial proceedings and issue of final orders) bbb) Length of service vii. Details of omission, imperfection or deficiencies in the Service Book which have been ignored [under rules 59(1) (b) (ii)] viii. Period not counting as qualifying service. ix. Additions to qualifying Service. x. Whether any leave without pay. xi. Net Qualifying service. xii. Qualifying service expressed in terms of complete six monthly periods (Period of three months and above is to be treated as completed six monthly periods (Rule 49) ccc) Emoluments c. Emoluments in terms of Rule33. d. Emoluments drawn during ten months pending retirement. ddd) Others: Details of Govt. dues recoverable out of gratuity eee) Licence fee for Govt. accommodation see sub-rules (2), (3) and (4) of rule 2) fff) Dues referred to in Rule 73 ggg) Amount indicated by Directorate of Estates to be withheld under Sub rule(S) of Rule 72 hhh) Post-retirement address of the retiree 142 Comprehensive Pension Management System User Manual (Version 6.2) 143 Comprehensive Pension Management System User Manual (Version 6.2) Fig 8.6 DH shall fill up the form 7 as per form 7 received from the HoO. It may be noted if there is any change from the form 7 received from HOO based on final papers received, Same may be fed. There shall be only one level passing. ******* Form 8 THIS STEP SHALL NOT BE AVAILABLE IN SIDECHANNEL i.e. SKIPPED ******* Send to PAO After all the aforementioned steps, DH will submit the form to Pension Section by clicking on Send to PAO . While sending this please ensure that all the documents mentioned in checklist are being sent. The papers may then be dispatched by post. This will be the only step in which two level passing will be there. For cases in which the final papers have not been received, the case shall not be processed beyond AE in Pension Section Module. 144 Comprehensive Pension Management System User Manual (Version 6.2) ******* View Forms All users can view the list of all the retirees and their generated forms. Printout of Form7 and Form 8 can be taken by clicking on Print and sent to Pension Section of concerned CCA office. (Fig 8.7) Fig 8.7 8.2.2 Family Pension Case There shall be no change in the treatment of family pension case while processing through Side Channel. While feeding the forms, all the details in S B module, form 12 14 and 18 - shall be filled based on the forms submitted by the retiree. 8.2.2.1 Personal Detail DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH Login Action Family Pension Personal Details)(Fig 8.8) Following information need to be entered on personal detail page: s. Title t. First Name u. Middle Name v. Last Name w. Type of retirement. x. Height y. Father s Husband s Name. z. Mothers Name aa. Date of Birth 145 Comprehensive Pension Management System User Manual (Version 6.2) bb. Gender cc. Aadhaar Number dd. PAN Number ee. FP Mobile Number ff. FP Email ID gg. FP Identification Mark 1 hh. FP Identification Mark 2 ii. Employee Code jj. Office Following details to be filled in Other Details section (All are mandatory fields): h. Designation at the time of death i. Group j. Date of Joining k. Date of Death l. Date of Superannuation m. Govt. Accommodation provided by Directorate of Estates BSNL at any time of service: YES NO (to select from the dropdown) n. Pay Commission After filling the aforementioned information, DH to click on Save button. Once filled the details cannot be changed so due diligence should be exercised before saving. (Fig 8.8) To expedite the disbursement of pensionary benefits, Family Pensioner should immediately be advised to submit the required information along with relevant documents to the HoO. Family Pensioners may also be advised to provide mobile phone number (mandatory) so that they can get updates on the pension process and pension disbursement. 146 Comprehensive Pension Management System User Manual (Version 6.2) Fig 8.8 After saving the details, an instance will be created in the Personal Details tab, as shown in the Fig 8.9 Fig 8.9 Then DH has to click on Govt. Emp Details edit tab, and proceeds to the subsequent screen. The next screen will show three tabs viz. Personal details, Family Details and Nomination Form1 (Fig 8.10) 147 Comprehensive Pension Management System User Manual (Version 6.2) Fig 8.10 Now the DH has to fill in details of Family and Nominations (Fig 8.11, Fig 8.12, Fig 8.13) Fig 8.11 148 Comprehensive Pension Management System User Manual (Version 6.2) Fig 8.12 Fig 8.13 After filling all the details, the DH will submit the form by clicking submit button. 8.2.2.2 Service Verification (Family Pension case) DH to select Service Verification tab from the Menu options. DH to check the form and send it for approval to HoO for service verification. Before this is done, physical service book verification needs to be done as well. Any 149 Comprehensive Pension Management System User Manual (Version 6.2) unverified portion of the service book should be noted and attached in the file being sent to Pension Section (CCA Office). (Fig 8.14) HoO to login and approve the service book verification. HoO also to verify the service book physically. HoO can also return the form to DH in case any discrepancy is found. Then DH will again have to verify and send the form to HoO for approval. Fig 8.14 8.2.2.3 Form 14 DH to verify and fill the required information in Form 14. DH may select the claimant who is eligible for pension. This is to be done as per CCS Pension Rules,1972. 14. The Form shows the claimant details, the bank details, the documents to be uploaded like photograph, signature and death certificate etc. The Bank Undertaking Form also needs to be uploaded duly signed by bank authorities as well the Family Pensioner. FMA option has to be chosen (if applicable and desired by the Family Pensioner) (Fig 8.11 (a), Fig 8.11 (b) and Fig 8.11 (c)). It should be seen that the photograph and signature are clear and visible. DH clicks on Form 12 tab. 150 Comprehensive Pension Management System User Manual (Version 6.2) Fig 8.11 (a) 151 Comprehensive Pension Management System User Manual (Version 6.2) Fig 8.11 (b) Fig 8.11 (c) IMPORTANT: -Form 14 prompts the user to enter bank details. The pension of the family pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Family Pensioner, and then uploaded on CPMS. 152 Comprehensive Pension Management System User Manual (Version 6.2) 8.2.2.4 Form 12 DH to verify and fill the required information in Form 12 for each claimant who has been nominated for gratuity in the nomination form filled earlier. Here DH has to click on the Edit button against the name of the nominee, and his her details will be populated in the Form 12 (Fig 8.15). Again Bank Account details and claimant s details, picture and signature needs to be uploaded. After updation of Form 12 and 14, the case will appear in Form 18 tab of DH. Fig 8.15 IMPORTANT: - Form 12 prompts the user to enter bank details. The DCRG of the family pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank 153 Comprehensive Pension Management System User Manual (Version 6.2) authorities and Family Pensioner, and then uploaded on CPMS. In case of claimant who is claimant for only Gratuity and not pension, only Mandate form, as generated, needs to be filled by the pensioner and uploaded in portal. In case where there is no nomination, DCRG has to be divided in equal shares among the family members. Accordingly, the nomination form and Form 12s will be filled. 8.2.2.5 Form 18 DH to verify and fill the required information in Form 18. Some information is auto populated in Form 18. DH should be careful while filling the important details like qualifying service, non-qualifying service period, last month s pay details etc. Pensionary benefits will be calculated on basis of these figures and hence figures must be cross-verified from service book. (Fig 8.16) DH will send Form 18 for approval of HoO. HoO will approve Return the form. HoO should verify the Pensionary benefit amounts. If the amounts are correct, he she can approve it. If incorrect, HoO can return the case back to DH for rectification. If HoO approves the form it will be processed further. In case of Return, form will be sent back to DH for reverification and thereafter process followed for approval. 154 Comprehensive Pension Management System User Manual (Version 6.2) 155 Comprehensive Pension Management System User Manual (Version 6.2) Fig 8.16 Form 18 and 19 to be put up in physical file for approval of competent authority. IMPORTANT: -Form 18 calculates the pensionary benefits and pension as applicable to the Family pensioner. Once this form is filled and next stage initiated it cannot be edited in HoO section. Therefore, due diligence should be exercised while filling in all important fields like Pay Band, pay level, Qualifying Non Qualifying service etc. In case Wrong or incorrect information is saved in the system, please immediately inform the Helpdesk. After processing the form 18 from DH level, if HoO user is satisfied then a printout may be taken and approval and signature of competent authority may be taken. If any error is detected in the process, then HOO may return to DH the form 18 for necessary correction. DH shall correct it and send it to HoO user. The approval of competent authority shall be taken. 156 Comprehensive Pension Management System User Manual (Version 6.2) 8.2.2.6 Send to PAO After all the aforementioned steps, DH will submit the form to Pension Section (CCA Office) by clicking on Send to PAO . For cases in which the final papers have not been received, the case shall not be processed beyond AE in Pension Section Module. 8.2.2.7 View Forms All users can view the list of all the retirees and their generated forms. (Fig 8.17) Fig 8.17 157 Comprehensive Pension Management System User Manual (Version 6.2) Chapter 9 9. ID Card Generation This chapter deals with the ID Card generation module, where the Retiree ID card can be generated. Note-ID Card generation will be applicable only for CDA Pensioners 9.1 Upload AO Pension signature Login with AO Pension go to the Masters Issuing Authority Signature. Click on the upload a file button and upload the signature of the Issuing Authority. (Fig 9.1) (Fig 9.2) After uploading the image, click on the Update button (Fig 9.3). (Fig 9.1) 158 Comprehensive Pension Management System User Manual (Version 6.2) (Fig 9.2) (Fig 9.3) 9.2 Generate ID Card Again login with AO Pension go to the Masters Generate ID Card. Here Fill the PPO number of pensioner and click on the Search button. (Fig 9.4) 159 Comprehensive Pension Management System User Manual (Version 6.2) (Fig 9.4) Now you can take the print of ID Card by clicking on the Print Front side and Print Back Side button, as shown in below figure. (Fig 9.5) (Fig 9.5) 160 Comprehensive Pension Management System User Manual (Version 6.2) Chapter 10 10. Revision Under revision module, there are 5 categories of revision (shown in Fig 10) 1. Revision in the rate of DA: Due to change of DA as on Date of retirement, the DCRG shall get revised and the differential amount of DCRG shall be due to be paid. For this sanction for payment shall be issued by Pension section and PDA shall make payment. Any impact on pension payable has to be separately calculated and paid in the next monthly bill. 2. Revision on account of withheld amount: On receipt of sanction for release of full part payment of Withheld amount, this revision shall be processed. 3. Revision of pension to family pension (f.p) on account of death (When No eligible f.p. is mentioned in the PPO): In case when claimant name is not mentioned in the PPO, then revision shall be done. Under this, PPO shall be issued by Pension section. 4. Revision of pension to f.p on account of death (when eligible f.p. is mentioned in PPO) In case when claimant name is mentioned in the PPO, then revision shall be done however as previous PPO will suffice, no fresh PPO shall be issued in such case. 5. Revision of pension due to pay change Court matter: Due to change in Pay details or service details arising out of Court order then, the Revision shall be processed. Fig (10) It is , currently ,mandatory that the Revision should only be done by the same DH Pension user who make entry of the case earlier in the CPMS. 10.1 Revision in the rate of DA This type of revision is used when there is a change in the DA Rate. No fresh sanction for HoO for this case shall be required. Steps for this revision are as follows: - First of all, login with the DH Pension Go to the Revision - Revision of Pension tab Fig (10.1.b). 161 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.1.b) Here fill in PPO No and select the Reason for Revision Fig (10.1.c). Fig (10.1.c) Now click on the edit button (pencil icon) in last Action column. Then next screen opens, here check the details and then click on the Send to Revision Sanction order button and enter w.e.f. date Fig (10.1.d). 162 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.1.d) Now Go to- Revision- Revision Sanction Order tab shown in Fig (10.1.e) Fig (10.1.e) From this screen you can view the Sanction order, by clicking on the View link Fig (10.1.e). Also you can take the printout of this Sanction Order Fig (10.1.f). 163 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.1.f) Now click on the Last Column, verify link to send record for AAO approval Fig (10.1.e). Now login with the AAO Pension. Go to - Approval- Revision- Revision Sanction Order Shown in Fig (10.1.g) Fig (10.1.g) Now Click on the View link to see the Sanction order and then Approve Return the record by click on the Approve Return link and send record for AO approval Fig (10.1.g). Now login with the AO Pension (only on IE Browser). Go to Approval Revision Revision Sanction Order Fig (10.1.h) 164 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.1.h) Click on View link to see or take the print of the Sanction Order, attach the DSc in the system for digital signature and click on the Approve link.At this stage, Digitally signed authority shall get generated. Now login with AO PDA, Go to- Allotment- Allotment To PDA DH Fig (10.1.i) Fig (10.1.i) Now select the case, Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on Send To DH button. After this login with PDA DH, Go to - Bill Generation- Revision, Fig (10.1.j) 165 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.1.j) Select the Record and Click on the Approve and Send to AAO button, Fig (10.1.j). Now login with AAO PDA, Go to- Approval- PDA- Revision, Fig (10.1.k) Fig (10.1.k) Now select the record and click on the Approve Bill and Send to AO . After this login from PDA AO, Go to- Approval- PDA- Revision Fig (10.1.l) 166 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.1.l) Select the Account Head from the dropdown, then it shows the respective records, then fill the Enter Not Payable Before Date and click on the Approve Bill button. After this record will show in PFMS for payment. Monthly Bill shall remain unchanged. 10.2 Revision Due to Withheld Amount This type of revision is used when there is a withheld amount of DCRG in r o pensioner and sanction for release of full amount part amount has been received from HoO. Steps for this revision are as follows: - First of all, login with the DH Pension Go to the Revision - Revision of Pension tab Fig (10.2.b). 167 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.2.b) Here fill all required information like PPO No and select the Reason for Revision Fig (10.2.c). Fig (10.2.c) Now click on the edit button (pencil icon) in last Action column. Then next screen opens, here Fill the Recoveries From Withheld amount (11.(3)) and then click on the Send to Revision Sanction order button Fig (10.2.d) and enter reason for withheld. 168 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.2.d) Now Go to- Revision- Revision Sanction Order tab shown in Fig (10.2.e) Fig (10.2.e) From this screen you can view the Sanction order, by clicking on the View Link Fig (10.2.e). Also you can take the printout of this Sanction Order Fig (10.2.f). 169 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.2.f) Now click on the Last column Verify link to send record for AAO approval Fig (10.2.e). Now login with the AAO Pension. Go to - Approval- Revision- Revision Sanction Order Shown in Fig (10.2.g) Fig (10.2.g) Now Click on the View link to see the Sanction order and then Approve Return the record by click on the Approve Return link and send record for AO approval Fig (10.2.g). Now login with the AO Pension (only on IE Browser). Go to Approval Revision Revision Sanction Order Fig (10.2.h) 170 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.2.h) Click on View link to see or take the print of the Sanction Order, attach the DSC in the system for digital signature and click on the Approve link. Now login with AO PDA, Go to- Allotment- Allotment To PDA DH Fig (10.2.i) Fig (10.2.i) Now select the record and Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on Send To DH button. After this login with PDA DH, Go to - Bill Generation- Revision, Fig (10.2.j) 171 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.2.j) Select the Record and Click on the Approve and Send to AAO button, Fig (10.2.j). Now login with AAO PDA, Go to- Approval- PDA- Revision, Fig (10.2.k) Fig (10.2.k) Now select the record and click on the Approve Bill and Send to AO . After this login from PDA AO, Go to- Approval- PDA- Revision Fig(10.2.l) 172 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.2.l) Now select the Account Head from the dropdown, then it shows the respective records, then fill the Enter Not Payable Before Date and click on the Approve Bill button. After this record will show in PFMS for payment. Monthly Bill shall remain unchanged. 10.3 Revision on account of Pay revision Court Order This type of revision is used when there is a revision in the pay court order. For this sanction shall be issued by HoO and sent to Pension section. Steps for this revision are as follows: - First of all, login with the DH Pension Go to the Revision - Revision of Pension tab Fig (10.3.b). 173 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.3.b) Here fill all required information like PPO No and select the Reason for Revision Fig (10.3.c) Fig (10.3.c) Now click on the edit button (pencil icon) in last Action column. Then next screen opens, here fill the all required details. DH should enter the following requisite parameters: a. New AE b. New Last Pay Drawn c. New Qualifying Service In case any figure remains unchanged then the original value has to be fed. System will then calculate the revised pension, commutation (if any) and gratuity (if any) and the additional amount. Also, if some data entry has been wrongly entered, Press cancel. Once satisfied, Press SAVE. 174 Comprehensive Pension Management System User Manual (Version 6.2) Sanction Order will then be generated and it will be available in revision sanction order Tab. DH may view the sanction order. Once a case is at this stage, it cannot be edited. Fig (10.3.d) Fig (10.3.d) 175 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.3.d) Now Go to- Revision- Revision Sanction Order tab shown in Fig (10.3.e) Fig (10.3.e) From this screen you can view the Sanction order, by clicking on the View link Fig (10.3.e). Also you can take the printout of this Sanction Order Fig (10.3.f). 176 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.3.f) Now click on the Last column Verify link to send record for AAO approval Fig (10.3.e). Now login with the AAO Pension. Go to - Approval- Revision- Revision Sanction Order Shown in Fig (10.3.g) Fig (10.3.g) Now Click on the View link to see the Sanction order and then Approve Return the record by click on the Approve Return link and send record for AO approval Fig (10.3.g). Now login with the AO Pension (only on IE Browser). Go to Approval Revision Revision Sanction Order Click on View link to see or take the print of the Sanction Order, attach the DSc in the system for digital signature and click on the Approve link. Now login with AO PDA, Go to- Allotment- Allotment To PDA DH Fig (10.3.i) 177 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.3.i) Now Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on Send To DH button. After this login with PDA DH, Go to - Action- PDA Section- Revision Sanction Order, Fig (10.3.j) DH PDA must view and save a digital copy of the Sanction Order and take requisite printouts, so that copy can be sent to HoO , pensioner, Pension section apart one office copy(after updation of date of restoration of 2nd commutation after completion of steps till point 10 ). The copy will not be available at any other screen, so saving a digital copy is mandatory. PDA section shall manually calculate the amount of arrears to be paid on account of the revision of pension from date of retirement till the current month. Also, For the month, pro rate calculation has to be done manually. The arrear amount may be fed in the monthly bill of the current month, if not paid already, and pushed from DH to AAO. Once the file moves from sanction received stage, the pension of current month will not be available at DH level any more. So the monthly bill for current month should be pushed with the arrear from DH to AAO. The monthly bill can be paid as per rules. After completion of above, DH PDA will now generate the bill for the above sanction order and send the generated bills namely commutation and gratuity to cash after three level passing. 178 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.3.j) Click on the Send button in front of the record. Now Go to- Action- Bill Generation- Revision. Select the Record and Click on the Approve and Send to AAO button. Now login with AAO PDA, Go to- Approval- PDA- Revision, Fig (10.3.k) Fig (10.3.k) Now select the record and click on the Approve Bill and Send to AO . After this login from PDA AO, Go to- Approval- PDA- Revision Fig (10.3.l) 179 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.3.l) Now select the Account Head from the dropdown, then it shows the respective records, then fill the Enter Not Payable Before Date and click on the Approve Bill button. After this record will show in PFMS for payment. The Commutation DCRG amount shall be paid by Cash the same day when AO PDA send it and same shall be used for arriving at the date of restoration of 2nd commutation and shall be updated in revision copy mentioned in point 6. After successful payment, LC and DLC will get refreshed and respective dates will be entered for the pensioner. Thereafter, the monthly bill in for current month lying at AAO shall be pushed with NPB date to AO and then to Cash for payment. The monthly pension of following months(including current month ) will come as per revised pension. 10.4 Revision of Pension to FP (No eligible family member mentioned in PPO) This type of revision will be done when after the death of pensioner, the claimant s name is not mentioned in the PPO. In such case, revision shall be initiated after form 14 with enclosure is duly forwarded by HOO along with sanction of payment manually. Documents that will be required to be submitted in this case shall be 1. Death certificate of Pensioner(s) 2. Life certificate DLC of the claimant 3. Mandate form cancelled cheque and undertaking 180 Comprehensive Pension Management System User Manual (Version 6.2) 4. Duly filled form 14 with enclosures. If there is a case where though the claimant name is not mentioned in the PPO, but same has been added via corrigendum and uploaded via Upload utility e.g. permanently disabled children siblings and disabled parent, then they will be processed though this type. In such cases form 14 shall be required to be submitted to the Pension PDA directly and no fresh sanction shall be called for. Documents that shall be required to be submitted in this case shall be 1. Death certificate of Pensioner(s) 2. Life certificate DLC of the claimant 3. Mandate form cancelled cheque and undertaking 4. Form 14 duly filled with enclosures. Steps for this revision are as follows: - First of all, login with the DH Pension Go to the Revision - Revision of Pension tab Fig (10.4.b). Fig (10.4.b) Here fill all required information like PPO No and select the Reason for Revision Fig (10.4.c). 181 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.4.c) Now click on the edit button (pencil icon) in last Action column. Then next screen opens, here Fill all the details and then click on the Save button Fig (10.4.d). 182 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.4.d) Now Go to- Revision- Revision Sanction Order tab shown in Fig (10.4.e) Fig (10.4.e) From this screen you can view the Sanction order, by clicking on the View link Fig (10.4.e). Also you can take the printout of this Sanction Order Fig (10.4.f). 183 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.4.f) Now click on the Last Column Verify link to send record for AAO approval Fig (10.4.e). Now login with the AAO Pension. Go to - Approval- Revision- Revision Sanction Order Shown in Fig (10.4.g) Fig (10.4.g) Now Click on the View link to see the Sanction order and then Approve Return the record by click on the Approve Return link and send record for AO approval Fig (10.4.g). Now login with the AO Pension (only on IE Browser). 184 Comprehensive Pension Management System User Manual (Version 6.2) Go to Approval Revision Revision Sanction Order Click on View link to see or take the print of the Sanction Order, attach the DSc in the system for digital signature and click on the Approve link. Now login with AO PDA, Go to- Allotment- Allotment To PDA DH Fig (10.4.i) Fig (10.4.i) Now Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on Send To DH button. After this login with PDA DH, Go to - Action- PDA Section- Revision Sanction Order, Fig (10.4.j) 185 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.4.j) After this the record will show in Vendor Verification, GO to- Action- Vendor Verification Fig (10.4.k). Fig (10.4.k) After this record will Go to- LC DLC Verification- LC Verification, Fig (10.4.l). 186 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.4.l) According to the dates filled in the revision module, case shall get processed. After this the case will show in Next month Monthly Bill ( from the month you process the case), Fig(10.4.m) Fig(10.4.m) Now Process the monthly bill and do payment. An assessment shall be done on account of amount payable , if any, due to delay of intimation of death , from the day following date of death till the the disbursement of 1st revised pension. Such amount shall be paid as arrear recovery along with 1st revised pension. 187 Comprehensive Pension Management System User Manual (Version 6.2) 10.5 Revision of Pension to FP (Eligible family member mentioned in PPO) This type of revision will be done when after the death of pensioner, the claimant - Spouse- name is mentioned in the PPO. In such case, documents that will be required to be submitted in this case shall be 1. Death certificate of Pensioner(s) 2. Life certificate DLC of the claimant 3. Revised Mandate Form Cancelled cheque and Undertaking, if not available. Steps for this revision are as follows: - First of all, login with the DH Pension Go to the Revision - Revision of Pension tab Fig (10.5.b). Fig (10.5.b) Here fill all required information like PPO No and select the Reason for Revision Fig (10.5.c). 188 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.5.c) Now click on the edit button (pencil icon) in last Action column. Then next screen opens, here fill the required details and then click on the Save button Fig (10.5.d). 189 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.5.d) Now Go to- Revision- Revision Sanction Order tab shown in Fig (10.5.e) Fig (10.5.e) From this screen you can view the Sanction order, by clicking on the View link Fig (10.5.e). Also you can take the printout of this Sanction Order Fig (10.5.f). 190 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.5.f) Now click on the Last Column Verify link to send record for AAO approval Fig (10.5.e). Now login with the AAO Pension. Go to - Approval- Revision- Revision Sanction Order Shown in Fig (10.5.g) Fig (10.5.g) Now Click on the View link to see the Sanction order and then Approve Return the record by click on the Approve Return link and send record for AO approval Fig (10.5.g). Now login with the AO Pension (only on IE Browser). Go to Approval Revision Revision Sanction Order Click on View link to see or take the print of the Sanction Order, attach the DSc in the system for digital signature and click on the Approve link. Now login with AO PDA, Go to- Allotment- Allotment To PDA DH Fig (10.5.i) 191 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.5.i) Now Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on Send To DH button. After this login with PDA DH, Go to - Action- PDA Section- Revision Sanction Order, Fig (10.5.j) Fig (10.5.j) After this the record will for Vendor Verification, GO to- Action- Vendor Verification Fig (10.5.k) 192 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.5.k) After this record will Go to- LC DLC Verification- LC Verification, Fig (10.5.l). 193 Comprehensive Pension Management System User Manual (Version 6.2) Fig (10.5.l) Now Do the LC DLC and according to the date filled in the revision module, pension shall be payable. After this the case will show in Next month ( from the month you process the case) Monthly Bill. Fig (10.5.m). An assessment shall be done on account of amount payable , if any, due to delay of intimation of death , from the day following date of death till the the disbursement of 1st revised pension. Such amount shall be paid as arrear recovery along with 1st revised pension. 194 Comprehensive Pension Management System User Manual (Version 6.2) Chapter 11 11.Profile Authentication 11.1 Retiree Profile Authentication In order to ensure that the data filled on retiree profile is correct and has been authenticated by AO Pension (in case of side channel) HOO user (in case of BSNL), retiree profile upload utility has been developed. On service book verification page (Fig(11.1)) DH user shall view the retiree profile and if satisfied then a printout of retiree profile shall be taken (Fig(11.2)). Fig(11.1) 195 Comprehensive Pension Management System User Manual (Version 6.2) Fig (11.2) AO Pension (in case of side channel) HOO user (in case of BSNL) will match the retiree details against physical records and the scanned copy is to be uploaded. Before signing, the retiree profile details shall be matched against physical records and in case of error necessary corrections be made. The scanned copy shall be uploaded (Fig(11.3)) only after it has been checked as mentioned above. 196 Comprehensive Pension Management System User Manual (Version 6.2) Fig (11.3) The uploaded verification form can be viewed by clicking eye button (Fig(11.3), Fig(11.4)). Fig (11.4) The form may then be sent to HOO(SSA Unit) for approval (Fig(11.5)). 197 Comprehensive Pension Management System User Manual (Version 6.2) Fig (11.5) 198 Comprehensive Pension Management System User Manual (Version 6.2) Chapter 12 12. Upload utility There are scenarios wherein either orders have been issued in continuation of PPO or corrigendum have been issued. It is important that these orders being critical in nature- are not just sent to the pensioner but are also uploaded to the pensioners dashboard. For this purpose, upload utility has been created. Some of the scenarios in which upload utility shall be used: 1. Issue of corrigendum of PPO for inclusion of permanently disabled children by PPO. 2. Sanction and payment of arrears of pension in cases where manual calculation has been done In such cases AO(pension) AO(PDA) shall upload the documents which shall be visible on the pensioners dashboard. Fig (12.1) After entering the PPO number and clicking on the Search button (Fig(12.1)), the below stated screen will get displayed (Fig(12.2)). The file to be sent to Pensioner s Dashboard shall be selected and then uploaded. The description of file shall be added (Fig(12.3)). It may be noted that multiple files may be sent to pensioner s Dashboard. Files can be sent by clicking the Submit button (Fig(12.3)). 199 Comprehensive Pension Management System User Manual (Version 6.2) Fig (12.2) Fig (12.3) The AO(pension) AO(PDA) can view the history or date wise records for the information being shared with the pensioner on its dashboard Fig(12.4). 200 Comprehensive Pension Management System User Manual (Version 6.2) Fig (12.4) Similarly, the pensioner can also view the information shared by the Department on his her dashboard by clicking on the tab Shared Documents (Fig(12.5), Fig(12.6)). Fig (12.5) 201 Comprehensive Pension Management System User Manual (Version 6.2) Fig (12.6) Chapter 13 13. Bill Payment slip generation For reconciliation sanction sent from PDA section with the bills appearing on PFMS and to ensure smooth processing, PDF for the bills sent to PFMS for payment can be generated. For bills sent on a day, a pdf can be generated. The date selected for generation of PDF is the date on which DH created the bills. The option of Bill Type and Status Type is to be selected as per the below stated criteria (Fig(13.1), Fig(13.2), Fig(13.3))according to user needs:- 202 Comprehensive Pension Management System User Manual (Version 6.2) Fig (13.1) Fig (13.2) Fig (13.3) A PDF of the bills generated in the particular time frame will be displayed downloaded on your desired location which can be sent to cash as sanction. The PDF (Fig(13.4)) can be generated for the files sent on a day and handed over to Cash for DSC. 203 Comprehensive Pension Management System User Manual (Version 6.2) Fig (13.4) Chapter 14 14. Updation of Mobile, Email and address For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option Edit Profile (Fig(14.1). 204 Comprehensive Pension Management System User Manual (Version 6.2) Fig (14.1) A Pop-up window (Fig(14.2)) will be displayed with the option to choose the following details which the user wants to edit or change: - Fig (14.2) The user has to select the option which he she wants to change or modify. 14.1 Mobile Number Update Upon selecting Mobile Number , the following screen will be displayed (Fig(14.3)) 205 Comprehensive Pension Management System User Manual (Version 6.2) Fig(14.3) Retiree can enter his her mobile number and then select either his her registered email ID or the entered mobile number to receive an OTP to verify the number (Fig (14.3)). Upon receiving the OTP, retiree should enter the OTP (Fig (14.4)) and save which will then update the mobile number. Fig (14.4) 14.2 Email ID Update Upon selecting Email ID , the following screen will be displayed (Fig(14.5)) 206 Comprehensive Pension Management System User Manual (Version 6.2) Fig (14.5) Retiree can enter his her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number (Fig (14.5)). Retiree should then enter the OTP received and click on save (Fig (14.6)) which would then update the email ID. Fig (14.6) 14.3 Address Update To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered (Fig (14.7)). 207 Comprehensive Pension Management System User Manual (Version 6.2) Fig (14.7) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. For more details on Grievance Management please refer Chapter 6. Chapter 15 15. Other pension Types The pension for VRS superannuation pension Pro-rata compensation pension shall be as per above user manual. However, following changes have to be kept in mind while processing the pension under Compulsory retirement Invalid pension Compassionate allowance. For these three categories of retirement, the application of applicant is eligible for commutation only after medical examination. So, the date on which the medical authority signs the medical report in Part III of Form 4 shall be required to be entered in retiree profile as in Fig 15.1. 208 Comprehensive Pension Management System User Manual (Version 6.2) Fig (15.1) Based on the date entered, the commutation will get calculated as per CCS (Commutation of Pension) Rules, 1981 and get displayed in pension papers. This shall be the only change in case of Invalid pension. Additionally, form 7 shall be provided with additional input fields in case of other two retirement types detailed as under. 15.1 Compulsory retirement In form 7 in case of compulsory retirement, at the time of creating the retiree profile, the Date of Medical Report is to be provided Fig(15.1.1). 209 Comprehensive Pension Management System User Manual (Version 6.2) Fig (15.1.1) After processing the case just like for the Normal Pension case, when the user reached at the Form 7 level, careful assessment is required at the time of entering the Percentage ( ) at which the pension and DCRG is to be reduced Fig(15.1.2). Fig (15.1.2) The impact of which can be seen at the Form 7 which the system calculates automatically (Fig(15.1.3), Fig(15.1.4), Fig(15.1.5)). 210 Comprehensive Pension Management System User Manual (Version 6.2) Fig (15.1.3) Fig (15.1.4) 211 Comprehensive Pension Management System User Manual (Version 6.2) Fig (15.1.5) 15.2 Compassionate Allowance The DH at the time of creating the retiree profile will exercise the option of Removal Dismissal from service (Rule 24 and 41) (Fig(15.2.1)) and the rest of the process flow will continue just like the same we process for the Normal Pension cases. However, at the time of the filling of Form 7, the DH must ensure to enter the correct and verified amount for the field wherein case of removal dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41). Fig (15.2.1) 212 Comprehensive Pension Management System User Manual (Version 6.2) The impact of which reflects in the Form 7 (Fig(15.2.2), Fig(15.2.3)). Fig (15.2.2) Fig (15.2.3) 213 Comprehensive Pension Management System User Manual (Version 6.2) 15.3 Miscellaneous issues In order to account for delayed submission of commutation, on Form 7 has been provided. As per rule 6 of CCS (Commutation of Pension ) Rules , 1981, the user will enter the date of receipt of Form 1 Medical Report signing date (In case where form is submitted after the date of retirement) (Fig(15.3.1)). In case where Form for commutation is submitted before date of retirement then commutation shall become absolute on the day following date of retirement. Fig (15.3.1) The impact will reflect at the time of processing of Form 7, the factor of commutation will take effect from the Date of submission of Form 1 Medical Report, i.e., In the case where the form is submitted after retirement. 214 Comprehensive Pension Management System User Manual (Version 6.2) Chapter 16 16. Income Tax Processing AO PDA will allot the Case to DH PDA. After this, DH PDA will login and process the case further. Now DH PDA will go to the Investment Declaration . DH PDA can see the detail of particular record by click on the View link shown in the last column of the grid. Proposed Declaration: Uploading of Supporting documents against savings investment is not mandatory so the figures will only be checked in r o ceiling as per IT Act. Actual Declaration: Uploading of Supporting documents against savings investment is mandatory so the figures as well as Proof of saving will also be checked. In case of documents not clear Documents not as per amount fed, pensioner may be requested to re upload. In such case, the declaration may be admitted as per available document with remarks mentioning that pensioner may upload proper document in respect of rejected column. After receiving of correct supporting document in fresh declaration, based on revised declaration income tax may be processed. should be processed. Fig(16.1) Fig(16.1) 215 Comprehensive Pension Management System User Manual (Version 6.2) Once the DHPDA click on the View link, he can see the below screen .Now DHPDA will check all the details filled by Retiree and send it to AAOPDA ,by selecting AAOPDA from the given dropdownlist and click on the Submit button as shown in Fig(16.2) Fig(16.2) Now AAOPDA will login and Process the case further. After login, AAOPDA will go to the Investment Declaration Approval . Now AAOPDA can check the detail by click the View link shown in the grid. After crosschecking the detail he can Approve or Return the record by click on the Approve or Return button shown in the grid shown in Fig(16.3) . Fig(16.3) 216 Comprehensive Pension Management System User Manual (Version 6.2) Now AAOPDA will go to the IT Calculation Sheet. From hereon, AAO can see the calculation Sheet by click on the View click shown in the grid Fig(16.4). Fig(16.4) Once AAOPDA click on the View link of View the below shown page will open Fig (16.5). Fig(16.5) 217 Comprehensive Pension Management System User Manual (Version 6.2) Also AAOPDA can see the Salary Breakup from here. (Fig 16.6) Fig(16.6) If no proposed actual declaration is sent by Pensioner, AAO PDA will run- Auto Calculation. Fig(16.7) Fig(16.7) And can see the IT calculation for any pensioner by feeding PPO No. Fig(16.8) 218 Comprehensive Pension Management System User Manual (Version 6.2) Fig(16.8) End 219", "type": "pdf"}, {"source": "PDF - Form1A_COP", "content": "FORM I-A FORM OF APPLICATION FOR COMMUTATION OF A PERCENTAGE OF SUPERANNUATION PENSION WITHOUT s MEDICAL EXAMINATION IF NOT APPLIED FOR IN FORM OF CENTRAL CIVIL SERVICES (PENSION) RULES, 1972 [see Rules 5(2), 12,13(3), (3A), (3B), 14(1) and 15(3)] (To be submitted in duplicate at least three months before the date of retirement) PART I To The . (Here indicate the designation and full address of the Head of Office) Subject:- Commutation of pension without medical examination. Sir, I desire to commute a percentage of my pension as indicated below in accordance with the provisions of the Central Civil Services (Commutation of Pension) Rules, 1981. The necessary particulars are furnished below - 1. Name (in Block Letters) 2. Father s husband s name 3. Designation at the time of retirement ... 4. Name of Office Department Ministry in which employed 5. Date of birth (by Christian era) 6. Date of retirement 7. Class of pension on which retired 8. Percentage of monthly pension proposed to be commuted (indicate percentage, equal to or less than 40 ) 9. Details of Bank account to which monthly pension shall be credited: (i) Name of Bank and Branch (ii) Account No. (iii) BSR Code: Place: Signature Date: Postal Address PART II ACKNOWLEDGEMENT Received from <PERSON> (name), (designation), application in Part I of Form I-A for the commutation of a percentage of pension without medical examination. Place: Signature Date: Head of Office NOTE. - If the application has been received by the Head of Office at least 3 months before the date of retirement on superannuation, this acknowledgement should be detached from the Form and handed over to the applicant. If the form has been received by post, it has to be acknowledged on the same day and the acknowledgement sent under registered cover to the applicant. In case it is received after the specified date, it should be accepted only if it has been put into the post on or before that date subject to the production of evidence to that effect by the applicant. PART III Forwarded to the Accounts Officer (here indicate the address and designation) with the remarks that - (i) the particulars furnished by the applicant in Part I have been verified and are correct; (ii) the applicant is eligible to get a percentage of his pension commuted without medical examination; (iii) Amount of pension authorised. [In case final amount of pension has not been authorised, indicate the amount of provisional pension sanctioned under Rule 64 of the Central Civil Services (Pension) Rules,1972] (iv) the commuted value of pension determined with reference to the Table applicable at present comes to Rs . (v) the amount of residuary pension after commutation will be Rs . 2. The pension papers of the applicant completed in all respects were forwarded under this Ministry Department Office Letter No , dated It is requested that the payment of commuted value of pension may be authorised through the Pension Payment Order which may be issued at least one month before the retirement of the applicant. 3. The receipt of Part I of the Form has been acknowledged in Part II which has been forwarded separately to the applicant on . Place: Signature Date: Head of Office", "type": "pdf"}, {"source": "PDF - implementation of income tax module_sampann (1)**********08032024", "content": "Comprehensive Pension Management System (CPMS) PROPOSED DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 15th April every year) Name of the pensioner : P.P.O No : PAN : Assessment Year : Name of the circle : Mobile No. : Income Amount (Rs.) (i) Income from Previous Other Employer(s) : (ii)Tax Deducted by Previous Other Employer(s) : (iii) Income from Other Sources : (iv) Income from House Property (Income Loss): Total : B. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under The Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank ( 5 years) : NSC interest reinvested : Housing Loan principal repaid : Life Insurance premium paid : ULIP investment approved for tax rebate : 2019 -Comprehensive Pension Management System (CPMS) Comprehensive Pension Management System (CPMS) Other deduction U S 80C : Others : U S 80CCC -- Investment in any approved Pension scheme : NPS under Section 80CCD(1) : Section 80CCD(1B) : Section 80CCD(2) : A. OTHER DEDUCTIONS UNDER CHAPTER VI A Amount of deduction claimed (Rs.) U S 80D Medical Insurance U S 80D Medical insurance for Senior Citizen U S 80DD maintenance of Handicap Dependent U S 80DDB Medical treatment on specified disease for super senior citizen U S 80DDB Medical treatment on specified disease for senior citizen U S 80DDB Medical treatment on specified disease U S 80E Higher education U S 80EE Interest on housing loan Donation U S 80G 100 scheme (Only for donation to PM CM LG Relief Fund) Donation U S 80G 50 scheme U S 80CCG- deduction in r o investment in ESS U S 80GG- deduction in respect of rent U S 80QQB-deduction in respect of royalty income U S 80 TTA Interest on deposit in Savings account 2019 -Comprehensive Pension Management System (CPMS) Comprehensive Pension Management System (CPMS) A. OTHER DEDUCTIONS UNDER CHAPTER VI A Amount of deduction claimed (Rs.) U S 80 TTB Interest on deposit senior citizen U S 80U Self Handicap (severe disability and other disability) I hereby declare that I shall submit the ACTUAL DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE along with proof of investment by 10th October of FY [ ] . In case I don t ensure submission of the same, I am aware that applicable tax will be deducted on the pension paid. Date : Signature of Pensioner : 2019 -Comprehensive Pension Management System (CPMS) Comprehensive Pension Management System (CPMS) ACTUAL DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 10th October every year) Name of the pensioner : P.P.O No : PAN : Assessment Year : Name of the circle : Mobile No. : Income Amount (Rs.) (i) Income from Previous Other Employer(s) : (ii) Tax Deducted by Previous Other Employer(s) : (iii) Income from Other Sources : (iv) Income from House Property (Income Loss): Total : B. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under the Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank ( 5 years) : NSC interest reinvested : Housing Loan principal repaid : Life Insurance premium paid : ULIP investment approved for tax rebate : 2019 -Comprehensive Pension Management System (CPMS) Comprehensive Pension Management System (CPMS) Other deduction U S 80C : Others : U S 80CCC -- Investment in any approved Pension scheme : NPS under Section 80CCD(1) : Section 80CCD(1B) : Section 80CCD(2) : A. OTHER DEDUCTIONS UNDER CHAPTER VI A Amount of deduction claimed (Rs.) U S 80D Medical Insurance U S 80D Medical insurance for Senior Citizen U S 80DD maintenance of Handicap Dependent U S 80DDB Medical treatment on specified disease for super senior citizen U S 80DDB Medical treatment on specified disease for senior citizen U S 80DDB Medical treatment on specified disease U S 80E Higher education U S 80EE Interest on housing loan Donation U S 80G 100 scheme (Only for donation to PM CM LG Relief Fund) Donation U S 80G 50 scheme U S 80CCG- deduction in r o investment in ESS U S 80GG- deduction in respect of rent U S 80QQB-deduction in respect of royalty income U S 80 TTA Interest on deposit in Savings account 2019 -Comprehensive Pension Management System (CPMS) Comprehensive Pension Management System (CPMS) A. OTHER DEDUCTIONS UNDER CHAPTER VI A Amount of deduction claimed (Rs.) U S 80 TTB Interest on deposit senior citizen U S 80U Self Handicap (severe disability and other disability) Enclosed proof of Investment as declared above Date : Signature of Pensioner : 2019 -Comprehensive Pension Management System (CPMS)", "type": "pdf"}, {"source": "PDF - know your pensioner kyp form638076718880512686", "content": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by <PERSON>sion<PERSON>) S. No. Description Details 1 Name 2 PPO No. 3 Pensioner s Code New PPO No.( If available) 4 Date of Birth 5 Date of Retirement 6 Post Held on Retirement 7 Group of the post held A B C D on retirement (TICK THE BOX) 8 Mobile No. 9 Aadhar No. (Self attested copy to be enclosed) 10 PAN No. (Self attested copy to be enclosed) 11 Email ID 12 Family Details : Sl. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if Pensioner expired) 1 Pensioner Name 2. Family Pensioner Name 3 Relation to Pensioner 4. PPO No. 5. Date of Birth of Family Pensioner 7. Date of Death of Pensioner 8. Mobile No. 9. Aadhar No. (Self attested copy to be enclosed) 10. PAN No. (Self attested copy to be enclosed) 11. Email ID BANK POST OFFICE DETAILS 1. Name of Bank IFSC 2. Address of Bank Post office 3. A C No. of Bank Post office CORRESPONDENCE DETAILS 1. Permanent Address with Pin code 2. Correspondence Address with Pin code CONSENT LETTER FOR O o CONTROLLER OF COMMUNICATION ACCOUNTS, DEPT. OF TELECOMMUNICATIONS I hereby give permission to O o Controller of Communication Accounts, Department of Telecommunications, Govt. of India for use of my above information for pension payment. Note: The information provided above will not override the Form-3 details present in the Service Book. Place: Date: Signature of the Pensioner Family Pensioner Toll Free: 91 http: cgca.gov.in", "type": "pdf"}, {"source": "PDF - KnowYourPensioner(KYP)Form", "content": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by <PERSON>sion<PERSON>) S. No. Description Details 1 Name 2 PPO No. 3 Pensioner s Code New PPO No.( If available) 4 Date of Birth 5 Date of Retirement 6 Post Held on Retirement 7 Group of the post held A B C D on retirement (TICK THE BOX) 8 Mobile No. 9 Aadhar No. (Self attested copy to be enclosed) 10 PAN No. (Self attested copy to be enclosed) 11 Email ID 12 Family Details : Sl. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if Pensioner expired) 1 Pensioner Name 2. Family Pensioner Name 3 Relation to Pensioner 4. PPO No. 5. Date of Birth of Family Pensioner 7. Date of Death of Pensioner 8. Mobile No. 9. Aadhar No. (Self attested copy to be enclosed) 10. PAN No. (Self attested copy to be enclosed) 11. Email ID BANK POST OFFICE DETAILS 1. Name of Bank IFSC 2. Address of Bank Post office 3. A C No. of Bank Post office CORRESPONDENCE DETAILS 1. Permanent Address with Pin code 2. Correspondence Address with Pin code CONSENT LETTER FOR O o CONTROLLER OF COMMUNICATION ACCOUNTS, DEPT. OF TELECOMMUNICATIONS I hereby give permission to O o Controller of Communication Accounts, Department of Telecommunications, Govt. of India for use of my above information for pension payment. Note: The information provided above will not override the Form-3 details present in the Service Book. Place: Date: Signature of the Pensioner Family Pensioner Toll Free: 91 http: cgca.gov.in", "type": "pdf"}, {"source": "PDF - lc dlc submission at any cca office (2)637600546902818537", "content": "Government of India Ministry of Communications Department of Telecommunications Offlce of Controller General of Communicatlon Accounts NICF Campus, Ghitorni, New Delhi - 110047. Dated 2.06.2021 No. 13-23 2020-21 BA IT 23- 2C7 To, All Pr. CCAs CCAs Subject: Submission of LC DLC by DoT pensioners (SAMPANN FMS) at any CCA office. It has been observed that SAMPANN FMS pensioners may face difficulties in submitting their Physical Life Certificates when they reside in a circle other than the circle which issued the PPO which could lead to delays and thereby stoppage of pension. Additionally, in case of Digital Life Certificate (DLC) via <PERSON><PERSON><PERSON>, it has been observed that sometimes the DLC is submitted to a CCA office other than concerned CCA office unknowingly by pensioners and therefore the details are not updated in the system(s). Therefore, for convenience of pensioners, I am directed to convey the following: 1. DoT pensioners receiving pension via SAMPANN Financial Management System can submit their Physical Life Certificate in any nearby CCA office. To ensure successful updation of Life Certificate, the following immediate steps may be taken: a. Recipient CCA should scan the Physical Life Certificate and send the soft copy to the concerned CCA Office via an official email ID. b. Subsequently, recipient CCA should send the hard copy in original by Post as well. c. Concerned CCA Office may update the Life Certificate details based on the soft copy received. 2. For Digital Life Certificate via <PERSON><PERSON><PERSON> submitted incorrectly by the pensioner, the following step may be taken: The recipient CCA, on identification of the concerned CCA, may share the data a. immediately with that concerned CCA via an official email ID Pr. CCA CCA Offices are requested to inform pensioners and Pensioners Associations via means of letters SMS call regarding the above. This is issued with approval of the Competent Authority 6 2 0- Taranjeet Singh ACGCA(BA IT) Copy to: 1. PS to Member (F) 2. PS to CGCA 3. PS to Advisor (F)", "type": "pdf"}, {"source": "PDF - PensionForms_Form4_Form8_Form10_Form12", "content": "FORM 4 [See rules 50 (15), 57, 58, 59, 60, 62, 74, 79 and 80] Details of Family Important 1. The original Form submitted by the Government servant is to be retained. All additions alterations are to be communicated by the Government servant pensioner along with the supporting documents and the changes shall be recorded in this Form under the signature of Head of Office in column (7). No new Form will substitute the original Form. However, the retiring Government servant should submit the details of family afresh along with Form 6. 2. The details of all members of family (whether eligible for family pension or not) including spouse, all children, parents parents in law and disabled siblings (brothers and sisters) may be given. 3. The Head of Office shall indicate the date of receipt of communication regarding addition or alteration in the family in the Remarks column. The fact regarding disability or change of marital status of a family member should also be indicated in the Remarks column. 4. Wife and husband shall include judicially separated wife and husband. 5. The pensioner shall intimate the details of change in family structure after retirement in Form 5. 6. Copies of birth certificates to be attached. If birth certificate is not available, then copy of any other certificate, as proof of date of birth, may be attached. _____________________________________________________________________________________________________ Name of the Government servant Designation Nationality Details of family members: S.N. Name Date of birth Aadhaar Relationship with Govt. Marital Remarks Dated (DD MM YYYY) no. servant status signature of (voluntary) Head of Office (1) (2) (3) (4) (5) (6) (7) 1. 2. 3. 4. 5. 6. 7. 8. I hereby undertake to keep the above particulars up to date by notifying to the Head of Office any addition or alteration. E-mail: (Optional) Place: Mobile: Date (Signature) Providing Aadhaar No. is voluntary. However, if it is provided, consent to link it to Bank Account and also for authentication of identity from UIDAI for pension related purpose only, is presumed. FORM 8 (See Rule 63(1) and 79(2) Application by a Government servant pensioner or his her spouse for including co-authorisation of names of permanently disabled child dependent parents disabled sibling as family pensioner in the Pension Payment Order Photograph(s) of the Family member(s) to be co-authorised 1. Details of Government servant Pensioner : Name Office Dept. Ministry Nationality Date of retirement Date of death PPO No. (If issued) (DD MM YYYY) (DD MM YYYY) 2. Details of primary existing family pensioner : Name Relationship with deceased PPO No. Government servant pensioner 3. Details of family member to be co-authorised for family pension i.e. Permanently Disabled Child Dependent Parents Permanently Disabled Sibling: Name Date of birth Aadhaar No. (DD MM YYYY) (voluntary) PAN Relationship with deceased Personal marks of Govt. servant identification Signature left hand Whether in receipt of any other pension family Thumb impression pension. If so, particulars and source from which being drawn 4. Postal address of family member to be co-authorised for family pension: Flat House No. Bldg. Name Street Locality Village Post Office Block City District State Pin Code Telephone Mobile No. E-mail ID 5. In case the family member to be co-authorised is minor or suffering from disorder or disability of mind, including mental retardation, details of guardian nominee, wherever applicable: Name Date of Birth Aadhaar No. (DD MM YYYY) (voluntary) PAN Relationship with minor mentally disabled family member Relationship with the Government servant pensioner Postal address of guardian nominee: Flat House No. Bldg. Name Street Locality Village Post Office Block City District State Pin Code Telephone Mobile No. E-mail ID 6. Details of Bank account of family member to be co-authorised (Optional): A c No. (Optional) Bank s Name and branch IFS Code Signature or left hand thumb impression of the Government servant Pensioner family pensioner Address.......................................................................................................... Mobile Telephone No Notes:- (i) If more than one family member are proposed to be co-authorised for family pension, photographs and details in item 3 to item 6 above in respect of all such family members may be given in separate sheets with this Form (ii) The name(s) of permanently disabled child children siblings and or dependent parents shall be added in the PPO only if there is no other eligible prior claimant for family pension (iii) The co-authorisation shall become invalid in case any other member of family becomes entitled to family pension prior to the co- authorised family member. List of Documents to be submitted with Form 8 in respect of each family member who is proposed to be co-authorised for family pension. 1. Two specimen signatures (to be furnished in a separate sheet) .If the member of the family cannot sign his her name then he she is required to put the impression of his her left right thumb etc. on the document in lieu of specimen signature. 2. Proof of identity. 3. Proof of relationship with the deceased Government servant pensioner. 4. Two copies of self attested passport size photographs of the member of the family. 5. Certificate of age showing the dates of birth. The certificate should be from the municipal authorities or from the local panchayat or from the head of a recognized school or Central state board of education. 6. Two specimen signatures of guardian (to be furnished in a separate sheet if the member of the family is minor or suffering from mental disability) 7. If the guardian cannot sign his her name then he she is required to put the impression of his her left right thumb etc. on the document in lieu of specimen signature. 8. A copy of Photo ID proof of the guardian along with proof of Permanent Address. 9. Two self attested copies of passport size photograph of the guardian nominee 10. Last Income Tax Return failing which Certificate from SDM failing which any other document regarding income in support of the claim for family pension. 11. Copy of the first page of the Pass Book or cancelled cheque or any other document showing name and account number in which the family pension is to be credited. (Name of the claimant in the form and in the bank account should be the same) FORM 10 [See rules 50, 71 , 74, 76, 79 and 80 ] Application to the Head of Office for Family Pension on Death of a Government Servant or Pensioner or on Death or Ineligibility of a Family Pensioner or when a Government Servant or Pensioner or Family Pensioner goes missing Photograph Application for family Pension on : (Tick one box) Death of Government Death of Pensioner Death of Family Pensioner Ineligibility of Family servant Pensioner Disappearance of Disappearance of Disappearance of Family Pensioner Government servant P e n s i o ner 1. Details of deceased missing Government Servant Pensioner (To be filled only if a Government servant pensioner has died or gone missing) Name Office Dept. Ministry Nationality Date of retirement Date of death (in Date from which missing (in case of case of death of (in case of missing pensioner) Government Government servant (DD MM YYYY) servant pensioner pensioner(DD MM YYYY) (DD MM YYYY) Date of lodging of report with PPO No. (If issued)(in case of Police (In case of missing pensioner) Government servant pensioner) (DD MM YYYY) 2. Details of previous family pensioner who has died or become ineligible or gone missing (To be filled only if a family pensioner has died or become ineligible or gone missing): Name of deceased Office Dept. Ministry Nationality Government servant pensioner Date of retirement of Date of death of PPO No. (Issued on Government servant Government servant retirement death of (DD MM YYYY) pensioner (DD MM YYYY) Government servant ) Name of previous family Date of death ineligibility Date from which pensioner who has of previous family missing (in case of died become ineligible pensioner (DD MM YYYY) missing family or gone missing pensioner only) (DD MM YYYY) Date of lodging of report with Police PPO No. sanctioning family pension to (In case of missing family pensioner) previous family pensioner who has died (DD MM YYYY) or become ineligible or gone missing Note: The information for items marked ( ) is to be given in respect of the person who was employed in the Department and on whose death, family pension was originally sanctioned. Remaining information is to be given in respect of deceased ineligible missing family pensioner. 3. Details of claimant: Name Date of birth Aadhaar No. (DD MM YYYY) (Voluntary) PAN Relationship with deceased missing Govt. servant pensioner If the claimant is a If the claimant is a divorced daughter If the claimant is a widowed daughter, date Date of filing of divorce disabled child sibling, of death of husband of petition (DD MM YYYY) date from which the claimant Date of divorce suffering from the (DD MM YYYY) (DD MM YYYY) disability (DD MM YYYY) 4. Postal address: Flat House No. Bldg. Name Street Locality Village Post Office Block City District State Pin Code Telephone Mobile No. E-mail ID 5. In case the claimant is minor or suffering from disorder or disability of mind, including mental retardation, details of guardian nominee, wherever applicable: Name Date of Birth Aadhaar No. (DD MM YYYY) (Voluntary) PAN Relationship with minor mentally disabled claimant Relationship with the deceased missing Government servant pensioner Postal address: Flat House No. Bldg. Name Street Locality Village Post Office Block City District State Pin Code Telephone Mobile No. E-mail ID 6. Details of Bank: A c No. Bank s Name and branch IFS Code 7. Indicate whether family pension is also admissible from any other source- (Tick whichever is applicable) Military State Govt. Public sector undertaking autonomous body local fund under the Central or State Govt. 8. Are there any criminal proceedings pending against the claimant? If so, give details. ................................... 9. Are there any charges of fraud or any other serious crime against the missing Government servant pensioner family pensioner ? If so give details. (Applicable in case of missing Government servant pensioner family pensioner) ...................................................... I declare that the information given by me is true to the best of my knowledge and nothing has been concealed therefrom. I am aware that future good conduct of the claimant family pensioner shall be an implied condition for every grant of family pension and its continuance. [ Place: DDMMYYYY Date: (Signature of the claimant guardian) Providing Aadhaar No. is voluntary. However, if it is provided, consent to link it to bank account and also for authentication of identity from UIDAI for pension related purpose only, is presumed. Note: If a member or members of family is are proposed to be co-authorised for family pension, an application in Form 8 may be attached. In accordance with Rule 63(1), the following members of family are eligible for co-authorisation for family pension along with spouse, if there is no other member of family eligible for family pension before them: Disabled child children Dependent parents. Disabled siblings. List of Documents to be submitted with Form 10 1. Two specimen signatures (to be furnished in a separate sheet) .If the claimant cannot sign his her name then he she is required to put the impression of his her left right thumb etc. on the document in lieu of specimen signature. 2. Proof of identity. 3. Proof of relationship with the deceased missing Government servant pensioner 4. Two copies of self attested passport size photographs of the claimant. 5. Details of family in Form 4. 6. Undertaking for refunding any excess payment made by the pension disbursing bank in Format 9. 7. Certificate(s) of age showing the dates of birth of the children. The certificates should be from the municipal authorities or from the local panchayat or from the head of a recognized school or Central state board of education. 8. Two specimen signatures of guardian (to be furnished in a separate sheet if the claimant is minor or suffering from mental disability) 9. If the guardian cannot sign his her name then he she is required to put the impression of his her left right thumb etc. on the document in lieu of specimen signature. 10. A copy of Photo ID proof of the guardian along with proof of Permanent Address. 11. Two self attested copies of passport size photograph of the guardian nominee 12. Copy of Pension Payment Order of previous pensioner family pensioner. 13. Copy of death certificate of Government servant pensioner previous family pensioner , if applicable 14. Copy of document regarding ineligibility of previous family pensioner, if applicable 15. Copy of report lodged with police in respect of missing Government servant or pensioner or previous family pensioner.(In case of missing pensioner family pensioner only) 16. Copy of the report from the police that the Government servant could not be traced so far despite all efforts made (In case of missing pensioner family pensioner only) 17. Indemnity Bond in Format 8 (In case of missing pensioner family pensioner only) 18. Last Income Tax Return failing which Certificate from SDM failing which any other document regarding income in support of the claim for family pension (Not applicable in the case of spouse). 19. Copy of the first page of the Pass Book showing name and account number in which the family pension is to be credited. (Name of the claimant in the form and in the bank account should be the same) 20. If the claimant is a widowed divorced daughter or a disabled child sibling, document in support of the eligibility of the claimant (i.e death certificate of husband in the case of widowed daughter divorce decree in the case of divorced daughter disability certificate in the case of a disabled child) 21 Form 8, if a family member is proposed to be co-authorised for family pension. FORM 12 [See Rule 79(2)] Application to be submitted to Pension Disbursing Authority by spouse co-authorised family member for commencement of family pension on death of a pensioner or family pensioner Photograph 1 . (i) Name of the Government servant pensioner in respect of whom family pension is being claimed (ii) Name of pensioner family pensioner on whose death family pension is claimed (iii) Date of death of pensioner family pensioner (iv) PPO No. of pensioner family pensioner 2 Name and other details of claimant Name Date of birth (DD MM YYYY) Relationship with the deceased Postal Address Government servant pensioner 3. In case the claimant is minor or suffering from disorder or disability of mind, including mental retardation, details of guardian nominee, wherever applicable Name Date of birth Relationship with Relationship with the Postal Address the minor deceased Government (DD MM YYYY) mentally disabled servant pensioner claimant 4. Details of Bank account to which family pension is to be credited A c No. Bank s Name and branch IFS Code I am aware that future good conduct of the claimant family pensioner shall be an implied condition for every grant of family pension and its continuance. Signature or left hand thumb impression of the claimant guardian Mobile Telephone No Permanent Account Number for Income Tax (PAN) .. Aadhar No. (voluntary)- List of Documents to be submitted with Form 12 1. Two specimen signatures of claimant (to be furnished in a separate sheet) (Two slips each bearing the left hand thumb and finger impressions may be furnished by a person who is not literate to sign his name. If such an on account of physical disability is unable to give left hand thumb and finger impressions he she may give thumb and finger impressions of the right hand. Where a Government servant has lost both the hands, he she may give toe impressions..) 2. Two copies of passport size photographs of the claimant 3. Undertaking for refunding any excess payment made by the pension disbursing Bank 4. Specimen signature or left hand thumb and finger impressions of guardian, in the case of the guardian who is not literate enough to sign his or her name 5. Two self -attested copies of passport size photograph of the guardian nominee 6. Descriptive roll of the guardian nominee, wherever applicable, showing the particulars of height and identification marks, self- attested. 7. Copy of PPO of pensioner previous family pensioner (To be provided, if available) 8. Proof of permanent address of the guardian. 9. Copy of death certificate of the deceased pensioner previous family pensioner", "type": "pdf"}, {"source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1", "content": "SAMPANN User Manual- BSNL VRS 2019- Ho<PERSON> (Version 1.1) BSNL Voluntary Retirement Scheme 2019 System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 1.1 Saturday, November 23, 2019 0 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Document Version Sl. No. Version Release Date Remarks First version for BSNL employees SSA unit 1 1.0 22-11-2019 2 1.1 23-11-2019 2nd Version customized for BSNL VRS 2019 1 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Executive Summary Sanchar Pension, Seedha Vitaran Department of Telecommunications (DoT) launched a Comprehensive Pension Management System (CPMS) with the brand name SAMPANN which brings processing, sanctioning, authorisation and disbursement under a common online platform for the pensioners of the Telecom Department. This platform shall also be used for BSNL Voluntary Retirement Scheme 2019 Pensioners. To this end, the Head of Office module will be extended to BSNL units like SSAs, Circles Offices and Corporate Office to enable them to initiate the pension cases and ensure smooth and quick functioning. 2 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Table of Contents Executive Summary .................................................................................................................... 2 Table of Contents ....................................................................................................................... 3 CHAPTER 1.................................................................................................................................. 4 1.1 Comprehensive Pension Management System (CPMS) ...................................................... 4 1.2 Users .................................................................................................................................... 4 1.3 Launching Logging into CPMS ........................................................................................... 4 1.4 CPMS Dashboard.................................................................................................................. 5 1.5 User Creation Management .............................................................................................. 6 1.7 Collection Submission of Pension Papers ......................................................................... 7 1.7.1 For BSNL .................................................................................................................................. 7 1.7.1.2 DH Creation ...................................................................................................................... 7 CHAPTER 2................................................................................................................................ 10 2. HoO Unit............................................................................................................................... 10 2.1 Normal Pension Case ......................................................................................................... 10 2.1.1 Creation of Retiree Profile .................................................................................................... 10 2.1.2 Service Book Verification ...................................................................................................... 11 2.1.3 Send Form to Retiree (8M BDR) ............................................................................................ 12 2.1.4 Form Received (6M BDR) ...................................................................................................... 14 2.1.5 Form Verification (4M BDR) .................................................................................................. 17 2.1.6 Form 7.................................................................................................................................... 19 2.1.7 Form 8.................................................................................................................................... 23 2.1.8 Send to PAO........................................................................................................................... 25 2.1.9 View Forms ............................................................................................................................ 27 3 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) CHAPTER 1 1.1 Comprehensive Pension Management System (CPMS) CPMS is a web portal for Pension Processing, Direct Disbursement, Accounting and Auditing of Pension and Pensionary Benefits to Pensioners of Department of Telecommunication. It has been designed with the following objectives: Direct Credit of pensionary benefits and pension to Pensioners Bank Account. Bringing greater transparency in processing of pensions. Reducing delays in disbursement of pension and arrears of pension. Digitization of forms and streamlining in HoO and CCA offices to reduce time and effort. Optimum utilization of resources in processing and disbursement of pension. Providing timely updates and alerts to the Pensioners. Creating a platform for direct interaction with pensioners for serving them better. Improving the quality of service through consistent and standardized work programs. Creating a faster and more responsive online grievance registration and redressal system. Providing real time MIS to CCA and senior officers of DoT and Controller General of Communication Accounts (CGCA). 1.2 Users This User Manual has been designed for the needs of different users of the application. The target users are listed below BSNL field units for BSNL Voluntary Retirement Scheme 2019 1.3 Launching Logging into CPMS Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpension.gov.in in web browser to go to login screen. ( the preferred web browser for CPMS use is Google Chrome and for DSC, it is Internet Explorer Version 9 or 11). 2. Enter User Name. 3. Enter Password. 4. Enter Captcha. 5. Click on Login button. 4 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) 1.4 CPMS Dashboard Upon successful logging into CPMS, user will land on the home screen. For consistency, the panel on the left shows the Menu options for selection depending upon the work involved. Menu is arranged as per the sequence of operation and the frequency of usage depending upon the type of user logged in. (Fig 1.0, 1.1) Once the selection is made, the information is displayed in tabular form. The top right corner of the screen will show the User (logged in) profile and photograph, if available. The logout option is next to the user detail at the top right. 5 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Fig 1.0 Fig 1.1 1.5 User Creation Management SAMPANN is based on a hierarchy of users and BSNL users will be a part of this hierarchy At the top level is the DoT User as Administrator, then CCA office user at level II and then comes BSNL users. There are two BSNL users: HoO, i.e. Head of Office user and 6 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) then DH i.e. Dealing Hand user. In addition, there are retiree users. Administrator At DoT level CCA At CCA level HoO At BSNL Approver level DH Retiree At BSNL level User IDs in SAMPANN system from BSNL perspective are created in the following manner: For every SSA, Circle and Corporate office, one user shall be created called HoO i.e. Head of office. These offices shall send an offline request to respective CCAs for creation of their users by providing mobile number to CCAs for creation of user. CCAs shall create the HoO user and intimate SSA offices about the user credentials. The officials shall login into the system with the user name and password shared and upon logging in, the official shall be required to provide basic details. Please note that the user should change his her password immediately after the first login. It should also be noted that there can only be one Head of Office user per SSA or Circle Office or Corporate Office. After this, HoO user shall create the DH user i.e. Dealing Hand user. It may be noted that DH and HoO users shall be as nominated by BSNL. 1.7 Collection Submission of Pension Papers 1.7.1 For BSNL 1.7.1.2 DH Creation a) HoO will create the login for DH. b) Path to create the DH User Login: Login as HoO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (Fig 1.8 (A) and 1.9 (A)) c) After creating the login, HoO will assign the authorization of modules to DH by clicking on the Lock icon in the authorization column. Fig 1.9 (i) (A) 7 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Fig 1.8 (A) Edit user details Fig 1.9 (A) 8 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Assign Access Rights Fig 1.9 (i) (A) 9 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) CHAPTER 2 2. HoO Unit This chapter deals with flow of pension papers in the Head of Office (The term HoO is used for BSNL SSA offices circle offices corporate offices. 2.1 Normal Pension Case 2.1.1 Creation of Retiree Profile Once Dealing Hand user logs into the system, a list of retirees, will be visible to him along with all details as shown in the Fig 2.1 (a). Select Retiree details on the Menu options available on the left pane. This data seen has been directly imported from BSNL s ERP system and is colour coded in Yellow. Since the data is from ERP, it will be non-editable. Fig 2.1(a) To view the details, click on the Edit button under the Action column. Once click on the Edit button, a pop up window will open having basic information of the retiree. IMPORTANT:-It should be ensured that for all BSNL Voluntary Retirement Scheme - 2019 retirees, the Type of Retirement is BSNL Voluntary Retirement Scheme -2019 and the Date of Retirement is 31 01 2020. If there is any discrepancy, it should be immediately informed to the concerned CCA office. Also, mobile number being entered should be an active number so that alerts can be sent to the retiree. 10 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) At this stage, if the Dealing Hand user feels that the retiree does not belong to his her circle but another or the retiree details are not correct, he she should immediately inform the respective circle CCA office. 2.1.2 Service Book Verification Now DH should go to the Action- Normal Pension- Service Book Verification. DH to re-check the form and send it for approval to HoO for service book verification by clicking on Send for Approval button. (Fig 2.2) Fig 2.2 After cases have been sent to the HoO for approval, the HoO will login and go to Approval Normal Pension Service Book Verification shown in Fig 2.2(a). Fig 2.2(a) 11 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) The HoO can either approve the cases or return to DH for correction in unverified period mentioned. If HoO approves the form, the form will appear in Send Form to Retiree tab in DH login. Upon return, the HoO has to enter his her remarks in the remarks column. Upon return, case will be visible at DH end under Action Normal Pension Service Book Verification. To that record select Return From HoO in the filter at the top of the page shown in Fig 2.2(b). Fig 2.2(b) Here DH see the remarks of the HoO, make the necessary corrections and resend the case for HoO s approval. IMPORTANT:- Before feeding Service Book information and updating, it has to be ensured that the Service Book is actually verified for the period mentioned. IMPORTANT: Please send the cases for approval in batches by selecting multiple at a time for faster processing and clearing of cases 2.1.3 Send Form to Retiree (8M BDR) To process all verified Service Book records, DH go to Action Normal Pension Send Form to Retiree (Fig 2.3) 12 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Fig 2.3 After checking the details DH send the cases to HoO for approval by clicking on Send For Approval After cases have been sent to the HoO for approval, the HoO login and login and go to Approval Normal Pension Send Form to Retiree shown in Fig 2.3(a). Fig 2.3(a) The HoO can either approve the cases or return to DH. If approved, the retiree login is created, and his her credentials are intimated via SMS. The Retiree can login now and fill up his pension related forms Upon return, DH go to Action Normal Pension Send Form to Retiree and then selecting Return From HoO in the filter at the top of the page. See the remarks of 13 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) the HoO, make the necessary corrections and resend the case for HoO s approval shown in Fig 2.3(b) Fig 2.3(b) IMPORTANT: Please send the cases for approval in batches by selecting multiple at a time for faster processing and clearing of cases 2.1.4 Form Received (6M BDR) After the forms have been filled by the retiree, they will be visible to the DH under Action Normal Pension Form Received (Fig 2.4). Fig2.4 Now DH can view the forms by clicking on the forms under the Forms Column shown in Fig (2.4). DH to check that the form received is correctly filled and that all scanned documents uploaded are of good quality. Then on receipt of hard copy (duly signed by the Retiree), DH may cross verify the details and also check whether all enclosures (as per checklist) have been duly attached. If now, any error is detected by DH, then case has to be returned to retiree with remarks. DH click on the Return button under the Return column and mention the error in forms filled shown in Fig 2.4(a). 14 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Fig 2.4(a) Retiree correct the error and resubmit the forms. Care must be taken to ensure that all details are correct because any errors are detected they can easily be corrected at this stage itself. After cases have been sent to the HoO for approval, HoO go to Approval Normal Pension Form Received shown in Fig 2.4(c ). 15 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Fig 2.4(c) HoO can either approve the cases or return to the DH for correction. Upon approval, the case moves to Form Verification under the DH s login. Upon return, the HoO enter remarks in the remarks column and click to Return button. Upon return, DH go to Action Normal Pension Send Form Received select Return From HoO in the filter at the top of the page view the remarks of the HoO, make the necessary corrections and resend the case for HoO s approval shown in Fig 2.4(d) Fig 2.4(d) IMPORTANT:- 1. Photographs uploaded (Single and Joint) should be of good quality and clear. 2. Signature should legible and visible clearly 3. Address can only have alphanumeric characters and no special characters should be entered. Instead space should be used if needed. 4. Bank Account Details should match the mandate form submitted by the retiree 16 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) 5. Family member name and Details (DoB Marital Status) should be verified with Service Book. IMPORTANT: Please send the cases for approval in batches by selecting multiple at a time for faster processing and clearing of cases 2.1.5 Form Verification (4M BDR) This process has been especially incorporated to put another check on correctness of the forms submitted by Retiree. DH go to Action Normal Pension Form Verification. (Fig 2.5) DH click the icon under View Forms column to view the forms. DH can view the images uploaded by the retiree by clicking on the icon under the View Images column. If now, any error is detected by DH, then case has to be returned. To return the case click on the Return button under the Return column. Retiree correct the error and resubmit the forms. After Checking and verifying the form send it for the approval to HoO by clicking on Verify Button HoO go to Approval Normal Pension Form Verification shown in Fig 2.5(a). Fig 2.5(a) Here, the HoO will be able to see the cases that have been sent by the DH for approval. He She can view the forms by clicking on the icon under the View Forms 17 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) column. The HoO can either approve the cases or return to the DH for correction. Upon return, the HoO has to enter his her remarks in the remarks column. Upon approval, the case moves to Form 7 under the DH s login. All forms filled by retiree along with annexures should be countersigned by this stage. HoO to Approve Return the form as applicable. (Fig 2.8) If HoO approves the form, it will appear in Form 7 tab of DH. In case the HoO returns the form, it goes back to DH for re-verification. Simultaneously, the forms papers will be countersigned by the competent authority in the physical file and process for preparation of Form 7 initiated. Fig2.7 18 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Fig 2.8 IMPORTANT: Please send the cases for approval in batches by selecting multiple at a time for faster processing and clearing of cases 2.1.6 Form 7 After HoO s approval, the case is fit for Form 7. DH go to Action Normal Pension Form 7. Click on the icon under Form 7. Some information in Form 7 is auto populated. Others have to be entered. (Fig 2.6) Once this form is filled and next stage initiated it cannot be edited in HoO Fig 2.6 19 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) For BSNL Voluntary Retirement Scheme 2019 Retirement Gratuity and Commutation, shall be deferred and shall be as per BSNL Voluntary Retirement Scheme 2019. Certain information in Form 7 is auto populated such as Name and Date of Birth. The DH has to enter other details such as details non-qualifying service shown in Fig 2.6(a). 20 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Fig 2.6(b) It should be noted that the fields related to dues will remain disabled. Also since retirees cannot apply for commutation at this stage for BSNL Voluntary Retirement Scheme 2019, the lump sum commutation will be zero After the verification, DH will click on tab Submit and Calculate to calculate the pensionary benefits. After this the DH will see the calculated Form 7. Then save the generated Form 7 shown in Fig 2.6(c). 21 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Fig 2.6( c ) DH click on save calculation and submit button and send it to HoO. HoO go to Approval Normal Pension Form 7.Click on the Current View to see the Form 7 shown in Fig2.6(d). Fig2.6(d) HoO will Approve Return the form. After approval, the case will move to Form 8 at DH s login. In case of Return, DH go to Action Normal Pension Form 7, select Return From HoO in the filter at the top of the page view the remarks of the HoO, make the necessary corrections and resend the Form 7 for HoO s approval shown in Fig2.6(e). 22 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Fig 2.6(e) IMPORTANT: -Form 7 calculates the pensionary benefits and pension as applicable to the pensioner. Once this form is filled and next stage initiated it cannot be edited in HoO section. Therefore, due diligence should be exercised while filling in all important fields like Pay Band, Pay level, Qualifying Non Qualifying service etc. In case Wrong or incorrect information is saved in the system, please immediately inform the concerned CCA. 2.1.7 Form 8 After HoO s approval, DH go to Action Normal Pension Form 8.Some part of information is auto populated in Form 8. The DH will click on the icon under Form 8 column to fill details. There will be no recoveries made in SAMPANN for retirees under the BSNL VRS Scheme 2019. Therefore, DH should simply click on Save and Send For Approval. (Fig 2.7). 23 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Fig 2.7 DH click Save and Send for Approval . The HoO go to Approval Normal Pension Form 8. HoO click on Current View to view the form. HoO must ensure that no recoveries are made in the Form 8 generated by the DH. HoO either approve Return the form. If HoO approves the form it will be processed further. In case of Return, DH go to Action Normal Pension Form 8 select Return From HoO in the filter at the top of the page, view the remarks of the HoO, make the necessary corrections and resend the Form 8 for HoO s approval shown in Fig 2.8 Fig 2.8 The printouts of Form 7 and 8 can be taken from View Forms tab. Approval of competent authority may be taken on the physical Form 7 and 8. 24 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) 2.1.8 Send to PAO After HoO s approval, DH go to Action Normal Pension Send to PAO. After viewing, the DH clicks on the icon under Send to PAO column to open the checklist shown in Fig 2.8. Fig 2.8 DH must see if all the required forms have been submitted. DH select the person eligible for family pension from the list of family members from the dropdown shown in Fig 2.8(a). Fig2.8(a) DH click on Send For Approval and record send for HoO s approval. HoO go to Approval Normal Pension Send to PAO. To View the record, click on the icon under the Approve column. HoO ensured that all papers as per checklist are also sent to CCA in physical copy as well shown in Fig 2.8(b). 25 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) Fig 2.8(b) HoO either click Approve Return. After approval, the case is sent to the Pension section of the concerned CCA office automatically. Upon return DH go to Action Normal Pension Send to PAO and select Return From HoO in the filter at the top of the page, view the remarks of the HoO, make the necessary corrections and resend the case for HoO s approval shown in Fig2.8( c ). Fig 2.8( c ) IMPORTANT:- After all the aforementioned steps, DH will submit the form to Pension Section by clicking on Send to PAO . While sending this please ensure that all the documents mentioned in checklist are being sent. The papers may then be dispatched by post. 26 SAMPANN User Manual- BSNL VRS 2019- HoO (Version 1.1) 2.1.9 View Forms At any point of time, DH and HoO can view the list of all the retirees and their generated forms that are to be sent to CCA office. Printout of all the forms and documents must be taken. These have to be sent to the concerned CCA in physical files. Only after the receipt of these forms generated by the system, the CCA offices would be able process the cases further.(Fig 2.9) Fig 2.9 27", "type": "pdf"}, {"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "content": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Document submission 1.1. First Pension: No additional document shall be required for discharge of first pension and so physical presence will not be required at that time. 1.2. Life certificate: Every pensioner Family pensioner shall be required to get himself verified (Submission of Digital Life certificate or duly signed Life certificate) once in 12 months or less, during any month of the year. If pensioner fails to get verified before 12 months from the date of last verification, the pension payment shall automatically be suspended. For example: If pensioner submits last life certificate on 05 04 2019, he is required to submit his next Life Certificate on or before 20 04 2019 (last day of bill processing). Non submission would mean his pension for April 2020 due on 30 04 2019 would be suspended until submission of life certificate. Upon submission of life certificate at a later date, his pension would resume and arrears due to stoppage will be paid. 1.3. In case <PERSON><PERSON><PERSON> opts for Physical Life Certification, then Life certificate in prescribed form shall be submitted by Pensioner in CCA office by Post hand. In cases where physical life certificate is submitted by the pensioner, life certificate must be signed by Authorities as mentioned in rule 343 of central treasury rules (CTR) and physical presence may be exempted. The format of declaration is attached in Annexure I. 1.4. Non remarriage Certificate: A declaration shall be obtained half-yearly from all recipients of family pension whose pension is terminable on their marriage or remarriage. However, in case of widow recipient of family pension, such declaration will be obtained only on the 1st occasion with an undertaking to report promptly to the PDA in the event of her remarriage (Annexure II). Son Unmarried daughter (including widowed divorced daughter) receiving family pension should furnish six monthly certificates in regard to his her marital status. The format of declaration is attached at Annexure III. 1.5. Non Employment Certificate: All Pensioners should furnish a certificate of non- employment re-employment once in a year in prescribed format. Retired Group A Government Servants are required to furnish half yearly declaration about acceptance non-acceptance of commercial employment within one year from the date of retirement and also about acceptance non-acceptance any employment under any government outside India. The format of declaration is attached at Annexure IV. 2. Action to be taken by CCA office 2.1. Based on receipt of documents, the data shall be fed by concerned DH into the system. Also the received documents shall be properly filed in the Pensioner s file for audit purpose. 2.2. The LC DLC report shall be used to prepare the list of Pensioners who shall be required to submit their Life certificate FMA in the next 5 months. (Annexure V) 2.3. Since Non-employment declaration is co-terminus with Life certificate submission. No separate list shall be maintained except in case of Group A retirees who are required to furnish half yearly declaration about acceptance non-acceptance of commercial employment within one year from the date of retirement and also about acceptance non-acceptance of any employment under any government outside India. 2.4. List of pensioners who get re-employed after retirement shall be separately maintained in Annexure VI. While processing the monthly bills, it must be ensured that correct DA amount is paid to the pensioner in case pensioners get employed. 2.5. List of pensioners where Non-remarriage Certificate Non-Marriage certificate shall be expiring in next 3 months shall be prepared and maintained as in Annexure VII. 2.6. Also, List of the documents Life certificate, Non-employment certificate, Non- remarriage certificate, FMA and Income certificate - as applicable to a particular pensioner shall be recorded and communicated to the pensioner by the office. 2.7. In case of a pensioner, where DLC is submitted, the information as available regarding Non-employment and Non-remarriage Non-marriage in DLC shall be updated in the system. It may be noted that DLC submission comes with default value of No Employment and No Remarriage. In case of retirees who have got employed they may submit a separate undertaking regarding the same and may send duly signed Life certificate. 2.8. List of all cases where pension has been stopped due to non-submission of documentation shall be maintained in Annexure VIII. 2.9. The Annexure V to VIII shall be updated on monthly basis. It shall be monitored by officer no less than the rank of Deputy Secretary in Government of India on quarterly basis. Enclosure III Process of DLC Generation Digital Life Certificate or Life Certificate:- Pensioner will be required to submit the physical life certificate or digital life certificate within one year of last submission. In case of fresh retiree, 1st LC DLC shall be submitted within one year of date of retirement. Digital Life Certificate(DLC) For submission of DLC, following process may be followed. Digital life certificate can be generated at Banks Post Offices which have Jeevan Pramaan Facility. In addition, the Pensioners Service Centre at SSAs, Pensioners Lounge at CCA Offices, banks and CSCs(Customer Service Centres) will facilitate online submission of Digital Life Certificate for the pensioner and the same can be delivered through Post Hand. For submission of DLC, the pensioner requires to be physically present to generate Biometric verification, and provide the PPO Mobile number Aadhaar Number at the CCAs Pensioners Service Centre Banks HPOs CSCs Aadhaar centers for completing the following procedures: Step 1: The pensioner s Aadhaar Number and Mobile number is to be entered in the online application form after which the pensioner receives an OTP (Fig 1) which is to be fed in the system (Fig 2) (Fig 1) (Fig 2) Step 2: If correct OTP is entered, following screen is displayed. On next screen (Fig.3), enter mandatory information like Pensioner Name, PPO Number, Type of pension, name of Sanctioning Authority, Disbursing Agency, email and Bank Account number etc. (Fig. 3) In above screen, currently for pensioners who are drawing pension via CPPCs, sanction authority is TELECOM, Disbursing Agency is Bank and Agency is the Bank name-SBI. However, in the new system, when pension is directly disbursed by CCA office- sanction authority will be TELECOM, Disbursing Agency will be Department of Telecommunication, and Agency is the CCA ABC.(Name of CCA which has issued the PPO) Step 3: Check small grey box as shown below (Fig.4). Then click Scan Finger button this will start the finger Iris scanning process. (Fig.4) Step 4: Once the Finger Print Iris Authentication is successful, the life certificate of the pensioner is displayed as shown below, (Fig.5) and an SMS acknowledgement is sent to the pensioner s mobile number. This SMS has Jeevan Pramaan Certificate ID. The generated certificates or DLCs are stored in the Life Certificate Repository and is available anytime anywhere for use by pensioner Pension Disbursing Agency. (Bank CCA as applicable) (Fig.5) Once the certificate is generated, the Jeevan Pramaan ID is sent in a SMS to the pensioner for further use (print). The Digital Life Certificate or the Jeevan Pramaan will be electronically delivered to the Pension Disbursing Agencies and pensioner is not required to submit any physical copy in this case. Annexure I LIFE CERTIFICATE Certified that I have seen the pensioner Shri Smt. Ms. holder of Pension Payment Order No. and that he she is alive on this date. Name: Designation of Authorised Officer Seal Place: Date: Note:-To be submitted by Pensioner family pensioner once a year . Annexure II CERTIFICATE OF RE-MARRIAGE MARRIAGE I hereby declare that I have not got re-married and I undertake to report such any event promptly to the Pension Disbursing Authority Bank. Signature Place: Name of the pensioner Date: P.P.O. No. I certify to the best of my knowledge and belief that the above declaration is correct. Signature of a responsible Officer or a well-known person Place: Name Date: Designation Applicable only for widow recipient of family pension and to be furnished only once Annexure III CERTIFICATE OF RE-MARRIAGE MARRIAGE I hereby declare that I am not married I have not got married during the past six months. Signature Place: Name of the pensioner Date: P.P.O. No. I certify to the best of my knowledge and belief that the above declaration is correct. Signature of a responsible Officer or a well-known person Place: Name Date: Designation To be submitted by once every six months Annexure V S No CCA Name of Pensioner PPO LC expiring FMA FMA office Family pensioner Number in the month Applicable declaration of Expiring .. Yes No in the month of Annexure VI S No CCA Name of Pensioner PPO Group Employed Dearness office Number Since Relief A B C D Applicable (Yes No) Annexure VII S No CCA office Name of Family PPO Number Non-Marriage Non pensioner Remarriage Certificate expiring in the month of .. Annexure VIII S No CCA Name of Pensioner PPO Pension Reason office Family pensioner Number Stopped for Since stoppage of Pension NON-EMPLOYMENT RE-EMPLOYMENT CERTIFICATE (To be given by pensioner once a year) (A) I declare that I have been employed re-employed in the Offices which is a part of financed by and was in receipt of the following monthly rates of emoluments during the year ended November, 20 or during the month of within the said year: (a) Pay (b) Special Pay (c) Other Allowances Fees Honorarium (it includes D.A., A.D.A., these to be shown clearly) Further, that the orders of my re-employment do do not stipulate my being held in abeyance during the re-employment period. I declare that I have not accepted any commercial employment in India. Or I declare that I have accepted commercial employment in India, after obtaining previous sanction of the Central Government and none of the conditions, if any, attached thereto by Government has been violated. Or I declare that I have accepted commercial Employment in India without obtaining the sanction of Central Government. This declaration is required to be given for a period of one year from the date of retirement. (B) I declare that I have not accepted any employment under a Govt. outside India an International Organization of which Govt. of India is not a member. Or I declare that I have accepted employment under a Govt. outside India an International Organization of which Govt. of India is not a member after obtaining the previous sanction of the Central Government and none of the conditions attached thereto by the Govt. has been deviated from. Or I declare that I have accepted employment under a Govt. outside India an International Organization of which Govt. of India is not a member, without obtaining the previous sanction of the Central Government of which Govt. of India is not a member, without obtaining the previous sanction of the Central Govt. Signature Name of the Pensioner P.P.O. No. Place: Date: . NOTE - Certificate at (A) and (B) are to be furnished only by retired Group A Officers on Half yearly basis.", "type": "pdf"}, {"source": "Web - About Us", "url": "https://dotpension.gov.in/Home/AboutUs", "title": "Comprehensive Pension Management System", "content": "\n\nList:\n1. Direct disbursement of pension on timely basis without intermediaries\n2. Single window system for complete pension process\n3. Online grievance management for the pensioners reduces paper work\n4. Tracking of pension status from home encourages transparency and accountability\n5. Covers 3.5 lakh current pensioners/family pensioners and 1.5 lakh future retirees.\n6. Faster processing of arrears and revision of pension\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• <PERSON><PERSON><PERSON>\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies\n", "type": "web"}, {"source": "Web - Retiree Corner", "url": "https://dotpension.gov.in/Home/RetireeCorner", "title": "Comprehensive Pension Management System", "content": "\n\nList:\n1. Direct disbursement of pension on timely basis without intermediaries\n2. Single window system for complete pension process\n3. Online grievance management for the pensioners reducing paper work\n4. Tracking of pension status from home encourages transparency and accountability\n5. Faster processing of arrears and revision of pension\n\n\nList:\n1. Pension Sanction: Pensioner can view the pension sanctions i.e. ePPO (Digitally signed PPO), Gratuity sanction and sanction of revision of pension on the dashboard, along with details of commutation payment and arrears. It may be noted these sanction will be available for future use.\n2. Lodge Grievance: Pension<PERSON> can lodge grievance online and check its status also.  Alternately the Pensioner can always call the national Helpline 1800-113-5800 or mail his grievances/query to sampann.cpms-dot[at]nic[dot]in.\n3. Pension statement: Pensioner can check the pension payments made till now on his/her dashboard.\n4. Mobile Number, Email ID and Address Update: Retirees will get the feature to update their mobile number, Email ID and address online at any time at their leisure.\n5. Retiring officer/official can see the progress of his/her application at the top of the dashboard till pension Authorization.\n\n\nList:\n• Track pension\n• View ePPO\n• Lodge and track grievances\n• View monthly Statement\n• Stay Updated\n\n\nList:\n• All India Toll-free helpline: 1800-113-5800\n• Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help :  dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies\n", "type": "web"}, {"source": "Web - Circulars", "url": "https://dotpension.gov.in/Home/RetireeCornerCir", "title": "Comprehensive Pension Management System", "content": "\n\nTable Data:\nS.No. | Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices |  | 04/03/2021\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• <PERSON><PERSON><PERSON>\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies\n", "type": "web"}, {"source": "Web - Manual", "url": "https://dotpension.gov.in/Home/DownloadManual", "title": "Comprehensive Pension Management System", "content": "\n\nTable Data:\n\n\nUser Manual\nSAMPANN User Manual- BSNL VRS 2019- HoO 1.1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• <PERSON><PERSON><PERSON>\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies\n", "type": "web"}, {"source": "Web - FAQ", "url": "https://dotpension.gov.in/FAQ/Index", "title": "Comprehensive Pension Management System", "content": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. It covers all activities starting from pension sanction, authorization, and disbursement. It also enables the pensioner to track latest status of pension.\n-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n2) | How to get connected to CPMS portal?\n\n\nAns : \nRetirees can get connected by logging on to www.dotpension.gov.in. Retiring employees will receive user ID and Password on their mobile number.\n3) | What changes after CPMS implementation?\n\n\nAns : \nThe pensioner will continue to be credited to the designated bank account of the pensioner after implementation of CPMS. However, Controller of Communication Accounts (CCA) becomes the pension disbursing authority (PDA) in place of Post Offices and CPPC of Banks. Thus, the CCA becomes the single window authority dealing with all pension relating activities of pensioners.\n4) | How does the Pensioner benefit from implementation of CPMS?\n\n\nAns : \nCPMS implementation will benefit the pensioner in the following ways:\r\n•\tDirect transfer of pension by the CCA to the pensioner’s bank account through CPMS will eliminate the delay caused by involvement of the CPPC \r\n•\tIt will eliminate the need for Transfer of pension cases from one Circle to another. This in turn will ensure that pensioners drawing pension in a bank located in another state (different from where he retired) start getting pension without any time-lag.\r\n•\tAny revision in pension will get effected without delay. Calculation and payment of arrear will take place automatically once the pension is revised.\r\n•\tThe time-lag between change in Dearness Relief (DR) and its payment will be minimized as latest DR rates will be updated centrally.\r\n•\tAge-linked enhancement of pension will take place on attaining the designated age.\r\n•\tThe pensioner will be able to access and take print-out of details of pension payment by logging into CPMS with the login ID provided to the pensioner.\r\n•\tTDS and Form-16 will be provided by the CCA without any intervention from the pensioner.\n5) | When is the pension credited to the pensioner's account by the paying Branch?\n\n\nAns : \nThe pension is credited to the account on the last working day of the month. In case of arrears etc. the amount will be credited Out of Cycle on earliest possible day.\n6) | Which Office will the pensioner approach for commencement of his/her pension?\n\n\nAns : \nThe pensioner is not required to visit any office.\n7) | Will the pensioner get a PPO in new system?\n\n\nAns : \nA digitally signed PPO shall be sent to Pensioners dashboard. However, a Printed copy shall also be sent via post to pensioner’s address by PDA section.\n8) | Can a pensioner open a Joint Account with his/ her spouse?\n\n\nAns : \nYes, the pensioner can open a joint account with his/her spouse. The account details of the same must be communicated to the Pension Section of the CCA office. The same will be updated into the CPMS software, and the pension will flow into the new account.\n9) | Can the pensioner login to CPMS?\n\n\nAns : \nYes. Every pensioner will get a login ID and Password after the forms are sent to retiree online for filling them and sending  to the Head of the Office (HoO).\n10) | What are the services which can be accessed by the pensioner by logging into CPMS with his login ID?\n\n\nAns : \nThe Pensioner will be able to access the following services by logging into CPMS:\r\n•\tProgress of his pension case. At any point of time.\r\n•\t Lodge a grievance from the CPMS portal itself. Otherwise he/she can always contact the CCA office over Phone with his query.\r\n•\tView details of pension received and take print-out of pension payment slip.\r\n•\tView PPO and sanction orders\n11) | What is to be done in case the pensioner wants to change the state in which his bank branch is located?\n\n\nAns : \nThe pensioners covered under CPMS needs to submit application in prescribed form along with details of bank account and bank undertaking to the CCA office. Pension will be credited to the new bank account once the PDA changes bank details in CPMS. There will be no need for recalling PPO from Bank and transferring pension case from one state to another.\n12) | In case of Family Pension, which office should the dependents approach?\n\n\nAns : \nIn case of unfortunate death of the employee/pensioner, the Death Certificate along with Life certificate (or DLC) of family pensioner may be sent to CCA office.\n13) | When does the family pension commence?\n\n\nAns : \nOnce the Death Certificate is received, the pension can either start in the same month or the next.  Family pensioner need not visit CCA office for commencing disbursement of family pension.\n14) | How to get details of pension through SMS?\n\n\nAns : \nThe CPMS software is integrated with SMS gateway. At various stages of the pensionary benefits calculation, the SMS and Emails will be triggered by the system to the retiree/dependent provided a working mobile number has been provided.\n15) | If the pensioner wants details of pension paid during a financial year or Pension Payment Slip, whom should he/she approach?\n\n\nAns : \nThe pensioner’s dashboard, has a link to Pension Ledger. It can be accessed online, and previous pension paid amounts will be shown.\n16) | If the pensioner has a query or requires clarification on the amount of pension paid, what should he/she do?\n\n\nAns : \nPensioner can directly contact the CCA office for the details. In case of any grievance, he/she can lodge the same from his dashboard itself.\n17) | Whether Income Tax (TDS) will be deducted at source?\n\n\nAns : \nYes, income tax is deducted at source for all pensioner (not family pensioners), as per provisions of Income Tax Act and instructions issued by the Income Tax Department.\n18) | Who is responsible for deduction of Income Tax at source from pension payment?\n\n\nAns : \nPDA section of CCA office is responsible for deducting the TDS on Pension.\n19) | Where should a pensioner submit details of investment for claiming relief under Income Tax Act?\n\n\nAns : \nThe investment details need to be sent to the PDA section of CCA offices. On the pensioner’s dashboard, pensioners may log in and feed their details and upload supporting documents. Taking a printout of above, they can send along with document to CCA office by post.\r\nPensioners can also download the income tax proforma available on www.dotpension.gov.in and fill it up and then send the duly filled form along with enclosures to CCA.\n20) | Which office will the pensioner approach for grievance resolution?\n\n\nAns : \nGrievance can be lodged from the Pensioner’s dashboard online. The grievance can also be reported to the CCA office directly. A dedicated toll-free helpline 1800-113-5800 is also available. Mails can be sent to sampann[dot]cpms-dot[at]gov[dot]in\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies\n", "type": "web"}, {"source": "Web - Training", "url": "https://dotpension.gov.in/Home/Training", "title": "Comprehensive Pension Management System", "content": "\n\nTable Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nUser Manual for Change in logic of calculation of gratuity family pension (AE vs. LPD and Penalty)\nUser manual for Deactivation module\nUser manual for Enhanced Monthly Bill Enhancements\nUser manual for Family and Migrated Pensioner Dashboard\nUser manual for FMA Update\nUser manual for Income tax related changes for family pensioners\nUser manual for logic of Minimum Pension\nUser manual for Part IV utility\nUser Manual for Pay related revision for BSNL VRS 2019 cases\nUser manual for Pensioner Pension Slip Generation\nUser Manual for Return Delete feature in Revision Module\nUser Manual Transfer of Pension\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• <PERSON><PERSON>n <PERSON>\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies\n", "type": "web"}, {"source": "Web - Whats New", "url": "https://dotpension.gov.in/Home/WhatsNew", "title": "Comprehensive Pension Management System", "content": "\n\nTable Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate generation and submission | Updated Digital Life Certificate Submission Process For DOT Pensioners\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• <PERSON><PERSON><PERSON>\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies\n", "type": "web"}, {"source": "Web - Contact", "url": "https://dotpension.gov.in/Home/Contact", "title": "No title found", "content": "", "type": "web"}, {"source": "Web - Home", "url": "https://dotpension.gov.in/", "title": "Comprehensive Pension Management System", "content": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/ SAMPANN Contact details for access by pensioners.\nCPMS portal at\nwww.dotpension.gov.in\nToll Free number – 1800-113-5800\n(Timings – 9:30 AM to 6:00 PM except on weekends & gazetted  holidays)\nCentralized helpdesk Mail Id – sampann[dot]cpms-dot[at]gov[dot]in\nAll CCA Contact Details -\nClick here\nSAMPANN APP -  Mobile application available on google play store\nFAQ For Pensioners -\nClick here\nClose\nContact us\nEmail : support[dot]cpms-dot[at]nic[dot]in\nFor Technical Help : 1800-113-5800\nFor Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\nClose\nAlert\nTo know your Identification/Life Certificate status\nClick Here\nजीवन प्रमाण पत्र की स्थिति जानने के लिए\nयहां क्लिक करें\nIf your Life Certificate has expired on or before 31st May, 2025, please submit your Life Certificate at the earliest and latest by 20th June, 2025 to avoid stoppage of pension. Also, if your Life Certificate is expiring in upcoming months, please ensure to submit it before expiry for uninterrupted pension payment. यदि आपका जीवन प्रमाण पत्र 31 मई, 2025 को या उससे पहले समाप्त हो गया है, तो कृपया पेंशन रोकने से बचने के लिए 20 जून, 2025 तक या उससे पहले जीवन प्रमाण पत्र जमा करें। इसके अलावा, यदि आपका जीवन प्रमाण पत्र आगामी महीनों में समाप्त हो रहा है, तो कृपया निर्बाध पेंशन भुगतान के लिए समाप्ति से पहले इसे जमा करना सुनिश्चित करें।\nInformation\nContact Us\nKnow Your Pensioner (KYP) Form\nKnow Your PPO No\nHelp line\nSelect Your Tax Regime\nLife Certificate Validity Check\n440931\nPensioners Benefited\n********\nTransactions Made\nHelpdesk\nEmail : support[dot]cpms-dot[at]nic[dot]in\nFor Technical Help : 1800-113-5800\nFor Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\nPensioners' Portal\nDepartment of Telecommunication\nJeevan Pramaan\nFeedback\nContact us\nPolicies\n\"© 2023 Designed by Controller General Of Communication Accounts, Department of Telecommunications, Developed and Maintained by M/s Uneecops Technologies Ltd, Hosted by NIC.\"\n\nList:\n• Information\n• Contact Us\n• Know Your Pensioner (KYP) Form\n\n\nList:\n• Know Your PPO No\n• Help line\n\n\nList:\n• Select Your Tax Regime\n• Life Certificate Validity Check\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies\n", "type": "web"}, {"source": "Web - About Us", "content": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home AboutUs Scraped: 2025-06-10 11:55:20 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reduces paper work Tracking of pension status from home encourages transparency and accountability Covers 3.5 lakh current pensioners family pensioners and 1.5 lakh future retirees. Faster processing of arrears and revision of pension UL List 2: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 3: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "type": "web"}, {"source": "Web - Circulars", "content": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home RetireeCornerCir Scraped: 2025-06-10 11:55:24 TABLES Table 1: Headers: S.No. Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03 2021 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 2: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "type": "web"}, {"source": "Web - Contact", "content": "Title: No title found URL: https: dotpension.gov.in Home Contact Scraped: 2025-06-10 11:55:35", "type": "web"}, {"source": "Web - FAQ", "content": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in FAQ Index Scraped: 2025-06-10 11:55:29 TABLES Table 1: Headers: 1) What is CPMS? Ans : Comprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. It covers all activities starting from pension sanction, authorization, and disbursement. It also enables the pensioner to track latest status of pension. 2) How to get connected to CPMS portal? Ans : Retirees can get connected by logging on to www.dotpension.gov.in. Retiring employees will receive user ID and Password on their mobile number. 3) What changes after CPMS implementation? Ans : The pensioner will continue to be credited to the designated bank account of the pensioner after implementation of CPMS. However, Controller of Communication Accounts (CCA) becomes the pension disbursing authority (PDA) in place of Post Offices and CPPC of Banks. Thus, the CCA becomes the single window authority dealing with all pension relating activities of pensioners. 4) How does the Pensioner benefit from implementation of CPMS? Ans : CPMS implementation will benefit the pensioner in the following ways: Direct transfer of pension by the CCA to the pensioner s bank account through CPMS will eliminate the delay caused by involvement of the CPPC It will eliminate the need for Transfer of pension cases from one Circle to another. This in turn will ensure that pensioners drawing pension in a bank located in another state (different from where he retired) start getting pension without any time-lag. Any revision in pension will get effected without delay. Calculation and payment of arrear will take place automatically once the pension is revised. The time-lag between change in Dearness Relief (DR) and its payment will be minimized as latest DR rates will be updated centrally. Age-linked enhancement of pension will take place on attaining the designated age. The pensioner will be able to access and take print-out of details of pension payment by logging into CPMS with the login ID provided to the pensioner. TDS and Form-16 will be provided by the CCA without any intervention from the pensioner. 5) When is the pension credited to the pensioner s account by the paying Branch? Ans : The pension is credited to the account on the last working day of the month. In case of arrears etc. the amount will be credited Out of Cycle on earliest possible day. 6) Which Office will the pensioner approach for commencement of his her pension? Ans : The pensioner is not required to visit any office. 7) Will the pensioner get a PPO in new system? Ans : A digitally signed PPO shall be sent to Pensioners dashboard. However, a Printed copy shall also be sent via post to pensioner s address by PDA section. 8) Can a pensioner open a Joint Account with his her spouse? Ans : Yes, the pensioner can open a joint account with his her spouse. The account details of the same must be communicated to the Pension Section of the CCA office. The same will be updated into the CPMS software, and the pension will flow into the new account. 9) Can the pensioner login to CPMS? Ans : Yes. Every pensioner will get a login ID and Password after the forms are sent to retiree online for filling them and sending to the Head of the Office (HoO). 10) What are the services which can be accessed by the pensioner by logging into CPMS with his login ID? Ans : The Pensioner will be able to access the following services by logging into CPMS: Progress of his pension case. At any point of time. Lodge a grievance from the CPMS portal itself. Otherwise he she can always contact the CCA office over Phone with his query. View details of pension received and take print-out of pension payment slip. View PPO and sanction orders 11) What is to be done in case the pensioner wants to change the state in which his bank branch is located? Ans : The pensioners covered under CPMS needs to submit application in prescribed form along with details of bank account and bank undertaking to the CCA office. Pension will be credited to the new bank account once the PDA changes bank details in CPMS. There will be no need for recalling PPO from Bank and transferring pension case from one state to another. 12) In case of Family Pension, which office should the dependents approach? Ans : In case of unfortunate death of the employee pensioner, the Death Certificate along with Life certificate (or DLC) of family pensioner may be sent to CCA office. 13) When does the family pension commence? Ans : Once the Death Certificate is received, the pension can either start in the same month or the next. Family pensioner need not visit CCA office for commencing disbursement of family pension. 14) How to get details of pension through SMS? Ans : The CPMS software is integrated with SMS gateway. At various stages of the pensionary benefits calculation, the SMS and Emails will be triggered by the system to the retiree dependent provided a working mobile number has been provided. 15) If the pensioner wants details of pension paid during a financial year or Pension Payment Slip, whom should he she approach? Ans : The pensioner s dashboard, has a link to Pension Ledger. It can be accessed online, and previous pension paid amounts will be shown. 16) If the pensioner has a query or requires clarification on the amount of pension paid, what should he she do? Ans : Pensioner can directly contact the CCA office for the details. In case of any grievance, he she can lodge the same from his dashboard itself. 17) Whether Income Tax (TDS) will be deducted at source? Ans : Yes, income tax is deducted at source for all pensioner (not family pensioners), as per provisions of Income Tax Act and instructions issued by the Income Tax Department. 18) Who is responsible for deduction of Income Tax at source from pension payment? Ans : PDA section of CCA office is responsible for deducting the TDS on Pension. 19) Where should a pensioner submit details of investment for claiming relief under Income Tax Act? Ans : The investment details need to be sent to the PDA section of CCA offices. On the pensioner s dashboard, pensioners may log in and feed their details and upload supporting documents. Taking a printout of above, they can send along with document to CCA office by post. Pensioners can also download the income tax proforma available on www.dotpension.gov.in and fill it up and then send the duly filled form along with enclosures to CCA. 20) Which office will the pensioner approach for grievance resolution? Ans : Grievance can be lodged from the Pensioner s dashboard online. The grievance can also be reported to the CCA office directly. A dedicated toll-free helpline 1800-113-5800 is also available. Mails can be sent to sampann[dot]cpms-dot[at]gov[dot]in LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 2: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "type": "web"}, {"source": "Web - Home", "content": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Scraped: 2025-06-10 11:55:39 Forgot Password Your password will be send on your E-mail address Close Save changes Help line CPMS SAMPANN Contact details for access by pensioners. CPMS portal at www.dotpension.gov.in Toll Free number 1800-113-5800 (Timings 9:30 AM to 6:00 PM except on weekends gazetted holidays) Centralized helpdesk Mail Id sampann[dot]cpms-dot[at]gov[dot]in All CCA Contact Details - Click here SAMPANN APP - Mobile application available on google play store FAQ For Pensioners - Click here Close Contact us Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in Close Alert To know your Identification Life Certificate status Click Here ज वन प रम ण पत र क स थ त ज नन क ल ए यह क ल क कर If your Life Certificate has expired on or before 31st May, 2025, please submit your Life Certificate at the earliest and latest by 20th June, 2025 to avoid stoppage of pension. Also, if your Life Certificate is expiring in upcoming months, please ensure to submit it before expiry for uninterrupted pension payment. यद आपक ज वन प रम ण पत र 31 मई, 2025 क य उसस पहल सम प त ह गय ह , त क पय प शन र कन स बचन क ल ए 20 ज न, 2025 तक य उसस पहल ज वन प रम ण पत र जम कर इसक अल व , यद आपक ज वन प रम ण पत र आग म मह न म सम प त ह रह ह , त क पय न र ब ध प शन भ गत न क ल ए सम प त स पहल इस जम करन स न श च त कर Information Contact Us Know Your Pensioner (KYP) Form Know Your PPO No Help line Select Your Tax Regime Life Certificate Validity Check 440931 Pensioners Benefited ******** Transactions Made Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies \" 2023 Designed by Controller General Of Communication Accounts, Department of Telecommunications, Developed and Maintained by M s Uneecops Technologies Ltd, Hosted by NIC.\" LISTS UL List 1: Information Contact Us Know Your Pensioner (KYP) Form UL List 2: Know Your PPO No Help line UL List 3: Select Your Tax Regime Life Certificate Validity Check UL List 4: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 5: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "type": "web"}, {"source": "Web - Manual", "content": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home DownloadManual Scraped: 2025-06-10 11:55:26 TABLES Table 1: Headers: User Manual SAMPANN User Manual- BSNL VRS 2019- HoO 1.1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 2: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "type": "web"}, {"source": "Web - Retiree Corner", "content": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home RetireeCorner Scraped: 2025-06-10 11:55:22 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reducing paper work Tracking of pension status from home encourages transparency and accountability Faster processing of arrears and revision of pension OL List 2: Pension Sanction: Pension<PERSON> can view the pension sanctions i.e. ePPO (Digitally signed PPO), Gratuity sanction and sanction of revision of pension on the dashboard, along with details of commutation payment and arrears. It may be noted these sanction will be available for future use. Lodge Grievance: Pensioner can lodge grievance online and check its status also. Alternately the Pensioner can always call the national Helpline 1800-113-5800 or mail his grievances query to sampann.cpms-dot[at]nic[dot]in. Pension statement: Pension<PERSON> can check the pension payments made till now on his her dashboard. Mobile Number, Email ID and Address Update: Retirees will get the feature to update their mobile number, Email ID and address online at any time at their leisure. Retiring officer official can see the progress of his her application at the top of the dashboard till pension Authorization. UL List 3: Track pension View ePPO Lodge and track grievances View monthly Statement Stay Updated UL List 4: All India Toll-free helpline: 1800-113-5800 Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in UL List 5: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 6: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "type": "web"}, {"source": "Web - Training", "content": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sleep through SAMPANN mobile App User Manual failed Cancelled Bill handling in SAMPANN User manual for Additional Quantum Pension User Manual for Change in logic of calculation of gratuity family pension (AE vs. LPD and Penalty) User manual for Deactivation module User manual for Enhanced Monthly Bill Enhancements User manual for Family and Migrated Pensioner Dashboard User manual for FMA Update User manual for Income tax related changes for family pensioners User manual for logic of Minimum Pension User manual for Part IV utility User Manual for Pay related revision for BSNL VRS 2019 cases User manual for Pensioner Pension Slip Generation User Manual for Return Delete feature in Revision Module User Manual Transfer of Pension LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 2: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "type": "web"}, {"source": "Web - Whats New", "content": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMPANN Hindi Generation of Pension Slip in SAMPANN English Process of Life Certificate Digital Life certificate generation and submission Updated Digital Life Certificate Submission Process For DOT Pensioners LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 2: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "type": "web"}, {"content": "11.............................................................................................................................. 195 11.Profile Authentication ....................................................................................................... 195", "source": "User Manual - Chapter 8, Section 10.5", "title": "Revision of Pension to FP (Eligible family member mentioned in PPO) ....................... 188", "type": "user_manual_procedure", "procedure_type": "GENERAL_PROCEDURE", "metadata": {"chapter": 8, "section": "10.5", "procedure_type": "GENERAL_PROCEDURE"}}, {"content": "16.............................................................................................................................. 215 16. Income Tax Processing ..................................................................................................... 215 8 Definitions and Acronyms Abbreviation / Term Description AAO Assistant Account officer ACCA Assistant Controller of Communication Accounts AE Account Enfacement AO Accounts Officer CCA Controller of Communication Accounts CGCA Controller General of Communication Accounts DH Dealing Hand DSC Digital Signature Certificate HoO Head of Office LPD Last Pay Drawn M / BDR Month / Before Date of Retirement PAO Pay & Accounts Office PDA Pension Disbursement Authority PFMS Public Financial Management System 9 What’s New What’s New in Version 3.0  Instruction for DH PDA amended in PDA module, relating to sending EPPO and Sanction hard copies to different sections (73, sub heading 4.1).  Instructions for AO Cash/PFMS amended in PDA module, relating to payment from PFMS (80, sub heading 4.4).  Instructions for creation of HoO and DH amended, relating to role assignment to officers (15, sub heading 1.5.1).  Instructions for filling Form 7 (43, sub heading 2.1.6), Form 18 (56, sub heading 2.2.5) and editing calculation sheet on (67, sub heading 3.5).  Instructions for filling bank details in Form 14 amended (52, sub heading 2.2.3).  Instructions for filling bank details in Form 12 amended (53, sub heading 2.2.4).  Instructions for filling bank details in Pensioner profile amended (91, sub heading", "source": "User Manual - Chapter 8, Section 15.3", "title": "Miscellaneous issues ...................................................................................................... 214", "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "15.3", "procedure_type": "FORM_PROCESSING"}}, {"content": " Instructions for AO pension section (after DSC) amended, relating to enclosures to be forwarded to AO PDA (72, sub heading 3.7).  Instructions in Vendor verification of PDA section amended, relating to PDA Utility tab (74, sub heading 4.2).  Arrear Section (82, sub heading 4.5)  Manual for DSC installation (97 onwards). What’s New in Version 4.0  Reference for Side Channel creation in Annexure 1 (17, sub heading 1.7.1.1).  Instructions for filling Retiree Profile (32, sub heading 2.1.1).  Instructions for correction needed in Form 7 and Form 8(42, sub heading 2.1.7).  Instructions for filling Form 12 (51,52, sub heading 2.2.4).  Instructions while allot case to DH by AO Pension (56, sub heading 3.1).  Instructions for AE Generated in Pension Section (61, sub heading 3.4).  Instructions for filling Pension Sanction (68, sub heading 3.7).  Instructions for filling Monthly Bill (82, sub heading 4.5).  Annexure 1 for Side Channel User creation (117 onwards). What’s New in Version 5.0  Reference for ID Card Generation for CDA Pensioners (142, sub heading 9.1)  Instructions for taking print of ID Card (144, sub heading 9.2) What’s New in Version 6.0  Addition of 10. Revision of Pension Module (146, sub heading 10)  Addition of 11. Profile Authentication (34 , 124 and 176 ) 10  Addition of chapter 12. Upload utility to chapter 15. Other Pension Types  Addition of 5.3 to 5.5 in Retiree Module What’s New in Version 6.2  Addition in chapter 5- 5.6 Income Tax Module  Addition in 16. Income Tax Processing 11", "source": "User Manual - Chapter 8, Section 5.2", "title": null, "type": "user_manual_procedure", "procedure_type": "RETIREE_PROFILE", "metadata": {"chapter": 8, "section": "5.2", "procedure_type": "RETIREE_PROFILE"}}, {"content": "SAMPANN(System for Accounting and Management of Pension) is brand Name for Comprehensive Pension Management System( CPMS) ,a web portal for Pension Processing, Direct Disbursement, Accounting and Auditing of Pension and Pensionary Benefits to Pensioners of Department of Telecommunication. It has been designed with the following objectives:  Direct Credit of pensionary benefits and pension to Pensioners’ Bank Account.  Bringing greater transparency in processing of pensions.  Reducing delays in disbursement of pension and arrears of pension.  Digitization of forms and streamlining in HoO and CCA offices to reduce time and effort.  Optimum utilization of resources in processing and disbursement of pension.  Providing timely updates and alerts to the Pensioners.  Creating a platform for direct interaction with pensioners for serving them better.  Improving the quality of service through consistent and standardized work programs.  Creating a faster and more responsive online grievance registration and redressal system.  Providing real time MIS to CCA and senior officers of DoT and Controller General of Communication Accounts (CGCA).", "source": "User Manual - Chapter 1, Section 1.1", "title": "SAMPANN", "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 1, "section": "1.1", "procedure_type": "FORM_PROCESSING"}}, {"content": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpension.gov.in in web browser* to go to login screen. (*the preferred web browser for CPMS use is Google Chrome and for DSC, it is Internet Explorer Version 9 or 11). 2. Enter User Name. 12 3. Enter Password. 4. Enter Captcha. 5. Click on Login button.", "source": "User Manual - Chapter 1, Section 1.3", "title": "Launching& Logging into CPMS", "type": "user_manual_procedure", "procedure_type": "GENERAL_PROCEDURE", "metadata": {"chapter": 1, "section": "1.3", "procedure_type": "GENERAL_PROCEDURE"}}, {"content": "Upon successful logging into CPMS, user will land on the home screen. For consistency, the panel on the left shows the Menu options for selection depending upon the work involved. Menu is arranged as per the sequence of operation and the frequency of usage depending upon the type of user logged in. (1.0, 1.1) Once the selection is made, the information is displayed in tabular form. The top right corner of the screen will show the User (logged in) profile and photograph, if available. The logout option is next to the user detail at the top right. 13 1.0 1.1", "source": "User Manual - Chapter 1, Section 1.4", "title": "CPMS Dashboard and Administrator Role", "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 1, "section": "1.4", "procedure_type": "FORM_PROCESSING"}}, {"content": "Administrator logins screen will show the following options to work with. (1.1.1) 14 1.1.1 Masters: This allows the Administrator User to manage the master data of different modules of CPMS. (1.1.2) 1.1.2 Users: User section consists of user roles, access rights, user registration and DSC approval. It allows the Administrator User to assign and modify roles, access rights and grant DSC approval to different users. (1.1.3) 15 1.1.3 Role Master: Administrator User can see the different roles available in the Role Master and can also add a new role (1.1.4)  Administrator Users Role Master 1.1.4 Role Rights: Administrator User can assign the access rights to the available roles (created by admin) (1.1.5)  Administrator Users Role Rights Click on Action icon of the respective roles to give the access rights.  Administrator User can select the respective role from the dropdown and assign the access of modules to it by clicking on check boxes next to the module name. Fig", "source": "User Manual - Chapter 1, Section 1.4", "title": null, "type": "user_manual_procedure", "procedure_type": "GENERAL_PROCEDURE", "metadata": {"chapter": 1, "section": "1.4", "procedure_type": "GENERAL_PROCEDURE"}}, {"content": " Administrator User will click on Submit button. 16 1.1.5 1.1.5 (a) User Registration: It shows the list of all the existing users of the application along with the details. Administrator User can also add new user from this screen. (1.1.6) 17 1.1.6 Reports: This allows the Administrator User to generate the reports as per requirements.", "source": "User Manual - Chapter 1, Section 1.1", "title": null, "type": "user_manual_procedure", "procedure_type": "GENERAL_PROCEDURE", "metadata": {"chapter": 1, "section": "1.1", "procedure_type": "GENERAL_PROCEDURE"}}, {"content": "The CCA roles will be created by the System Administrator beforehand. It may be noted that the entire work under CPMS has been segmented into four broad parts as follows: 1. System Administrator. 2. Collection and submission of Pension papers. The HoO and DH terms are used for CCA offices/BSNL offices/Other DOT units for Head of that particular office and Dealing hand. It is recommended HoO should be any officer of the rank of AAO or AO and <PERSON>H should be officer of the rank below AAO (Sr. Acct./Jr. Acct.). 3. Processing and sanction of Pension. 4. Disbursement of pensionary benefits and monthly pension.", "source": "User Manual - Chapter 1, Section 1.5", "title": null, "type": "user_manual_procedure", "procedure_type": "PENSION_SANCTIONING", "metadata": {"chapter": 1, "section": "1.5", "procedure_type": "PENSION_SANCTIONING"}}, {"content": "a) Administrator User will create CCA user name/password for login. b) CCA or any officer nominated by CCA shall be the highest authority in CCA office for login in CPMS. c) Path to create the CCA user: Login as Administrator  UsersUser Registration Click on “ADD NEW” (fill the user detail and save it).(1.3) 19 1.3 d) In next step Administrator User will assign roles to CCA. e) Path to assign the CCA Role and Rights: Login as Administrator  Users Role Master Click on “ADD NEW” (fill the user detail and save it).. 1.4&1.5) 1.4 20 1.5", "source": "User Manual - Chapter 1, Section 1.6", "title": null, "type": "user_manual_procedure", "procedure_type": "GENERAL_PROCEDURE", "metadata": {"chapter": 1, "section": "1.6", "procedure_type": "GENERAL_PROCEDURE"}}, {"content": "a) CCA will create the login of HoO. b) HoO is the second level login after CCA. c) Path to create the HoO User Login: Login as CCA Users User Registration (Select the Role Type as HoO, fill the user detail and save it).(1.6 (A)) d) For side channel (Telecom Circle) HoO creation, please refer Annexure 1. e) After creating the login, CCA will assign the authorization of modules to HoO by clicking on the Lock icon in the authorization column. (1.6 (i) (A)) f) CCA will subsequently assign rights. . 1.6 (i)(A)) g) CCA will enter/edit details of the HoO. (1.7 (A)) 1.6 (A) 21 Assign Access Rights 1.6 (i) (A) Edit user details 1.7 (A)", "source": "User Manual - Chapter 1, Section 1.7", "title": null, "type": "user_manual_procedure", "procedure_type": "GENERAL_PROCEDURE", "metadata": {"chapter": 1, "section": "1.7", "procedure_type": "GENERAL_PROCEDURE"}}, {"content": "a) DH is the third level login and subordinate to HoO. b) HoO will create the login for DH. 22 c) Path to create the DH User Login: Login as HoO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.8 (A) and 1.9 (A)) d) After creating the login, HoO will assign the authorization of modules to DH by clicking on the Lock icon in the authorization column. 1.9 (i) (A) 1.8 (A) Edit user details 1.9 (A) 23 Assign Access Rights 1.9 (i) (A)", "source": "User Manual - Chapter 1, Section 1.7", "title": null, "type": "user_manual_procedure", "procedure_type": "GENERAL_PROCEDURE", "metadata": {"chapter": 1, "section": "1.7", "procedure_type": "GENERAL_PROCEDURE"}}, {"content": "a) A<PERSON> is the second level login and subordinate to CCA in Pension Section. b) CCA will create the login for AO to work in Pension Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (1.10 and 1.11) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. 1.11 (a) e) CCA will subsequently assign rights. (1.11 (a)) f) Multiple AO Users can be created. 24 1.10 Edit user details 1.11 25 1.11 (a)", "source": "User Manual - Chapter 1, Section 1.8", "title": null, "type": "user_manual_procedure", "procedure_type": "GENERAL_PROCEDURE", "metadata": {"chapter": 1, "section": "1.8", "procedure_type": "GENERAL_PROCEDURE"}}, {"content": "a) AAO is the third level login and subordinate to AO in Pension Section. b) AO will create the login for AAO to work in Pension Section. (1.12 and 1.13) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (1.12 and 1.13) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. 1.13 (a) e) AO will subsequently assign rights. (1.13 (a)) f) Multiple AAO Users can be created. 1.12 26 Edit user details (1.13) Assign Access Rights 1.13 (a)", "source": "User Manual - Chapter 1, Section 1.8", "title": null, "type": "user_manual_procedure", "procedure_type": "GENERAL_PROCEDURE", "metadata": {"chapter": 1, "section": "1.8", "procedure_type": "GENERAL_PROCEDURE"}}, {"content": "a) DH is the third level login and subordinate to AAO. b) AAO will create the login for DH. c) Path to create the DH User Login: Login as AAO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.14 and 1.15) d) After creating the login, AAO will assign the authorization of modules to DH by clicking on Lock icon in the authorization column. 1.15 (i) e) Multiple DH Users can be created. 27 1.14 Edit user details 1.15 28 Assign Access Rights 1.15 (i)", "source": "User Manual - Chapter 1, Section 1.8", "title": null, "type": "user_manual_procedure", "procedure_type": "GENERAL_PROCEDURE", "metadata": {"chapter": 1, "section": "1.8", "procedure_type": "GENERAL_PROCEDURE"}}, {"content": "a) A<PERSON> is the second level login and subordinate to CCA in Pension Disbursement Section. b) CCA will create the login for AO to work in Pension Disbursement Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (1.16 and 1.17) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. 1.17 (i) e) CCA will subsequently assign rights. (1.17 (i)) f) Multiple AO Users can be created. 1.16 29 Edit user details (1.17) Assign Access Rights 1.17 (i)", "source": "User Manual - Chapter 1, Section 1.9", "title": null, "type": "user_manual_procedure", "procedure_type": "GENERAL_PROCEDURE", "metadata": {"chapter": 1, "section": "1.9", "procedure_type": "GENERAL_PROCEDURE"}}, {"content": "a) AAO is the third level login and subordinate to AO in Pension Disbursement Section. b) AO will create the login for AAO to work in Pension Disbursement Section. (1.18 and 1.19) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (1.18 and 1.19) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. 1.19 (i) e) AO will subsequently assign rights. (1.19 (i)) 30 f) Multiple AAO Users can be created. (1.18) Edit user details (1.19) 31 Assign Access Rights 1.19 (i)", "source": "User Manual - Chapter 1, Section 1.9", "title": null, "type": "user_manual_procedure", "procedure_type": "GENERAL_PROCEDURE", "metadata": {"chapter": 1, "section": "1.9", "procedure_type": "GENERAL_PROCEDURE"}}, {"content": "a) D<PERSON> is the third level login and subordinate to AAO in Pension Disbursement Section. b) AAO will create the login for DH. c) Path to create the DH User Login: Login as AAO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.20 and 1.21) d) After creating the login, AAO will assign the authorization of modules to DH by clicking on lock icon in authorization column. 1.21 (i) e) Multiple DH Users can be created. (1.20) 32 Edit user details (1.21) Assign Access Rights (1.21) (i) Important Points to Remember  Administrator cannot create the login for any other User except CCA.  CCA cannot create login for any other user except HoO, AO(Pen) and AO(PDA)  HoO cannot create login for any other user except DH.  AO cannot create login for any other user except AAO.  AAO cannot create login for any other user except D<PERSON>.  In CPMS, each user is uniquely identified with a username, password, role and rights alongside other settings. The role and rights determine which tasks a user can perform, what data user can see, and how to perform the required activities using the available data. 33 Additional Key Points 1. On first login, all Users shall be prompted to update their profiles and change passwords. 2. In case of transfer of personnel or for any updating of profile information, the new details can be incorporated by writing to the Authority who created the user. 3. It is important that user creation is immediately followed by grant of authorisation rights, otherwise the user will find an empty screen. 4. CC<PERSON> user must check the list of HoO available for creation. If he finds any error in name, it should be immediately brought to the notice of Administrator by writing to CP<PERSON> helpdesk <EMAIL>. 34", "source": "User Manual - Chapter 1, Section 1.9", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 1, "section": "1.9", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to login to create the retiree profile.  Select Retiree details on the Menu options available on the left pane.  Click on “Add New” button at the right top corner of Retiree Profile page to get the form to fill.  Enter the following information on retiree detail form (underlined parameters are the mandatory fields): a. Title b. First Name c. Middle Name d. Last Name e. Type of Retirement f. Height g. Father’s/ Husband’s Name. h. Mother’s Name i. Date of Birth j. Gender k. A<PERSON>ar Number l. PAN Number m. Mobile Number (This should be an Active Number) n. Email ID o. Identification Mark 1 p. Identification Mark 2  Following details to be filled in other details section (All are mandatory fields): a. Designation b. Group c. Date of Joining d. Date of Retirement e. Date of Superannuation f. Govt. Accommodation provided by Directorate of Estates/BSNL at any time of service: YES/ NO (to select from the dropdown) 35 g. Pay Commission (this should be the Pay Commission corresponding to the last pay drawn) *After filling the aforementioned information, DH to click on Save button. By using the Clear button, User can also refresh the form and fill in fresh information. (2.1) IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore, due diligence should be exercised while creating the Retiree Profile. In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk. 2.1 Creation of the Retiree Profile is the most important step in the process, so it should be ensured entries made therein are correct in all respects. After saving the information, it is recommended that a printout of the page be taken from the screen and Vetted by HoO level User before proceeding ahead in Service Verification. If any error is detected, then retiree profile be edited before initiating 2.1.2(Further updates in chapter 11) 36", "source": "User Manual - Chapter 2, Section 2.1", "title": null, "type": "user_manual_procedure", "procedure_type": "RETIREE_PROFILE", "metadata": {"chapter": 2, "section": "2.1", "procedure_type": "RETIREE_PROFILE"}}, {"content": " DH to re-check the form and send it for approval to HoO for service book verification.  HoO to login and approve the service book verification. After HoO approves the form, the form will appear in ‘Send Form to Retiree’ tab.  HoO can also return the form to DH in case any discrepancy is found. Then DH will have to again verify and send the form to HoO for approval. (2.2)  Before feeding Service Book information and updating, it has to be ensured that the Service Book is actually verified for the period mentioned. If any period remains unverified, please mention it in Remarks and keep a printout in the pension file. Service Book verification should be completed at the earliest.  This activity has to be completed 12 months before date of retirement. 2.2", "source": "User Manual - Chapter 2, Section 2.1", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 2, "section": "2.1", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to send the form for approval to HoO. (2.3)  HoO to login and approve the form. HoO can also return the form to DH by clicking on Return button in case any discrepancy has been found. (2.4)  After the approval form HoO, form will be sent to Retiree to fill the required information. 37  This activity has to be completed by 8 months before date of retirement. 2.3 2.4 38", "source": "User Manual - Chapter 2, Section 2.1", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 2, "section": "2.1", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to check that the form received is correctly filled and that all scanned documents uploaded are of good quality. Then on receipt of hard copy (duly signed by the Re<PERSON>ree), DH may cross verify the details and also check whether all enclosures (as per checklist) have been duly attached. Then DH shall send it to HoO.  HoO to Approve/ Return the form as applicable. (2.6)  If HoO approves the form, it will appear in ‘Form Verification’ tab of DH User. In case HoO returns the form, it goes back to DH for re-verification. If now, any error is detected by DH, then file has to be returned to retiree with remarks. Re<PERSON>ree shall correct the error and resubmit the papers. 2.5 39 2.6", "source": "User Manual - Chapter 2, Section 2.5", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 2, "section": "2.5", "procedure_type": "FORM_PROCESSING"}}, {"content": " This process has been especially incorporated to put another check on correctness of the forms submitted by Retiree.  DH to check and verify the form and send it for the approval to HoO by clicking on “Verify” <PERSON><PERSON> (2.7)  HoO to Approve/ Return the form as applicable. (2.8)  If HoO approves the form, it will appear in ‘Form 7’ tab of DH. In case the HoO returns the form, it goes back to DH for re-verification.  Simultaneously, the forms/papers will be countersigned by the competent authority in the physical file and process for preparation of Form 7 initiated. 40 Fig2.7 2.8", "source": "User Manual - Chapter 2, Section 2.1", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 2, "section": "2.1", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to verify and fill the required information in Form 7.  Some information in Form 7 is auto populated. Others have to be entered. (2.9) a) Name of the retiring Government Employee 41 b) Father's/Husband's Name c) PAN NO. d) Height & Marks of identification e) Date of Birth f) Service to which he/she belongs (indicate name of Organised service, if any, otherwise say General Central Service) g) Particulars of post held at the time of retirement h) Name of the office i) Post held j) Scale of Pay/Pay Band & Grade pay of the post k) Basic Pay/Pay in the pay band & Grade pay. l) Basic Pay/Pay in the pay band & Grade pay m) Whether the appointment mentioned above was under Government or outside the Government on foreign service terms? n) If on foreign service, scale of pay/pay band, pay in the pay band and grade pay of the post in the parent department. o) Whether declared substantive in any Post under the Central Govt.? p) Date of beginning of service q) Date of ending service r) Cause of ending service s) In case of compulsory retirement, the orders of the competent authority whether pension may be allowed at full rates or at reduced rates and in case of reduced rates, the percentage at which it is to be allowed (Please See Rule 41) t) In case of removal/dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41) u) Particulars relating to military service, if any. v) Particulars relating to the service in autonomous body, if any. w) Whether any Departmental or judicial proceedings in terms of rule 9 of the CCS(Pension) Rules, 1972 are pending against the retiring employee. (lf yes, in terms of Rule 69, provisional pension will be admissible and gratuity will be withheld till the conclusion of departmental or judicial proceedings and issue of final orders) x) Length of service i. Details of omission, imperfection or deficiencies in the Service Book which have been ignored [under rules 59(1) (b) (ii)] ii. Period not counting as qualifying service. iii. Additions to qualifying Service. iv. Whether any leave without pay. v. Net Qualifying service. 42 vi. Qualifying service expressed in terms of complete six monthly periods (Period of three months and above is to be treated as completed six monthly periods (Rule 49) y) Emoluments a. Emoluments in terms of Rule33. b. Emoluments drawn during ten months pending retirement. z) Others: Details of Govt. dues recoverable out of gratuity aa) Licence fee for Govt. accommodation see sub-rules (2), (3) and (4) of rule 2) bb) Dues referred to in Rule 73 cc) Amount indicated by Directorate of Estates to be withheld under Sub rule(S) of Rule 72 dd) Post-retirement address of the retiree 43 2.9  After the verification, DH will click on tab “Submit and Calculate” to calculate the pensionary benefits.  DH will then send form 7 to HoO by clicking on save calculation and submit  HoO will Approve/ Return the form.  If HoO approves the form it will be processed further. In case of Return, form will be sent back to DH for reverification and the same process followed for approval. IMPORTANT: -Form 7 calculates the pensionary benefits and pension as applicable to the pensioner. Once this form is filled and next stage initiated it cannot be edited in HoO section. Therefore, due diligence 44 should be exercised while filling in all important fields like Pay Band, Pay level, Qualifying/Non Qualifying service etc. In case Wrong or incorrect information is saved in the system, please immediately inform the Helpdesk.", "source": "User Manual - Chapter 2, Section 2.1", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 2, "section": "2.1", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to verify and fill the minimum required information in Form 8. Some part of information is auto populated in Form 8. The details of recovery under various heads has to be filled up in this Form. It may be ensured that the total matches with the details filled in Form 7 (2.10).  If Nil recovery is due, then just click “Save and Send for Approval”.  HoO will approve/ Return the form.  If HoO approves the form it will be processed further. In case of Return, Form will be sent back to DH for reverification and the same process followed for approval.  The printouts of Form 7 and 8 can be taken from ‘View Forms’ tab. Approval of competent authority may be taken on the physical Form 7 and 8. 45 2.10 Form 7 and Form 8 will be processed roughly four months before the date of retirement (Superannuation). HOO will again send the final papers like retirement order, LPC, No dues and Vigilance clearance. If there is any change in the pay etc. before retirement, then based on final papers received Form 7 shall be digitally revised in Pension section. After processing the form 7 from DH level, if HoO user is satisfied then a printout may be taken and along with details of recovery which will be fed in form 8, approval and signature of competent authority may be taken. If any error is detected in the process, then HoO may return to DH the form 7 for necessary correction. DH shall correct it and send it to HoO user again. The approval of competent authority shall be taken. Afterwards, form 8 shall also be filled as already approved and approval taken.", "source": "User Manual - Chapter 2, Section 2.1", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 2, "section": "2.1", "procedure_type": "FORM_PROCESSING"}}, {"content": " After all the aforementioned steps, DH will submit the form to Pension Section by clicking on “Send to PAO”. While sending this please ensure that all the documents mentioned in checklist are being sent. The papers may then be dispatched by post.", "source": "User Manual - Chapter 2, Section 2.1", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 2, "section": "2.1", "procedure_type": "FORM_PROCESSING"}}, {"content": " All users’ can view the list of all the retirees and their generated forms.  Printout of Form7 and Form 8 can be taken by clicking on ’Print’ and sent to Pension Section of concerned CCA office. (2.11) 46 2.11", "source": "User Manual - Chapter 2, Section 2.1", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 2, "section": "2.1", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)", "source": "User Manual - Chapter 2, Section 2.2", "title": null, "type": "user_manual_procedure", "procedure_type": "DH_CREATION", "metadata": {"chapter": 2, "section": "2.2", "procedure_type": "DH_CREATION"}}, {"content": " Following information need to be entered on personal detail page: a. Title b. First Name c. Middle Name d. Last Name e. Type of retirement. f. Height g. Father’s/ Husband’s Name. h. Mothers Name i. Date of Birth j. Gender k. Aadhaar Number l. PAN Number m. FP Mobile Number n. FP Email ID o. FP Identification Mark 1 p. FP Identification Mark 2 q. Employee Code 47 r. Office  Following details to be filled in Other Details section (All are mandatory fields): a. Designation at the time of death b. Group c. Date of Joining d. Date of Death e. Date of Superannuation f. Govt. Accommodation provided by Directorate of Estates/BSNL at any time of service: YES/ NO (to select from the dropdown) g. Pay Commission *After filling the aforementioned information, DH to click on Save button. Once filled the details cannot be changed so due diligence should be exercised before saving. (2.13) To expedite the disbursement of pensionary benefits, Family Pensioner should immediately be advised to submit the required information along with relevant documents to the HoO. Family Pensioners’ may also be advised to provide mobile phone number (mandatory) so that they can get updates on the pension process and pension disbursement. 2.13 48  After saving the details, an instance will be created in the Personal Details tab, as shown in the 2.14. 2.14  Then DH has to click on Govt. Emp Details edit tab, and proceeds to the subsequent screen.  The next screen will show three tabs viz. Personal details, Family Details and Nomination Form1 (2.15) 2.15 49  Now the DH has to fill in details of Family and Nominations (2.16, 2.17, 2.18) 2.16 2.17 50 2.18  After filling all the details, the DH will submit the form by clicking submit button.", "source": "User Manual - Chapter 2, Section 2.13", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 2, "section": "2.13", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to select ‘Service Verification’ tab from the Menu options.  DH to check the form and send it for approval to HoO for service verification. Before this is done, physical service book verification needs to be done as well. Any unverified portion of the service book should be noted and attached in the file being sent to Pension Section (CCA Office). (2.14)  HoO to login and approve the service book verification. HoO also to verify the service book physically.  HoO can also return the form to DH in case any discrepancy is found. Then DH will again have to verify and send the form to HoO for approval. 2.14 51", "source": "User Manual - Chapter 2, Section 2.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 2, "section": "2.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to verify and fill the required information in Form 14. DH may select the claimant who is eligible for pension. This is to be done as per CCS Pension Rules,1972. The Form shows the claimant details, the bank details, the documents to be uploaded like photograph, signature and death certificate etc. The Bank Undertaking Form also needs to be uploaded duly signed by bank authorities as well the Family Pensioner. FMA option has to be chosen (if applicable and desired by the Family Pensioner) (2.15 (a), 2.15 (b) and 2.15 (c)).  It should be seen that the photograph and signature are clear and visible.  DH clicks on ‘Form 12’ tab. 52 2.15 (a) 53 2.15 (b) 2.15 (c) IMPORTANT: -Form 14 prompts the user to enter bank details. The pension of the family pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Family Pensioner, and then uploaded on CPMS. 54", "source": "User Manual - Chapter 2, Section 2.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 2, "section": "2.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to verify and fill the required information in Form 12. For each claimant who has been nominated for gratuity in the nomination form filled earlier. Here <PERSON><PERSON> has to click on the Edit button against the name of the nominee, and his/her details will be populated in the Form 12 (2.16).  Again Bank Account details and claimant’s details, signature needs to be uploaded.  After updation of Form 12 and 14, the case will appear in ‘Form 18’ tab of DH. 2.16 IMPORTANT: - Form 12 prompts the user to enter bank details. The DCRG of the family pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Family Pensioner, and then uploaded on CPMS. In case of claimant who is claimant for only Gratuity and not pension, only Mandate form, as generated, needs to be filled by the pensioner and uploaded in portal. In case where there is no nomination, DCRG has to be divided in equal shares among the family members. Accordingly, the nomination form and Form 12s will be filled. 55", "source": "User Manual - Chapter 2, Section 2.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 2, "section": "2.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to verify and fill the required information in Form 18. Some information is auto populated in Form 18. D<PERSON> should be careful while filling the important details like qualifying service, non-qualifying service period, last month’s pay details etc. Pensionary benefits will be calculated on basis of these figures and hence figures must be cross-verified from service book. (2.17)  DH will send Form 18 for approval of HoO.  HoO will approve/ Return the form. <PERSON><PERSON> should verify the Pensionary benefit amounts. If the amounts are correct, he/she can approve it. If incorrect, <PERSON><PERSON> can return the case back to DH for rectification.  If HoO approves the form it will be processed further. In case of Return, form will be sent back to DH for reverification and thereafter process followed for approval. 56 57 2.17  Form 18 and 19 to be put up in physical file for approval of competent authority. IMPORTANT: -Form 18 calculates the pensionary benefits and pension as applicable to the Family pensioner. Once this form is filled and next stage initiated it cannot be edited in HoO section. Therefore, due diligence should be exercised while filling in all important fields like Pay Band, pay level, Qualifying/Non Qualifying service etc. In case Wrong or incorrect information is saved in the system, please immediately inform the Helpdesk. After processing the form 18 from DH level, if HoO user is satisfied then a printout may be taken and approval and signature of competent authority may be taken. If any error is detected in the process, then HOO may return to DH the form 18 for necessary correction. DH shall correct it and send it to HoO user. The approval of competent authority shall be taken. 58", "source": "User Manual - Chapter 2, Section 2.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 2, "section": "2.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " After all the aforementioned steps, DH will submit the form to Pension Section (CCA Office) by clicking on “Send to PAO”.", "source": "User Manual - Chapter 2, Section 2.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 2, "section": "2.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " The pension case coming from HoO will appear in ‘Allotment’ tab of the Accounts Officer (AO) of the Pension Sanctioning Section. AO pension sanctioning section will allot the case to the Dealing Hand of the section. (3.1)  The current practice based on which cases are allotted to DH by AOs may be continued. This will be done by the AO who has been authorized to allot the case (It is to be noted that only 1 AO is authorised for allotment). 3.1", "source": "User Manual - Chapter 3, Section 3.1", "title": "Allotment of Pension Case to DH", "type": "user_manual_procedure", "procedure_type": "PENSION_SANCTIONING", "metadata": {"chapter": 3, "section": "3.1", "procedure_type": "PENSION_SANCTIONING"}}, {"content": " DH (Pension Sanctioning Section) will receive the case allotted by AO (Action->Pension Section->Form Received).  DH (Pension Sanctioning Section) will select on the particular pension case, and click on Receive Form tab (3.2). 60  DH (Pension Sanctioning Section) will then select the forms submitted by the HoO/ACCA. DH will then enter the actual date of receipt of physical forms and other documents. DH will then save the case (3.3).  A physical file has to be opened by the DH upon receipt of forms and documents by the DH. 3.2 3.3 61", "source": "User Manual - Chapter 3, Section 3.2", "title": "Form Received", "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 3, "section": "3.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " The case will now flow into the ‘Pay Regulation’ tab of DH (Pension Sanctioning Section) level (Action->Pension Section->Pay Regulation).  DH (Pension Sanctioning Section) will select the case and click on Pay Regulation Sheet (3.4).  DH (Pension Sanctioning Section) will then add Pay Scale Data for the particular pension case and save the same. 3.4 62 3.5 (a) 3.5 (b)", "source": "User Manual - Chapter 3, Section 3.3", "title": "Pay Regulation", "type": "user_manual_procedure", "procedure_type": "PENSION_SANCTIONING", "metadata": {"chapter": 3, "section": "3.3", "procedure_type": "PENSION_SANCTIONING"}}, {"content": " The above case will now flow into Account Enfacement (AE) at DH (Pension Sanctioning Section) level (Action->Pension Section->Account Enfacement). 63  The DH (Pension Sanctioning Section) will then select the particular pension case. The DH (Pension Sanctioning Section) can edit the Pay Details by clicking on ‘Pay Regulation’ tab, if the same was not correctly done in 3.3 above.  DH (Pension Sanctioning Section) shall analyse if the case is fit for finalisation of pension or not. If some documents have not been received, then he/she will generate AE by clicking on ‘Generate AE’ tab. The same shall be put up for approval of competent authority along with pay regulation sheet.  The Approved AE shall be uploaded by in the software.  The physical copy of the AE will be sent by mail/dak to concerned HoO. Reminders can be sent using facility for regenerating AE. However, when necessary papers have been received, the ‘Resolve AE’ tab may be clicked. 3.6 (a) 64 3.6 (b) A Pension case in which all the papers have not been received or some matter remains unaddressed shall remain pending at AE stage itself. Only when all issues relating to pensioner etc are finalized then case should move beyond AE tab. Before going to Revise Form Stage, it may be checked that Pay regulation has been correctly filled.", "source": "User Manual - Chapter 3, Section 3.4", "title": "Account Enfacement", "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 3, "section": "3.4", "procedure_type": "FORM_PROCESSING"}}, {"content": " The case will now flow from Account Enfacement at DH (Pension Sanctioning Section) level to ‘Revise Form List’ Tab(Action->Pension Section->Revise Form List).  The DH (Pension Sanctioning Section) will then select the particular pension case. The DH (Pension Sanctioning Section) can view Pay Regulation Form. The calculation sheet can be reviewed and edited, if required. It should be noted that if there is any discrepancy in calculation of pension or pensionary benefits, the same can be rectified here. After this the figures cannot be rectified. Hence due diligence need to be exercise here. (3.7)  DH (Pension Sanctioning Section) will, if required, edit FORM 7 (Family Pension) or FORM 18 (Normal Pension) and calculate the Retiree’s pensionary benefits. (3.7)  DH (Pension Sanctioning Section) will then enter the following information in FORM 7/ FORM18 (some information is auto populated): 65 a) Service to which he/she belongs. b) Scale of Pay/Pay Band & Grade pay of the post. c) Whether the appointment mentioned above was under Government or outside the Government on foreign service terms d) Particulars relating to military service, if any e) Particulars relating to the service in autonomous body, if any f) Period not counting as qualifying service? g) Additions to qualifying Service? h) Whether any leave without pay? *Points d, e, f, g and h to be filled in carefully, as they will impact the calculation of net qualifying service. i) Emoluments drawn during 1/ 10 month before retirement. *Point “i” will calculate Average Emolument, which will be compared against LPD to arrive at pensionary benefits. j) Govt. dues recoverable out of gratuity.  After filling the details, the DH will click on submit and calculate button. The amount of gratuity, commutation and monthly pension will be calculated and displayed.  ‘Revise Form ‘action can be performed if there is a need to carry out correction in Form 7 received from HoO. 3.7 (a) 66 3.7 (b) 67 3.7 (c) 68 IMPORTANT: -Calculation Sheet shows the pensionary benefits and pension as applicable to the pensioner. Once this form is reviewed and next stage initiated, it cannot be edited. Therefore, due diligence should be exercised while reviewing and correcting, if required, in all important fields like Pay Band, pay level, Qualifying/Non Qualifying service etc. In case Wrong or incorrect information is saved in the system, please immediately inform the Helpdesk.", "source": "User Manual - Chapter 3, Section 3.5", "title": "Revise Form List", "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 3, "section": "3.5", "procedure_type": "FORM_PROCESSING"}}, {"content": " The case will now flow into Calculation Sheet at DH (Pension Sanctioning Section) level (Action->Pension Section-> Calculation Sheet).  DH (Pension Sanctioning Section) will select the case and view the calculation sheet and submit it to AAO for further approval. (3.8) 69 3.8 70  Now the case will flow to AAO (Pension Sanctioning Section) (AAO Login Approval Pension Section Calculation Sheet)  The AAO (Pension Sanctioning Section) can either Approve/ Return the case. (3.9)  If AAO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. In case of Approval, it will go to AO (Pension Sanctioning Section).  Now the case will flow to AO (Pension Sanctioning Section) (AO Login Approval Pension Section Calculation Sheet)  The AO (Pension Sanctioning Section) can Approve/ Return the case.  If AO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension Sanctioning Section) for editing in “Revise Form” tab. 3.9 (a) 71 3.9 (b)", "source": "User Manual - Chapter 3, Section 3.6", "title": "Calculation Sheet", "type": "user_manual_procedure", "procedure_type": "AO_LOGIN_CREATION", "metadata": {"chapter": 3, "section": "3.6", "procedure_type": "AO_LOGIN_CREATION"}}, {"content": " After the Approval of Calculation Sheet by the AO, the case will come to Pension Sanction tab of DH Login.(DH Login Action Pension Section Pension Sanction)  DH can view Sanctions and ePPO. 3.10) (a)  Now DH will send the case to AAO for approval.  AAO can approve the case and send it to AO for further approval. (AAO Login Action Pension Section Pension Sanction)  AO can approve the case (AO Login Action Pension Section Pension Sanction)", "source": "User Manual - Chapter 3, Section 3.7", "title": "Pension Sanction", "type": "user_manual_procedure", "procedure_type": "AO_LOGIN_CREATION", "metadata": {"chapter": 3, "section": "3.7", "procedure_type": "AO_LOGIN_CREATION"}}, {"content": " This approval can be done only on Internet Explorer version 9 or 11 and by using the Digital Signature Certificate (DSC) of AO Pension Section.  Post approval by AO, case will move to the AO- PDA section.  DSC installation and signature process is dealt separately in the on DSC Installation. 72 3.10 (a) 3.10 (b) 73 3.10 (b) Important: - AO Pension section will send the physical copy of following documents to AO PDA, after Digital signing of EPPO and Sanctions are done (the same is also mentioned in the enclosures section of PPO Authority Model). 1. Physically signed copy of Pension authority 2. Photo Copy of CDA/IDA/DR List. 3. Photocopy of PAN no and Aadhaar no. 4. Bank undertaking and Mandate form of pensioner, indicating name and full address of the Authorised Public Sector Bank. 74", "source": "User Manual - Chapter 3, Section 3.10", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 3, "section": "3.10", "procedure_type": "FORM_PROCESSING"}}, {"content": " The pension case coming from Pension Sanctioning Section will appear in the ‘Allotment’ tab of the AO PDA Section. (AO PDA Login Allotment Allocate to PDA- DH)  AO PDA will allot the case to the Dealing Hand of the PDA section. .4.1) Fig.4.1  Now Case will flow to DH PDA Section. (DH PDA Login Action PDA Section Sanction Order Received).  DH should take the printouts of digitally signed EPPO(2 copies) Commutation Sanction, Gratuity Sanction, PPO Authority Model in 5 sets from the links available on the page, as shown in the screenshot below. After AO PDA approves and sends the case to PFMS for payment, the above hardcopies (1 set each) needs to be physically sent to HoO section, Pension section, Cash section and Pensioner. One copy will remain with PDA section.  Now the DH (PDA Section) will select the case and click on Receive Sanction & Send to Bill button. At this stage the name of the employee will appear in the name column of the table as shown below. 75 Fig.4.2", "source": "User Manual - Chapter 4, Section 4.1", "title": "Allotment of Pension Case", "type": "user_manual_procedure", "procedure_type": "PENSION_SANCTIONING", "metadata": {"chapter": 4, "section": "4.1", "procedure_type": "PENSION_SANCTIONING"}}, {"content": " Now, from the DH login of PDA Section go to Action then Vendor Verification (DH PDA Login Action Vendor Verification). The case will appear here in the name of pensioners/ claimants. From PFMS the pensioner’s/ claimant’s (Vendor) bank credentials will be verified and the status with error description in the table will be shown as in Fig. 4.3 below. This process takes approximately 5-10 minutes and when the verification is complete, the case will automatically go to Bill Generation Tab. Otherwise failed cases will remain at this stage which may be Resend.  In case the failure is due to wrong Bank account details, the same will be flashed in the Error Description column. The wrong Bank account details can be rectified from the PDA Utility tab in the AO PDA (Go to AO PDA login, open PDA Utility tab, enter PPO no. of the particular case and edit the Bank details as shown in Fig. 4.4(a) and Fig. 4.4(b)). 76 Fig.4.3  If the case has failed because of any reason other than wrong bank details, the case may be Resend. Fig.4.4 77 Fig.4.4(a) Fig.4.4(b) After completion of Vendor Verification in PFMS the bill will move to the next section of DH log-in of PDA Section i.e. Bill Generation.", "source": "User Manual - Chapter 4, Section 4.2", "title": null, "type": "user_manual_procedure", "procedure_type": "DH_CREATION", "metadata": {"chapter": 4, "section": "4.2", "procedure_type": "DH_CREATION"}}, {"content": " From the DH log-in of PDA Section go to Others Bill Generate (DH PDA LoginBill GenerationOthers Bill Generation). Bill other than monthly bills like Commutation, DCRG, etc. will appear at this Tab. Here the bills are appearing in the name of claimants who will get the payment through PFMS. Now select the case from the check box and click Generate <PERSON>. The bill will be generated and it will go to AAO of PDA Section. 78 Fig.4.5  AAO of PDA Section will log-in and select the case by clicking the check box corresponding to the case and click on Approve button (AAO PDA LoginApprovalOthers BillsApprove). The case will go to AO of PDA Section for approval. Fig.4.6  Now, AO of PDA Section will log-in and go to approval then other bills (AO PDA Login Approval Others Bills). The AO will then select the appropriate Account Head Code from the drop down list on the top. After entering the “Not Payable Before Date” click on Approve button. Now the Bill will automatically go to PFMS for payment.  After this, the 5 copies of digitally signed Sanctions (taken earlier by DH) should be sent to the HoO section, Pension Section, Cash Section, Pensioner and 1 copy retained in PDA section (as mentioned on 71, sub heading 4.1 of this manual). 79 Fig.4.7", "source": "User Manual - Chapter 4, Section 4.3", "title": null, "type": "user_manual_procedure", "procedure_type": "PENSION_SANCTIONING", "metadata": {"chapter": 4, "section": "4.3", "procedure_type": "PENSION_SANCTIONING"}}, {"content": " Now go to PFMS and log-in with PAO credential. Select the Bill(s), Generate Payment Batch File and sign Digitally with the DSC. If the amount of the payment is more than 10 lakhs, signatory 2 is required for making the payment. The Payment screens are given below. Fig.4.8 80 Fig.4.9 Fig.4.10 81 Fig.4.11  It is recommended that above payment from PFMS should be done on the same day, when AO PDA sends the case to PFMS. After successful payment from PFMS the cases will appear LC & DLC Verification tab of PDA DH.  PDA AO can view, if required, the PFMS transaction report of pensionary benefits of the pensioner/claimant, in the Reports TabOther Bill Report.  Thereafter the cases will be processed as per the arrear section, below.", "source": "User Manual - Chapter 4, Section 4.4", "title": null, "type": "user_manual_procedure", "procedure_type": "PAYMENT_PROCESSING", "metadata": {"chapter": 4, "section": "4.4", "procedure_type": "PAYMENT_PROCESSING"}}, {"content": " After successful payment, login from DH PDA and click the tab LC & DLC Verification (DH PDA Login LC & DLC Verification  LC Verification). 82 Fig.4.12  Now click on Verify link shown in the Action column, a popup window will appear as shown in the figure below. Here we have to select the Life Certificate and Non Employment (FMA also, in case of CDA pensioners) Validity, From and to dates (From date will be next date, from the date of retirement). After filling the details click on Confirm button. (Note-The above certificate date is for non-employment of pensioner) Fig.4.13  After LC & DLC the case will go in Arrears tab (PDA DH login Action Bill Generation  Arrears) 83  Here select the Reason for Arrear and fill the PPO Number of pensioner, then click on the Search button. Now it will show the Arrear calculation. After checking the calculation, printout of the arrear details should be taken and the soft copy of the same saved (by clicking ctrl +P). Now click on the Save & Send for Approval button. Then case will go to AAO of PDA Section for approval. (The above printout will be used as Arrear Sanction for PFMS payment).  AAO PDA will select the case (by clicking the check box corresponding to the case) and click on Approve button (PDA AAO login Action ApprovalPDA Arrears). The case will go to AO of PDA Section for approval. 84  Now, AO PDA (PDA AO login Action ApprovalPDA Arrears) will select the appropriate Account Head Code from the drop down list. Now select the case (by clicking the check box corresponding to the case) and enter Not Payable Before Date and click on Approve button. Now the Bill will automatically go to PFMS for arrear payment.  After Arrears payment, the case will go to monthly bill section (PDA DH login Action Bill Generation Monthly bill (Normal/Family, IDA/CDA)).", "source": "User Manual - Chapter 4, Section 4.5", "title": "Arrears", "type": "user_manual_procedure", "procedure_type": "AO_LOGIN_CREATION", "metadata": {"chapter": 4, "section": "4.5", "procedure_type": "AO_LOGIN_CREATION"}}, {"content": " While processing the case for monthly bill the income tax deducted shall be filled and accordingly the bill shall be processed. The income tax deducted shall be used for feeding income tax on monthly and quarterly basis.  A printout of the monthly bill shall be taken and handed over to AO Cash conveying the sanction.  Thereafter the cases in DH can be passed to AAO and further to AO for approval. Then AO will send the case for monthly bill/pension payment, through PFMS. This cycle will be repeated for every month. 85 Fig.4.14 Fig.4.15 86  PDA AO can view the PFMS arrear transaction report of the pensioner/claimant, in the Reports TabOther Bill Report. (4.16) Fig.4.16", "source": "User Manual - Chapter 4, Section 4.6", "title": "Monthly Bill", "type": "user_manual_procedure", "procedure_type": "PENSION_SANCTIONING", "metadata": {"chapter": 4, "section": "4.6", "procedure_type": "PENSION_SANCTIONING"}}, {"content": " Retiring officer/official can see the progress of his/her application at the top of the dashboard (5.1)  Milestones completed will be shown in GREEN colour dots and the pending ones with RED colour dots.  <PERSON><PERSON><PERSON> can see the pensionary benefits and lodge their grievance, if any, using the dashboard.  <PERSON><PERSON><PERSON> can see his/her pension details by clicking on pension ledger. 87 5.1", "source": "User Manual - Chapter 5, Section 5.1", "title": null, "type": "user_manual_procedure", "procedure_type": "GRIEVANCE_MANAGEMENT", "metadata": {"chapter": 5, "section": "5.1", "procedure_type": "GRIEVANCE_MANAGEMENT"}}, {"content": " Pensioner/Retiree should login into CPMS using PAN and Password received on SMS (NOTE: In case of Family Pension the Retiree Profile will be created by the SSA office itself). Click on Pensioners Details >> Profile. 5.2 88 5.2  Few details will be pre-populated in the profile of the retiree and rest of the details will be filled by the retiree (In case there is any discrepancy in the pre-populated details of the retiree, he/she may write to SSA unit and get the same rectified). Personal Details Tab:  Click on Personal Details Tab and fill the same. After filling the same click on Save button and move on to next tab ‘Commutation and FMA’. 5.3 89 5.3 Commutation and FMA Tab: 90 In this tab user will fill the FMA and Commutation details. The commutation percentage can be maximum 40%. 5.4 In case FMA is applicable, retiree needs to select his/her area of residence as CGHS or non- CGHS. Other requirements may also be filled in. 5.4 Family Details Tab:  <PERSON><PERSON><PERSON> will fill information about his/her family members in this tab.  Then the Retiree should fill the nominations, alternate nominees etc. and keep clicking save button to move on to the next tabs. 5.5  The <PERSON><PERSON><PERSON> should NOT fill his/her own details in this tab. 91 5.5 Nomination Form 1: 5.6 5.6 92 Nomination Form A 5.7 5.7 Bank Details Tab: <PERSON><PERSON><PERSON> will fill the bank details in this tab. He/she should be careful while entering these details as this is important information for the pension disbursement. 5.8  After filling the bank details, bank undertaking will be printed (button provided on screen as Print Bank Undertaking). Pensioner will himself/herself sign the same and also get signed from bank authorities. Retiree will then upload the signed Bank Undertaking on CPMS (upload a file). 93 5.8 IMPORTANT: -Pensioner profile prompts the pensioner to enter bank details. The pension and pensionary benefits of the pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Pensioner, and then uploaded on CPMS. Check List Tab: The Retiree will fill the Check List as per the criteria and scenarios. SL. No. 2, 7, 9 cannot be NO. Retiree need to fill them carefully. 5.9 5.9 *Click on ‘Final Submission ‘tab whereupon the case will be finally saved. Once finally submitted, the retiree will not be able to change the data. Hence the details should be thoroughly checked before submission. 5.9 94 The Retiree shall take print out of the forms-form 5, form 3, Nomination forms and Form 1A-and after signing duly submit them with enclosures as mentioned in the checklist of form 5 to HoO.", "source": "User Manual - Chapter 5, Section 5.2", "title": null, "type": "user_manual_procedure", "procedure_type": "RETIREE_PROFILE", "metadata": {"chapter": 5, "section": "5.2", "procedure_type": "RETIREE_PROFILE"}}, {"content": "For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (5.10)). (5.10) A Pop-up window (5.11)) will be displayed with the option to choose the following details which the user wants to edit or change: - 95 (5.11) The user has to select the option which he/she wants to change or modify.", "source": "User Manual - Chapter 5, Section 5.3", "title": "Updation of Mobile, Email and address", "type": "user_manual_procedure", "procedure_type": "PROFILE_UPDATE", "metadata": {"chapter": 5, "section": "5.3", "procedure_type": "PROFILE_UPDATE"}}, {"content": "Upon selecting ‘Mobile Number’, the following screen will be displayed (5.12)) Fig(5.12) <PERSON><PERSON><PERSON> can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((5.12)). Upon receiving the OTP, retiree should enter the OTP ((5.13)) and save which will then update the mobile number. 96 (5.13)", "source": "User Manual - Chapter 5, Section 5.3", "title": null, "type": "user_manual_procedure", "procedure_type": "PROFILE_UPDATE", "metadata": {"chapter": 5, "section": "5.3", "procedure_type": "PROFILE_UPDATE"}}, {"content": "Upon selecting ‘Email ID’, the following screen will be displayed (5.14)) (5.14) <PERSON><PERSON><PERSON> can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((5.14)). <PERSON><PERSON><PERSON> should then enter the OTP received and click on save ((5.15)) which would then update the email ID. 97 (5.15)", "source": "User Manual - Chapter 5, Section 5.3", "title": null, "type": "user_manual_procedure", "procedure_type": "PROFILE_UPDATE", "metadata": {"chapter": 5, "section": "5.3", "procedure_type": "PROFILE_UPDATE"}}, {"content": "To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((5.16)). (5.16) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. 98", "source": "User Manual - Chapter 5, Section 5.3", "title": null, "type": "user_manual_procedure", "procedure_type": "GRIEVANCE_MANAGEMENT", "metadata": {"chapter": 5, "section": "5.3", "procedure_type": "GRIEVANCE_MANAGEMENT"}}, {"content": " <PERSON><PERSON><PERSON> can login and raise his/her Grievance related to pension, if any. <PERSON><PERSON><PERSON> Login Pensioners detailGrievance.  <PERSON><PERSON><PERSON> can select the Grievance Type from the dropdown and add the description about it. (5.17) 5.17  <PERSON><PERSON><PERSON> can also upload the attachment related to the Grievance, if any. (5.18)  After filling all the details, <PERSON><PERSON><PERSON> will click on Submit button.(5.19) 99 5.18 5.19 <PERSON><PERSON><PERSON> shall be able track the status of his grievance from grievance history.(5.19)", "source": "User Manual - Chapter 5, Section 5.4", "title": "Lodge Grievance", "type": "user_manual_procedure", "procedure_type": "GRIEVANCE_MANAGEMENT", "metadata": {"chapter": 5, "section": "5.4", "procedure_type": "GRIEVANCE_MANAGEMENT"}}, {"content": "Proposed Declaration: All the pensioners drawing pension via SAMPANN are to submit the proposed declaration forms of investment and other information for availing income tax rebate to the respective pension paying branch by 15th April each year. They can submit by filling the online form from their Dashboard or fill a physical copy and send to Concerned CCA office. In case of online filling of proposed form, no physical copy needs to be sent to the CCA office. The pensioners who fail to submit the proposed declaration in time schedule mentioned above but submit subsequently on a later date, the tax deduction shall get impacted after receipt of the declaration. No proposed declaration would be entertained however after 9th October. After that Actual declaration shall submitted by the pensioner. Pensioner has to take following steps in order to fill the income tax declaration in SAMPANN application: 101 1. Pensioner shall open SAMPANN website- www.dotpension.gov.in and login using his/her credentials as screen shown in the below Fig(5.21). (5.21) Now Pen<PERSON><PERSON> has to click on the Pensioner Details-> Investment Declaration link shown in below (5.22) Pension<PERSON> will have the option to submit proposed declaration or actual declaration. A B C D 102 Fig(5.22) User has to click on the 1st “Fill Proposed Investment” button shown in (5.23) and fill his proposed investment declarations details. (5.23) 103 Document upload is not mandatory in case of Proposed Investment Declaration. Also proposed declaration can be submitted by the pensioner before 15 April. In case multiple declarations are submitted, latest one shall be taken into account. (5.25) 104 (5.25) After entering the Proposed Investment Declarations, the below screen will be shown. Here Retiree can see his filled information by clicking on the View link shown in grid under “Submitted Declaration” title shown in (5.26). (5.26) 105 After click on View link below screen will shown (5.27). From this screen Retiree can take the print of this page. (5.27)", "source": "User Manual - Chapter 5, Section 5.6", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 5, "section": "5.6", "procedure_type": "FORM_PROCESSING"}}, {"content": "Actual declaration: All the pensioners drawing pension via SAMPANN are to submit the actual declaration forms of investment and other information for availing income tax rebate to the respective pension paying branch by 10th October each year. They can submit by filling the online form from their Dashboard. However, they can also fill a physical copy available on Pensioners’ Dashboard and send to Concerned CCA office. If no actual declaration is received by 10th October then the proposed declaration, if received, shall stand nullified. Also, Actual declaration submitted later will be taken into cognizance in the monthly bills remaining to be processed. Hence, no actual declaration shall be accepted beyond 15th Feb . (5.28) Pensioners should fill Actual declaration of investment as indicated below: - (5.28) 106 (5.28) After selecting the Actual Investment Declaration option the following form will appear: - (5.29) 107 (5.29) 108 (5.30) In this Actual investment declaration form, Pensioner may upload proof of investment/savings. All documents required as proof of saving (Investment) should be uploaded. They can be uploaded against each entry or as one file against any one of the fields. After entering the Actual Investment Declarations, the below screen will be shown. Here <PERSON><PERSON><PERSON> can see his filled information by clicking on the View link shown in grid under “Submitted Declaration” title shown in (5.30) . 109 (5.30) The proposed /Actual declaration shall be received by CCA Office and there after pensioner may view the admitted declaration. In case of any issue, fresh declaration may be submitted. The reason for change in submitted & Admitted document shall be available in remarks. It may be noted that fresh declaration can be submitted only when pensioner’s last declaration has been admitted. Proposed/Actual declaration submitted by the pensioner shall be available for view & accounting to PDA section. Now Retiree can see the Approved Calculation sheet on Dashboardby click on the View link shown in the grid under “Admitted Declaration” title shown in 5.30. 110", "source": "User Manual - Chapter 5, Section 5.6", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 5, "section": "5.6", "procedure_type": "FORM_PROCESSING"}}, {"content": " <PERSON><PERSON><PERSON> can login and raise his/her Grievance related to pension, if any. <PERSON><PERSON><PERSON> Login Pensioners detailGrievance.  <PERSON><PERSON><PERSON> can select the Grievance Type from the dropdown and add the description about it. (6.1)  <PERSON><PERSON><PERSON> can also upload the attachment related to the Grievance, if any. (6.2)  After filling all the details, <PERSON><PERSON><PERSON> will click on Submit button.  The concerned section in CCA office/HoO will be displayed as shown at point no. 3. (6.1) *After submission, the Grievance will be received by Account Officer (AO) of concerned section/HoO as applicable and the history will be maintained in Grievance History section. (6.3) point 6. 6.1 111 6.2 6.3", "source": "User Manual - Chapter 6, Section 6.1", "title": "Pensioner Grievance", "type": "user_manual_procedure", "procedure_type": "GRIEVANCE_MANAGEMENT", "metadata": {"chapter": 6, "section": "6.1", "procedure_type": "GRIEVANCE_MANAGEMENT"}}, {"content": " Once the Grievance is received at AO (Concerned Section) level, he/she will assign that Grievance to the respective DH (Concerned Section). (6.4) 112  AO Login Grievance Management Assign Grievance.  AO (Concerned Section) to select the respective DH (Concerned Section) has to click on Grievance check box and then click on Assign button. (6.4)  After all the aforementioned steps, Grievance will move to respective selected DH (Concerned Section). 6.4", "source": "User Manual - Chapter 6, Section 6.2", "title": "Allotment of Grievance to DH", "type": "user_manual_procedure", "procedure_type": "AO_LOGIN_CREATION", "metadata": {"chapter": 6, "section": "6.2", "procedure_type": "AO_LOGIN_CREATION"}}, {"content": " Once the Grievances received at DH (Concerned Section) DH will take the required action on the grievance.  DH Login Action Grievance Management Resolve Grievance. (6.5)  However, before settling the grievance, the DH (Concerned Section) should get approval of competent authority in physical file also.  History of Grievance will be shown in Grievance history with status “Recent” or ‘Settled”.  AO (concerned section) will be able to see it in Settled Grievance. *The Pensioner will get Notification on Pensioner Dashboard once the Grievance is settled or any other action taken on the Grievance. 113 6.5 114", "source": "User Manual - Chapter 6, Section 6.3", "title": "Grievance Resolution", "type": "user_manual_procedure", "procedure_type": "DH_CREATION", "metadata": {"chapter": 6, "section": "6.3", "procedure_type": "DH_CREATION"}}, {"content": "134  Login from CCA User-id and go to “Users-> User Registration” tab.  Click on the Add New button shown there. Then a new window will open shown in 8.0. From the drop down menu, Select the Head of Office (SSA Unit) for side channel user. 8.0 135  8.1  After selection you see the list as shown in above screen. From that list, select the option which ends with “Telecom Circle” and having the SSA Unit Code starting with ”9” which is the unique identifier for the side channel user shown in 8.1.  Now enter the user-id of HoO as per the format given (<EMAIL>) in Email Id box (this is a user name only and no need to fill valid email ID) ,Mobile No. and select the “Menu List” Options give below and save the record.  Now, once the user is created, login from that particular user id, fill the mandatory information. Also you can change the default password from there and save the record again.  Once the HoO id for Side channel is created, then login from the same user-id, go to “Users -> User Registration”, click on the “ADD NEW” button and create the next hierarchy i.e. DH for the Side channel of SSA unit.  Once the DH user is created, click on the “Authorization” lock icon shown in the grid and assign the necessary menu rights to the user shown in 8.3. 136 8.2a 8.2b  After giving the rights, login from the DH user-id and default password and fill the necessary info and save the record. Now user will be able to process case from side channel.", "source": "User Manual - Chapter 8, Section 8.1", "title": "Annexure 1 (Side Channel User Creation)", "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "8.1", "procedure_type": "FORM_PROCESSING"}}, {"content": "Processing a Case) This chapter deals with flow of pension papers in the CCA office when the case is processed through Side channel on behalf of the HOO unit and the pensioner to enable creation of digital profile of pensioner. 137", "source": "User Manual - Chapter 8, Section 8.2", "title": "HoO Unit- Side channel (Steps for", "type": "user_manual_procedure", "procedure_type": "GENERAL_PROCEDURE", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "GENERAL_PROCEDURE"}}, {"content": " DH has to login to create the retiree profile.  Select Retiree details on the Menu options available on the left panel.  Click on “Add New” button at the right top corner of Retiree Profile page to get the form to fill.  Enter the following information on retiree detail form (underlined parameters are the mandatory fields): q. Title r. First Name s. Middle Name t. Last Name u. Type of Retirement v. Height w. Father’s/ Husband’s Name. x. Mother’s Name y. Date of Birth z. Gender aa. Aadhaar Number bb. PAN Number cc. Mobile Number (This should be an Active Number) dd. Email ID ee. Identification Mark 1 ff. Identification Mark 2  Following details to be filled in other details section (All are mandatory fields): h. Designation i. Group j. Date of Joining k. Date of Retirement l. Date of Superannuation m. Govt. Accommodation provided by Directorate of Estates/BSNL at any time of service: YES/ NO (to select from the dropdown) n. Pay Commission (this should be the Pay Commission corresponding to the last pay drawn) *After filling the aforementioned information, DH to click on Save button. By using the Clear button, User can also refresh the form and fill in fresh information. (8.3) 138 IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore, due diligence should be exercised while creating the Retiree Profile. In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk. 8.3 Creation of the Retiree Profile is the most important step in the process, so it should be ensured entries made therein are correct in all respects. After saving the information, it is recommended a printout of the page be taken from the screen and Vetted by HoO level User before proceeding ahead in Service Verification. If any error is detected, then retiree profile be edited before initiating 8.2.1.2. (further updates in chapter 11)", "source": "User Manual - Chapter 8, Section 8.2", "title": null, "type": "user_manual_procedure", "procedure_type": "RETIREE_PROFILE", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "RETIREE_PROFILE"}}, {"content": " It may be noted while the case is processed via side channel, the timelines related to lose their relevance.  Based on the S/B received in office, same may be filled in the module by DH. If some portion remains unverified it may be filled in remarks column.  There shall be only one level passing. 139", "source": "User Manual - Chapter 8, Section 8.2", "title": null, "type": "user_manual_procedure", "procedure_type": "GENERAL_PROCEDURE", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "GENERAL_PROCEDURE"}}, {"content": " At this stage after DH sends form to retiree, the digital profile of pensioner will be created.  There shall be only one level passing. 140", "source": "User Manual - Chapter 8, Section 8.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " Prior to Form received stage, DH shall be required to fill up the forms as per documents submitted by the pensioner. Before submission the final submit button, it may be checked and vetted that the details are correctly filled.  If any error is detected however at the form received stage, then the case may be returned for refilling.  It may be noted that retiree will not be required to fill the forms. This activity shall be completed by DH.  For filling the form go to “Form Received”, where you find the record under status “Cases pending at the Pensioner level” which is default selected. Now click on the Edit button (Pencil Icon) shown in the last Action column and fill all the detail of Pensioner very carefully. 8.4 8.4  After filling up the profile, on the same “Form Received” page select the option “Cases submitted without Physical Copy” from Status dropdown list shown in Fig", "source": "User Manual - Chapter 8, Section 8.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "FORM_PROCESSING"}}, {"content": "“Form 7” for processing by skipping the step for “Form Verification”.  There shall be only one level passing. 141 8.5", "source": "User Manual - Chapter 8, Section 8.5", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "8.5", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to verify and fill the required information in Form 7.  Some information in Form 7 is auto populated. Others have to be entered. (8.6) ee) Name of the retiring Government Employee ff) Father's/Husband's Name gg) PAN NO. hh) Height & Marks of identification ii) Date of Birth jj) Service to which he/she belongs (indicate name of Organised service, if any, otherwise say General Central Service) kk) Particulars of post held at the time of retirement ll) Name of the office mm) Post held nn) Scale of Pay/Pay Band & Grade pay of the post oo) Basic Pay/Pay in the pay band & Grade pay. pp) Basic Pay/Pay in the pay band & Grade pay qq) Whether the appointment mentioned above was under Government or outside the Government on foreign service terms? rr) If on foreign service, scale of pay/pay band, pay in the pay band and grade pay of the post in the parent department. 142 ss) Whether declared substantive in any Post under the Central Govt.? tt) Date of beginning of service uu) Date of ending service vv) Cause of ending service ww) In case of compulsory retirement, the orders of the competent authority whether pension may be allowed at full rates or at reduced rates and in case of reduced rates, the percentage at which it is to be allowed (Please See Rule 41) xx)In case of removal/dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41) yy) Particulars relating to military service, if any. zz) Particulars relating to the service in autonomous body, if any. aaa) Whether any Departmental or judicial proceedings in terms of rule 9 of the CCS(Pension) Rules, 1972 are pending against the retiring employee. (lf yes, in terms of Rule 69, provisional pension will be admissible and gratuity will be withheld till the conclusion of departmental or judicial proceedings and issue of final orders) bbb) Length of service vii. Details of omission, imperfection or deficiencies in the Service Book which have been ignored [under rules 59(1) (b) (ii)] viii. Period not counting as qualifying service. ix. Additions to qualifying Service. x. Whether any leave without pay. xi. Net Qualifying service. xii. Qualifying service expressed in terms of complete six monthly periods (Period of three months and above is to be treated as completed six monthly periods (Rule 49) ccc) Emoluments c. Emoluments in terms of Rule33. d. Emoluments drawn during ten months pending retirement. ddd) Others: Details of Govt. dues recoverable out of gratuity eee) Licence fee for Govt. accommodation see sub-rules (2), (3) and (4) of rule 2) fff) Dues referred to in Rule 73 ggg) Amount indicated by Directorate of Estates to be withheld under Sub rule(S) of Rule 72 hhh) Post-retirement address of the retiree 143 144 8.6  DH shall fill up the form 7 as per form 7 received from the HoO. It may be noted if there is any change from the form 7 received from HOO based on final papers received, Same may be fed.  There shall be only one level passing.", "source": "User Manual - Chapter 8, Section 8.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " After all the aforementioned steps, DH will submit the form to Pension Section by clicking on “Send to PAO”. While sending this please ensure that all the documents mentioned in checklist are being sent. The papers may then be dispatched by post. This will be the only step in which two level passing will be there.  For cases in which the final papers have not been received, the case shall not be processed beyond AE in Pension Section Module. 145", "source": "User Manual - Chapter 8, Section 8.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " All users’ can view the list of all the retirees and their generated forms.  Printout of Form7 and Form 8 can be taken by clicking on ’Print’ and sent to Pension Section of concerned CCA office. (8.7) 8.7", "source": "User Manual - Chapter 8, Section 8.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "FORM_PROCESSING"}}, {"content": "There shall be no change in the treatment of family pension case while processing through Side Channel. While feeding the forms, all the details – in S/B module, form 12/14 and 18 - shall be filled based on the forms submitted by the retiree.", "source": "User Manual - Chapter 8, Section 8.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to login and click on Action, then on Family Pension, then on Personal Details option in left pane(DH LoginActionFamily PensionPersonal Details)", "source": "User Manual - Chapter 8, Section 8.2", "title": null, "type": "user_manual_procedure", "procedure_type": "DH_CREATION", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "DH_CREATION"}}, {"content": " Following information need to be entered on personal detail page: s. Title t. First Name u. Middle Name v. Last Name w. Type of retirement. x. Height y. Father’s/ Husband’s Name. z. Mothers Name aa. Date of Birth 146 bb. Gender cc. Aadhaar Number dd. PAN Number ee. FP Mobile Number ff. FP Email ID gg. FP Identification Mark 1 hh. FP Identification Mark 2 ii. Employee Code jj. Office  Following details to be filled in Other Details section (All are mandatory fields): h. Designation at the time of death i. Group j. Date of Joining k. Date of Death l. Date of Superannuation m. Govt. Accommodation provided by Directorate of Estates/BSNL at any time of service: YES/ NO (to select from the dropdown) n. Pay Commission *After filling the aforementioned information, DH to click on Save button. Once filled the details cannot be changed so due diligence should be exercised before saving. (8.8) To expedite the disbursement of pensionary benefits, Family Pensioner should immediately be advised to submit the required information along with relevant documents to the HoO. Family Pensioners’ may also be advised to provide mobile phone number (mandatory) so that they can get updates on the pension process and pension disbursement. 147 8.8  After saving the details, an instance will be created in the Personal Details tab, as shown in the 8.9 8.9  Then DH has to click on Govt. Emp Details edit tab, and proceeds to the subsequent screen.  The next screen will show three tabs viz. Personal details, Family Details and Nomination Form1 (8.10) 148 8.10  Now the DH has to fill in details of Family and Nominations (8.11, 8.12, 8.13) 8.11 149 8.12 8.13  After filling all the details, the DH will submit the form by clicking submit button.", "source": "User Manual - Chapter 8, Section 8.8", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "8.8", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to select ‘Service Verification’ tab from the Menu options.  DH to check the form and send it for approval to HoO for service verification. Before this is done, physical service book verification needs to be done as well. Any 150 unverified portion of the service book should be noted and attached in the file being sent to Pension Section (CCA Office). (8.14)  HoO to login and approve the service book verification. HoO also to verify the service book physically.  HoO can also return the form to DH in case any discrepancy is found. Then DH will again have to verify and send the form to HoO for approval. 8.14", "source": "User Manual - Chapter 8, Section 8.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to verify and fill the required information in Form 14. DH may select the claimant who is eligible for pension. This is to be done as per CCS Pension Rules,1972. 14. The Form shows the claimant details, the bank details, the documents to be uploaded like photograph, signature and death certificate etc. The Bank Undertaking Form also needs to be uploaded duly signed by bank authorities as well the Family Pensioner. FMA option has to be chosen (if applicable and desired by the Family Pensioner) (8.11 (a), 8.11 (b) and 8.11 (c)).  It should be seen that the photograph and signature are clear and visible.  DH clicks on ‘Form 12’ tab. 151 8.11 (a) 152 8.11 (b) 8.11 (c) IMPORTANT: -Form 14 prompts the user to enter bank details. The pension of the family pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank authorities and Family Pensioner, and then uploaded on CPMS. 153", "source": "User Manual - Chapter 8, Section 8.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to verify and fill the required information in Form 12 for each claimant who has been nominated for gratuity in the nomination form filled earlier. Here <PERSON><PERSON> has to click on the Edit button against the name of the nominee, and his/her details will be populated in the Form 12 (8.15).  Again Bank Account details and claimant’s details, picture and signature needs to be uploaded.  After updation of Form 12 and 14, the case will appear in ‘Form 18’ tab of DH. 8.15 IMPORTANT: - Form 12 prompts the user to enter bank details. The DCRG of the family pensioner will be credited into this account. Therefore, due diligence should be exercised while filling in all important fields like Bank Account no., IFSC code etc. After required, correct information is filled, Bank Undertaking has to be printed and physically signed by Bank 154 authorities and Family Pensioner, and then uploaded on CPMS. In case of claimant who is claimant for only Gratuity and not pension, only Mandate form, as generated, needs to be filled by the pensioner and uploaded in portal. In case where there is no nomination, DCRG has to be divided in equal shares among the family members. Accordingly, the nomination form and Form 12s will be filled.", "source": "User Manual - Chapter 8, Section 8.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " DH to verify and fill the required information in Form 18. Some information is auto populated in Form 18. D<PERSON> should be careful while filling the important details like qualifying service, non-qualifying service period, last month’s pay details etc. Pensionary benefits will be calculated on basis of these figures and hence figures must be cross-verified from service book. (8.16)  DH will send Form 18 for approval of HoO.  HoO will approve/ Return the form. <PERSON><PERSON> should verify the Pensionary benefit amounts. If the amounts are correct, he/she can approve it. If incorrect, <PERSON><PERSON> can return the case back to DH for rectification.  If HoO approves the form it will be processed further. In case of Return, form will be sent back to DH for reverification and thereafter process followed for approval. 155 156 8.16  Form 18 and 19 to be put up in physical file for approval of competent authority. IMPORTANT: -Form 18 calculates the pensionary benefits and pension as applicable to the Family pensioner. Once this form is filled and next stage initiated it cannot be edited in HoO section. Therefore, due diligence should be exercised while filling in all important fields like Pay Band, pay level, Qualifying/Non Qualifying service etc. In case Wrong or incorrect information is saved in the system, please immediately inform the Helpdesk. After processing the form 18 from DH level, if HoO user is satisfied then a printout may be taken and approval and signature of competent authority may be taken. If any error is detected in the process, then HOO may return to DH the form 18 for necessary correction. DH shall correct it and send it to HoO user. The approval of competent authority shall be taken. 157", "source": "User Manual - Chapter 8, Section 8.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " After all the aforementioned steps, DH will submit the form to Pension Section (CCA Office) by clicking on “Send to PAO”.  For cases in which the final papers have not been received, the case shall not be processed beyond AE in Pension Section Module.", "source": "User Manual - Chapter 8, Section 8.2", "title": null, "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "8.2", "procedure_type": "FORM_PROCESSING"}}, {"content": " Login with AO Pension go to the Masters Issuing Authority Signature.  Click on the “upload a file” button and upload the signature of the Issuing Authority. (9.1) (9.2)  After uploading the image, click on the Update button (9.3). (9.1) 159 (9.2) (9.3)", "source": "User Manual - Chapter 8, Section 9.1", "title": "Upload AO Pension signature", "type": "user_manual_procedure", "procedure_type": "PROFILE_UPDATE", "metadata": {"chapter": 8, "section": "9.1", "procedure_type": "PROFILE_UPDATE"}}, {"content": " Again login with AO Pension go to the Masters Generate ID Card.  Here Fill the PPO number of pensioner and click on the Search button. (9.4) 160 (9.4)  Now you can take the print of ID Card by clicking on the “Print Front side” and “Print Back Side” button, as shown in below figure. (9.5) (9.5) 161 10 10. Revision Under revision module, there are 5 categories of revision (shown in 10) 1. Revision in the rate of DA: Due to change of DA as on Date of retirement, the DCRG shall get revised and the differential amount of DCRG shall be due to be paid. For this sanction for payment shall be issued by Pension section and PDA shall make payment. Any impact on pension payable has to be separately calculated and paid in the next monthly bill. 2. Revision on account of withheld amount: On receipt of sanction for release of full/part payment of Withheld amount, this revision shall be processed. 3. Revision of pension to family pension (f.p) on account of death (When No eligible f.p. is mentioned in the PPO): In case when claimant name is not mentioned in the PPO, then revision shall be done. Under this, PPO shall be issued by Pension section. 4. Revision of pension to f.p on account of death (when eligible f.p. is mentioned in PPO) In case when claimant name is mentioned in the PPO, then revision shall be done however as previous PPO will suffice, no fresh PPO shall be issued in such case. 5. Revision of pension due to pay change/Court matter: Due to change in Pay details or service details arising out of Court order then, the Revision shall be processed. (10) It is , currently ,mandatory that the Revision should only be done by the same DH Pension user who make entry of the case earlier in the CPMS.", "source": "User Manual - Chapter 8, Section 9.2", "title": "Generate ID Card", "type": "user_manual_procedure", "procedure_type": "PENSION_SANCTIONING", "metadata": {"chapter": 8, "section": "9.2", "procedure_type": "PENSION_SANCTIONING"}}, {"content": "This type of revision is used when there is a change in the DA Rate. No fresh sanction for HoO for this case shall be required. Steps for this revision are as follows: -  First of all, login with the DH Pension  Go to the Revision -> Revision of Pension tab (10.1.b). 162 (10.1.b)  Here fill in PPO No and select the “Reason for Revision” (10.1.c). (10.1.c)  Now click on the edit button (pencil icon) in last Action column.  Then next screen opens, here check the details and then click on the “Send to Revision Sanction order” button and enter w.e.f. date (10.1.d). 163 (10.1.d)  Now Go to-> Revision->Revision Sanction Order tab shown in (10.1.e) (10.1.e)  From this screen you can view the Sanction order, by clicking on the View link (10.1.e). Also you can take the printout of this Sanction Order (10.1.f). 164 (10.1.f)  Now click on the Last Column, verify link to send record for AAO approval (10.1.e).  Now login with the AAO Pension. Go to ->Approval->Revision->Revision Sanction Order Shown in (10.1.g) (10.1.g)  Now Click on the View link to see the Sanction order and then Approve/Return the record by click on the Approve/Return link and send record for AO approval (10.1.g).  Now login with the AO Pension (only on IE Browser).  Go to Approval>Revision>Revision Sanction Order (10.1.h) 165 (10.1.h)  Click on View link to see or take the print of the Sanction Order, attach the DSc in the system for digital signature and click on the Approve link.At this stage, Digitally signed authority shall get generated.  Now login with AO PDA, Go to->Allotment->Allotment To PDA DH (10.1.i) (10.1.i)  Now select the case, Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on “Send To DH” button.  After this login with PDA DH, Go to ->Bill Generation->Revision, (10.1.j) 166 (10.1.j)  Select the Record and Click on the “Approve and Send to AAO” button, (10.1.j).  Now login with AAO PDA, Go to->Approval->PDA->Revision, (10.1.k) (10.1.k)  Now select the record and click on the “Approve Bill and Send to AO”.  After this login from PDA AO, Go to->Approval->PDA->Revision (10.1.l) 167 (10.1.l)  Select the Account Head from the dropdown, then it shows the respective records, then fill the “Enter Not Payable Before Date” and click on the Approve Bill button.  After this record will show in PFMS for payment. Monthly Bill shall remain unchanged.", "source": "User Manual - Chapter 8, Section 10.1", "title": "Revision in the rate of DA", "type": "user_manual_procedure", "procedure_type": "PENSION_SANCTIONING", "metadata": {"chapter": 8, "section": "10.1", "procedure_type": "PENSION_SANCTIONING"}}, {"content": "This type of revision is used when there is a withheld amount of DCRG in r/o pensioner and sanction for release of full amount/part amount has been received from HoO. Steps for this revision are as follows: -  First of all, login with the DH Pension  Go to the Revision -> Revision of Pension tab (10.2.b). 168 (10.2.b)  Here fill all required information like PPO No and select the “Reason for Revision” (10.2.c). (10.2.c)  Now click on the edit button (pencil icon) in last Action column.  Then next screen opens, here Fill the ‘Recoveries From Withheld amount’ (11.(3)) and then click on the “Send to Revision Sanction order” button (10.2.d) and enter reason for withheld. 169 (10.2.d)  Now Go to-> Revision->Revision Sanction Order tab shown in (10.2.e) (10.2.e)  From this screen you can view the Sanction order, by clicking on the View Link (10.2.e). Also you can take the printout of this Sanction Order (10.2.f). 170 (10.2.f)  Now click on the Last column Verify link to send record for AAO approval (10.2.e).  Now login with the AAO Pension. Go to ->Approval->Revision->Revision Sanction Order Shown in (10.2.g) (10.2.g)  Now Click on the View link to see the Sanction order and then Approve/Return the record by click on the Approve/Return link and send record for AO approval (10.2.g).  Now login with the AO Pension (only on IE Browser).  Go to Approval>Revision>Revision Sanction Order (10.2.h) 171 (10.2.h)  Click on View link to see or take the print of the Sanction Order, attach the DSC in the system for digital signature and click on the Approve link.  Now login with AO PDA, Go to->Allotment->Allotment To PDA DH (10.2.i) (10.2.i)  Now select the record and Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on “Send To DH” button.  After this login with PDA DH, Go to ->Bill Generation->Revision, (10.2.j) 172 (10.2.j)  Select the Record and Click on the “Approve and Send to AAO” button, (10.2.j).  Now login with AAO PDA, Go to->Approval->PDA->Revision, (10.2.k) (10.2.k)  Now select the record and click on the “Approve Bill and Send to AO”.  After this login from PDA AO, Go to->Approval->PDA->Revision Fig(10.2.l) 173 (10.2.l)  Now select the Account Head from the dropdown, then it shows the respective records, then fill the “Enter Not Payable Before Date” and click on the Approve Bill button.  After this record will show in PFMS for payment. Monthly Bill shall remain unchanged.", "source": "User Manual - Chapter 8, Section 10.2", "title": "Revision Due to Withheld Amount", "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "10.2", "procedure_type": "FORM_PROCESSING"}}, {"content": "revision/Court Order This type of revision is used when there is a revision in the pay /court order. For this sanction shall be issued by HoO and sent to Pension section. Steps for this revision are as follows: -  First of all, login with the DH Pension  Go to the Revision -> Revision of Pension tab (10.3.b). 174 (10.3.b)  Here fill all required information like PPO No and select the “Reason for Revision” (10.3.c) (10.3.c)  Now click on the edit button (pencil icon) in last Action column.  Then next screen opens, here fill the all required details. DH should enter the following requisite parameters: a. New AE b. New Last Pay Drawn c. New Qualifying Service In case any figure remains unchanged then the original value has to be fed.  System will then calculate the revised pension, commutation (if any) and gratuity (if any) and the additional amount. Also, if some data entry has been wrongly entered, Press cancel. Once satisfied, Press SAVE. 175  Sanction Order will then be generated and it will be available in revision sanction order Tab. DH may view the sanction order. Once a case is at this stage, it cannot be edited. (10.3.d) (10.3.d) 176 (10.3.d)  Now Go to-> Revision->Revision Sanction Order tab shown in (10.3.e) (10.3.e)  From this screen you can view the Sanction order, by clicking on the View link (10.3.e). Also you can take the printout of this Sanction Order (10.3.f). 177 (10.3.f)  Now click on the Last column Verify link to send record for AAO approval (10.3.e).  Now login with the AAO Pension. Go to ->Approval->Revision->Revision Sanction Order Shown in (10.3.g) (10.3.g)  Now Click on the View link to see the Sanction order and then Approve/Return the record by click on the Approve/Return link and send record for AO approval (10.3.g).  Now login with the AO Pension (only on IE Browser).  Go to Approval>Revision>Revision Sanction Order  Click on View link to see or take the print of the Sanction Order, attach the DSc in the system for digital signature and click on the Approve link.  Now login with AO PDA, Go to->Allotment->Allotment To PDA DH (10.3.i) 178 (10.3.i)  Now Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on “Send To DH” button.  After this login with PDA DH, Go to ->Action->PDA Section->Revision Sanction Order, (10.3.j)  DH PDA must view and save a digital copy of the Sanction Order and take requisite printouts, so that copy can be sent to HoO , pensioner, Pension section apart one office copy(after updation of date of restoration of 2nd commutation after completion of steps till point 10 ). The copy will not be available at any other screen, so saving a digital copy is mandatory.  PDA section shall manually calculate the amount of arrears to be paid on account of the revision of pension from date of retirement till the current month. Also, For the month, pro rate calculation has to be done manually.  The arrear amount may be fed in the monthly bill of the current month, if not paid already, and pushed from DH to AAO. Once the file moves from sanction received stage, the pension of current month will not be available at DH level any more. So the monthly bill for current month should be pushed with the arrear from DH to AAO. The monthly bill can be paid as per rules. After completion of above, DH PDA will now generate the bill for the above sanction order and send the generated bills namely commutation and gratuity to cash after three level passing. 179 (10.3.j)  Click on the Send button in front of the record.  Now Go to->Action->Bill Generation->Revision.  Select the Record and Click on the “Approve and Send to AAO” button.  Now login with AAO PDA, Go to->Approval->PDA->Revision, (10.3.k) (10.3.k)  Now select the record and click on the “Approve Bill and Send to AO”.  After this login from PDA AO, Go to->Approval->PDA->Revision (10.3.l) 180 (10.3.l)  Now select the Account Head from the dropdown, then it shows the respective records, then fill the “Enter Not Payable Before Date” and click on the Approve Bill button.  After this record will show in PFMS for payment. The Commutation/ DCRG amount shall be paid by Cash the same day when AO PDA send it and same shall be used for arriving at the date of restoration of 2nd commutation and shall be updated in revision copy mentioned in point 6.  After successful payment, LC and DLC will get refreshed and respective dates will be entered for the pensioner.  Thereafter, the monthly bill in for current month lying at AAO shall be pushed with NPB date to AO and then to Cash for payment.  The monthly pension of following months(including current month ) will come as per revised pension.", "source": "User Manual - Chapter 8, Section 10.3", "title": "Revision on account of Pay", "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "10.3", "procedure_type": "FORM_PROCESSING"}}, {"content": "family member mentioned in PPO) This type of revision will be done when after the death of pensioner, the claimant’s name is not mentioned in the PPO. In such case, revision shall be initiated after form 14 with enclosure is duly forwarded by HOO along with sanction of payment manually. Documents that will be required to be submitted in this case shall be 1. Death certificate of Pensioner(s) 2. Life certificate/DLC of the claimant 3. Mandate form/cancelled cheque and undertaking 181 4. Duly filled form 14 with enclosures. If there is a case where though the claimant name is not mentioned in the PPO, but same has been added via corrigendum and uploaded via Upload utility e.g. permanently disabled children/siblings and disabled parent, then they will be processed though this type. In such cases form 14 shall be required to be submitted to the Pension/PDA directly and no fresh sanction shall be called for. Documents that shall be required to be submitted in this case shall be 1. Death certificate of Pensioner(s) 2. Life certificate/DLC of the claimant 3. Mandate form/cancelled cheque and undertaking 4. Form 14 duly filled with enclosures. Steps for this revision are as follows: -  First of all, login with the DH Pension  Go to the Revision -> Revision of Pension tab (10.4.b). (10.4.b)  Here fill all required information like PPO No and select the “Reason for Revision” (10.4.c). 182 (10.4.c)  Now click on the edit button (pencil icon) in last Action column.  Then next screen opens, here Fill all the details and then click on the “Save” button (10.4.d). 183 (10.4.d)  Now Go to-> Revision->Revision Sanction Order tab shown in (10.4.e) (10.4.e)  From this screen you can view the Sanction order, by clicking on the View link (10.4.e). Also you can take the printout of this Sanction Order (10.4.f). 184 (10.4.f)  Now click on the Last Column Verify link to send record for AAO approval (10.4.e).  Now login with the AAO Pension. Go to ->Approval->Revision->Revision Sanction Order Shown in (10.4.g) (10.4.g)  Now Click on the View link to see the Sanction order and then Approve/Return the record by click on the Approve/Return link and send record for AO approval (10.4.g).  Now login with the AO Pension (only on IE Browser). 185  Go to Approval>Revision>Revision Sanction Order  Click on View link to see or take the print of the Sanction Order, attach the DSc in the system for digital signature and click on the Approve link.  Now login with AO PDA, Go to->Allotment->Allotment To PDA DH (10.4.i) (10.4.i)  Now Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on “Send To DH” button.  After this login with PDA DH, Go to ->Action->PDA Section->Revision Sanction Order, (10.4.j) 186 (10.4.j)  After this the record will show in Vendor Verification, GO to->Action->Vendor Verification (10.4.k). (10.4.k)  After this record will Go to->LC & DLC Verification->LC Verification, (10.4.l). 187 (10.4.l)  According to the dates filled in the revision module, case shall get processed.  After this the case will show in Next month Monthly Bill ( from the month you process the case), Fig(10.4.m) Fig(10.4.m)  Now Process the monthly bill and do payment. An assessment shall be done on account of amount payable , if any, due to delay of intimation of death , from the day following date of death till the the disbursement of 1st revised pension. Such amount shall be paid as arrear/recovery along with 1st revised pension. 188", "source": "User Manual - Chapter 8, Section 10.4", "title": "Revision of Pension to FP (No eligible", "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "10.4", "procedure_type": "FORM_PROCESSING"}}, {"content": "member mentioned in PPO) This type of revision will be done when after the death of pensioner, the claimant’- Spouse- name is mentioned in the PPO. In such case, documents that will be required to be submitted in this case shall be 1. Death certificate of Pensioner(s) 2. Life certificate/DLC of the claimant 3. Revised Mandate Form/Cancelled cheque and Undertaking, if not available. Steps for this revision are as follows: -  First of all, login with the DH Pension  Go to the Revision -> Revision of Pension tab (10.5.b). (10.5.b)  Here fill all required information like PPO No and select the “Reason for Revision” (10.5.c). 189 (10.5.c)  Now click on the edit button (pencil icon) in last Action column.  Then next screen opens, here fill the required details and then click on the “Save” button (10.5.d). 190 (10.5.d)  Now Go to-> Revision->Revision Sanction Order tab shown in (10.5.e) (10.5.e)  From this screen you can view the Sanction order, by clicking on the View link (10.5.e). Also you can take the printout of this Sanction Order (10.5.f). 191 (10.5.f)  Now click on the Last Column Verify link to send record for AAO approval (10.5.e).  Now login with the AAO Pension. Go to ->Approval->Revision->Revision Sanction Order Shown in (10.5.g) (10.5.g)  Now Click on the View link to see the Sanction order and then Approve/Return the record by click on the Approve/Return link and send record for AO approval (10.5.g).  Now login with the AO Pension (only on IE Browser).  Go to Approval>Revision>Revision Sanction Order  Click on View link to see or take the print of the Sanction Order, attach the DSc in the system for digital signature and click on the Approve link.  Now login with AO PDA, Go to->Allotment->Allotment To PDA DH (10.5.i) 192 (10.5.i)  Now Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on “Send To DH” button.  After this login with PDA DH, Go to ->Action->PDA Section->Revision Sanction Order, (10.5.j) (10.5.j) After this the record will for Vendor Verification, GO to->Action->Vendor Verification (10.5.k) 193 (10.5.k)  After this record will Go to->LC & DLC Verification->LC Verification, (10.5.l). 194 (10.5.l)  Now Do the LC & DLC and according to the date filled in the revision module, pension shall be payable.  After this the case will show in Next month ( from the month you process the case) Monthly Bill. (10.5.m). An assessment shall be done on account of amount payable , if any, due to delay of intimation of death , from the day following date of death till the the disbursement of 1st revised pension. Such amount shall be paid as arrear/recovery along with 1st revised pension. 195 11 11.Profile Authentication", "source": "User Manual - Chapter 8, Section 10.5", "title": "Revision of Pension to FP (Eligible family", "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "10.5", "procedure_type": "FORM_PROCESSING"}}, {"content": "In order to ensure that the data filled on retiree profile is correct and has been authenticated by AO Pension (in case of side channel) / HOO user (in case of BSNL), retiree profile upload utility has been developed. On service book verification page (11.1)) DH user shall view the retiree profile and if satisfied then a printout of retiree profile shall be taken (11.2)). Fig(11.1) 196 (11.2) AO Pension (in case of side channel) / HOO user (in case of BSNL) will match the retiree details against physical records and the scanned copy is to be uploaded. Before signing, the retiree profile details shall be matched against physical records and in case of error necessary corrections be made. The scanned copy shall be uploaded (11.3)) only after it has been checked as mentioned above. 197 (11.3) The uploaded verification form can be viewed by clicking eye button (11.3), Fig(11.4)). (11.4) The form may then be sent to HOO(SSA Unit) for approval (11.5)). 198 (11.5) 199 12 12. Upload utility There are scenarios wherein either orders have been issued in continuation of PPO or corrigendum have been issued. It is important that these orders – being critical in nature- are not just sent to the pensioner but are also uploaded to the pensioners’ dashboard. For this purpose, upload utility has been created. Some of the scenarios in which upload utility shall be used: 1. Issue of corrigendum of PPO for inclusion of permanently disabled children by PPO. 2. Sanction and payment of arrears of pension in cases where manual calculation has been done In such cases AO(pension)/AO(PDA) shall upload the documents which shall be visible on the pensioners’ dashboard. (12.1) After entering the PPO number and clicking on the “Search” button (12.1)), the below stated screen will get displayed (12.2)). The file to be sent to Pensioner’s Dashboard shall be selected and then uploaded. The description of file shall be added (12.3)). It may be noted that multiple files may be sent to pensioner’s Dashboard. Files can be sent by clicking the Submit button (12.3)). 200 (12.2) (12.3) The AO(pension)/AO(PDA) can view the history or date wise records for the information being shared with the pensioner on its dashboard Fig(12.4). 201 (12.4) Similarly, the pensioner can also view the information shared by the Department on his/her dashboard by clicking on the tab “Shared Documents” (12.5), Fig(12.6)). (12.5) 202 (12.6) 13 13. Bill / Payment slip generation For reconciliation sanction sent from PDA section with the bills appearing on PFMS and to ensure smooth processing, PDF for the bills sent to PFMS for payment can be generated. For bills sent on a day, a pdf can be generated. The date selected for generation of PDF is the date on which DH created the bills. The option of Bill Type and Status Type is to be selected as per the below stated criteria (13.1), Fig(13.2), Fig(13.3))according to user needs:- 203 (13.1) (13.2) (13.3) A PDF of the bills generated in the particular time frame will be displayed/downloaded on your desired location which can be sent to cash as sanction. The PDF (13.4)) can be generated for the files sent on a day and handed over to Cash for DSC. 204 (13.4) 14 14. Updation of Mobile, Email and address For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefits, following process will be followed. The user/pensioner will have to first login using the PAN no. as the Username. After login, click on the profile picture and select the option “Edit Profile” (14.1). 205 (14.1) A Pop-up window (14.2)) will be displayed with the option to choose the following details which the user wants to edit or change: - (14.2) The user has to select the option which he/she wants to change or modify.", "source": "User Manual - Chapter 8, Section 11.1", "title": "Retiree Profile Authentication", "type": "user_manual_procedure", "procedure_type": "RETIREE_PROFILE", "metadata": {"chapter": 8, "section": "11.1", "procedure_type": "RETIREE_PROFILE"}}, {"content": "Upon selecting ‘Mobile Number’, the following screen will be displayed (14.3)) 206 Fig(14.3) <PERSON><PERSON><PERSON> can enter his/her mobile number and then select either his/her registered email ID or the entered mobile number to receive an OTP to verify the number ((14.3)). Upon receiving the OTP, retiree should enter the OTP ((14.4)) and save which will then update the mobile number. (14.4)", "source": "User Manual - Chapter 8, Section 14.1", "title": "Mobile Number Update", "type": "user_manual_procedure", "procedure_type": "PROFILE_UPDATE", "metadata": {"chapter": 8, "section": "14.1", "procedure_type": "PROFILE_UPDATE"}}, {"content": "Upon selecting ‘Email ID’, the following screen will be displayed (14.5)) 207 (14.5) <PERSON><PERSON><PERSON> can enter his/her new email ID and the click on generate OTP which will then send an OTP to the registered mobile number ((14.5)). <PERSON><PERSON><PERSON> should then enter the OTP received and click on save ((14.6)) which would then update the email ID. (14.6)", "source": "User Manual - Chapter 8, Section 14.2", "title": "Email ID Update", "type": "user_manual_procedure", "procedure_type": "PROFILE_UPDATE", "metadata": {"chapter": 8, "section": "14.2", "procedure_type": "PROFILE_UPDATE"}}, {"content": "To update address, pensioner would be taken to the pensioner grievance page where a grievance related to updation of address can be registered ((14.7)). 208 (14.7) Once such a grievance has been registered, it will be assigned to the respective DH who will then update the address. Uploading of proof of address is mandatory in such case. For more details on Grievance Management please refer 6. 15 15. Other pension Types The pension for VRS/superannuation pension/Pro-rata/compensation pension shall be as per above user manual. However, following changes have to be kept in mind while processing the pension under Compulsory retirement/Invalid pension/Compassionate allowance. For these three categories of retirement, the application of applicant is eligible for commutation only after medical examination. So, the date on which the medical authority signs the medical report in Part III of Form 4 shall be required to be entered in retiree profile as in 15.1. 209 (15.1) Based on the date entered, the commutation will get calculated as per CCS (Commutation of Pension) Rules, 1981 and get displayed in pension papers. This shall be the only change in case of Invalid pension. Additionally, form 7 shall be provided with additional input fields in case of other two retirement types detailed as under.", "source": "User Manual - Chapter 8, Section 14.3", "title": "Address Update", "type": "user_manual_procedure", "procedure_type": "RETIREE_PROFILE", "metadata": {"chapter": 8, "section": "14.3", "procedure_type": "RETIREE_PROFILE"}}, {"content": "In form 7 in case of compulsory retirement, at the time of creating the retiree profile, the Date of Medical Report is to be provided Fig(15.1.1). 210 (15.1.1) After processing the case just like for the Normal Pension case, when the user reached at the Form 7 level, careful assessment is required at the time of entering the Percentage (%) at which the pension and DCRG is to be reduced Fig(15.1.2). (15.1.2) The impact of which can be seen at the Form 7 which the system calculates automatically (15.1.3), Fig(15.1.4), Fig(15.1.5)). 211 (15.1.3) (15.1.4) 212 (15.1.5)", "source": "User Manual - Chapter 8, Section 15.1", "title": "Compulsory retirement", "type": "user_manual_procedure", "procedure_type": "RETIREE_PROFILE", "metadata": {"chapter": 8, "section": "15.1", "procedure_type": "RETIREE_PROFILE"}}, {"content": "The DH at the time of creating the retiree profile will exercise the option of “Removal/Dismissal from service (Rule 24 and 41)” (15.2.1)) and the rest of the process flow will continue just like the same we process for the Normal Pension cases. However, at the time of the filling of Form 7, the DH must ensure to enter the correct and verified amount for the field wherein case of removal/dismissal from service whether orders of the competent authority have been obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41). (15.2.1) 213 The impact of which reflects in the Form 7 (15.2.2), Fig(15.2.3)). (15.2.2) (15.2.3) 214", "source": "User Manual - Chapter 8, Section 15.2", "title": "Compassionate Allowance", "type": "user_manual_procedure", "procedure_type": "RETIREE_PROFILE", "metadata": {"chapter": 8, "section": "15.2", "procedure_type": "RETIREE_PROFILE"}}, {"content": "In order to account for delayed submission of commutation, on Form 7 has been provided. As per rule 6 of CCS (Commutation of Pension ) Rules , 1981, the user will enter the date of receipt of Form 1/Medical Report signing date (In case where form is submitted after the date of retirement) (15.3.1)). In case where Form for commutation is submitted before date of retirement then commutation shall become absolute on the day following date of retirement. (15.3.1) The impact will reflect at the time of processing of Form 7, the factor of commutation will take effect from the Date of submission of Form 1/Medical Report, i.e., In the case where the form is submitted after retirement. ********************** 215 16 16. Income Tax Processing AO PDA will allot the Case to DH PDA. After this, DH PDA will login and process the case further. Now DH PDA will go to the “Investment Declaration”. DH PDA can see the detail of particular record by click on the “View” link shown in the last column of the grid. Proposed Declaration: Uploading of Supporting documents against savings/investment is not mandatory so the figures will only be checked in r/o ceiling as per IT Act. Actual Declaration: Uploading of Supporting documents against savings/investment is mandatory so the figures as well as Proof of saving will also be checked. In case of documents not clear/Documents not as per amount fed, pensioner may be requested to re upload. In such case, the declaration may be admitted as per available document with remarks mentioning that pensioner may upload proper document in respect of rejected column. After receiving of correct supporting document in fresh declaration, based on revised declaration income tax may be processed. should be processed. Fig(16.1) Fig(16.1) 216 Once the DHPDA click on the View link, he can see the below screen .Now DHPDA will check all the details filled by Retiree and send it to AAOPDA ,by selecting AAOPDA from the given dropdownlist and click on the Submit button as shown in Fig(16.2) Fig(16.2) Now AAOPDA will login and Process the case further. After login, AAOPDA will go to the “Investment Declaration Approval”. Now AAOPDA can check the detail by click the View link shown in the grid. After crosschecking the detail he can Approve or Return the record by click on the “Approve” or “Return” button shown in the grid shown in Fig(16.3) . Fig(16.3) 217 Now AAOPDA will go to the IT Calculation Sheet. From hereon, AAO can see the calculation Sheet by click on the View click shown in the grid Fig(16.4). Fig(16.4) Once AAOPDA click on the View link of “View” the below shown page will open (16.5). Fig(16.5) 218 Also AAOPDA can see the “Salary Breakup” from here. (16.6) Fig(16.6) If no proposed/actual declaration is sent by Pensioner, AAO PDA will run- Auto Calculation. Fig(16.7) Fig(16.7) And can see the IT calculation for any pensioner by feeding PPO No. Fig(16.8) 219 Fig(16.8) ********End*******", "source": "User Manual - Chapter 8, Section 15.3", "title": "Miscellaneous issues", "type": "user_manual_procedure", "procedure_type": "FORM_PROCESSING", "metadata": {"chapter": 8, "section": "15.3", "procedure_type": "FORM_PROCESSING"}}]