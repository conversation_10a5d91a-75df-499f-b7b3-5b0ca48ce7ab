#!/usr/bin/env python3
"""
CPMS Frontend HTTP Server
Serves the modern HTML/CSS/JS frontend for CPMS chatbot
"""

import os
import sys
import json
import mimetypes
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import webbrowser
import time

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

try:
    from backend.config import config
except ImportError:
    # Fallback configuration if backend config is not available
    class Config:
        API_PORT = 8001
        GRADIO_PORT = 7861
        BACKEND_API_HOST = "*************"
        BACKEND_API_PORT = 8001
        BACKEND_API_BASE_URL = f"http://*************:8001"
    
    config = Config()

class CPMSFrontendHandler(SimpleHTTPRequestHandler):
    """Custom HTTP request handler for CPMS frontend"""
    
    def __init__(self, *args, **kwargs):
        # Set the directory to serve files from
        self.static_dir = Path(__file__).parent / "static"
        super().__init__(*args, directory=str(self.static_dir), **kwargs)
    
    def end_headers(self):
        """Add CORS headers and security headers"""
        # CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        
        # Security headers
        self.send_header('X-Content-Type-Options', 'nosniff')
        self.send_header('X-Frame-Options', 'DENY')
        self.send_header('X-XSS-Protection', '1; mode=block')
        self.send_header('Referrer-Policy', 'strict-origin-when-cross-origin')
        
        # Cache control
        if self.path.endswith(('.css', '.js')):
            self.send_header('Cache-Control', 'public, max-age=3600')  # 1 hour
        elif self.path.endswith(('.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg')):
            self.send_header('Cache-Control', 'public, max-age=86400')  # 1 day
        else:
            self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        
        super().end_headers()
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # Serve index.html for root path
        if path == '/' or path == '':
            self.serve_index()
            return
        
        # Handle API configuration endpoint
        if path == '/api/config':
            self.serve_api_config()
            return
        
        # Handle health check
        if path == '/health':
            self.serve_health_check()
            return
        
        # Serve static files
        super().do_GET()
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS preflight"""
        self.send_response(200)
        self.end_headers()
    
    def serve_index(self):
        """Serve the main index.html file with configuration injection"""
        try:
            index_path = self.static_dir / "index.html"
            
            if not index_path.exists():
                self.send_error(404, "Frontend files not found")
                return
            
            with open(index_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Inject configuration into HTML
            config_script = f"""
            <script>
                window.CPMS_CONFIG = {{
                    apiUrl: '{config.BACKEND_API_BASE_URL}',
                    version: '1.0.0',
                    buildTime: '{time.strftime("%Y-%m-%d %H:%M:%S")}'
                }};
            </script>
            """
            
            # Insert config script before closing head tag
            content = content.replace('</head>', f'{config_script}\n</head>')
            
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.send_header('Content-Length', str(len(content.encode('utf-8'))))
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except Exception as e:
            print(f"❌ Error serving index.html: {e}")
            self.send_error(500, f"Internal server error: {str(e)}")
    
    def serve_api_config(self):
        """Serve API configuration as JSON"""
        try:
            config_data = {
                'apiUrl': config.BACKEND_API_BASE_URL,
                'version': '1.0.0',
                'features': {
                    'chat': True,
                    'suggestions': True,
                    'systemInfo': True,
                    'fileUpload': False  # Not implemented yet
                }
            }
            
            content = json.dumps(config_data, indent=2)
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Content-Length', str(len(content.encode('utf-8'))))
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except Exception as e:
            print(f"❌ Error serving API config: {e}")
            self.send_error(500, f"Internal server error: {str(e)}")
    
    def serve_health_check(self):
        """Serve frontend health check"""
        try:
            health_data = {
                'status': 'healthy',
                'service': 'CPMS Frontend',
                'version': '1.0.0',
                'timestamp': time.time(),
                'backend_url': config.BACKEND_API_BASE_URL
            }
            
            content = json.dumps(health_data, indent=2)
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Content-Length', str(len(content.encode('utf-8'))))
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except Exception as e:
            print(f"❌ Error serving health check: {e}")
            self.send_error(500, f"Internal server error: {str(e)}")
    
    def log_message(self, format, *args):
        """Custom log message format"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

class CPMSFrontendServer:
    """CPMS Frontend Server"""
    
    def __init__(self, host='localhost', port=None):
        self.host = host
        self.port = port or getattr(config, 'GRADIO_PORT', 7861)
        self.server = None
        self.server_thread = None
        
        # Ensure static directory exists
        self.static_dir = Path(__file__).parent / "static"
        if not self.static_dir.exists():
            raise FileNotFoundError(f"Static directory not found: {self.static_dir}")
        
        # Check for required files
        required_files = ['index.html', 'css/styles.css', 'js/app.js', 'js/api.js']
        for file_path in required_files:
            if not (self.static_dir / file_path).exists():
                raise FileNotFoundError(f"Required file not found: {file_path}")
    
    def start(self, open_browser=True):
        """Start the frontend server"""
        try:
            print("🎨 CPMS Modern Frontend Server")
            print("=" * 50)
            print(f"📁 Static files: {self.static_dir}")
            print(f"🌐 Frontend URL: http://{self.host}:{self.port}")
            print(f"🔌 Backend API: {config.BACKEND_API_BASE_URL}")
            print("=" * 50)
            
            # Create HTTP server
            self.server = HTTPServer((self.host, self.port), CPMSFrontendHandler)
            
            print(f"✅ Frontend server started on http://{self.host}:{self.port}")
            print("💡 Press Ctrl+C to stop the server")
            
            # Open browser if requested
            if open_browser:
                def open_browser_delayed():
                    time.sleep(1)  # Wait for server to start
                    try:
                        webbrowser.open(f"http://{self.host}:{self.port}")
                        print(f"🌐 Opened browser at http://{self.host}:{self.port}")
                    except Exception as e:
                        print(f"⚠️ Could not open browser: {e}")
                
                browser_thread = threading.Thread(target=open_browser_delayed, daemon=True)
                browser_thread.start()
            
            # Start server
            self.server.serve_forever()
            
        except KeyboardInterrupt:
            print("\n👋 Frontend server stopped by user")
            self.stop()
        except Exception as e:
            print(f"❌ Failed to start frontend server: {e}")
            raise
    
    def stop(self):
        """Stop the frontend server"""
        if self.server:
            print("🔄 Shutting down frontend server...")
            self.server.shutdown()
            self.server.server_close()
            print("✅ Frontend server stopped")

def main():
    """Main function to start the frontend server"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CPMS Modern Frontend Server")
    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="Host to bind the server to (default: 0.0.0.0)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=getattr(config, 'GRADIO_PORT', 7861),
        help=f"Port to bind the server to (default: {getattr(config, 'GRADIO_PORT', 7861)})"
    )
    parser.add_argument(
        "--no-browser",
        action="store_true",
        help="Don't open browser automatically"
    )
    
    args = parser.parse_args()
    
    try:
        server = CPMSFrontendServer(host=args.host, port=args.port)
        server.start(open_browser=not args.no_browser)
    except FileNotFoundError as e:
        print(f"❌ {e}")
        print("\n🔧 Make sure you have run the frontend setup:")
        print("1. Ensure all static files are in place")
        print("2. Check that the static directory exists")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
