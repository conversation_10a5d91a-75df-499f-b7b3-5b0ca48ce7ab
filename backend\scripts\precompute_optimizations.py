"""
Precomputation Script for CPMS Chatbot Optimizations
Builds FAQ cache, FAISS index, and precomputes embeddings for maximum speed
"""
import json
import time
from pathlib import Path
from typing import List, Dict, Any

# Apply compatibility patch before importing anything that uses sentence-transformers
try:
    from huggingface_compatibility_patch import apply_huggingface_compatibility_patch
    apply_huggingface_compatibility_patch()
except ImportError:
    print("⚠️ Compatibility patch not available, continuing...")

import config

def precompute_faq_responses():
    """Precompute responses for top FAQ questions"""
    print("🔄 Precomputing FAQ responses...")

    # Import here to avoid circular imports
    try:
        from ultra_optimized_rag_system import UltraOptimizedRAGSystem
        from faq_cache import faq_cache
        use_ultra_optimized = True
    except ImportError as e:
        print(f"⚠️ Ultra-optimized system not available: {e}")
        print("Using standard optimized system...")
        try:
            from optimized_rag_system import OptimizedCPMSRAGSystem as RAGSystem
            from faq_cache import faq_cache
            use_ultra_optimized = False
        except ImportError as e2:
            print(f"⚠️ Optimized system not available: {e2}")
            print("Using standard system...")
            from rag_system import CPMSRAGSystem as RAGSystem
            # Create a simple cache placeholder
            class SimpleFAQCache:
                def get_faq_response(self, query): return None
                def cache_response(self, query, result): pass
                def save_cache(self): pass
            faq_cache = SimpleFAQCache()
            use_ultra_optimized = False

    # Common CPMS questions to precompute
    common_questions = [
        "What is CPMS?",
        "What does CPMS stand for?",
        "Tell me about CPMS",
        "Explain CPMS",
        "What is Comprehensive Pension Management System?",

        "How to login to CPMS?",
        "How can I access CPMS portal?",
        "CPMS login procedure",
        "How to sign in to CPMS?",
        "CPMS portal access",

        "When is pension credited?",
        "Pension payment date",
        "When do I get my pension?",
        "Pension credit schedule",
        "Monthly pension timing",

        "What is the helpline number?",
        "CPMS contact number",
        "Support phone number",
        "Helpdesk contact",
        "Customer care number",

        "How to change bank account details?",
        "Update bank account in CPMS",
        "Change bank details",
        "Bank account modification",
        "Update banking information",

        "How to submit tax declarations?",
        "Tax declaration procedure",
        "Income tax submission",
        "Tax filing in CPMS",
        "Submit IT declaration",

        "What documents are needed for family pension?",
        "Family pension requirements",
        "Documents for family pension",
        "Family pension application",
        "Survivor pension documents",

        "How to lodge a grievance?",
        "Complaint procedure",
        "Grievance registration",
        "How to file complaint?",
        "Raise grievance in CPMS",

        "What is life certificate?",
        "Life certificate procedure",
        "Digital life certificate",
        "Jeevan Pramaan",
        "Annual life certificate",

        "How to check pension status?",
        "Pension status inquiry",
        "Track pension application",
        "Check pension details",
        "Pension verification",

        "Who creates CCA login?",
        "CCA login creation",
        "Who creates CCA account?",
        "System administrator CCA login",
        "How to create CCA login?"
    ]

    try:
        # Initialize RAG system
        if use_ultra_optimized:
            rag_system = UltraOptimizedRAGSystem(model_type="gguf")
        else:
            rag_system = RAGSystem(model_type="gguf")

        precomputed_count = 0
        for question in common_questions:
            print(f"Processing: {question}")

            # Check if already cached
            if faq_cache.get_faq_response(question):
                print(f"  ✅ Already cached")
                continue

            try:
                # Generate response
                result = rag_system.query(question, include_sources=True)

                # Cache the response
                faq_cache.cache_response(question, result)
                precomputed_count += 1

                print(f"  ✅ Cached in {result['processing_time']}s")

                # Small delay to prevent overwhelming the system
                time.sleep(0.1)

            except Exception as e:
                print(f"  ❌ Failed: {e}")
                continue

        # Save cache to disk
        faq_cache.save_cache()

        print(f"✅ Precomputed {precomputed_count} FAQ responses")
        return True

    except Exception as e:
        print(f"❌ FAQ precomputation failed: {e}")
        return False

def build_faiss_index():
    """Build optimized FAISS index"""
    print("🔄 Building FAISS index...")

    try:
        from faiss_vector_store import build_faiss_vector_store

        vector_store = build_faiss_vector_store()
        if vector_store:
            stats = vector_store.get_collection_stats()
            print(f"✅ FAISS index built: {stats}")
            return True
        else:
            print("❌ Failed to build FAISS index")
            return False

    except ImportError as e:
        print(f"⚠️ FAISS not available: {e}")
        print("Using ChromaDB optimization instead...")
        # Fallback to ChromaDB optimization
        try:
            from vector_store import build_vector_store

            vector_store = build_vector_store()
            if vector_store:
                print("✅ ChromaDB vector store built successfully")
                return True
            else:
                print("❌ Failed to build ChromaDB vector store")
                return False
        except Exception as e2:
            print(f"❌ ChromaDB fallback failed: {e2}")
            return False

    except Exception as e:
        print(f"❌ Vector store building failed: {e}")
        print("Trying ChromaDB fallback...")
        try:
            from vector_store import build_vector_store
            vector_store = build_vector_store()
            if vector_store:
                print("✅ ChromaDB vector store built successfully")
                return True
        except Exception as e2:
            print(f"❌ All vector store options failed: {e2}")
        return False

def precompute_embeddings():
    """Precompute embeddings for all documents"""
    print("🔄 Precomputing embeddings...")

    try:
        # Load processed data
        processed_data_file = config.PROCESSED_DATA_PATH / "all_processed_data.json"

        if not processed_data_file.exists():
            print("❌ Processed data not found")
            return False

        with open(processed_data_file, 'r', encoding='utf-8') as f:
            documents = json.load(f)

        print(f"Found {len(documents)} documents to process")

        # Initialize embedding model
        from sentence_transformers import SentenceTransformer
        embedding_model = SentenceTransformer(config.EMBEDDINGS_MODEL)

        # Extract texts
        texts = []
        for doc in documents:
            if isinstance(doc, dict):
                texts.append(doc.get('content', str(doc)))
            else:
                texts.append(str(doc))

        # Generate embeddings in batches
        batch_size = config.EMBEDDING_BATCH_SIZE
        embeddings = []

        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i+batch_size]
            batch_embeddings = embedding_model.encode(batch_texts, show_progress_bar=True)
            embeddings.extend(batch_embeddings)
            print(f"Processed {min(i+batch_size, len(texts))}/{len(texts)} documents")

        # Save embeddings
        embeddings_file = config.VECTOR_DB_PATH / "precomputed_embeddings.npy"
        import numpy as np
        np.save(embeddings_file, np.array(embeddings))

        print(f"✅ Precomputed {len(embeddings)} embeddings")
        return True

    except Exception as e:
        print(f"❌ Embedding precomputation failed: {e}")
        return False

def optimize_model_loading():
    """Optimize model loading and caching"""
    print("🔄 Optimizing model loading...")

    try:
        from optimized_model_handler import OptimizedCPMSModelHandler

        # Load and cache GGUF model
        print("Loading GGUF model...")
        gguf_handler = OptimizedCPMSModelHandler(model_type="gguf")
        model_info = gguf_handler.get_model_info()
        print(f"✅ GGUF model loaded: {model_info}")

        # Test generation to warm up
        test_prompt = "What is CPMS?"
        response = gguf_handler.generate_response(test_prompt, max_tokens=50)
        print(f"✅ Model warmed up with test generation")

        return True

    except Exception as e:
        print(f"❌ Model optimization failed: {e}")
        return False

def run_performance_benchmark():
    """Run performance benchmark to measure improvements"""
    print("🔄 Running performance benchmark...")

    try:
        from ultra_optimized_rag_system import UltraOptimizedRAGSystem

        # Initialize system
        rag_system = UltraOptimizedRAGSystem(model_type="gguf")

        # Test queries
        test_queries = [
            "What is CPMS?",
            "How to login to CPMS?",
            "When is pension credited?",
            "What is the helpline number?",
            "How to change bank account details?"
        ]

        total_time = 0
        cache_hits = 0

        print("\nBenchmark Results:")
        print("-" * 50)

        for query in test_queries:
            start_time = time.time()
            result = rag_system.query(query)
            end_time = time.time()

            response_time = end_time - start_time
            total_time += response_time

            if result.get('cached', False):
                cache_hits += 1
                cache_status = f"✅ {result.get('cache_type', 'Cached')}"
            else:
                cache_status = "🔄 Generated"

            print(f"{query[:30]:<30} | {response_time:>6.3f}s | {cache_status}")

        avg_time = total_time / len(test_queries)
        cache_hit_rate = (cache_hits / len(test_queries)) * 100

        print("-" * 50)
        print(f"Average Response Time: {avg_time:.3f}s")
        print(f"Cache Hit Rate: {cache_hit_rate:.1f}%")
        print(f"Total Benchmark Time: {total_time:.3f}s")

        # System stats
        stats = rag_system.get_system_stats()
        print(f"\nSystem Statistics:")
        print(f"Vector Store Documents: {stats['vector_store']['total_documents']}")
        print(f"Cached Embeddings: {stats['vector_store']['cached_embeddings']}")
        print(f"FAQ Cache Size: {stats['cache_stats']['precomputed_faqs']}")

        return True

    except Exception as e:
        print(f"❌ Benchmark failed: {e}")
        return False

def main():
    """Main precomputation pipeline"""
    print("🚀 Starting CPMS Chatbot Optimization Pipeline")
    print("=" * 60)

    start_time = time.time()

    # Step 1: Build vector index
    print("\n1. Building Vector Index")
    if not build_faiss_index():
        print("❌ Vector index building failed")
        return False

    # Step 2: Precompute embeddings
    print("\n2. Precomputing Embeddings")
    if not precompute_embeddings():
        print("⚠️ Embedding precomputation failed, continuing...")

    # Step 3: Optimize model loading
    print("\n3. Optimizing Model Loading")
    if not optimize_model_loading():
        print("❌ Model optimization failed")
        return False

    # Step 4: Precompute FAQ responses
    print("\n4. Precomputing FAQ Responses")
    if not precompute_faq_responses():
        print("⚠️ FAQ precomputation failed, continuing...")

    # Step 5: Run benchmark
    print("\n5. Performance Benchmark")
    run_performance_benchmark()

    total_time = time.time() - start_time
    print(f"\n✅ Optimization pipeline completed in {total_time:.2f}s")
    print("🎯 System is now optimized for ultra-fast responses!")

    return True

if __name__ == "__main__":
    main()
