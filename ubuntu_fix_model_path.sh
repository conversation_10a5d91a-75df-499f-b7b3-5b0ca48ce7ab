#!/bin/bash

echo "🔧 Fixing Model Path for Ubuntu Server"
echo "======================================"

MODEL_DIR="/home/<USER>/CPMS_CHATBOT/models/mistral-7b-instruct-v0.2.Q4_K_S"

# Check if model directory exists
if [ ! -d "$MODEL_DIR" ]; then
    echo "❌ Model directory not found: $MODEL_DIR"
    echo "💡 Please ensure your model is in the correct location"
    exit 1
fi

echo "✅ Model directory found: $MODEL_DIR"

# Find GGUF files
echo "🔍 Looking for GGUF files..."
GGUF_FILES=$(find "$MODEL_DIR" -name "*.gguf" -type f)

if [ -z "$GGUF_FILES" ]; then
    echo "❌ No .gguf files found in $MODEL_DIR"
    echo "📁 Files in directory:"
    ls -la "$MODEL_DIR"
    echo ""
    echo "💡 Expected filename: mistral-7b-instruct-v0.2.Q4_K_S.gguf"
    exit 1
fi

echo "✅ Found GGUF files:"
for file in $GGUF_FILES; do
    size=$(du -h "$file" | cut -f1)
    echo "  - $(basename "$file") ($size)"
done

# Use the first (or only) GGUF file found
GGUF_FILE=$(echo "$GGUF_FILES" | head -n1)
echo ""
echo "🎯 Using model: $GGUF_FILE"

# Set environment variable
export MISTRAL_MODEL_PATH="$GGUF_FILE"
echo "✅ Set environment variable: MISTRAL_MODEL_PATH=$GGUF_FILE"

# Create a permanent environment setup script
cat > set_model_env.sh << EOF
#!/bin/bash
# Set the model path environment variable
export MISTRAL_MODEL_PATH="$GGUF_FILE"
echo "✅ Model path set to: \$MISTRAL_MODEL_PATH"
EOF

chmod +x set_model_env.sh
echo "✅ Created set_model_env.sh for permanent setup"

# Update the config file if it exists
CONFIG_FILE="backend/config/config.py"
if [ -f "$CONFIG_FILE" ]; then
    echo "📝 Updating configuration file..."
    
    # Create backup
    cp "$CONFIG_FILE" "${CONFIG_FILE}.backup"
    echo "✅ Created backup: ${CONFIG_FILE}.backup"
    
    # Update the Ubuntu model path
    sed -i "s|MISTRAL_MODEL_PATH = \"/home/<USER>/CPMS_CHATBOT/models/mistral-7b-instruct-v0.2.Q4_K_S/mistral-7b-instruct-v0.2.Q4_K_S.gguf\"|MISTRAL_MODEL_PATH = \"$GGUF_FILE\"|g" "$CONFIG_FILE"
    
    echo "✅ Updated configuration file"
else
    echo "⚠️ Configuration file not found: $CONFIG_FILE"
fi

# Test the setup
echo ""
echo "🧪 Testing setup..."
python3 -c "
import os
import sys
sys.path.insert(0, 'backend')

try:
    from config import config
    print(f'✅ Config loaded: {config.MISTRAL_MODEL_PATH}')
    
    if os.path.exists(config.MISTRAL_MODEL_PATH):
        print('✅ Model file exists')
        size_mb = os.path.getsize(config.MISTRAL_MODEL_PATH) / (1024 * 1024)
        print(f'   Size: {size_mb:.1f} MB')
    else:
        print('❌ Model file not found at config path')
        
except Exception as e:
    print(f'❌ Config test failed: {e}')
"

echo ""
echo "✅ Setup Complete!"
echo "=================="
echo ""
echo "🚀 To start the backend with local model:"
echo "   source set_model_env.sh"
echo "   cd backend"
echo "   python3 run_backend.py"
echo ""
echo "💡 The backend should now use your local GGUF model"
echo "   instead of downloading from HuggingFace"
