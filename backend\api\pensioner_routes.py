"""
Pensioner-specific API routes for pension queries
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Dict, Any, Optional
import time

from backend.database.database import get_db
from backend.services.pensioner_service import pensioner_service
from backend.models.pensioner_models import Pensioner
from backend.api.auth_routes import get_current_pensioner

# Create router
router = APIRouter(prefix="/pensioner", tags=["Pensioner Services"])

# Pydantic models for responses
class PensionerResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    processing_time: Optional[float] = None

@router.get("/lc-expiry", response_model=PensionerResponse)
async def get_lc_expiry(
    pensioner: Pensioner = Depends(get_current_pensioner),
    db: Session = Depends(get_db)
):
    """
    Get Life Certificate (LC) expiry date for the authenticated pensioner
    """
    start_time = time.time()
    try:
        result = pensioner_service.get_lc_expiry_date(pensioner, db)
        processing_time = time.time() - start_time
        
        # Log the query
        pensioner_service.log_query(
            pensioner_id=pensioner.id,
            query_text="LC expiry date query",
            query_type="lc_expiry",
            response_text=result["message"],
            processing_time=processing_time,
            session_id=f"api_{pensioner.id}_{int(time.time())}",
            db=db
        )
        
        return PensionerResponse(
            **result,
            processing_time=processing_time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve LC information"
        )

@router.get("/pension-breakup", response_model=PensionerResponse)
async def get_pension_breakup(
    pensioner: Pensioner = Depends(get_current_pensioner),
    db: Session = Depends(get_db)
):
    """
    Get last month pension breakup for the authenticated pensioner
    """
    start_time = time.time()
    try:
        result = pensioner_service.get_pension_breakup(pensioner, db)
        processing_time = time.time() - start_time
        
        # Log the query
        pensioner_service.log_query(
            pensioner_id=pensioner.id,
            query_text="Pension breakup query",
            query_type="pension_breakup",
            response_text=result["message"],
            processing_time=processing_time,
            session_id=f"api_{pensioner.id}_{int(time.time())}",
            db=db
        )
        
        return PensionerResponse(
            **result,
            processing_time=processing_time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve pension breakup"
        )

@router.get("/commutation-restoration", response_model=PensionerResponse)
async def get_commutation_restoration(
    pensioner: Pensioner = Depends(get_current_pensioner),
    db: Session = Depends(get_db)
):
    """
    Get commutation restoration date for the authenticated pensioner
    """
    start_time = time.time()
    try:
        result = pensioner_service.get_commutation_restoration_date(pensioner, db)
        processing_time = time.time() - start_time
        
        # Log the query
        pensioner_service.log_query(
            pensioner_id=pensioner.id,
            query_text="Commutation restoration date query",
            query_type="commutation_restoration",
            response_text=result["message"],
            processing_time=processing_time,
            session_id=f"api_{pensioner.id}_{int(time.time())}",
            db=db
        )
        
        return PensionerResponse(
            **result,
            processing_time=processing_time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve commutation information"
        )

@router.get("/da-rate", response_model=PensionerResponse)
async def get_da_rate(
    pensioner: Pensioner = Depends(get_current_pensioner),
    db: Session = Depends(get_db)
):
    """
    Get current DA (Dearness Allowance) rate for the authenticated pensioner
    """
    start_time = time.time()
    try:
        result = pensioner_service.get_current_da_rate(pensioner, db)
        processing_time = time.time() - start_time
        
        # Log the query
        pensioner_service.log_query(
            pensioner_id=pensioner.id,
            query_text="Current DA rate query",
            query_type="da_rate",
            response_text=result["message"],
            processing_time=processing_time,
            session_id=f"api_{pensioner.id}_{int(time.time())}",
            db=db
        )
        
        return PensionerResponse(
            **result,
            processing_time=processing_time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve DA information"
        )

@router.get("/additional-pension", response_model=PensionerResponse)
async def get_additional_pension(
    pensioner: Pensioner = Depends(get_current_pensioner),
    db: Session = Depends(get_db)
):
    """
    Get additional pension rate for the authenticated pensioner
    """
    start_time = time.time()
    try:
        result = pensioner_service.get_additional_pension_rate(pensioner, db)
        processing_time = time.time() - start_time
        
        # Log the query
        pensioner_service.log_query(
            pensioner_id=pensioner.id,
            query_text="Additional pension rate query",
            query_type="additional_pension",
            response_text=result["message"],
            processing_time=processing_time,
            session_id=f"api_{pensioner.id}_{int(time.time())}",
            db=db
        )
        
        return PensionerResponse(
            **result,
            processing_time=processing_time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve additional pension information"
        )

@router.get("/family-pension", response_model=PensionerResponse)
async def get_family_pension(
    pensioner: Pensioner = Depends(get_current_pensioner),
    db: Session = Depends(get_db)
):
    """
    Get normal and enhanced family pension information for the authenticated pensioner
    """
    start_time = time.time()
    try:
        result = pensioner_service.get_family_pension_info(pensioner, db)
        processing_time = time.time() - start_time
        
        # Log the query
        pensioner_service.log_query(
            pensioner_id=pensioner.id,
            query_text="Family pension information query",
            query_type="family_pension",
            response_text=result["message"],
            processing_time=processing_time,
            session_id=f"api_{pensioner.id}_{int(time.time())}",
            db=db
        )
        
        return PensionerResponse(
            **result,
            processing_time=processing_time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve family pension information"
        )

@router.get("/fma", response_model=PensionerResponse)
async def get_fma(
    pensioner: Pensioner = Depends(get_current_pensioner),
    db: Session = Depends(get_db)
):
    """
    Get FMA (Fixed Medical Allowance) information for the authenticated pensioner
    """
    start_time = time.time()
    try:
        result = pensioner_service.get_fma_info(pensioner, db)
        processing_time = time.time() - start_time
        
        # Log the query
        pensioner_service.log_query(
            pensioner_id=pensioner.id,
            query_text="FMA information query",
            query_type="fma",
            response_text=result["message"],
            processing_time=processing_time,
            session_id=f"api_{pensioner.id}_{int(time.time())}",
            db=db
        )
        
        return PensionerResponse(
            **result,
            processing_time=processing_time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve FMA information"
        )

@router.get("/mobile-change-info", response_model=PensionerResponse)
async def get_mobile_change_info(
    pensioner: Pensioner = Depends(get_current_pensioner),
    db: Session = Depends(get_db)
):
    """
    Get information about how to change mobile number
    """
    start_time = time.time()
    try:
        result = pensioner_service.get_mobile_change_info(pensioner, db)
        processing_time = time.time() - start_time
        
        # Log the query
        pensioner_service.log_query(
            pensioner_id=pensioner.id,
            query_text="Mobile number change process query",
            query_type="mobile_change",
            response_text=result["message"],
            processing_time=processing_time,
            session_id=f"api_{pensioner.id}_{int(time.time())}",
            db=db
        )
        
        return PensionerResponse(
            **result,
            processing_time=processing_time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve mobile change information"
        )

@router.get("/sampann-migration", response_model=PensionerResponse)
async def get_sampann_migration(
    pensioner: Pensioner = Depends(get_current_pensioner),
    db: Session = Depends(get_db)
):
    """
    Get Sampann migration date and status for the authenticated pensioner
    """
    start_time = time.time()
    try:
        result = pensioner_service.get_sampann_migration_info(pensioner, db)
        processing_time = time.time() - start_time
        
        # Log the query
        pensioner_service.log_query(
            pensioner_id=pensioner.id,
            query_text="Sampann migration information query",
            query_type="sampann_migration",
            response_text=result["message"],
            processing_time=processing_time,
            session_id=f"api_{pensioner.id}_{int(time.time())}",
            db=db
        )
        
        return PensionerResponse(
            **result,
            processing_time=processing_time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve migration information"
        )
