"""
Optimized Vector Store for CPMS chatbot with caching and parallel processing
Reduces vector search time significantly
"""
import json
import time
import threading
from typing import List, Dict, Any, Optional
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor
import numpy as np
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from backend.config import config

# Global caches
_EMBEDDING_CACHE = {}
_SEARCH_CACHE = {}
_CACHE_LOCK = threading.Lock()

class OptimizedCPMSVectorStore:
    def __init__(self, persist_directory: str = None):
        self.persist_directory = persist_directory or str(config.VECTOR_DB_PATH)

        # Use a faster, smaller embedding model for speed
        self.embedding_model = SentenceTransformer(
            config.EMBEDDINGS_MODEL,
            device='cuda' if config.EMBEDDINGS_MODEL == 'cuda' else 'cpu'
        )

        # Optimize text splitter for smaller chunks (faster processing)
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=config.CHUNK_SIZE // 2,  # Smaller chunks for faster processing
            chunk_overlap=config.CHUNK_OVERLAP // 2,
            length_function=len,
        )

        # Initialize ChromaDB with optimized settings
        self.client = chromadb.PersistentClient(
            path=self.persist_directory,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True,
                is_persistent=True
            )
        )

        # Thread pool for parallel processing
        self.executor = ThreadPoolExecutor(max_workers=4)

        try:
            self.collection = self.client.get_collection("cpms_documents")
            print("✅ Existing vector collection loaded")
        except:
            self.collection = self.client.create_collection(
                name="cpms_documents",
                metadata={"hnsw:space": "cosine"}  # Optimized for cosine similarity
            )
            print("✅ New vector collection created")

    @lru_cache(maxsize=1000)
    def _cached_embed_query(self, query: str) -> List[float]:
        """Cache embeddings for queries"""
        return self.embedding_model.encode([query])[0].tolist()

    def _batch_embed_texts(self, texts: List[str]) -> List[List[float]]:
        """Embed texts in batches for efficiency"""
        batch_size = config.EMBEDDING_BATCH_SIZE if hasattr(config, 'EMBEDDING_BATCH_SIZE') else 16
        embeddings = []

        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            batch_embeddings = self.embedding_model.encode(
                batch,
                show_progress_bar=False,
                batch_size=batch_size,
                convert_to_tensor=False
            )
            embeddings.extend(batch_embeddings.tolist())

        return embeddings

    def add_documents(self, documents: List[Dict[str, Any]]):
        """Add documents to vector store with optimizations"""
        print(f"Processing {len(documents)} documents...")

        # Convert to Document objects
        doc_objects = []
        for doc in documents:
            doc_objects.append(Document(
                page_content=doc['content'],
                metadata=doc.get('metadata', {})
            ))

        # Split documents into chunks
        print("Splitting documents into chunks...")
        chunks = []
        for doc in doc_objects:
            doc_chunks = self.text_splitter.split_documents([doc])
            chunks.extend(doc_chunks)

        print(f"Created {len(chunks)} chunks")

        # Prepare data for ChromaDB
        texts = [chunk.page_content for chunk in chunks]
        metadatas = [chunk.metadata for chunk in chunks]
        ids = [f"chunk_{i}_{int(time.time())}" for i in range(len(chunks))]

        # Generate embeddings in batches
        print("Generating embeddings in batches...")
        embeddings = self._batch_embed_texts(texts)

        # Add to collection in batches
        batch_size = 100
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i+batch_size]
            batch_embeddings = embeddings[i:i+batch_size]
            batch_metadatas = metadatas[i:i+batch_size]
            batch_ids = ids[i:i+batch_size]

            self.collection.add(
                documents=batch_texts,
                embeddings=batch_embeddings,
                metadatas=batch_metadatas,
                ids=batch_ids
            )

        print(f"✅ Added {len(chunks)} chunks to vector store")

    def similarity_search(self, query: str, k: int = None) -> List[Dict[str, Any]]:
        """Optimized similarity search with caching"""
        k = k or config.SIMILARITY_SEARCH_K if hasattr(config, 'SIMILARITY_SEARCH_K') else 3

        # Check cache first
        cache_key = f"{query}_{k}"
        if config.ENABLE_VECTOR_CACHE if hasattr(config, 'ENABLE_VECTOR_CACHE') else False:
            with _CACHE_LOCK:
                if cache_key in _SEARCH_CACHE:
                    cache_entry = _SEARCH_CACHE[cache_key]
                    if time.time() - cache_entry['timestamp'] < config.CACHE_TTL_SECONDS:
                        print("✅ Search results served from cache")
                        return cache_entry['results']

        # Generate query embedding
        query_embedding = self._cached_embed_query(query)

        # Perform search
        results = self.collection.query(
            query_embeddings=[query_embedding],
            n_results=k,
            include=['documents', 'metadatas', 'distances']
        )

        # Format results
        formatted_results = []
        if results['documents'] and results['documents'][0]:
            for i, (doc, metadata, distance) in enumerate(zip(
                results['documents'][0],
                results['metadatas'][0],
                results['distances'][0]
            )):
                formatted_results.append({
                    'content': doc,
                    'metadata': metadata,
                    'score': 1 - distance  # Convert distance to similarity score
                })

        # Cache results
        if config.ENABLE_VECTOR_CACHE if hasattr(config, 'ENABLE_VECTOR_CACHE') else False:
            with _CACHE_LOCK:
                _SEARCH_CACHE[cache_key] = {
                    'results': formatted_results,
                    'timestamp': time.time()
                }

                # Clean old cache entries
                if len(_SEARCH_CACHE) > (config.MAX_CACHE_SIZE if hasattr(config, 'MAX_CACHE_SIZE') else 100):
                    oldest_key = min(_SEARCH_CACHE.keys(),
                                   key=lambda k: _SEARCH_CACHE[k]['timestamp'])
                    del _SEARCH_CACHE[oldest_key]

        return formatted_results

    def get_relevant_context(self, query: str, max_context_length: int = None) -> str:
        """Get relevant context with length optimization"""
        max_context_length = max_context_length or config.MAX_CONTEXT_LENGTH if hasattr(config, 'MAX_CONTEXT_LENGTH') else 800

        # Get fewer but more relevant results for speed
        search_results = self.similarity_search(query, k=2)

        context_parts = []
        current_length = 0

        for result in search_results:
            content = result['content']

            # Add content if it fits within the limit
            if current_length + len(content) <= max_context_length:
                context_parts.append(content)
                current_length += len(content)
            else:
                # Add partial content to fill the remaining space
                remaining_space = max_context_length - current_length
                if remaining_space > 100:  # Only add if there's meaningful space
                    context_parts.append(content[:remaining_space] + "...")
                break

        return "\n\n".join(context_parts)

    def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics"""
        try:
            count = self.collection.count()
            return {
                'total_documents': count,
                'collection_name': 'cpms_documents',
                'embedding_model': config.EMBEDDINGS_MODEL,
                'optimizations_enabled': True
            }
        except Exception as e:
            return {
                'total_documents': 0,
                'error': str(e),
                'collection_name': 'cpms_documents',
                'embedding_model': config.EMBEDDINGS_MODEL,
                'optimizations_enabled': True
            }

    def clear_collection(self):
        """Clear the collection"""
        try:
            self.client.delete_collection("cpms_documents")
            self.collection = self.client.create_collection(
                name="cpms_documents",
                metadata={"hnsw:space": "cosine"}
            )
            print("✅ Collection cleared and recreated")
        except Exception as e:
            print(f"Error clearing collection: {e}")

    def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        try:
            # Test embedding
            test_embedding = self._cached_embed_query("test")

            # Test search
            test_results = self.similarity_search("test", k=1)

            stats = self.get_collection_stats()

            return {
                'status': 'healthy',
                'embedding_model_working': len(test_embedding) > 0,
                'search_working': isinstance(test_results, list),
                'total_documents': stats.get('total_documents', 0),
                'optimizations_enabled': True
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'optimizations_enabled': True
            }

def build_optimized_vector_store():
    """Build optimized vector store from processed data"""
    # Load processed data
    processed_data_file = config.PROCESSED_DATA_PATH / "all_processed_data.json"

    if not processed_data_file.exists():
        print("Processed data not found. Please run data_processor.py first.")
        return None

    with open(processed_data_file, 'r', encoding='utf-8') as f:
        documents = json.load(f)

    print(f"Loaded {len(documents)} documents")

    # Create optimized vector store
    vector_store = OptimizedCPMSVectorStore()

    # Add documents
    vector_store.add_documents(documents)

    # Print stats
    stats = vector_store.get_collection_stats()
    print(f"Optimized vector store stats: {stats}")

    return vector_store

def clear_vector_caches():
    """Clear vector store caches"""
    global _EMBEDDING_CACHE, _SEARCH_CACHE
    with _CACHE_LOCK:
        _EMBEDDING_CACHE.clear()
        _SEARCH_CACHE.clear()
    print("✅ Vector store caches cleared")

# Test the optimized vector store
if __name__ == "__main__":
    print("Testing Optimized CPMS Vector Store...")

    vector_store = OptimizedCPMSVectorStore()

    # Health check
    health = vector_store.health_check()
    print(f"Health check: {health}")

    # Test search
    if health['status'] == 'healthy':
        test_query = "What is CPMS?"
        start_time = time.time()
        results = vector_store.similarity_search(test_query, k=3)
        end_time = time.time()

        print(f"Search query: {test_query}")
        print(f"Search time: {end_time - start_time:.2f}s")
        print(f"Results found: {len(results)}")

        if results:
            print(f"Top result score: {results[0]['score']:.3f}")

    # Get stats
    stats = vector_store.get_collection_stats()
    print(f"Collection stats: {stats}")
