/**
 * Authentication System for CPMS Enhanced Portal
 */
class AuthSystem {
    constructor() {
        this.baseURL = 'http://localhost:8001' || 'http://*************:8001';
        this.token = localStorage.getItem('cpms_token');
        this.user = JSON.parse(localStorage.getItem('cpms_user') || 'null');
        this.otpSession = null;
        this.otpTimer = null;
        
        this.initializeElements();
        this.bindEvents();
        this.checkAuthStatus();
    }
    
    initializeElements() {
        // Modal elements
        this.authModal = document.getElementById('auth-modal');
        this.loginForm = document.getElementById('login-form');
        this.otpForm = document.getElementById('otp-form');
        
        // Login form elements
        this.eppoInput = document.getElementById('eppo-number');
        this.mobileInput = document.getElementById('mobile-number');
        this.sendOtpBtn = document.getElementById('send-otp-btn');
        this.guestModeBtn = document.getElementById('guest-mode-btn');
        
        // OTP form elements
        this.otpMobile = document.getElementById('otp-mobile');
        this.otpCountdown = document.getElementById('otp-countdown');
        this.otpInput = document.getElementById('otp-code');
        this.verifyOtpBtn = document.getElementById('verify-otp-btn');
        this.resendOtpBtn = document.getElementById('resend-otp-btn');
        this.backToLoginBtn = document.getElementById('back-to-login-btn');
        
        // Header elements
        this.loginBtn = document.getElementById('login-btn');
        this.logoutBtn = document.getElementById('logout-btn');
        this.userInfo = document.getElementById('user-info');
        this.userName = document.getElementById('user-name');
        this.userEppo = document.getElementById('user-eppo');
        this.authStatus = document.getElementById('auth-status');
        this.quickActions = document.getElementById('quick-actions');
    }
    
    bindEvents() {
        // Login button
        this.loginBtn?.addEventListener('click', () => this.showLoginModal());
        
        // Guest mode button
        this.guestModeBtn?.addEventListener('click', () => this.enterGuestMode());
        
        // Send OTP button
        this.sendOtpBtn?.addEventListener('click', () => this.sendOTP());
        
        // Verify OTP button
        this.verifyOtpBtn?.addEventListener('click', () => this.verifyOTP());
        
        // Resend OTP button
        this.resendOtpBtn?.addEventListener('click', () => this.resendOTP());
        
        // Back to login button
        this.backToLoginBtn?.addEventListener('click', () => this.showLoginForm());
        
        // Logout button
        this.logoutBtn?.addEventListener('click', () => this.logout());
        
        // Input validation
        this.eppoInput?.addEventListener('input', () => this.validateLoginForm());
        this.mobileInput?.addEventListener('input', () => this.validateLoginForm());
        this.otpInput?.addEventListener('input', () => this.validateOtpForm());
        
        // Enter key handling
        this.eppoInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.mobileInput?.focus();
        });
        this.mobileInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendOTP();
        });
        this.otpInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.verifyOTP();
        });
        
        // Modal close on outside click
        this.authModal?.addEventListener('click', (e) => {
            if (e.target === this.authModal) {
                this.hideModal();
            }
        });
    }
    
    checkAuthStatus() {
        if (this.token && this.user) {
            this.showAuthenticatedState();
        } else {
            this.showUnauthenticatedState();
        }
    }
    
    showLoginModal() {
        this.authModal?.classList.remove('hidden');
        this.showLoginForm();
        this.eppoInput?.focus();
    }
    
    hideModal() {
        this.authModal?.classList.add('hidden');
        this.clearForms();
        this.clearOtpTimer();
    }
    
    showLoginForm() {
        this.loginForm?.classList.remove('hidden');
        this.otpForm?.classList.add('hidden');
        this.clearOtpTimer();
    }
    
    showOtpForm() {
        this.loginForm?.classList.add('hidden');
        this.otpForm?.classList.remove('hidden');
        this.otpInput?.focus();
    }
    
    enterGuestMode() {
        this.hideModal();
        this.showUnauthenticatedState();
    }
    
    async sendOTP() {
        const eppo = this.eppoInput?.value.trim().toUpperCase();
        const mobile = this.mobileInput?.value.trim();
        
        if (!this.validateLoginInputs(eppo, mobile)) return;
        
        this.setButtonLoading(this.sendOtpBtn, true);
        
        try {
            const response = await fetch(`${this.baseURL}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    eppo_number: eppo,
                    mobile_number: mobile
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.otpSession = {
                    session_id: result.session_id,
                    mobile: mobile,
                    expires_in: result.expires_in
                };
                
                this.otpMobile.textContent = mobile;
                this.showOtpForm();
                this.startOtpTimer(result.expires_in);
                this.showNotification('OTP sent successfully!', 'success');
            } else {
                this.showNotification(result.message, 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showNotification('Network error. Please try again.', 'error');
        } finally {
            this.setButtonLoading(this.sendOtpBtn, false);
        }
    }
    
    async verifyOTP() {
        const otp = this.otpInput?.value.trim();
        
        if (!otp || otp.length !== 6) {
            this.showNotification('Please enter a valid 6-digit OTP', 'error');
            return;
        }
        
        if (!this.otpSession) {
            this.showNotification('OTP session expired. Please try again.', 'error');
            this.showLoginForm();
            return;
        }
        
        this.setButtonLoading(this.verifyOtpBtn, true);
        
        try {
            const response = await fetch(`${this.baseURL}/auth/verify-otp`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_id: this.otpSession.session_id,
                    otp: otp
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.token = result.access_token;
                this.user = result.pensioner;
                
                localStorage.setItem('cpms_token', this.token);
                localStorage.setItem('cpms_user', JSON.stringify(this.user));
                
                this.hideModal();
                this.showAuthenticatedState();
                this.showNotification(`Welcome back, ${this.user.name}!`, 'success');
                
                // Notify chat system about authentication
                if (window.enhancedChat) {
                    window.enhancedChat.onAuthenticationChange(true, this.user);
                }
            } else {
                this.showNotification(result.message, 'error');
            }
        } catch (error) {
            console.error('OTP verification error:', error);
            this.showNotification('Verification failed. Please try again.', 'error');
        } finally {
            this.setButtonLoading(this.verifyOtpBtn, false);
        }
    }
    
    async resendOTP() {
        if (!this.otpSession) return;
        
        this.setButtonLoading(this.resendOtpBtn, true);
        
        try {
            // Use the same login endpoint to resend OTP
            const response = await fetch(`${this.baseURL}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    eppo_number: this.eppoInput?.value.trim().toUpperCase(),
                    mobile_number: this.otpSession.mobile
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.otpSession.session_id = result.session_id;
                this.startOtpTimer(result.expires_in);
                this.showNotification('OTP resent successfully!', 'success');
            } else {
                this.showNotification(result.message, 'error');
            }
        } catch (error) {
            console.error('Resend OTP error:', error);
            this.showNotification('Failed to resend OTP. Please try again.', 'error');
        } finally {
            this.setButtonLoading(this.resendOtpBtn, false);
        }
    }
    
    logout() {
        this.token = null;
        this.user = null;
        localStorage.removeItem('cpms_token');
        localStorage.removeItem('cpms_user');
        
        this.showUnauthenticatedState();
        this.showNotification('Logged out successfully', 'info');
        
        // Notify chat system about logout
        if (window.enhancedChat) {
            window.enhancedChat.onAuthenticationChange(false, null);
        }
    }
    
    showAuthenticatedState() {
        this.loginBtn?.classList.add('hidden');
        this.userInfo?.classList.remove('hidden');
        this.quickActions?.classList.remove('hidden');
        
        if (this.user) {
            this.userName.textContent = this.user.name;
            this.userEppo.textContent = this.user.eppo_number;
        }
        
        this.authStatus?.classList.remove('guest');
        this.authStatus?.classList.add('authenticated');
        this.authStatus.textContent = 'Authenticated';
    }
    
    showUnauthenticatedState() {
        this.loginBtn?.classList.remove('hidden');
        this.userInfo?.classList.add('hidden');
        this.quickActions?.classList.add('hidden');
        
        this.authStatus?.classList.remove('authenticated');
        this.authStatus?.classList.add('guest');
        this.authStatus.textContent = 'Guest Mode';
    }
    
    validateLoginInputs(eppo, mobile) {
        if (!eppo || eppo.length < 8) {
            this.showNotification('Please enter a valid EPPO number', 'error');
            this.eppoInput?.focus();
            return false;
        }
        
        if (!mobile || mobile.length < 10) {
            this.showNotification('Please enter a valid mobile number', 'error');
            this.mobileInput?.focus();
            return false;
        }
        
        return true;
    }
    
    validateLoginForm() {
        const eppo = this.eppoInput?.value.trim();
        const mobile = this.mobileInput?.value.trim();
        const isValid = eppo && eppo.length >= 8 && mobile && mobile.length >= 10;
        
        if (this.sendOtpBtn) {
            this.sendOtpBtn.disabled = !isValid;
        }
    }
    
    validateOtpForm() {
        const otp = this.otpInput?.value.trim();
        const isValid = otp && otp.length === 6;
        
        if (this.verifyOtpBtn) {
            this.verifyOtpBtn.disabled = !isValid;
        }
    }
    
    startOtpTimer(seconds) {
        this.clearOtpTimer();
        let remaining = seconds;
        
        const updateTimer = () => {
            const minutes = Math.floor(remaining / 60);
            const secs = remaining % 60;
            this.otpCountdown.textContent = `${minutes}:${secs.toString().padStart(2, '0')}`;
            
            if (remaining <= 0) {
                this.clearOtpTimer();
                this.resendOtpBtn.disabled = false;
                this.otpCountdown.textContent = 'Expired';
            } else {
                remaining--;
            }
        };
        
        updateTimer();
        this.otpTimer = setInterval(updateTimer, 1000);
        this.resendOtpBtn.disabled = true;
    }
    
    clearOtpTimer() {
        if (this.otpTimer) {
            clearInterval(this.otpTimer);
            this.otpTimer = null;
        }
    }
    
    clearForms() {
        if (this.eppoInput) this.eppoInput.value = '';
        if (this.mobileInput) this.mobileInput.value = '';
        if (this.otpInput) this.otpInput.value = '';
        this.otpSession = null;
    }
    
    setButtonLoading(button, loading) {
        if (!button) return;
        
        if (loading) {
            button.classList.add('loading');
            button.disabled = true;
        } else {
            button.classList.remove('loading');
            button.disabled = false;
        }
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // Add styles
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: '500',
            zIndex: '10000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease',
            maxWidth: '300px'
        });
        
        // Set background color based on type
        const colors = {
            success: '#059669',
            error: '#dc2626',
            info: '#1e40af',
            warning: '#d97706'
        };
        notification.style.backgroundColor = colors[type] || colors.info;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 5 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 5000);
    }
    
    getAuthHeaders() {
        return this.token ? {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
        } : {
            'Content-Type': 'application/json'
        };
    }
    
    isAuthenticated() {
        return !!(this.token && this.user);
    }
    
    getUser() {
        return this.user;
    }
    
    getToken() {
        return this.token;
    }
}

// Export for use in other scripts
window.AuthSystem = AuthSystem;
