"""
FAISS-based Vector Store for Ultra-Fast Similarity Search
Reduces vector search time from seconds to milliseconds
"""
import os
import json
import time
import threading
import numpy as np
from typing import List, Dict, Any, Optional
from pathlib import Path
import pickle

# Apply compatibility patch before importing sentence-transformers
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    from backend.models.huggingface_compatibility_patch import apply_huggingface_compatibility_patch
    apply_huggingface_compatibility_patch()
except ImportError:
    print("⚠️ Compatibility patch not available, continuing...")

try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    print("FAISS not available. Install with: pip install faiss-cpu")

from sentence_transformers import SentenceTransformer
from backend.config import config

# Global caches
_EMBEDDING_CACHE = {}
_SEARCH_CACHE = {}
_CACHE_LOCK = threading.Lock()

class FAISSVectorStore:
    def __init__(self, persist_directory: str = None):
        """Initialize FAISS vector store with optimized settings"""
        if not FAISS_AVAILABLE:
            raise ImportError("FAISS is required for optimized vector search")

        self.persist_directory = Path(persist_directory or config.VECTOR_DB_PATH)
        self.persist_directory.mkdir(parents=True, exist_ok=True)

        # Initialize embedding model with caching
        print("Loading embedding model...")
        self.embedding_model = SentenceTransformer(config.EMBEDDINGS_MODEL)
        self.embedding_dim = self.embedding_model.get_sentence_embedding_dimension()

        # FAISS index settings
        self.index = None
        self.documents = []
        self.metadata = []

        # File paths
        self.index_file = self.persist_directory / "faiss_index.bin"
        self.documents_file = self.persist_directory / "documents.pkl"
        self.metadata_file = self.persist_directory / "metadata.pkl"
        self.embeddings_file = self.persist_directory / "embeddings.npy"

        # Load existing index if available
        self.load_index()

        print(f"✅ FAISS Vector Store initialized with {len(self.documents)} documents")

    def create_hnsw_index(self, embeddings: np.ndarray) -> faiss.Index:
        """Create optimized HNSW index for fast approximate search"""
        d = embeddings.shape[1]

        # Create HNSW index with optimized parameters
        index = faiss.IndexHNSWFlat(d, config.FAISS_M)
        index.hnsw.efConstruction = config.FAISS_EF_CONSTRUCTION

        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(embeddings)

        print(f"Building HNSW index with {len(embeddings)} vectors...")
        start_time = time.time()
        index.add(embeddings)
        build_time = time.time() - start_time

        print(f"✅ HNSW index built in {build_time:.2f}s")
        return index

    def add_documents(self, documents: List[Dict[str, Any]]):
        """Add documents to the vector store with batch processing"""
        if not documents:
            return

        print(f"Processing {len(documents)} documents...")

        # Extract texts and metadata
        texts = []
        doc_metadata = []

        for doc in documents:
            if isinstance(doc, dict):
                content = doc.get('content', str(doc))
                metadata = {
                    'source': doc.get('source', 'Unknown'),
                    'title': doc.get('title', ''),
                    'page': doc.get('page', 0),
                    'chunk_id': doc.get('chunk_id', len(texts))
                }
            else:
                content = str(doc)
                metadata = {'source': 'Unknown', 'chunk_id': len(texts)}

            texts.append(content)
            doc_metadata.append(metadata)

        # Generate embeddings with caching
        embeddings = self.generate_embeddings_cached(texts)

        # Store documents and metadata
        self.documents.extend(texts)
        self.metadata.extend(doc_metadata)

        # Create or update FAISS index
        if self.index is None:
            self.index = self.create_hnsw_index(embeddings)
        else:
            # Add to existing index
            faiss.normalize_L2(embeddings)
            self.index.add(embeddings)

        # Save to disk
        self.save_index()

        print(f"✅ Added {len(documents)} documents to FAISS index")

    def generate_embeddings_cached(self, texts: List[str]) -> np.ndarray:
        """Generate embeddings with aggressive caching"""
        embeddings = []
        cache_hits = 0

        for text in texts:
            text_hash = hash(text)

            # Check cache first
            if config.PRECOMPUTE_EMBEDDINGS:
                with _CACHE_LOCK:
                    if text_hash in _EMBEDDING_CACHE:
                        embeddings.append(_EMBEDDING_CACHE[text_hash])
                        cache_hits += 1
                        continue

            # Generate new embedding
            embedding = self.embedding_model.encode([text])[0]
            embeddings.append(embedding)

            # Cache the embedding
            if config.PRECOMPUTE_EMBEDDINGS:
                with _CACHE_LOCK:
                    _EMBEDDING_CACHE[text_hash] = embedding

        if cache_hits > 0:
            print(f"✅ Cache hits: {cache_hits}/{len(texts)} embeddings")

        return np.array(embeddings, dtype=np.float32)

    def similarity_search(self, query: str, k: int = None) -> List[Dict[str, Any]]:
        """Ultra-fast similarity search using FAISS HNSW"""
        if self.index is None or len(self.documents) == 0:
            return []

        k = k or config.SIMILARITY_SEARCH_K

        # Check cache first
        cache_key = f"{hash(query)}_{k}"
        if config.ENABLE_VECTOR_CACHE:
            with _CACHE_LOCK:
                if cache_key in _SEARCH_CACHE:
                    cache_entry = _SEARCH_CACHE[cache_key]
                    if time.time() - cache_entry['timestamp'] < config.CACHE_TTL_SECONDS:
                        print("✅ Search results served from cache")
                        return cache_entry['results']

        # Generate query embedding
        start_time = time.time()
        query_embedding = self.embedding_model.encode([query])
        faiss.normalize_L2(query_embedding)

        # Set search parameters for speed vs accuracy trade-off
        self.index.hnsw.efSearch = config.FAISS_EF_SEARCH

        # Perform search
        distances, indices = self.index.search(query_embedding, min(k, len(self.documents)))

        search_time = time.time() - start_time

        # Format results
        results = []
        for i, (distance, idx) in enumerate(zip(distances[0], indices[0])):
            if idx < len(self.documents):  # Valid index
                similarity_score = 1 - distance  # Convert distance to similarity
                results.append({
                    'content': self.documents[idx],
                    'metadata': self.metadata[idx],
                    'score': float(similarity_score),
                    'rank': i + 1
                })

        print(f"🔍 FAISS search completed in {search_time:.3f}s")

        # Cache results
        if config.ENABLE_VECTOR_CACHE:
            with _CACHE_LOCK:
                _SEARCH_CACHE[cache_key] = {
                    'results': results,
                    'timestamp': time.time()
                }

                # Clean old cache entries
                if len(_SEARCH_CACHE) > config.MAX_CACHE_SIZE:
                    oldest_key = min(_SEARCH_CACHE.keys(),
                                   key=lambda k: _SEARCH_CACHE[k]['timestamp'])
                    del _SEARCH_CACHE[oldest_key]

        return results

    def get_relevant_context(self, query: str, max_context_length: int = None) -> str:
        """Get relevant context for query with optimized retrieval"""
        max_length = max_context_length or config.MAX_CONTEXT_LENGTH

        # Get similar documents
        similar_docs = self.similarity_search(query, k=config.SIMILARITY_SEARCH_K)

        if not similar_docs:
            return "No relevant information found."

        # Build context from most relevant documents
        context_parts = []
        current_length = 0

        for doc in similar_docs:
            content = doc['content']
            source = doc['metadata'].get('source', 'Unknown')

            # Add source attribution
            attributed_content = f"[Source: {source}]\n{content}"

            # Check if adding this content would exceed limit
            if current_length + len(attributed_content) > max_length:
                # Try to fit partial content
                remaining_space = max_length - current_length - 50  # Leave some buffer
                if remaining_space > 100:  # Only add if meaningful content can fit
                    partial_content = attributed_content[:remaining_space] + "..."
                    context_parts.append(partial_content)
                break

            context_parts.append(attributed_content)
            current_length += len(attributed_content)

        return "\n\n".join(context_parts)

    def save_index(self):
        """Save FAISS index and metadata to disk"""
        try:
            if self.index is not None:
                faiss.write_index(self.index, str(self.index_file))

            with open(self.documents_file, 'wb') as f:
                pickle.dump(self.documents, f)

            with open(self.metadata_file, 'wb') as f:
                pickle.dump(self.metadata, f)

            # Save embedding cache
            if config.PRECOMPUTE_EMBEDDINGS and _EMBEDDING_CACHE:
                cache_file = self.persist_directory / "embedding_cache.pkl"
                with open(cache_file, 'wb') as f:
                    with _CACHE_LOCK:
                        pickle.dump(_EMBEDDING_CACHE, f)

            print(f"✅ FAISS index saved with {len(self.documents)} documents")

        except Exception as e:
            print(f"Warning: Could not save FAISS index: {e}")

    def load_index(self):
        """Load FAISS index and metadata from disk"""
        try:
            if self.index_file.exists():
                self.index = faiss.read_index(str(self.index_file))
                print(f"✅ FAISS index loaded from {self.index_file}")

            if self.documents_file.exists():
                with open(self.documents_file, 'rb') as f:
                    self.documents = pickle.load(f)

            if self.metadata_file.exists():
                with open(self.metadata_file, 'rb') as f:
                    self.metadata = pickle.load(f)

            # Load embedding cache
            cache_file = self.persist_directory / "embedding_cache.pkl"
            if cache_file.exists() and config.PRECOMPUTE_EMBEDDINGS:
                with open(cache_file, 'rb') as f:
                    cached_embeddings = pickle.load(f)
                    with _CACHE_LOCK:
                        _EMBEDDING_CACHE.update(cached_embeddings)
                print(f"✅ Loaded {len(_EMBEDDING_CACHE)} cached embeddings")

        except Exception as e:
            print(f"Warning: Could not load FAISS index: {e}")
            self.index = None
            self.documents = []
            self.metadata = []

    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the vector store"""
        return {
            'total_documents': len(self.documents),
            'embedding_model': config.EMBEDDINGS_MODEL,
            'embedding_dimension': self.embedding_dim,
            'index_type': 'FAISS HNSW' if self.index else 'None',
            'cached_embeddings': len(_EMBEDDING_CACHE),
            'cached_searches': len(_SEARCH_CACHE),
            'faiss_available': FAISS_AVAILABLE
        }

    def clear_caches(self):
        """Clear all caches"""
        with _CACHE_LOCK:
            _EMBEDDING_CACHE.clear()
            _SEARCH_CACHE.clear()
        print("✅ Vector store caches cleared")

def build_faiss_vector_store():
    """Build FAISS vector store from processed data"""
    # Load processed data
    processed_data_file = config.PROCESSED_DATA_PATH / "all_processed_data.json"

    if not processed_data_file.exists():
        print("Processed data not found. Please run data_processor.py first.")
        return None

    with open(processed_data_file, 'r', encoding='utf-8') as f:
        documents = json.load(f)

    print(f"Loaded {len(documents)} documents")

    # Create FAISS vector store
    vector_store = FAISSVectorStore()

    # Add documents
    vector_store.add_documents(documents)

    # Print stats
    stats = vector_store.get_collection_stats()
    print(f"FAISS vector store stats: {stats}")

    return vector_store
