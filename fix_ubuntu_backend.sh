#!/bin/bash

echo "🔧 Fixing CPMS Backend for Ubuntu Server"
echo "========================================"

# Step 1: Uninstall problematic python-jose version
echo "📦 Uninstalling problematic python-jose..."
pip uninstall python-jose -y

# Step 2: Install compatible version
echo "📦 Installing compatible python-jose version..."
pip install "python-jose[cryptography]==3.3.0"

# Step 3: Verify installation
echo "✅ Verifying installation..."
python3 -c "
try:
    from jose import jwt
    print('✅ python-jose imported successfully')
except ImportError as e:
    print(f'❌ Import failed: {e}')
"

# Step 4: Alternative fix - use PyJWT instead
echo "🔄 Installing alternative PyJWT (more reliable)..."
pip install PyJWT[crypto]==2.8.0

echo ""
echo "🎯 Quick Test:"
echo "Try running: python3 -c 'from jose import jwt; print(\"JWT works!\")'"
echo ""
echo "If jose still fails, we'll switch to PyJWT in the code."
echo "Run this script and then try starting your backend again."
