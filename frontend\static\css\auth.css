/* Authentication Modal Styles */
.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.auth-modal.hidden {
    opacity: 0;
    pointer-events: none;
}

.auth-content {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.auth-modal.hidden .auth-content {
    transform: translateY(-20px);
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h2 {
    color: #1e40af;
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.auth-header p {
    color: #6b7280;
    margin: 0;
    font-size: 0.9rem;
}

/* Form Styles */
.auth-form {
    display: block;
}

.auth-form.hidden {
    display: none;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #374151;
    font-weight: 500;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #1e40af;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.form-group input.error {
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* Button Styles */
.auth-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.auth-btn.primary {
    background: #1e40af;
    color: white;
}

.auth-btn.primary:hover:not(:disabled) {
    background: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

.auth-btn.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.auth-btn.secondary:hover:not(:disabled) {
    background: #e5e7eb;
}

.auth-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* OTP Specific Styles */
.otp-info {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.otp-info p {
    margin: 0.25rem 0;
    color: #0369a1;
    font-size: 0.9rem;
}

.otp-timer {
    font-weight: 600;
}

#otp-code {
    text-align: center;
    font-size: 1.5rem;
    letter-spacing: 0.5rem;
    font-family: 'JetBrains Mono', monospace;
}

/* Auth Footer */
.auth-footer {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.auth-footer p {
    color: #6b7280;
    font-size: 0.85rem;
    margin: 0 0 1rem 0;
}

/* Header User Info */
.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-right: 1rem;
}

.user-info.hidden {
    display: none;
}

.user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.user-name {
    font-weight: 600;
    color: #1e40af;
    font-size: 0.9rem;
}

.user-eppo {
    font-size: 0.75rem;
    color: #6b7280;
    font-family: 'JetBrains Mono', monospace;
}

.login-btn {
    background: #1e40af;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    gap: 0.5rem;
}

.login-btn:hover {
    background: #1d4ed8;
}

/* Quick Actions Panel */
.quick-actions {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-bottom: 1px solid #bae6fd;
    padding: 1rem 0;
}

.quick-actions.hidden {
    display: none;
}

.quick-actions-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.quick-actions h3 {
    color: #0369a1;
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
}

.action-btn {
    background: white;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: #0369a1;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn:hover {
    background: #f0f9ff;
    border-color: #0369a1;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(3, 105, 161, 0.15);
}

.action-btn i {
    font-size: 1rem;
}

/* Auth Status in Input Footer */
.auth-status {
    color: #059669;
    font-weight: 500;
    font-size: 0.8rem;
}

.auth-status.guest {
    color: #d97706;
}

.auth-status.authenticated {
    color: #059669;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-content {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .user-info {
        display: none;
    }
    
    .login-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* Loading States */
.auth-btn.loading {
    position: relative;
    color: transparent;
}

.auth-btn.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Enhanced Chat Styles */
.message-metadata {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #e5e7eb;
    font-size: 0.8rem;
    color: #6b7280;
}

.metadata-items {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.metadata-item {
    background: #f3f4f6;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
}

.metadata-item.query-type {
    background: #dbeafe;
    color: #1e40af;
    font-weight: 500;
}

.metadata-item.cached {
    background: #d1fae5;
    color: #059669;
}

.sources {
    margin-top: 0.5rem;
}

.sources ul {
    margin: 0.25rem 0 0 1rem;
    padding: 0;
}

.sources li {
    margin: 0.1rem 0;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem 0;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6b7280;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.error-message .message-text {
    color: #dc2626;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    padding: 0.75rem;
}

/* Notification styles */
.notification {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    font-size: 0.9rem;
}
