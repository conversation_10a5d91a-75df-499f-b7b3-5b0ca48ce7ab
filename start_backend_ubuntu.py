#!/usr/bin/env python3
"""
Start CPMS Backend on Ubuntu with proper model path handling
"""
import os
import sys
from pathlib import Path

def setup_environment():
    """Setup environment for Ubuntu"""
    print("Setting up Ubuntu environment...")
    
    # Set model path if not already set
    model_path = "/home/<USER>/CPMS_CHATBOT/models/mistral-7b-instruct-v0.2.Q4_K_S"
    
    # Find GGUF file in the directory
    if os.path.exists(model_path):
        gguf_files = [f for f in os.listdir(model_path) if f.endswith('.gguf')]
        if gguf_files:
            full_model_path = os.path.join(model_path, gguf_files[0])
            os.environ['MISTRAL_MODEL_PATH'] = full_model_path
            print(f"Model path set to: {full_model_path}")
        else:
            print("Warning: No GGUF files found in model directory")
    else:
        print("Warning: Model directory not found")
    
    # Disable CUDA if no GPU
    os.environ['CUDA_VISIBLE_DEVICES'] = ''
    
    # Add backend to Python path
    backend_path = str(Path(__file__).parent / "backend")
    if backend_path not in sys.path:
        sys.path.insert(0, backend_path)

def start_server():
    """Start the backend server"""
    try:
        print("Starting CPMS Backend Server...")
        print("=" * 40)
        
        # Import and run the server
        from backend.api.server import main
        main()
        
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error starting server: {e}")
        print("\nTroubleshooting:")
        print("1. Check if all dependencies are installed")
        print("2. Verify model path is correct")
        print("3. Ensure database is initialized")
        sys.exit(1)

if __name__ == "__main__":
    setup_environment()
    start_server()
