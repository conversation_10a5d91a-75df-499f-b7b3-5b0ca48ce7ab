# CPMS Enhanced Chatbot Testing Guide

## Overview
This guide explains how to test the enhanced CPMS chatbot with pensioner authentication and personal query capabilities.

## Prerequisites

### 1. Backend Setup
```bash
cd backend
pip install -r requirements.txt
python scripts/populate_sample_data.py  # Creates test data
python run_backend.py                   # Starts backend on port 8000
```

### 2. Frontend Setup
```bash
cd frontend
python run_frontend.py                  # Starts frontend on port 5000
```

## Test Data

### Sample Pensioners (Pre-populated)
| Name | EPPO Number | Mobile Number | Notes |
|------|-------------|---------------|-------|
| <PERSON><PERSON> | DOT12345678 | ********** | LC expires 2024-12-31 |
| <PERSON><PERSON> | DOT87654321 | ********** | LC expires 2024-08-31 |
| <PERSON><PERSON> | DOT11223344 | ********** | LC expires 2024-03-31 (soon) |

## Testing Methods

### Method 1: Automated Testing Script
```bash
python test_enhanced_chatbot.py
```

This script tests:
- Backend health check
- API endpoints
- General queries (no auth required)
- Authentication flow
- Pensioner-specific queries

### Method 2: Manual Frontend Testing

#### Step 1: Access the Enhanced Interface
1. Open browser and go to: `http://localhost:5000/enhanced_index.html`
2. You should see the enhanced CPMS portal with login capability

#### Step 2: Test Guest Mode
1. Click "Continue as Guest" or just start chatting
2. Try general queries:
   - "What is CPMS?"
   - "How to submit Life Certificate?"
   - "What is the CPMS helpline number?"
3. Try pensioner-specific queries (should prompt to login):
   - "When does my LC expire?"
   - "Show my pension breakup"

#### Step 3: Test Authentication
1. Click the "Login" button in the header
2. Enter test credentials:
   - EPPO Number: `DOT12345678`
   - Mobile Number: `**********`
3. Click "Send OTP"
4. Check backend console logs for the OTP (e.g., "📱 OTP for **********: 123456")
5. Enter the OTP and click "Verify & Login"
6. You should see welcome message and user info in header

#### Step 4: Test Authenticated Queries
Once logged in, try these queries:

**Personal Information Queries:**
- "When does my LC expire?"
- "Show my pension breakup"
- "What is the current DA rate?"
- "When will my commutation be restored?"
- "What is my family pension amount?"
- "What is my FMA amount?"
- "How to change my mobile number?"
- "When was I migrated to Sampann?"

**Quick Action Buttons:**
- Use the quick action buttons that appear after login
- Each button sends a pre-defined query

### Method 3: API Testing with curl/Postman

#### Test General Query (No Auth)
```bash
curl -X POST http://localhost:8000/query \
  -H "Content-Type: application/json" \
  -d '{"message": "What is CPMS?", "include_sources": true}'
```

#### Test Authentication Flow
```bash
# Step 1: Login
curl -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"eppo_number": "DOT12345678", "mobile_number": "**********"}'

# Step 2: Verify OTP (use session_id from step 1 and OTP from backend logs)
curl -X POST http://localhost:8000/auth/verify-otp \
  -H "Content-Type: application/json" \
  -d '{"session_id": 1, "otp": "123456"}'
```

#### Test Authenticated Query
```bash
# Use token from authentication
curl -X POST http://localhost:8000/enhanced-query \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{"message": "When does my LC expire?", "include_sources": true}'
```

## Expected Behaviors

### Guest Mode
- ✅ Can ask general CPMS questions
- ✅ Gets informative responses about CPMS procedures
- ❌ Cannot access personal pension information
- ❌ Pensioner-specific queries return "login required" message

### Authenticated Mode
- ✅ Can ask all general CPMS questions
- ✅ Can access personal pension information
- ✅ Gets personalized responses with actual data
- ✅ Quick action buttons are available
- ✅ User info displayed in header

### Authentication Flow
- ✅ EPPO and mobile number validation
- ✅ OTP sent (logged in backend console)
- ✅ OTP verification with proper error handling
- ✅ JWT token generation and storage
- ✅ Automatic logout after token expiry

## Query Types and Expected Responses

### Personal Queries (Authenticated Only)

| Query Type | Example Query | Expected Response |
|------------|---------------|-------------------|
| LC Expiry | "When does my LC expire?" | Date, days remaining, status |
| Pension Breakup | "Show my pension breakup" | Basic pension, DA, allowances, total |
| DA Rate | "What is current DA rate?" | Current rate %, your DA amount |
| Commutation | "When will commutation restore?" | Restoration date, amount |
| Family Pension | "What is my family pension?" | Normal and enhanced amounts |
| FMA | "What is my FMA amount?" | Fixed medical allowance amount |
| Mobile Change | "How to change mobile number?" | Process steps, required documents |
| Sampann Migration | "When was I migrated?" | Migration date and status |

### General Queries (Available to All)

| Query Type | Example Query | Expected Response |
|------------|---------------|-------------------|
| CPMS Info | "What is CPMS?" | System overview and features |
| Procedures | "How to submit LC?" | Step-by-step process |
| Contact | "CPMS helpline number?" | Contact information |
| Registration | "How to register?" | Registration process |

## Troubleshooting

### Backend Issues
- **Port 8000 in use**: Kill existing process or change port in config
- **Database errors**: Run `python scripts/populate_sample_data.py` again
- **Model loading fails**: Check if model files exist in `models/` directory

### Frontend Issues
- **CORS errors**: Ensure backend is running and CORS is configured
- **Authentication fails**: Check backend logs for OTP codes
- **Styling issues**: Ensure all CSS files are loaded correctly

### Common Problems

1. **OTP not received**: Check backend console logs for the OTP code
2. **Token expired**: Logout and login again
3. **Personal queries not working**: Ensure you're logged in and using correct endpoint
4. **Backend connection failed**: Verify backend is running on port 8000

## Performance Testing

### Load Testing
```bash
# Test concurrent general queries
for i in {1..10}; do
  curl -X POST http://localhost:8000/query \
    -H "Content-Type: application/json" \
    -d '{"message": "What is CPMS?"}' &
done
```

### Response Time Testing
- General queries: Should respond within 2-3 seconds
- Authenticated queries: Should respond within 1-2 seconds (database lookup)
- Authentication: OTP generation should be instant

## Security Testing

### Test Invalid Credentials
- Try invalid EPPO numbers
- Try mismatched mobile numbers
- Try expired OTP codes
- Try invalid JWT tokens

### Test Authorization
- Try accessing authenticated endpoints without token
- Try using expired tokens
- Try accessing other users' data

## Success Criteria

✅ **Authentication System**
- EPPO/mobile validation works
- OTP generation and verification works
- JWT token management works
- Session management works

✅ **Query Processing**
- General queries work for all users
- Personal queries work only for authenticated users
- Query type detection works correctly
- Response formatting is proper

✅ **User Experience**
- Smooth login/logout flow
- Clear error messages
- Responsive design works
- Quick actions work properly

✅ **Security**
- Proper authentication required for personal data
- Tokens expire appropriately
- No data leakage between users
- Input validation works

## Next Steps

After successful testing:
1. Deploy to staging environment
2. Integrate with actual SMS gateway for OTP
3. Connect to production pension database
4. Add more comprehensive logging and monitoring
5. Implement rate limiting and security measures
