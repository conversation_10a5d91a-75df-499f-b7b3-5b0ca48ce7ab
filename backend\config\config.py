"""
Configuration file for CPMS Chatbot
"""
from pathlib import Path

# Base paths - Point to workspace root (two levels up from backend/config/)
BASE_DIR = Path(__file__).parent.parent.parent
DATA_DIR = BASE_DIR / "data"
MODELS_DIR = BASE_DIR / "models"
OUTPUT_DIR = BASE_DIR / "output"

# Data paths
PDF_DATA_PATH = BASE_DIR / "pdf_data"
WEB_SCRAPED_PATH = BASE_DIR / "web_scraped"
PROCESSED_DATA_PATH = DATA_DIR / "processed"
TRAINING_DATA_PATH = DATA_DIR / "training_data.jsonl"

# Model paths
# Auto-detect model path based on OS
import os
if os.name == 'nt':  # Windows
    MISTRAL_MODEL_PATH = r"C:\python programs\Chatbot_Fine_Tuning\models\mistral-7b-instruct-v0.2.Q4_K_S\mistral-7b-instruct-v0.2.Q4_K_S.gguf"
else:  # Linux/Ubuntu
    # Use the actual Ubuntu model path
    MISTRAL_MODEL_PATH = "/home/<USER>/CPMS_CHATBOT/models/mistral-7b-instruct-v0.2.Q4_K_S/mistral-7b-instruct-v0.2.Q4_K_S.gguf"
FINE_TUNED_MODEL_PATH = MODELS_DIR / "cpms_mistral_finetuned"
EMBEDDINGS_MODEL = "all-MiniLM-L6-v2"

# Vector database
VECTOR_DB_PATH = BASE_DIR / "vector_db"
CHUNK_SIZE = 1000
CHUNK_OVERLAP = 200

# Training parameters
LORA_R = 8
LORA_ALPHA = 32
LORA_DROPOUT = 0.05
LEARNING_RATE = 2e-4
BATCH_SIZE = 2
GRADIENT_ACCUMULATION_STEPS = 16
MAX_STEPS = 2000
MAX_LENGTH = 512

# API settings
API_HOST = "0.0.0.0"  # Server binds to all interfaces
API_PORT = 8001
GRADIO_PORT = 7861

# Client connection settings (for frontend to connect to backend)
BACKEND_API_HOST = "*************"  # Frontend connects to backend server
BACKEND_API_PORT = 8001
BACKEND_API_BASE_URL = f"http://{BACKEND_API_HOST}:{BACKEND_API_PORT}"

# Model generation parameters
MAX_NEW_TOKENS = 256  # Reduced for faster responses (was 512)
TEMPERATURE = 0.6
TOP_P = 0.9
TOP_K = 40

# Timeout configurations
API_REQUEST_TIMEOUT = 500  # 2 minutes for API requests
MODEL_GENERATION_TIMEOUT = 500  # 1.5 minutes for model generation
VECTOR_SEARCH_TIMEOUT = 50  # 10 seconds for vector search
FRONTEND_REQUEST_TIMEOUT = 500  # 2.5 minutes for frontend requests

# Performance optimization settings
ENABLE_MODEL_CACHING = True
ENABLE_RESPONSE_CACHING = True
ENABLE_EMBEDDING_CACHING = True
CACHE_TTL_SECONDS = 3600  # 1 hour cache
MAX_CACHE_SIZE = 1000
ENABLE_ASYNC_PROCESSING = True
BATCH_EMBEDDING_SIZE = 32
PRELOAD_MODELS = True

# FAQ Cache Settings - NEW
ENABLE_FAQ_CACHE = True
FAQ_CACHE_SIZE = 100
FAQ_CACHE_TTL = 86400  # 24 hours for FAQ cache
PRECOMPUTE_FAQS = True

# Persistent Answer Cache Settings - NEW
ENABLE_PERSISTENT_CACHE = True
PERSISTENT_CACHE_PATH = BASE_DIR / "vector_db" / "persistent_answer_cache.db"
PERSISTENT_CACHE_NO_EXPIRY = True  # Never expire exact question matches

# Query Relevance Filtering - NEW
ENABLE_QUERY_FILTERING = True

CPMS_KEYWORDS = [
    # Core CPMS terms
    'cpms', 'comprehensive pension management system',
    'sampann', 'sampann pension portal', 'dotpension', 'dot pension',
    'controller of communication accounts', 'cca', 'pda',
    'pension‑disbursing authority', 'pension disbursement',
    'cgca', 'controller general of communication accounts',

    # Pension concepts & documents
    'pension', 'pensioner', 'retirement benefit', 'retirement gratuity',
    'sanction', 'authorization', 'sanction order', 'authorization letter',
    'family pension', 'orphan pension', 'survivors pension',
    'gratuity', 'commutation', 'commutation amount', 'commutation factor',
    'arrears', 'pension arrears', 'pension adjustment',
    'ppo', 'ppo issuance', 'ppo download', 'pension payment order',
    'life certificate', 'annual life certificate submission',
    'life certificate procedure', 'life certificate process', 'life certificate submission',
    'digital life certificate', 'dlc', 'life certificate validity',
    'jeevan pramaan', 'online life certificate', 'e‑life certificate',
    'certificate', 'verification certificate', 'pensioner verification',
    'bank mandate', 'mandate verification', 'bank account details',
    'ifsc code', 'branch code', 'micro atm', 'post office payment',

    # Workflows & processes
    'case registration', 'new case entry', 'document upload',
    'case aging', 'aging report', 'sla breach', 'sla management',
    'verification', 'document verification', 'service records',
    'disbursement', 'batch processing', 'file generation',
    'status', 'track', 'track my pension', 'track application',
    'monitor', 'dashboard', 'pending cases', 'completed cases',
    'reports', 'monthly report', 'quarterly report', 'annual report',
    'escalation', 'escalation matrix', 'audit trail', 'exception report',
    'error log', 'data sync', 'system log', 'transaction log',

    # Portal & access
    'login', 'user login', 'password reset', 'forgot password',
    'multi‑factor authentication', 'mfa', 'otp', 'user registration',
    'roles', 'permissions', 'user roles', 'role‑based access control',
    'administrator', 'super user', 'system admin', 'helpdesk admin',
    'cgca nodal officer', 'nodal officer login',
    'helpline', 'toll‑free', 'contact us', 'email support',
    'support ticket', 'raise issue', 'portal navigation',
    'home', 'about us', 'downloads', 'orders & circulars',
    'faqs', 'user guide', 'user manual', 'sop', 'standard operating procedure',

    # Forms & attachments
    'form 4', 'form 9', 'form 10', 'form 11', 'form 12',
    'declaration form', 'nomination form', 'kyp', 'know your pensioner',
    'medical certificate', 'disability certificate',
    'pdf', 'excel', 'xlsx', 'xml', 'csv', 'jpeg', 'png',
    'api endpoint', 'api documentation', 'rest api', 'swagger',

    # Related programs & schemes
    'vrs 2019', 'voluntary retirement scheme', 'vrs 2020',
    'bsnl pension', 'mtnl pension', 'ssa', 'circle office',
    'corporate office', 'telecom pension', 'doordarshan pension',
    'railway pension', 'army pension (for ex‑servicemen)',

    # Policy & compliance
    'pencom guidelines', 'pfrda', 'epfo', 'pension regulators',
    'government orders', 'goi policy', 'doT circular', 'ministry guidelines',
    'financial year closure', 'audit compliance', 'cag audit',

    # Analytics & BI
    'performance metrics', 'key performance indicator', 'kpi',
    'trend analysis', 'data visualization', 'power bi', 'excel pivot',
    'chart', 'graph', 'pie chart', 'bar chart', 'line chart',

    # Notifications & Alerts
    'system notification', 'email alert', 'sms alert',
    'push notification', 'in‑app notification', 'alert settings',

    # Training & onboarding
    'training module', 'user onboarding', 'e‑learning',
    'webinar', 'workshop', 'training calendar', 'help videos',
]

IRRELEVANT_TOPICS = [
    #  Cooking & Food
    'cook', 'cooking', 'recipe', 'food', 'kitchen', 'meal', 'ingredient',
    'stove', 'oven', 'bake', 'fry', 'boil', 'chop', 'dice', 'season', 'grill',
    'microwave', 'dish', 'spice', 'sauce', 'cuisine', 'restaurant', 'snack',
    'dessert', 'lunch', 'dinner', 'breakfast', 'grocery', 'nutritionist',

    #  Weather & Nature
    'weather', 'climate', 'rain', 'snow', 'temperature', 'storm', 'forecast',
    'humidity', 'thunder', 'wind', 'tsunami', 'cyclone', 'flood', 'lightning',

    #  Sports & Games
    'sports', 'football', 'cricket', 'tennis', 'basketball', 'match', 'tournament',
    'game', 'games', 'player', 'score', 'goal', 'team', 'league', 'coach', 'umpire',
    'badminton', 'golf', 'hockey', 'kabaddi', 'olympics', 'fifa', 'ipl', 'wimbledon',

    #  Entertainment & Media
    'movie', 'movies', 'film', 'cinema', 'actor', 'actress', 'music',
    'entertainment', 'celebrity', 'tv', 'tv show', 'drama', 'song', 'album',
    'netflix', 'amazon prime', 'youtube', 'spotify', 'series', 'bollywood', 'hollywood',

    #  Travel & Tourism
    'travel', 'vacation', 'trip', 'holiday', 'beach', 'resort', 'tourism',
    'hiking', 'camping', 'cruise', 'destination', 'flight', 'airport',
    'visa', 'passport', 'itinerary', 'adventure', 'airfare', 'hotel', 'taxi',

    #  Lifestyle & Fashion
    'shopping', 'fashion', 'beauty', 'makeup', 'clothes', 'style',
    'hairstyle', 'outfit', 'jewelry', 'perfume', 'skincare', 'lipstick',
    'cosmetics', 'boutique', 'heels', 'salon', 'nail polish', 'accessories',

    # Health & Fitness
    'health tips', 'exercise', 'fitness', 'diet', 'nutrition', 'gym',
    'yoga', 'meditation', 'weight loss', 'calories', 'workout', 'trainer',
    'running', 'jogging', 'bodybuilding', 'zumba', 'aerobics', 'keto', 'protein',

    # Technology & Programming
    'technology', 'tech', 'computer', 'laptop', 'smartphone', 'mobile', 'tablet',
    'app', 'software', 'hardware', 'programming', 'coding', 'python', 'java',
    'javascript', 'developer', 'ai', 'artificial intelligence', 'machine learning',
    'chatgpt', 'openai', 'robotics', 'coding bootcamp', 'cloud computing',

    # Science & Academics
    'mathematics', 'math', 'algebra', 'geometry', 'science', 'physics',
    'chemistry', 'biology', 'experiment', 'theory', 'atom', 'equation',
    'neuron', 'engineering', 'calculus', 'statistics',

    #  Hacking & Cybersecurity
    'hack', 'hacking', 'crack', 'exploit', 'breach', 'attack',
    'password crack', 'sql injection', 'vulnerability', 'malware',
    'phishing', 'cyber attack', 'ddos', 'penetration testing', 'keylogger',
    'dark web', 'anonymous', 'trojan', 'backdoor', 'spyware',

    # Stock Market & Crypto
    'crypto', 'bitcoin', 'ethereum', 'stock market', 'stocks', 'shares',
    'trading', 'nifty', 'sensex', 'forex', 'option trading', 'mutual fund',
    'blockchain', 'coin', 'altcoin', 'wallet', 'portfolio', 'day trading',

    # Relationships & Social
    'relationship', 'love', 'dating', 'marriage', 'wedding', 'breakup',
    'crush', 'flirt', 'girlfriend', 'boyfriend', 'valentine', 'romantic',
    'affair', 'matchmaking',

    # Astrology & Spirituality
    'astrology', 'horoscope', 'zodiac', 'tarot', 'numerology', 'palmistry',
    'dream meaning', 'starsign', 'planets', 'sun sign', 'fortune', 'reiki',
    'chakras', 'past life', 'mantra', 'pooja', 'ritual',

    #Vehicles & Transport
    'car', 'bike', 'automobile', 'motorcycle', 'scooter', 'bus',
    'truck', 'vehicle insurance', 'car wash', 'car rental', 'engine',
    'license', 'rc book', 'tyre', 'traffic', 'speed', 'mileage',

    # Real Estate
    'real estate', 'property', 'house rent', 'flat', 'apartment', 'villa',
    'buy home', 'sell house', 'mortgage', 'land', 'broker', 'plot', 'construction',
    'builder', 'rera', 'lease',

    # Psychology & Mental Health
    'mental health', 'anxiety', 'depression', 'therapy', 'counseling',
    'psychologist', 'psychiatrist', 'stress', 'trauma', 'ocd', 'bipolar',

    # Household Chores
    'cleaning', 'laundry', 'vacuum', 'mop', 'dusting', 'washing machine',
    'dishwasher', 'toilet cleaning', 'detergent', 'stain removal', 'sweeping',

    # Jobs & Careers (Non-govt)
    'resume', 'cv', 'interview', 'job opening', 'fresher jobs', 'internship',
    'linkedin', 'job alert', 'walk-in interview', 'corporate job',

    # Misc Fun
    'joke', 'meme', 'funny', 'gif', 'prank', 'comedy', 'cartoon', 'anime',
    'gaming', 'console', 'playstation', 'xbox', 'minecraft', 'fortnite',

    # Events & Celebs
    'birthday', 'anniversary', 'party', 'festival', 'celebrity news',
    'award show', 'red carpet', 'oscars', 'met gala', 'fashion week','dog','cat'
]

# Vector search optimization - MAINTAIN CONTEXT QUALITY
MAX_CONTEXT_LENGTH = 1500  # Keep full context for accuracy
SIMILARITY_SEARCH_K = 4   # Increased for better retrieval
EMBEDDING_BATCH_SIZE = 16
ENABLE_VECTOR_CACHE = True
ENABLE_FAISS_INDEX = True  # NEW: Use FAISS for faster search
FAISS_INDEX_TYPE = "HNSW"  # HNSW for fast approximate search
FAISS_M = 64              # HNSW parameter for accuracy
FAISS_EF_CONSTRUCTION = 200  # Build-time accuracy
FAISS_EF_SEARCH = 50      # Search-time speed vs accuracy

# Response optimization
ENABLE_STREAMING = True
CHUNK_RESPONSE = True
PARALLEL_RETRIEVAL = True
ENABLE_SESSION_CACHE = True  # NEW: Session-level caching
SESSION_CACHE_SIZE = 50

# Aggressive caching without compromising context
AGGRESSIVE_CACHING = True
PRECOMPUTE_EMBEDDINGS = True  # Pre-compute and cache all embeddings
PERSISTENT_CACHE = True       # Save cache to disk
ENABLE_QUERY_NORMALIZATION = True  # Normalize similar queries for better cache hits

# Create directories
for dir_path in [DATA_DIR, MODELS_DIR, OUTPUT_DIR, PROCESSED_DATA_PATH, VECTOR_DB_PATH]:
    dir_path.mkdir(parents=True, exist_ok=True)

# CPMS specific prompts
SYSTEM_PROMPT = """You are a helpful assistant for the Comprehensive Pension Management System (CPMS).
You provide accurate information about pension management, CPMS procedures, forms, and help users with their pension-related queries.
Always be professional, accurate, and helpful. If you're not sure about something, direct users to contact the CCA office or helpline."""

INSTRUCTION_TEMPLATE = """### System:
{system}

### User:
{user}

### Assistant:
{assistant}"""

# Contact information
HELPLINE_NUMBER = "1800-113-5800"
HELPDESK_EMAIL = "<EMAIL>"
TECHNICAL_EMAIL = "<EMAIL>"
ADMIN_EMAIL = "<EMAIL>"

# UI Configuration
UI_TITLE = "CPMS - Comprehensive Pension Management System"
UI_DESCRIPTION = "Your AI-powered assistant for pension management queries and CPMS procedures"
UI_THEME_PRIMARY_COLOR = "#1e40af"
UI_THEME_SECONDARY_COLOR = "#059669"
UI_THEME_ACCENT_COLOR = "#dc2626"

# Web Interface Settings
ENABLE_ANALYTICS = False
ENABLE_SHARING = False
SHOW_API = False
MAX_CHAT_HISTORY = 50
CHAT_HEIGHT = 600

# Basic optimizations (safe settings)
ENABLE_BASIC_CACHE = True
BASIC_CACHE_SIZE = 50
ENABLE_RESPONSE_CACHE = True
RESPONSE_CACHE_TTL = 1800  # 30 minutes
