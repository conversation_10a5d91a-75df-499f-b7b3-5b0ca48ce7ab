/* CPMS Professional Redesigned Frontend Styles */

/* CSS Variables for refined professional theming */
:root {
    /* Refined Professional Color Palette */
    /* Navy Blues - Primary trustworthy colors */
    --navy-50: #f8fafc;
    --navy-100: #f1f5f9;
    --navy-200: #e2e8f0;
    --navy-300: #cbd5e1;
    --navy-400: #94a3b8;
    --navy-500: #64748b;
    --navy-600: #475569;
    --navy-700: #334155;
    --navy-800: #1e293b;
    --navy-900: #0f172a;

    /* Slate Grays - Neutral foundation */
    --slate-50: #f8fafc;
    --slate-100: #f1f5f9;
    --slate-200: #e2e8f0;
    --slate-300: #cbd5e1;
    --slate-400: #94a3b8;
    --slate-500: #64748b;
    --slate-600: #475569;
    --slate-700: #334155;
    --slate-800: #1e293b;
    --slate-900: #0f172a;

    /* Teal Accent - Vibrant call-to-action color */
    --teal-50: #f0fdfa;
    --teal-100: #ccfbf1;
    --teal-200: #99f6e4;
    --teal-300: #5eead4;
    --teal-400: #2dd4bf;
    --teal-500: #14b8a6;
    --teal-600: #0d9488;
    --teal-700: #0f766e;
    --teal-800: #115e59;
    --teal-900: #134e4a;

    /* Semantic Colors */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: var(--teal-500);

    /* Primary System Colors */
    --primary-color: var(--navy-700);
    --primary-light: var(--navy-600);
    --primary-dark: var(--navy-800);
    --primary-darker: var(--navy-900);
    --accent-color: var(--teal-500);
    --accent-light: var(--teal-400);
    --accent-dark: var(--teal-600);

    /* Professional Background System */
    --bg-primary: #ffffff;
    --bg-secondary: var(--slate-50);
    --bg-tertiary: var(--slate-100);
    --bg-card: #ffffff;
    --bg-sidebar: var(--slate-50);
    --bg-chat: var(--slate-25, #fefefe);
    --bg-overlay: rgba(15, 23, 42, 0.6);
    --bg-glass: rgba(255, 255, 255, 0.8);
    --bg-glass-dark: rgba(15, 23, 42, 0.8);

    /* Professional Text Colors */
    --text-primary: var(--slate-900);
    --text-secondary: var(--slate-600);
    --text-muted: var(--slate-500);
    --text-inverse: #ffffff;
    --text-accent: var(--teal-600);
    --text-on-accent: #ffffff;

    /* Professional Border System */
    --border-light: var(--slate-200);
    --border-medium: var(--slate-300);
    --border-dark: var(--slate-400);
    --border-accent: var(--teal-200);
    --border-focus: var(--teal-500);

    /* Card and Surface Colors */
    --surface-primary: #ffffff;
    --surface-secondary: var(--slate-50);
    --surface-elevated: #ffffff;
    --surface-accent: var(--teal-50);

    /* Professional Shadow System - Subtle and refined */
    --shadow-xs: 0 1px 2px 0 rgba(15, 23, 42, 0.04);
    --shadow-sm: 0 1px 3px 0 rgba(15, 23, 42, 0.08), 0 1px 2px 0 rgba(15, 23, 42, 0.04);
    --shadow-md: 0 4px 6px -1px rgba(15, 23, 42, 0.08), 0 2px 4px -1px rgba(15, 23, 42, 0.04);
    --shadow-lg: 0 10px 15px -3px rgba(15, 23, 42, 0.08), 0 4px 6px -2px rgba(15, 23, 42, 0.04);
    --shadow-xl: 0 20px 25px -5px rgba(15, 23, 42, 0.1), 0 10px 10px -5px rgba(15, 23, 42, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(15, 23, 42, 0.15);
    --shadow-inner: inset 0 2px 4px 0 rgba(15, 23, 42, 0.04);
    --shadow-card: 0 1px 3px 0 rgba(15, 23, 42, 0.06), 0 1px 2px 0 rgba(15, 23, 42, 0.04);
    --shadow-elevated: 0 4px 6px -1px rgba(15, 23, 42, 0.06), 0 2px 4px -1px rgba(15, 23, 42, 0.04);
    --shadow-header: 0 1px 3px 0 rgba(15, 23, 42, 0.08);
    --shadow-focus: 0 0 0 3px rgba(20, 184, 166, 0.15);
    --shadow-accent: 0 4px 14px 0 rgba(20, 184, 166, 0.2);

    /* Professional Spacing System - Consistent gutters and margins */
    --spacing-xs: 0.25rem;   /* 4px */
    --spacing-sm: 0.5rem;    /* 8px */
    --spacing-md: 1rem;      /* 16px - Base spacing unit */
    --spacing-lg: 1.5rem;    /* 24px */
    --spacing-xl: 2rem;      /* 32px */
    --spacing-2xl: 3rem;     /* 48px */
    --spacing-3xl: 4rem;     /* 64px */

    /* Content Spacing */
    --content-padding: var(--spacing-lg);
    --card-padding: var(--spacing-xl);
    --section-gap: var(--spacing-2xl);

    /* Professional Border Radius - Gentle rounded corners */
    --radius-xs: 0.125rem;   /* 2px */
    --radius-sm: 0.25rem;    /* 4px */
    --radius-md: 0.5rem;     /* 8px - Primary card radius */
    --radius-lg: 0.75rem;    /* 12px - Chat bubbles */
    --radius-xl: 1rem;       /* 16px */
    --radius-2xl: 1.5rem;    /* 24px */
    --radius-full: 9999px;   /* Pills and avatars */

    /* Professional Typography System */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Source Sans Pro', sans-serif;
    --font-family-mono: 'JetBrains Mono', 'SF Mono', 'Consolas', 'Monaco', monospace;
    --font-family-display: 'Inter', system-ui, sans-serif;

    /* Professional Font Scale - Optimized for readability */
    --text-xs: 0.75rem;      /* 12px */
    --text-sm: 0.875rem;     /* 14px */
    --text-base: 1rem;       /* 16px - Base reading size */
    --text-lg: 1.125rem;     /* 18px */
    --text-xl: 1.25rem;      /* 20px */
    --text-2xl: 1.5rem;      /* 24px */
    --text-3xl: 1.875rem;    /* 30px */
    --text-4xl: 2.25rem;     /* 36px */

    /* Professional Line Heights - Enhanced readability */
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 1.75;

    /* Font Weights */
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Enhanced Transitions with more variety */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-smooth: 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94);

    /* Enhanced Z-index layers */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;

    /* New: Backdrop blur values */
    --blur-sm: 4px;
    --blur-md: 8px;
    --blur-lg: 12px;
    --blur-xl: 16px;
}

/* Professional Dark Theme */
[data-theme="dark"] {
    /* Dark Background System */
    --bg-primary: var(--slate-900);
    --bg-secondary: var(--slate-800);
    --bg-tertiary: var(--slate-700);
    --bg-card: var(--slate-800);
    --bg-sidebar: var(--slate-900);
    --bg-chat: var(--slate-850, #1a202c);
    --bg-glass: rgba(15, 23, 42, 0.8);
    --bg-glass-dark: rgba(255, 255, 255, 0.05);

    /* Dark Text Colors */
    --text-primary: var(--slate-100);
    --text-secondary: var(--slate-300);
    --text-muted: var(--slate-400);
    --text-inverse: var(--slate-900);
    --text-accent: var(--teal-400);

    /* Dark Borders */
    --border-light: var(--slate-700);
    --border-medium: var(--slate-600);
    --border-dark: var(--slate-500);
    --border-accent: var(--teal-700);
    --border-focus: var(--teal-400);

    /* Dark Surfaces */
    --surface-primary: var(--slate-800);
    --surface-secondary: var(--slate-700);
    --surface-elevated: var(--slate-750, #2d3748);
    --surface-accent: var(--teal-900);

    /* Dark Shadows */
    --shadow-card: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 1px 2px 0 rgba(0, 0, 0, 0.1);
    --shadow-elevated: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
    --shadow-header: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    height: 100%;
}

body {
    font-family: var(--font-family-sans);
    font-size: var(--text-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--leading-relaxed);
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
    height: 100%;
    letter-spacing: -0.01em;
}

/* Enhanced Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Modern utility classes */
.glass {
    background: var(--bg-glass);
    backdrop-filter: blur(var(--blur-md));
    border: 1px solid var(--border-glass);
}

.glow {
    box-shadow: var(--shadow-glow);
}

.glow-secondary {
    box-shadow: var(--shadow-glow-secondary);
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hover-lift {
    transition: transform var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-2px);
}

/* Professional Loading screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    color: var(--text-inverse);
}

.loading-content {
    text-align: center;
    animation: fadeInUp 0.6s ease-out;
}

.loading-logo {
    font-size: 4rem;
    margin-bottom: var(--spacing-md);
    animation: bounce 2s infinite;
}

.loading-text {
    font-size: var(--text-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    letter-spacing: 0.1em;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--text-inverse);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

.loading-status {
    font-size: var(--text-sm);
    opacity: 0.8;
    animation: pulse 2s infinite;
}

/* Main app layout */
.app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--bg-secondary);
}

/* Professional Header Design */
.header {
    background: var(--surface-primary);
    color: var(--text-primary);
    padding: var(--spacing-lg) var(--spacing-xl);
    box-shadow: var(--shadow-header);
    position: relative;
    z-index: var(--z-sticky);
    border-bottom: 1px solid var(--border-light);
    backdrop-filter: blur(var(--blur-sm));
}

.header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-color), var(--teal-400), var(--accent-color));
    opacity: 0.8;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
}

.header-left {
    display: flex;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-icon {
    font-size: 2.5rem;
    animation: float 3s ease-in-out infinite;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    transition: all var(--transition-normal);
}

.logo-icon:hover {
    transform: scale(1.1) translateY(-2px);
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4));
}

.logo-text h1 {
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-bold);
    margin: 0;
    letter-spacing: -0.02em;
    color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-text p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
    font-weight: var(--font-weight-medium);
    letter-spacing: 0.01em;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--surface-accent);
    border: 1px solid var(--border-accent);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.connection-status:hover {
    background: var(--teal-100);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: var(--radius-full);
    background-color: var(--warning-color);
    position: relative;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: var(--radius-full);
    background: inherit;
    opacity: 0.3;
    animation: statusPulse 2s infinite;
}

.status-indicator.connected {
    background-color: var(--success-color);
}

.status-indicator.connected::before {
    animation: none;
}

.status-indicator.error {
    background-color: var(--error-color);
}

.status-indicator.error::before {
    animation: statusBlink 1s infinite;
}

.header-btn {
    background: var(--surface-secondary);
    border: 1px solid var(--border-light);
    color: var(--text-secondary);
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    font-size: var(--text-sm);
}

.header-btn:hover {
    background: var(--accent-color);
    color: var(--text-on-accent);
    border-color: var(--accent-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-accent);
}

.header-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.header-btn:focus {
    outline: none;
    box-shadow: var(--shadow-focus);
}

/* Main content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Professional Sidebar - Card-style with narrower width */
.sidebar {
    width: 280px;
    background: var(--bg-sidebar);
    border-right: 1px solid var(--border-light);
    display: flex;
    flex-direction: column;
    transition: all var(--transition-smooth);
    box-shadow: var(--shadow-card);
    position: relative;
    margin: var(--spacing-md);
    margin-right: 0;
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
    overflow: hidden;
}

.sidebar.collapsed {
    transform: translateX(-100%);
}

.sidebar-header {
    padding: var(--card-padding);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--surface-primary);
    position: relative;
}

.sidebar-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--spacing-lg);
    right: var(--spacing-lg);
    height: 1px;
    background: var(--accent-color);
    opacity: 0.3;
}

.sidebar-header h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
    letter-spacing: -0.01em;
}

.sidebar-header h3 i {
    color: var(--accent-color);
    font-size: var(--text-base);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.suggestions-container {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.suggestions-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    color: var(--text-secondary);
    font-size: var(--text-sm);
}

.spinner-small {
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--primary-color);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
}

.suggestion-btn {
    width: 100%;
    text-align: left;
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
    background: var(--surface-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--leading-relaxed);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    box-shadow: var(--shadow-xs);
}

.suggestion-btn:hover {
    background: var(--accent-color);
    color: var(--text-on-accent);
    border-color: var(--accent-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-accent);
}

.suggestion-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.suggestion-btn:focus {
    outline: none;
    box-shadow: var(--shadow-focus);
}

.sidebar-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    background: var(--bg-tertiary);
}

.clear-chat-btn {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--error-color);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.clear-chat-btn:hover {
    background: #dc2626;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.clear-chat-btn:active {
    transform: translateY(0);
}

/* Animations */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -10px, 0);
    }
    70% {
        transform: translate3d(0, -5px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-5px);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Professional Chat Section - Card-style with centered layout */
.chat-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--bg-chat);
    position: relative;
    margin: var(--spacing-md);
    margin-left: 0;
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    box-shadow: var(--shadow-card);
    overflow: hidden;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--content-padding);
    scroll-behavior: smooth;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: var(--radius-full);
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--border-dark);
}

/* Welcome message */
.welcome-message {
    text-align: center;
    padding: var(--spacing-2xl);
    max-width: 600px;
    margin: 0 auto;
    animation: fadeInUp 0.8s ease-out;
}

.welcome-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    animation: float 3s ease-in-out infinite;
}

.welcome-message h2 {
    font-size: var(--text-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-message p {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: var(--leading-relaxed);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.welcome-features {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    transition: all var(--transition-normal);
    min-width: 140px;
    box-shadow: var(--shadow-card);
}

.feature:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-elevated);
    border-color: var(--accent-color);
}

.feature i {
    font-size: var(--text-xl);
    color: var(--accent-color);
    transition: color var(--transition-normal);
}

.feature:hover i {
    color: var(--accent-dark);
}

.feature span {
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    text-align: center;
}

/* Chat messages */
.message {
    display: flex;
    margin-bottom: var(--spacing-lg);
    animation: slideInUp 0.3s ease-out;
}

.message.user {
    justify-content: flex-end;
}

.message.assistant {
    justify-content: flex-start;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-base);
    margin: 0 var(--spacing-md);
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    border: 2px solid var(--border-light);
}

.message.user .message-avatar {
    background: var(--primary-color);
    color: var(--text-on-accent);
    order: 2;
    border-color: var(--primary-color);
}

.message.assistant .message-avatar {
    background: var(--accent-color);
    color: var(--text-on-accent);
    border-color: var(--accent-color);
}

.message-content {
    max-width: 70%;
    position: relative;
}

.message-bubble {
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    position: relative;
    word-wrap: break-word;
    box-shadow: var(--shadow-card);
    transition: all var(--transition-normal);
    border: 1px solid var(--border-light);
    animation: messageSlideIn 0.3s ease-out;
}

.message.user .message-bubble {
    background: var(--accent-color);
    color: var(--text-on-accent);
    border-bottom-right-radius: var(--radius-sm);
    border-color: var(--accent-color);
    margin-left: var(--spacing-xl);
}

.message.assistant .message-bubble {
    background: var(--surface-primary);
    color: var(--text-primary);
    border-bottom-left-radius: var(--radius-sm);
    margin-right: var(--spacing-xl);
}

.message-bubble:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-elevated);
}

.message-time {
    font-size: var(--text-xs);
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
    text-align: right;
}

.message.assistant .message-time {
    text-align: left;
}

.message-sources {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(0, 0, 0, 0.05);
    border-radius: var(--radius-md);
    border-left: 3px solid var(--secondary-color);
}

.message-sources h4 {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.message-sources ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.message-sources li {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    padding-left: var(--spacing-md);
    position: relative;
}

.message-sources li:before {
    content: "•";
    color: var(--secondary-color);
    position: absolute;
    left: 0;
}

/* Typing indicator */
.typing-indicator {
    display: flex;
    align-items: flex-end;
    margin-bottom: var(--spacing-lg);
    animation: slideInLeft 0.3s ease-out;
}

.typing-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    color: var(--text-inverse);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
    margin-right: var(--spacing-md);
}

.typing-bubble {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.typing-dots {
    display: flex;
    gap: var(--spacing-xs);
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: var(--radius-full);
    background-color: var(--text-muted);
    animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typingDots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Professional Input Area - Sticky footer with clear affordances */
.input-area {
    padding: var(--spacing-lg);
    background: var(--surface-primary);
    border-top: 1px solid var(--border-light);
    box-shadow: 0 -2px 8px rgba(15, 23, 42, 0.04);
    position: sticky;
    bottom: 0;
    z-index: var(--z-sticky);
}

.input-container {
    max-width: 1000px;
    margin: 0 auto;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    background: var(--surface-primary);
    border: 2px solid var(--border-medium);
    border-radius: var(--radius-xl);
    padding: var(--spacing-md) var(--spacing-lg);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    position: relative;
}

.input-wrapper:focus-within {
    border-color: var(--border-focus);
    box-shadow: var(--shadow-focus);
    transform: translateY(-1px);
}

#message-input {
    flex: 1;
    border: none;
    outline: none;
    resize: none;
    font-family: var(--font-family-sans);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    color: var(--text-primary);
    background: transparent;
    min-height: 24px;
    max-height: 120px;
    overflow-y: auto;
    padding: 0;
}

#message-input::placeholder {
    color: var(--text-muted);
}

#message-input::-webkit-scrollbar {
    width: 4px;
}

#message-input::-webkit-scrollbar-track {
    background: transparent;
}

#message-input::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: var(--radius-full);
}

.input-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-left: var(--spacing-md);
}

.input-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.input-btn:hover:not(:disabled) {
    background: var(--border-light);
    color: var(--text-primary);
}

.input-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.send-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--accent-color);
    color: var(--text-on-accent);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    font-size: var(--text-sm);
}

.send-btn:hover:not(:disabled) {
    background: var(--accent-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-accent);
}

.send-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    background: var(--slate-300);
}

.send-btn:focus {
    outline: none;
    box-shadow: var(--shadow-focus);
}

/* Enhanced Chat Input Container - Modern Website Chat Style */
.chat-input-container {
    padding: var(--spacing-lg);
    background: var(--surface-primary);
    border-top: 1px solid var(--border-light);
    box-shadow: 0 -4px 16px rgba(15, 23, 42, 0.08);
    position: sticky;
    bottom: 0;
    z-index: var(--z-sticky);
    backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.95);
}

.chat-input-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: flex-end;
    background: var(--surface-primary);
    border: 2px solid var(--border-medium);
    border-radius: var(--radius-xl);
    padding: var(--spacing-md);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
    position: relative;
    min-height: 56px;
}

.chat-input-wrapper:focus-within {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow-lg);
    transform: translateY(-2px);
}

#chat-input {
    flex: 1;
    border: none;
    outline: none;
    resize: none;
    font-family: var(--font-family-sans);
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    color: var(--text-primary);
    background: transparent;
    min-height: 24px;
    max-height: 150px;
    overflow-y: auto;
    padding: var(--spacing-sm) 0;
    margin-right: var(--spacing-md);
}

#chat-input::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

#chat-input::-webkit-scrollbar {
    width: 4px;
}

#chat-input::-webkit-scrollbar-track {
    background: transparent;
}

#chat-input::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: var(--radius-full);
}

#chat-input::-webkit-scrollbar-thumb:hover {
    background: var(--border-dark);
}

.input-footer {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-sm);
    padding: 0 var(--spacing-sm);
}

.input-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    font-size: var(--text-xs);
    color: var(--text-muted);
}

#char-count {
    font-family: var(--font-family-mono);
    font-weight: 500;
}

.auth-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    font-weight: 500;
}

.auth-status::before {
    content: "👤";
    font-size: var(--text-xs);
}

.char-counter {
    font-family: var(--font-family-mono);
}

.input-hint {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.input-hint kbd {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    padding: 2px 6px;
    font-family: var(--font-family-mono);
    font-size: var(--text-xs);
    color: var(--text-secondary);
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    backdrop-filter: blur(4px);
    animation: fadeIn 0.2s ease-out;
}

.modal-content {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl), 0 0 40px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--border-light);
    max-width: 520px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: bounceIn 0.4s ease-out;
    backdrop-filter: blur(var(--blur-lg));
    position: relative;
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    opacity: 0.6;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-primary) 100%);
}

.modal-header h3 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.modal-header h3 i {
    color: var(--primary-color);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    max-height: calc(80vh - 120px);
}

.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

.modal-body::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: var(--radius-full);
}

/* Settings modal */
.setting-group {
    margin-bottom: var(--spacing-lg);
}

.setting-group label {
    display: block;
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.setting-group select {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--text-base);
    transition: all var(--transition-fast);
}

.setting-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.checkbox-label {
    display: flex !important;
    align-items: center;
    gap: var(--spacing-md);
    cursor: pointer;
    font-weight: 400 !important;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-medium);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark:after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-inverse);
    font-size: var(--text-sm);
    font-weight: 600;
}

/* System info modal */
.info-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    color: var(--text-secondary);
    font-size: var(--text-sm);
}

.system-info {
    display: grid;
    gap: var(--spacing-md);
}

.info-card {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.info-card h4 {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.info-card h4 i {
    color: var(--primary-color);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-light);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.info-value {
    font-size: var(--text-sm);
    color: var(--text-primary);
    font-family: var(--font-family-mono);
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.healthy {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-badge.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

/* Responsive design */
@media (max-width: 1024px) {
    .sidebar {
        width: 280px;
    }

    .header-content {
        padding: 0 var(--spacing-md);
    }

    .logo-text h1 {
        font-size: var(--text-xl);
    }

    .welcome-features {
        gap: var(--spacing-md);
    }

    .feature {
        min-width: 100px;
        padding: var(--spacing-md);
    }
}

/* Mobile-first responsive design with hamburger menu */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
        margin: 0;
    }

    .sidebar {
        width: 100%;
        height: auto;
        max-height: 200px;
        border-right: none;
        border-bottom: 1px solid var(--border-light);
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        margin: var(--spacing-sm);
        margin-bottom: 0;
        position: relative;
        transform: translateY(0);
        transition: transform var(--transition-normal);
    }

    .sidebar.collapsed {
        transform: translateY(-100%);
    }

    .sidebar-toggle {
        transform: rotate(90deg);
    }

    .sidebar-toggle.collapsed {
        transform: rotate(-90deg);
    }

    .suggestions-container {
        max-height: 120px;
        padding: var(--spacing-md);
    }

    .chat-section {
        border-radius: 0 0 var(--radius-lg) var(--radius-lg);
        margin: 0 var(--spacing-sm) var(--spacing-sm);
    }

    .header-content {
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }

    .header-right {
        order: 3;
        width: 100%;
        justify-content: center;
    }

    .message-content {
        max-width: 85%;
    }

    .message-bubble {
        margin-left: var(--spacing-md);
        margin-right: var(--spacing-md);
    }

    .welcome-features {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .input-footer {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .modal-content {
        width: 95%;
        margin: var(--spacing-md);
        border-radius: var(--radius-lg);
    }

    /* Touch-friendly button sizes */
    .header-btn,
    .send-btn {
        min-width: 44px;
        min-height: 44px;
    }

    .suggestion-btn {
        padding: var(--spacing-lg);
        font-size: var(--text-base);
    }
}

@media (max-width: 480px) {
    .header {
        padding: var(--spacing-md);
    }

    .logo-icon {
        font-size: 2rem;
    }

    .logo-text h1 {
        font-size: var(--text-lg);
    }

    .logo-text p {
        font-size: var(--text-xs);
    }

    .chat-messages {
        padding: var(--spacing-md);
    }

    .input-area {
        padding: var(--spacing-md);
    }

    .message-content {
        max-width: 90%;
    }

    .welcome-message {
        padding: var(--spacing-lg);
    }

    .welcome-icon {
        font-size: 3rem;
    }

    .welcome-message h2 {
        font-size: var(--text-2xl);
    }

    .welcome-message p {
        font-size: var(--text-base);
    }

    .connection-status {
        display: none;
    }
}

/* Enhanced Animation keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes avatarShimmer {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--primary-color);
    }
    50% {
        box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes statusPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.6;
    }
}

@keyframes statusBlink {
    0%, 50% {
        opacity: 0.3;
    }
    51%, 100% {
        opacity: 0.8;
    }
}

/* Print styles */
@media print {
    .header,
    .sidebar,
    .input-area,
    .modal {
        display: none !important;
    }

    .chat-section {
        height: auto !important;
    }

    .chat-messages {
        overflow: visible !important;
        height: auto !important;
    }

    .message-bubble {
        box-shadow: none !important;
        border: 1px solid var(--border-light) !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --border-light: #000000;
        --border-medium: #000000;
        --border-dark: #000000;
        --text-muted: #000000;
    }

    [data-theme="dark"] {
        --border-light: #ffffff;
        --border-medium: #ffffff;
        --border-dark: #ffffff;
        --text-muted: #ffffff;
    }
}

/* Enhanced focus styles for better accessibility */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(30, 64, 175, 0.2);
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-full);
    border: 1px solid var(--border-light);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-light), var(--secondary-light));
}

/* Selection styles */
::selection {
    background: var(--primary-color);
    color: var(--text-inverse);
}

::-moz-selection {
    background: var(--primary-color);
    color: var(--text-inverse);
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .animate-pulse,
    .animate-bounce,
    .animate-float {
        animation: none !important;
    }
}

/* Modern UI Enhancements */

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 56px;
    height: 56px;
    border-radius: var(--radius-full);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: var(--text-inverse);
    border: none;
    box-shadow: var(--shadow-xl), var(--shadow-glow);
    cursor: pointer;
    transition: all var(--transition-bounce);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    z-index: var(--z-fixed);
    backdrop-filter: blur(var(--blur-md));
}

.fab:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
}

.fab:active {
    transform: scale(1.05) translateY(-1px);
}

/* Toast notifications */
.toast {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(var(--blur-md));
    z-index: var(--z-toast);
    animation: slideInFromRight 0.3s ease-out;
    max-width: 400px;
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

.toast.info {
    border-left: 4px solid var(--info-color);
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900);
    color: var(--text-inverse);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: var(--z-tooltip);
    margin-bottom: var(--spacing-xs);
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--gray-900);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Progress bar */
.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
    overflow: hidden;
    position: relative;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-full);
    transition: width var(--transition-smooth);
    width: var(--progress, 0%);
}

/* Badge */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge.primary {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.badge.secondary {
    background: var(--secondary-color);
    color: var(--text-inverse);
}

.badge.success {
    background: var(--success-color);
    color: var(--text-inverse);
}

.badge.warning {
    background: var(--warning-color);
    color: var(--text-inverse);
}

.badge.error {
    background: var(--error-color);
    color: var(--text-inverse);
}

/* Enhanced Send Button - Override existing styles */
.send-btn {
    width: 48px !important;
    height: 48px !important;
    border: none !important;
    background: linear-gradient(135deg, var(--accent-color) 0%, #2563eb 100%) !important;
    color: var(--text-on-accent) !important;
    border-radius: var(--radius-lg) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all var(--transition-normal) !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;
    font-size: var(--text-base) !important;
    position: relative !important;
    overflow: hidden !important;
    flex-shrink: 0 !important;
}

.send-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.send-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4) !important;
}

.send-btn:hover:not(:disabled)::before {
    left: 100%;
}

.send-btn:active {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;
}

.send-btn:disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    transform: none !important;
    background: var(--slate-300) !important;
    box-shadow: none !important;
}

.send-btn:focus {
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2), 0 2px 8px rgba(59, 130, 246, 0.3) !important;
}

/* Enhanced Chat Container */
.chat-container {
    background: var(--surface-primary) !important;
    border: 1px solid var(--border-light) !important;
    box-shadow: 0 8px 32px rgba(15, 23, 42, 0.12) !important;
    border-radius: var(--radius-xl) !important;
}

.chat-messages {
    background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%) !important;
}

/* Force input wrapper focus styles */
.chat-input-wrapper:focus-within {
    border-color: var(--accent-color) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 20px rgba(15, 23, 42, 0.15) !important;
    transform: translateY(-2px) !important;
}

#chat-input:focus {
    outline: none !important;
}