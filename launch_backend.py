#!/usr/bin/env python3
"""
Backend launcher for CPMS Chatbot
"""
import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Launch the backend scripts"""
    print("🏛️ CPMS Backend Launcher")
    print("=" * 40)
    
    try:
        # Import and run the optimized web launcher
        from backend.scripts.launch_optimized_web import main as launch_optimized
        
        print("🚀 Starting optimized backend launcher...")
        launch_optimized()
        
    except KeyboardInterrupt:
        print("\n👋 Backend stopped by user")
    except Exception as e:
        print(f"❌ Error launching backend: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure all dependencies are installed: pip install -r backend/requirements.txt")
        print("2. Check that model files are accessible")
        print("3. Verify the backend directory structure is correct")
        sys.exit(1)

if __name__ == "__main__":
    main()
