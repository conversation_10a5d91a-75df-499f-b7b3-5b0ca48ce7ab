"""
Web interface for CPMS chatbot using Gradio
"""
import gradio as gr
import time
from typing import List, Tuple

# Apply compatibility patch before importing RAG systems
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    from backend.models.huggingface_compatibility_patch import apply_huggingface_compatibility_patch
    apply_huggingface_compatibility_patch()
except ImportError:
    print("⚠️ Compatibility patch not available, continuing without it...")

# Ultra-optimized system is the primary choice
try:
    from backend.core.ultra_optimized_rag_system import UltraOptimizedRAGSystem
    ULTRA_OPTIMIZED_AVAILABLE = True
    print("✅ Ultra-optimized system available")
except ImportError:
    UltraOptimizedRAGSystem = None
    ULTRA_OPTIMIZED_AVAILABLE = False
    print("⚠️ Ultra-optimized system not available")

# Legacy fallback systems (optional)
CPMSRAGSystem = None
OptimizedCPMSRAGSystem = None
from backend.config import config

class CPMSWebInterface:
    def __init__(self, model_type: str = "gguf", use_optimized: bool = True):
        """Initialize the web interface"""
        self.model_type = model_type
        self.use_optimized = use_optimized
        self.rag_system = None
        self.chat_history = []
        self.initialize_system()

    def initialize_system(self):
        """Initialize the RAG system"""
        try:
            if self.use_optimized:
                print(f"Initializing ULTRA-OPTIMIZED CPMS chatbot with {self.model_type} model...")
                if ULTRA_OPTIMIZED_AVAILABLE:
                    self.rag_system = UltraOptimizedRAGSystem(model_type=self.model_type)
                    print("✅ Ultra-Optimized system initialized successfully!")
                else:
                    raise ImportError("Ultra-optimized system not available. Please ensure all required files are present.")
            else:
                print("⚠️ Standard mode not available in this ultra-optimized version.")
                print("Switching to ultra-optimized mode...")
                if ULTRA_OPTIMIZED_AVAILABLE:
                    self.rag_system = UltraOptimizedRAGSystem(model_type=self.model_type)
                    self.use_optimized = True  # Force optimized mode
                    print("✅ Ultra-Optimized system initialized successfully!")
                else:
                    raise ImportError("No RAG system available")
        except Exception as e:
            print(f"Failed to initialize with {self.model_type} model: {e}")
            if self.model_type == "gguf":
                print("Falling back to Hugging Face model...")
                try:
                    print("Trying HuggingFace model with ultra-optimized system...")
                    if ULTRA_OPTIMIZED_AVAILABLE:
                        self.rag_system = UltraOptimizedRAGSystem(model_type="huggingface")
                        self.use_optimized = True  # Ensure optimized mode
                    else:
                        raise ImportError("Ultra-optimized system not available")
                    self.model_type = "huggingface"
                    print("✅ Fallback successful!")
                except Exception as e2:
                    print(f"Fallback also failed: {e2}")
                    raise e2

    def chat_response(self, message: str, history: List[List[str]]) -> Tuple[str, List[List[str]]]:
        """Process chat message and return response"""
        if not self.rag_system:
            return "System not initialized. Please try again.", history

        if not message.strip():
            return "Please enter a question.", history

        try:
            # Get response from RAG system
            result = self.rag_system.query(message, include_sources=True)
            response = result['response']

            # Add source information if available
            if result.get('sources'):
                response += "\n\n📚 **Sources:**"
                for i, source in enumerate(result['sources'][:2], 1):
                    response += f"\n{i}. {source['source']} (Relevance: {source['relevance_score']})"

            # Add processing time and optimization status with cache info
            if result.get('cached', False):
                cache_type = result.get('cache_type', 'Cache')
                optimization_status = f"⚡ {cache_type} Hit" if self.use_optimized else "⚙️ Standard"
            else:
                optimization_status = "🚀 Ultra-Optimized" if self.use_optimized else "⚙️ Standard"

            response += f"\n\n⏱️ *Response time: {result['processing_time']}s* | {optimization_status}"

            # Update history
            history.append([message, response])

            return "", history

        except Exception as e:
            error_response = f"I apologize, but I encountered an error: {str(e)}\n\nPlease try again or contact support at {config.HELPLINE_NUMBER}"
            history.append([message, error_response])
            return "", history

    def get_suggested_questions(self) -> List[str]:
        """Get suggested questions"""
        if self.rag_system:
            return self.rag_system.suggest_questions(10)
        else:
            return [
                "What is CPMS?",
                "How can I login to CPMS portal?",
                "When is pension credited?",
                "What is the helpline number?",
                "How to lodge a grievance?"
            ]

    def handle_suggestion_click(self, suggestion: str, history: List[List[str]]) -> Tuple[str, List[List[str]]]:
        """Handle clicking on a suggested question"""
        return self.chat_response(suggestion, history)

    def clear_chat(self) -> List[List[str]]:
        """Clear chat history"""
        return []

    def get_system_info(self) -> str:
        """Get system information"""
        if not self.rag_system:
            return "System not initialized"

        try:
            stats = self.rag_system.get_system_stats()
            health = self.rag_system.health_check()

            info = f"""
## System Information

**Model Type:** {self.model_type}
**Status:** {health['status']}
**Vector Store Documents:** {stats['vector_store'].get('total_documents', 'Unknown')}
**Embedding Model:** {stats['vector_store'].get('embedding_model', 'Unknown')}

## Contact Information
- **Helpline:** {config.HELPLINE_NUMBER}
- **Email:** {config.HELPDESK_EMAIL}
- **Technical Support:** {config.TECHNICAL_EMAIL}

## Health Check
"""
            if health['issues']:
                info += "**Issues Found:**\n"
                for issue in health['issues']:
                    info += f"- {issue}\n"
            else:
                info += "✅ All systems operational"

            return info

        except Exception as e:
            return f"Error getting system info: {str(e)}"

    def get_enhanced_system_info(self) -> str:
        """Get enhanced system information with better formatting"""
        if not self.rag_system:
            return """
            <div class="status-indicator status-error">
                <span>❌</span>
                <span>System not initialized</span>
            </div>
            """

        try:
            stats = self.rag_system.get_system_stats()
            health = self.rag_system.health_check()

            # Determine status color and icon
            status_class = "status-success" if health['status'] == 'healthy' else "status-error"
            status_icon = "✅" if health['status'] == 'healthy' else "⚠️"

            info_html = f"""
            <div style="display: grid; gap: 1.5rem;">
                <div class="status-indicator {status_class}">
                    <span>{status_icon}</span>
                    <span>System Status: {health['status'].title()}</span>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div style="padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 4px solid var(--primary-color);">
                        <h4 style="margin: 0 0 0.5rem 0; color: var(--primary-color);">🤖 Model Information</h4>
                        <p style="margin: 0; font-weight: 600;">{self.model_type.upper()}</p>
                        <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">Active model type</p>
                    </div>

                    <div style="padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 4px solid var(--secondary-color);">
                        <h4 style="margin: 0 0 0.5rem 0; color: var(--secondary-color);">📚 Documents</h4>
                        <p style="margin: 0; font-weight: 600;">{stats['vector_store'].get('total_documents', 'Unknown')}</p>
                        <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">Indexed documents</p>
                    </div>

                    <div style="padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 4px solid #f59e0b;">
                        <h4 style="margin: 0 0 0.5rem 0; color: #f59e0b;">🔍 Embedding Model</h4>
                        <p style="margin: 0; font-weight: 600; font-size: 0.85rem;">{stats['vector_store'].get('embedding_model', 'Unknown')}</p>
                        <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">Vector embeddings</p>
                    </div>
                </div>

                <div style="padding: 1.5rem; background: var(--background-secondary); border-radius: var(--radius-md); border: 1px solid var(--border-color);">
                    <h4 style="margin: 0 0 1rem 0; color: var(--text-primary);">📞 Support Contacts</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                        <div>
                            <strong style="color: var(--primary-color);">Helpline:</strong>
                            <p style="margin: 0.25rem 0 0 0;">{config.HELPLINE_NUMBER}</p>
                        </div>
                        <div>
                            <strong style="color: var(--primary-color);">Email:</strong>
                            <p style="margin: 0.25rem 0 0 0;">{config.HELPDESK_EMAIL}</p>
                        </div>
                        <div>
                            <strong style="color: var(--primary-color);">Technical:</strong>
                            <p style="margin: 0.25rem 0 0 0;">{config.TECHNICAL_EMAIL}</p>
                        </div>
                    </div>
                </div>
            """

            if health['issues']:
                issues_html = """
                <div style="padding: 1rem; background: rgba(220, 38, 38, 0.1); border-radius: var(--radius-md); border-left: 4px solid var(--accent-color);">
                    <h4 style="margin: 0 0 0.5rem 0; color: var(--accent-color);">⚠️ Issues Found</h4>
                    <ul style="margin: 0; padding-left: 1.5rem; color: var(--text-secondary);">
                """
                for issue in health['issues']:
                    issues_html += f"<li>{issue}</li>"
                issues_html += "</ul></div>"
                info_html += issues_html
            else:
                info_html += """
                <div style="padding: 1rem; background: rgba(5, 150, 105, 0.1); border-radius: var(--radius-md); border-left: 4px solid var(--secondary-color);">
                    <h4 style="margin: 0 0 0.5rem 0; color: var(--secondary-color);">✅ Health Check</h4>
                    <p style="margin: 0; color: var(--text-secondary);">All systems operational and functioning normally.</p>
                </div>
                """

            info_html += "</div>"
            return info_html

        except Exception as e:
            return f"""
            <div class="status-indicator status-error">
                <span>❌</span>
                <span>Error getting system info: {str(e)}</span>
            </div>
            """

    def run_comprehensive_health_check(self) -> str:
        """Run a comprehensive health check and return detailed results"""
        if not self.rag_system:
            return """
            <div class="status-indicator status-error">
                <span>❌</span>
                <span>System not initialized - cannot run health check</span>
            </div>
            """

        try:
            # Run health check
            health = self.rag_system.health_check()

            # Get additional stats
            stats = self.rag_system.get_system_stats()

            # Test a simple query to check response time
            import time
            start_time = time.time()
            try:
                _ = self.rag_system.query("What is CPMS?", include_sources=False)
                response_time = round(time.time() - start_time, 2)
                query_test_passed = True
            except Exception:
                response_time = "Failed"
                query_test_passed = False

            # Determine overall status
            overall_status = "healthy" if health['status'] == 'healthy' and query_test_passed else "issues"
            status_class = "status-success" if overall_status == "healthy" else "status-error"
            status_icon = "✅" if overall_status == "healthy" else "⚠️"

            health_html = f"""
            <div style="display: grid; gap: 1.5rem;">
                <div class="status-indicator {status_class}">
                    <span>{status_icon}</span>
                    <span>Comprehensive Health Check: {overall_status.title()}</span>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div style="padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 4px solid {'var(--secondary-color)' if query_test_passed else 'var(--accent-color)'};">
                        <h4 style="margin: 0 0 0.5rem 0; color: {'var(--secondary-color)' if query_test_passed else 'var(--accent-color)'};">🔍 Query Test</h4>
                        <p style="margin: 0; font-weight: 600;">{'Passed' if query_test_passed else 'Failed'}</p>
                        <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">Response time: {response_time}s</p>
                    </div>

                    <div style="padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 4px solid var(--primary-color);">
                        <h4 style="margin: 0 0 0.5rem 0; color: var(--primary-color);">🗄️ Vector Store</h4>
                        <p style="margin: 0; font-weight: 600;">{'Healthy' if 'vector_store' in stats else 'Issues'}</p>
                        <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">Document retrieval system</p>
                    </div>

                    <div style="padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 4px solid #f59e0b;">
                        <h4 style="margin: 0 0 0.5rem 0; color: #f59e0b;">🤖 Model Loading</h4>
                        <p style="margin: 0; font-weight: 600;">{'Healthy' if health['status'] == 'healthy' else 'Issues'}</p>
                        <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">Language model status</p>
                    </div>
                </div>
            """

            if health['issues'] or not query_test_passed:
                issues_html = """
                <div style="padding: 1rem; background: rgba(220, 38, 38, 0.1); border-radius: var(--radius-md); border-left: 4px solid var(--accent-color);">
                    <h4 style="margin: 0 0 0.5rem 0; color: var(--accent-color);">⚠️ Issues Detected</h4>
                    <ul style="margin: 0; padding-left: 1.5rem; color: var(--text-secondary);">
                """
                if health['issues']:
                    for issue in health['issues']:
                        issues_html += f"<li>{issue}</li>"
                if not query_test_passed:
                    issues_html += "<li>Query processing test failed - model may not be responding correctly</li>"
                issues_html += "</ul></div>"
                health_html += issues_html
            else:
                health_html += """
                <div style="padding: 1rem; background: rgba(5, 150, 105, 0.1); border-radius: var(--radius-md); border-left: 4px solid var(--secondary-color);">
                    <h4 style="margin: 0 0 0.5rem 0; color: var(--secondary-color);">✅ All Systems Operational</h4>
                    <p style="margin: 0; color: var(--text-secondary);">All components are functioning normally. The system is ready to handle user queries.</p>
                </div>
                """

            health_html += "</div>"
            return health_html

        except Exception as e:
            return f"""
            <div class="status-indicator status-error">
                <span>❌</span>
                <span>Health check failed: {str(e)}</span>
            </div>
            """

    def create_interface(self):
        """Create the Gradio interface"""
        # Enhanced Custom CSS for Professional CPMS Interface
        css = """
        /* Import Google Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap');

        /* Root Variables for Consistent Theming */
        :root {
            --primary-color: #1e40af;
            --primary-dark: #1e3a8a;
            --primary-light: #3b82f6;
            --secondary-color: #059669;
            --accent-color: #dc2626;
            --background-primary: #ffffff;
            --background-secondary: #f8fafc;
            --background-tertiary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --border-light: #f1f5f9;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        /* Global Styles */
        * {
            box-sizing: border-box;
        }

        /* Main Container Styling */
        .gradio-container {
            max-width: 1400px !important;
            margin: 0 auto !important;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
            min-height: 100vh !important;
        }

        /* Header Styling */
        .gradio-container > div:first-child {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
            color: white !important;
            padding: 2rem 1.5rem !important;
            margin: -1rem -1rem 2rem -1rem !important;
            border-radius: 0 0 var(--radius-xl) var(--radius-xl) !important;
            box-shadow: var(--shadow-lg) !important;
            position: relative !important;
        }

        .gradio-container > div:first-child::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>') !important;
            opacity: 0.3 !important;
            pointer-events: none !important;
        }

        .gradio-container > div:first-child h1 {
            font-size: 2.5rem !important;
            font-weight: 700 !important;
            margin-bottom: 0.5rem !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
            position: relative !important;
            z-index: 1 !important;
        }

        .gradio-container > div:first-child p {
            font-size: 1.1rem !important;
            opacity: 0.9 !important;
            line-height: 1.6 !important;
            position: relative !important;
            z-index: 1 !important;
        }

        /* Tab Styling */
        .tab-nav {
            background: var(--background-primary) !important;
            border-radius: var(--radius-lg) !important;
            padding: 0.5rem !important;
            box-shadow: var(--shadow-md) !important;
            margin-bottom: 1.5rem !important;
            border: 1px solid var(--border-color) !important;
        }

        .tab-nav button {
            background: transparent !important;
            border: none !important;
            padding: 0.75rem 1.5rem !important;
            border-radius: var(--radius-md) !important;
            font-weight: 500 !important;
            color: var(--text-secondary) !important;
            transition: all 0.2s ease !important;
            font-size: 0.95rem !important;
        }

        .tab-nav button:hover {
            background: var(--background-tertiary) !important;
            color: var(--text-primary) !important;
        }

        .tab-nav button.selected {
            background: var(--primary-color) !important;
            color: white !important;
            box-shadow: var(--shadow-sm) !important;
        }

        /* Chat Interface Styling */
        .chatbot {
            background: var(--background-primary) !important;
            border-radius: var(--radius-xl) !important;
            border: 1px solid var(--border-color) !important;
            box-shadow: var(--shadow-lg) !important;
            overflow: hidden !important;
        }

        .chatbot .message {
            padding: 1rem 1.5rem !important;
            margin: 0.5rem !important;
            border-radius: var(--radius-lg) !important;
            max-width: 85% !important;
            word-wrap: break-word !important;
            line-height: 1.6 !important;
            font-size: 0.95rem !important;
        }

        .chatbot .message.user {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%) !important;
            color: white !important;
            margin-left: auto !important;
            margin-right: 0.5rem !important;
            box-shadow: var(--shadow-md) !important;
        }

        .chatbot .message.bot {
            background: var(--background-secondary) !important;
            color: var(--text-primary) !important;
            border: 1px solid var(--border-light) !important;
            margin-right: auto !important;
            margin-left: 0.5rem !important;
            position: relative !important;
        }

        .chatbot .message.bot::before {
            content: '🤖';
            position: absolute;
            left: -2.5rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5rem;
            background: var(--secondary-color);
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-sm);
        }

        /* Input Area Styling */
        .input-container {
            background: var(--background-primary) !important;
            border-radius: var(--radius-xl) !important;
            padding: 1rem !important;
            border: 1px solid var(--border-color) !important;
            box-shadow: var(--shadow-md) !important;
            margin-top: 1rem !important;
        }

        .input-container input[type="text"],
        .input-container textarea {
            border: 2px solid var(--border-color) !important;
            border-radius: var(--radius-lg) !important;
            padding: 0.875rem 1rem !important;
            font-size: 1rem !important;
            font-family: inherit !important;
            background: var(--background-secondary) !important;
            transition: all 0.2s ease !important;
            color: var(--text-primary) !important;
        }

        .input-container input[type="text"]:focus,
        .input-container textarea:focus {
            outline: none !important;
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
            background: var(--background-primary) !important;
        }

        /* Button Styling */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
            color: white !important;
            border: none !important;
            padding: 0.875rem 1.5rem !important;
            border-radius: var(--radius-lg) !important;
            font-weight: 600 !important;
            font-size: 0.95rem !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            box-shadow: var(--shadow-md) !important;
        }

        .btn-primary:hover {
            transform: translateY(-1px) !important;
            box-shadow: var(--shadow-lg) !important;
            background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%) !important;
        }

        .btn-secondary {
            background: var(--background-secondary) !important;
            color: var(--text-primary) !important;
            border: 2px solid var(--border-color) !important;
            padding: 0.875rem 1.5rem !important;
            border-radius: var(--radius-lg) !important;
            font-weight: 500 !important;
            font-size: 0.95rem !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
        }

        .btn-secondary:hover {
            background: var(--background-tertiary) !important;
            border-color: var(--primary-color) !important;
            color: var(--primary-color) !important;
            transform: translateY(-1px) !important;
        }

        /* Suggestion Buttons */
        .suggestion-container {
            background: var(--background-primary) !important;
            border-radius: var(--radius-xl) !important;
            padding: 1.5rem !important;
            border: 1px solid var(--border-color) !important;
            box-shadow: var(--shadow-md) !important;
            margin-top: 1rem !important;
        }

        .suggestion-container h3 {
            color: var(--text-primary) !important;
            font-size: 1.1rem !important;
            font-weight: 600 !important;
            margin-bottom: 1rem !important;
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
        }

        .suggestion-btn {
            background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-tertiary) 100%) !important;
            color: var(--text-primary) !important;
            border: 1px solid var(--border-color) !important;
            padding: 0.75rem 1rem !important;
            margin: 0.25rem !important;
            border-radius: var(--radius-lg) !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            font-size: 0.9rem !important;
            font-weight: 500 !important;
            display: inline-block !important;
            text-align: left !important;
            width: 100% !important;
            box-shadow: var(--shadow-sm) !important;
        }

        .suggestion-btn:hover {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%) !important;
            color: white !important;
            transform: translateY(-2px) !important;
            box-shadow: var(--shadow-md) !important;
            border-color: var(--primary-color) !important;
        }

        /* Loading and Status Indicators */
        .loading-indicator {
            display: inline-flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            color: var(--text-secondary) !important;
            font-size: 0.9rem !important;
            padding: 0.5rem 1rem !important;
            background: var(--background-secondary) !important;
            border-radius: var(--radius-md) !important;
            border: 1px solid var(--border-color) !important;
        }

        .status-indicator {
            display: inline-flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            padding: 0.5rem 1rem !important;
            border-radius: var(--radius-md) !important;
            font-size: 0.85rem !important;
            font-weight: 500 !important;
        }

        .status-success {
            background: rgba(5, 150, 105, 0.1) !important;
            color: var(--secondary-color) !important;
            border: 1px solid rgba(5, 150, 105, 0.2) !important;
        }

        .status-error {
            background: rgba(220, 38, 38, 0.1) !important;
            color: var(--accent-color) !important;
            border: 1px solid rgba(220, 38, 38, 0.2) !important;
        }

        /* Card Styling for Info Sections */
        .info-card {
            background: var(--background-primary) !important;
            border-radius: var(--radius-xl) !important;
            padding: 1.5rem !important;
            border: 1px solid var(--border-color) !important;
            box-shadow: var(--shadow-md) !important;
            margin-bottom: 1.5rem !important;
        }

        .info-card h2, .info-card h3 {
            color: var(--text-primary) !important;
            font-weight: 600 !important;
            margin-bottom: 1rem !important;
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
        }

        .info-card h2 {
            font-size: 1.25rem !important;
            border-bottom: 2px solid var(--border-color) !important;
            padding-bottom: 0.5rem !important;
        }

        .info-card h3 {
            font-size: 1.1rem !important;
            color: var(--primary-color) !important;
        }

        .info-card p, .info-card li {
            color: var(--text-secondary) !important;
            line-height: 1.6 !important;
            margin-bottom: 0.5rem !important;
        }

        .info-card ul {
            padding-left: 1.5rem !important;
        }

        .info-card a {
            color: var(--primary-color) !important;
            text-decoration: none !important;
            font-weight: 500 !important;
            transition: color 0.2s ease !important;
        }

        .info-card a:hover {
            color: var(--primary-dark) !important;
            text-decoration: underline !important;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .gradio-container {
                max-width: 100% !important;
                margin: 0 !important;
                padding: 0.5rem !important;
            }

            .gradio-container > div:first-child {
                margin: -0.5rem -0.5rem 1rem -0.5rem !important;
                padding: 1.5rem 1rem !important;
                border-radius: 0 0 var(--radius-lg) var(--radius-lg) !important;
            }

            .gradio-container > div:first-child h1 {
                font-size: 1.75rem !important;
            }

            .gradio-container > div:first-child p {
                font-size: 1rem !important;
            }

            .chatbot .message {
                max-width: 95% !important;
                padding: 0.875rem 1rem !important;
                margin: 0.25rem !important;
            }

            .chatbot .message.bot::before {
                display: none !important;
            }

            .input-container {
                padding: 0.75rem !important;
            }

            .btn-primary, .btn-secondary {
                padding: 0.75rem 1rem !important;
                font-size: 0.9rem !important;
            }

            .suggestion-btn {
                padding: 0.625rem 0.875rem !important;
                font-size: 0.85rem !important;
                margin: 0.125rem !important;
            }

            .info-card {
                padding: 1rem !important;
                margin-bottom: 1rem !important;
            }

            .tab-nav button {
                padding: 0.625rem 1rem !important;
                font-size: 0.9rem !important;
            }
        }

        @media (max-width: 480px) {
            .gradio-container > div:first-child h1 {
                font-size: 1.5rem !important;
            }

            .chatbot .message {
                max-width: 100% !important;
                padding: 0.75rem !important;
                font-size: 0.9rem !important;
            }

            .input-container input[type="text"],
            .input-container textarea {
                padding: 0.75rem !important;
                font-size: 0.95rem !important;
            }

            .btn-primary, .btn-secondary {
                padding: 0.625rem 0.875rem !important;
                font-size: 0.85rem !important;
            }
        }

        /* Accessibility Improvements */
        .sr-only {
            position: absolute !important;
            width: 1px !important;
            height: 1px !important;
            padding: 0 !important;
            margin: -1px !important;
            overflow: hidden !important;
            clip: rect(0, 0, 0, 0) !important;
            white-space: nowrap !important;
            border: 0 !important;
        }

        /* Focus Styles for Keyboard Navigation */
        button:focus-visible,
        input:focus-visible,
        textarea:focus-visible {
            outline: 2px solid var(--primary-color) !important;
            outline-offset: 2px !important;
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.3s ease-in-out !important;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pulse {
            animation: pulse 2s infinite !important;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Custom Scrollbar */
        .chatbot::-webkit-scrollbar {
            width: 6px !important;
        }

        .chatbot::-webkit-scrollbar-track {
            background: var(--background-secondary) !important;
            border-radius: var(--radius-sm) !important;
        }

        .chatbot::-webkit-scrollbar-thumb {
            background: var(--border-color) !important;
            border-radius: var(--radius-sm) !important;
        }

        .chatbot::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted) !important;
        }
        """

        # Create custom theme
        custom_theme = gr.themes.Soft(
            primary_hue="blue",
            secondary_hue="green",
            neutral_hue="slate",
            font=[gr.themes.GoogleFont("Inter"), "Arial", "sans-serif"],
            font_mono=[gr.themes.GoogleFont("JetBrains Mono"), "Consolas", "monospace"]
        ).set(
            body_background_fill="*background_fill_primary",
            body_text_color="*neutral_800",
            button_primary_background_fill="*primary_600",
            button_primary_background_fill_hover="*primary_700",
            button_primary_text_color="white",
            button_secondary_background_fill="*neutral_100",
            button_secondary_background_fill_hover="*neutral_200",
            button_secondary_text_color="*neutral_800",
            input_background_fill="*neutral_50",
            input_border_color="*neutral_300",
            input_border_color_focus="*primary_600"
        )

        with gr.Blocks(
            css=css,
            title="CPMS - Comprehensive Pension Management System",
            theme=custom_theme,
            analytics_enabled=False
        ) as interface:
            # Enhanced Header with Professional Branding
            with gr.Row(elem_classes="header-container"):
                gr.HTML("""
                <div style="text-align: center; position: relative; z-index: 1;">
                    <h1 style="margin: 0; display: flex; align-items: center; justify-content: center; gap: 1rem;">
                        <span style="font-size: 2.5rem;">🏛️</span>
                        <span>CPMS - Comprehensive Pension Management System</span>
                    </h1>
                    <p style="margin: 0.5rem 0 0 0; font-size: 1.1rem; opacity: 0.9;">
                        Your AI-powered assistant for pension management queries and CPMS procedures
                    </p>
                    <div style="margin-top: 1rem; display: flex; justify-content: center; gap: 2rem; flex-wrap: wrap; font-size: 0.95rem;">
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <span>📞</span>
                            <span>Helpline: 1800-113-5800</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <span>📧</span>
                            <span>Email: <EMAIL></span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <span>🌐</span>
                            <span>Portal: dotpension.gov.in</span>
                        </div>
                    </div>
                </div>
                """)

            # Status Indicator
            with gr.Row():
                gr.HTML(
                    f"""
                    <div class="status-indicator status-success fade-in">
                        <span>✅</span>
                        <span>System Online - Model: {self.model_type.upper()}</span>
                    </div>
                    """,
                    elem_classes="status-container"
                )

            with gr.Tab("💬 Chat", elem_id="chat-tab"):
                # Chat Interface with Enhanced Styling
                with gr.Column(elem_classes="chat-container"):
                    chatbot = gr.Chatbot(
                        height=600,
                        show_label=False,
                        container=True,
                        bubble_full_width=False,
                        avatar_images=("👤", "🤖"),
                        elem_classes="chatbot fade-in",
                        show_copy_button=True,
                        type="tuples"
                    )

                    # Input Area with Enhanced Design
                    with gr.Row(elem_classes="input-container"):
                        with gr.Column(scale=4):
                            msg = gr.Textbox(
                                placeholder="💬 Ask me anything about CPMS, pension procedures, forms, or any related queries...",
                                show_label=False,
                                container=False,
                                lines=1,
                                max_lines=3,
                                elem_classes="chat-input"
                            )
                        with gr.Column(scale=1, min_width=120):
                            with gr.Row():
                                submit_btn = gr.Button(
                                    "📤 Send",
                                    variant="primary",
                                    elem_classes="btn-primary",
                                    size="lg"
                                )
                                clear_btn = gr.Button(
                                    "🗑️ Clear",
                                    variant="secondary",
                                    elem_classes="btn-secondary",
                                    size="lg"
                                )

                    # Enhanced Suggested Questions Section
                    with gr.Column(elem_classes="suggestion-container"):
                        gr.HTML("""
                        <h3>
                            <span>💡</span>
                            <span>Quick Start - Try These Questions</span>
                        </h3>
                        """)

                        suggestions = self.get_suggested_questions()

                        # Create suggestion buttons in a responsive grid
                        with gr.Row():
                            with gr.Column(scale=1):
                                for i in range(0, len(suggestions), 2):
                                    if i < len(suggestions):
                                        suggestion_btn = gr.Button(
                                            f"❓ {suggestions[i]}",
                                            size="sm",
                                            variant="secondary",
                                            elem_classes="suggestion-btn"
                                        )
                                        suggestion_btn.click(
                                            fn=lambda x=suggestions[i]: self.handle_suggestion_click(x, []),
                                            outputs=[msg, chatbot]
                                        )

                            with gr.Column(scale=1):
                                for i in range(1, len(suggestions), 2):
                                    if i < len(suggestions):
                                        suggestion_btn = gr.Button(
                                            f"❓ {suggestions[i]}",
                                            size="sm",
                                            variant="secondary",
                                            elem_classes="suggestion-btn"
                                        )
                                        suggestion_btn.click(
                                            fn=lambda x=suggestions[i]: self.handle_suggestion_click(x, []),
                                            outputs=[msg, chatbot]
                                        )

                    # Quick Help Section
                    with gr.Accordion("📚 Quick Help & Tips", open=False):
                        gr.HTML("""
                        <div class="info-card">
                            <h3>🎯 How to Use This Chatbot</h3>
                            <ul>
                                <li><strong>Ask Natural Questions:</strong> Type your questions in plain English</li>
                                <li><strong>Be Specific:</strong> Include details like form numbers, procedures, or specific issues</li>
                                <li><strong>Use Suggestions:</strong> Click on suggested questions to get started quickly</li>
                                <li><strong>Check Sources:</strong> Review the source documents provided with each answer</li>
                            </ul>

                            <h3>📋 What I Can Help With</h3>
                            <ul>
                                <li>CPMS portal login and navigation</li>
                                <li>Pension payment schedules and procedures</li>
                                <li>Form filling guidance (Form 1A, KYP, etc.)</li>
                                <li>Grievance lodging and tracking</li>
                                <li>Bank account changes and updates</li>
                                <li>Life certificate submission</li>
                                <li>Family pension procedures</li>
                                <li>Contact information and helpline details</li>
                            </ul>

                            <h3>⚠️ Important Notes</h3>
                            <ul>
                                <li>This chatbot provides information based on official CPMS documents</li>
                                <li>For urgent issues, contact the helpline: <strong>1800-113-5800</strong></li>
                                <li>Always verify critical information with official sources</li>
                                <li>Keep your personal information secure - don't share sensitive details</li>
                            </ul>
                        </div>
                        """)

                # Enhanced Event Handlers with Loading States
                def submit_with_loading(message, history):
                    if not message.strip():
                        gr.Warning("Please enter a question before sending.")
                        return "", history
                    return self.chat_response(message, history)

                def clear_with_confirmation():
                    gr.Info("Chat history cleared successfully!")
                    return self.clear_chat()

                # Event handlers
                msg.submit(
                    fn=submit_with_loading,
                    inputs=[msg, chatbot],
                    outputs=[msg, chatbot],
                    show_progress="minimal"
                )

                submit_btn.click(
                    fn=submit_with_loading,
                    inputs=[msg, chatbot],
                    outputs=[msg, chatbot],
                    show_progress="minimal"
                )

                clear_btn.click(
                    fn=clear_with_confirmation,
                    outputs=[chatbot],
                    show_progress="hidden"
                )

                # Enable Enter key submission
                msg.submit(
                    fn=lambda: "",
                    outputs=[msg],
                    show_progress="hidden"
                )

            with gr.Tab("ℹ️ System Information", elem_id="system-tab"):
                with gr.Column(elem_classes="info-container"):
                    gr.HTML("""
                    <div class="info-card">
                        <h2>🔧 System Status & Information</h2>
                        <p>Monitor the health and performance of your CPMS chatbot system.</p>
                    </div>
                    """)

                    with gr.Row():
                        with gr.Column(scale=2):
                            system_info = gr.HTML(
                                self.get_enhanced_system_info(),
                                elem_classes="info-card"
                            )
                        with gr.Column(scale=1):
                            with gr.Column(elem_classes="info-card"):
                                gr.HTML("<h3>🔄 Quick Actions</h3>")
                                refresh_btn = gr.Button(
                                    "🔄 Refresh System Info",
                                    variant="primary",
                                    elem_classes="btn-primary",
                                    size="lg"
                                )

                                health_check_btn = gr.Button(
                                    "🏥 Run Health Check",
                                    variant="secondary",
                                    elem_classes="btn-secondary",
                                    size="lg"
                                )

                                gr.HTML("""
                                <div style="margin-top: 1rem; padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border: 1px solid var(--border-color);">
                                    <h4 style="margin: 0 0 0.5rem 0; color: var(--text-primary);">💡 System Tips</h4>
                                    <ul style="margin: 0; padding-left: 1rem; color: var(--text-secondary); font-size: 0.9rem;">
                                        <li>Refresh info to see latest statistics</li>
                                        <li>Health check validates all components</li>
                                        <li>Monitor response times for performance</li>
                                        <li>Check document count for data completeness</li>
                                    </ul>
                                </div>
                                """)

                    # Event handlers for system info
                    refresh_btn.click(
                        fn=self.get_enhanced_system_info,
                        outputs=[system_info],
                        show_progress="minimal"
                    )

                    health_check_btn.click(
                        fn=self.run_comprehensive_health_check,
                        outputs=[system_info],
                        show_progress="minimal"
                    )

            with gr.Tab("📋 Quick Reference Guide", elem_id="reference-tab"):
                with gr.Column(elem_classes="reference-container"):
                    gr.HTML("""
                    <div class="info-card">
                        <h2>📋 CPMS Quick Reference Guide</h2>
                        <p>Essential information, contacts, and procedures for CPMS users.</p>
                    </div>
                    """)

                    with gr.Row():
                        with gr.Column(scale=1):
                            # Contact Information Card
                            gr.HTML("""
                            <div class="info-card">
                                <h3>📞 Emergency Contacts</h3>
                                <div style="display: grid; gap: 1rem;">
                                    <div style="padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 4px solid var(--accent-color);">
                                        <h4 style="margin: 0 0 0.5rem 0; color: var(--accent-color);">🚨 Toll-Free Helpline</h4>
                                        <p style="margin: 0; font-size: 1.1rem; font-weight: 600;">1800-113-5800</p>
                                        <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">Available 24/7 for urgent queries</p>
                                    </div>

                                    <div style="padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 4px solid var(--primary-color);">
                                        <h4 style="margin: 0 0 0.5rem 0; color: var(--primary-color);">📧 Email Support</h4>
                                        <p style="margin: 0; font-weight: 600;"><EMAIL></p>
                                        <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">General queries and support</p>
                                    </div>

                                    <div style="padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 4px solid var(--secondary-color);">
                                        <h4 style="margin: 0 0 0.5rem 0; color: var(--secondary-color);">🔧 Technical Support</h4>
                                        <p style="margin: 0; font-weight: 600;"><EMAIL></p>
                                        <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">Technical issues and portal problems</p>
                                    </div>

                                    <div style="padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 4px solid #8b5cf6;">
                                        <h4 style="margin: 0 0 0.5rem 0; color: #8b5cf6;">👥 Administrative Help</h4>
                                        <p style="margin: 0; font-weight: 600;"><EMAIL></p>
                                        <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">Policy and administrative queries</p>
                                    </div>
                                </div>
                            </div>
                            """)

                            # Important Links Card
                            gr.HTML("""
                            <div class="info-card">
                                <h3>🌐 Essential Portals & Links</h3>
                                <div style="display: grid; gap: 0.75rem;">
                                    <a href="https://dotpension.gov.in" target="_blank" style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; background: var(--background-secondary); border-radius: var(--radius-md); text-decoration: none; color: var(--text-primary); border: 1px solid var(--border-color); transition: all 0.2s ease;">
                                        <span style="font-size: 1.25rem;">🏛️</span>
                                        <div>
                                            <div style="font-weight: 600; color: var(--primary-color);">CPMS Portal</div>
                                            <div style="font-size: 0.85rem; color: var(--text-secondary);">dotpension.gov.in</div>
                                        </div>
                                    </a>

                                    <a href="http://pensionersportal.gov.in" target="_blank" style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; background: var(--background-secondary); border-radius: var(--radius-md); text-decoration: none; color: var(--text-primary); border: 1px solid var(--border-color); transition: all 0.2s ease;">
                                        <span style="font-size: 1.25rem;">👥</span>
                                        <div>
                                            <div style="font-weight: 600; color: var(--primary-color);">Pensioners' Portal</div>
                                            <div style="font-size: 0.85rem; color: var(--text-secondary);">pensionersportal.gov.in</div>
                                        </div>
                                    </a>

                                    <a href="http://dot.gov.in" target="_blank" style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; background: var(--background-secondary); border-radius: var(--radius-md); text-decoration: none; color: var(--text-primary); border: 1px solid var(--border-color); transition: all 0.2s ease;">
                                        <span style="font-size: 1.25rem;">🏢</span>
                                        <div>
                                            <div style="font-weight: 600; color: var(--primary-color);">DoT Official</div>
                                            <div style="font-size: 0.85rem; color: var(--text-secondary);">dot.gov.in</div>
                                        </div>
                                    </a>

                                    <a href="https://jeevanpramaan.gov.in" target="_blank" style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; background: var(--background-secondary); border-radius: var(--radius-md); text-decoration: none; color: var(--text-primary); border: 1px solid var(--border-color); transition: all 0.2s ease;">
                                        <span style="font-size: 1.25rem;">📋</span>
                                        <div>
                                            <div style="font-weight: 600; color: var(--primary-color);">Jeevan Pramaan</div>
                                            <div style="font-size: 0.85rem; color: var(--text-secondary);">Digital Life Certificate</div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            """)

                        with gr.Column(scale=1):
                            # Forms and Documents Card
                            gr.HTML("""
                            <div class="info-card">
                                <h3>📝 Essential Forms & Documents</h3>
                                <div style="display: grid; gap: 0.75rem;">
                                    <div style="padding: 0.75rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 3px solid var(--primary-color);">
                                        <h4 style="margin: 0 0 0.25rem 0; color: var(--primary-color);">Form 1A</h4>
                                        <p style="margin: 0; font-size: 0.9rem; color: var(--text-secondary);">Commutation of Pension Application</p>
                                    </div>

                                    <div style="padding: 0.75rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 3px solid var(--secondary-color);">
                                        <h4 style="margin: 0 0 0.25rem 0; color: var(--secondary-color);">KYP Form</h4>
                                        <p style="margin: 0; font-size: 0.9rem; color: var(--text-secondary);">Know Your Pensioner Form</p>
                                    </div>

                                    <div style="padding: 0.75rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 3px solid #f59e0b;">
                                        <h4 style="margin: 0 0 0.25rem 0; color: #f59e0b;">Forms 4, 8, 10, 12</h4>
                                        <p style="margin: 0; font-size: 0.9rem; color: var(--text-secondary);">Various Pension Application Forms</p>
                                    </div>

                                    <div style="padding: 0.75rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 3px solid #8b5cf6;">
                                        <h4 style="margin: 0 0 0.25rem 0; color: #8b5cf6;">Bank Undertaking</h4>
                                        <p style="margin: 0; font-size: 0.9rem; color: var(--text-secondary);">For bank account changes</p>
                                    </div>

                                    <div style="padding: 0.75rem; background: var(--background-secondary); border-radius: var(--radius-md); border-left: 3px solid var(--accent-color);">
                                        <h4 style="margin: 0 0 0.25rem 0; color: var(--accent-color);">Life Certificate (DLC)</h4>
                                        <p style="margin: 0; font-size: 0.9rem; color: var(--text-secondary);">Annual requirement for pensioners</p>
                                    </div>
                                </div>
                            </div>
                            """)

                            # Key Features Card
                            gr.HTML("""
                            <div class="info-card">
                                <h3>💡 CPMS Key Features</h3>
                                <div style="display: grid; gap: 0.5rem;">
                                    <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.5rem;">
                                        <span style="color: var(--secondary-color); font-size: 1.25rem;">✅</span>
                                        <div>
                                            <div style="font-weight: 500;">Direct Pension Transfer</div>
                                            <div style="font-size: 0.85rem; color: var(--text-secondary);">No intermediaries required</div>
                                        </div>
                                    </div>

                                    <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.5rem;">
                                        <span style="color: var(--primary-color); font-size: 1.25rem;">🏢</span>
                                        <div>
                                            <div style="font-weight: 500;">Single Window System</div>
                                            <div style="font-size: 0.85rem; color: var(--text-secondary);">Complete pension process</div>
                                        </div>
                                    </div>

                                    <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.5rem;">
                                        <span style="color: #f59e0b; font-size: 1.25rem;">📱</span>
                                        <div>
                                            <div style="font-weight: 500;">Online Grievance Management</div>
                                            <div style="font-size: 0.85rem; color: var(--text-secondary);">Reduced paperwork</div>
                                        </div>
                                    </div>

                                    <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.5rem;">
                                        <span style="color: #8b5cf6; font-size: 1.25rem;">📊</span>
                                        <div>
                                            <div style="font-weight: 500;">Status Tracking</div>
                                            <div style="font-size: 0.85rem; color: var(--text-secondary);">Monitor from home</div>
                                        </div>
                                    </div>

                                    <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.5rem;">
                                        <span style="color: var(--accent-color); font-size: 1.25rem;">⚡</span>
                                        <div>
                                            <div style="font-weight: 500;">Automatic Processing</div>
                                            <div style="font-size: 0.85rem; color: var(--text-secondary);">Fast arrears & updates</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            """)

                    # Important Timelines Card (Full Width)
                    gr.HTML("""
                    <div class="info-card">
                        <h3>⏰ Important Timelines & Schedules</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                            <div style="padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border-top: 3px solid var(--primary-color);">
                                <h4 style="margin: 0 0 0.5rem 0; color: var(--primary-color); display: flex; align-items: center; gap: 0.5rem;">
                                    <span>💰</span>
                                    <span>Pension Credit</span>
                                </h4>
                                <p style="margin: 0; font-weight: 600;">Last working day of month</p>
                                <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">Regular monthly pension payment</p>
                            </div>

                            <div style="padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border-top: 3px solid var(--secondary-color);">
                                <h4 style="margin: 0 0 0.5rem 0; color: var(--secondary-color); display: flex; align-items: center; gap: 0.5rem;">
                                    <span>📈</span>
                                    <span>Arrears Payment</span>
                                </h4>
                                <p style="margin: 0; font-weight: 600;">Out of cycle processing</p>
                                <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">Processed at earliest possible</p>
                            </div>

                            <div style="padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border-top: 3px solid #f59e0b;">
                                <h4 style="margin: 0 0 0.5rem 0; color: #f59e0b; display: flex; align-items: center; gap: 0.5rem;">
                                    <span>👨‍👩‍👧‍👦</span>
                                    <span>Family Pension</span>
                                </h4>
                                <p style="margin: 0; font-weight: 600;">Same or next month</p>
                                <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">After death certificate submission</p>
                            </div>

                            <div style="padding: 1rem; background: var(--background-secondary); border-radius: var(--radius-md); border-top: 3px solid #8b5cf6;">
                                <h4 style="margin: 0 0 0.5rem 0; color: #8b5cf6; display: flex; align-items: center; gap: 0.5rem;">
                                    <span>🎂</span>
                                    <span>Age Enhancement</span>
                                </h4>
                                <p style="margin: 0; font-weight: 600;">Automatic processing</p>
                                <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">On designated age milestones</p>
                            </div>
                        </div>
                    </div>
                    """)

            # Professional Footer
            gr.HTML("""
            <div style="margin-top: 3rem; padding: 2rem 1rem 1rem 1rem; border-top: 2px solid var(--border-color); background: var(--background-secondary); border-radius: var(--radius-lg);">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-bottom: 1.5rem;">
                    <div>
                        <h4 style="margin: 0 0 1rem 0; color: var(--primary-color); display: flex; align-items: center; gap: 0.5rem;">
                            <span>🏛️</span>
                            <span>About CPMS</span>
                        </h4>
                        <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem; line-height: 1.5;">
                            The Comprehensive Pension Management System (CPMS) is a centralized web-based solution for
                            end-to-end pension processing, from sanction to disbursement, ensuring transparency and efficiency.
                        </p>
                    </div>

                    <div>
                        <h4 style="margin: 0 0 1rem 0; color: var(--primary-color); display: flex; align-items: center; gap: 0.5rem;">
                            <span>🤖</span>
                            <span>AI Assistant</span>
                        </h4>
                        <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem; line-height: 1.5;">
                            This AI-powered chatbot provides instant answers to your CPMS queries using official documentation
                            and procedures. Available 24/7 for your convenience.
                        </p>
                    </div>

                    <div>
                        <h4 style="margin: 0 0 1rem 0; color: var(--primary-color); display: flex; align-items: center; gap: 0.5rem;">
                            <span>🔒</span>
                            <span>Privacy & Security</span>
                        </h4>
                        <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem; line-height: 1.5;">
                            Your privacy is important. This chatbot processes queries locally and doesn't store personal information.
                            Always verify sensitive information through official channels.
                        </p>
                    </div>
                </div>

                <div style="text-align: center; padding-top: 1.5rem; border-top: 1px solid var(--border-color);">
                    <p style="margin: 0; color: var(--text-muted); font-size: 0.85rem;">
                        © 2024 Department of Telecommunications, Government of India |
                        <a href="https://dotpension.gov.in" target="_blank" style="color: var(--primary-color); text-decoration: none;">CPMS Portal</a> |
                        <a href="http://dot.gov.in" target="_blank" style="color: var(--primary-color); text-decoration: none;">DoT Official</a>
                    </p>
                    <p style="margin: 0.5rem 0 0 0; color: var(--text-muted); font-size: 0.8rem;">
                        For technical support, contact: <strong style="color: var(--primary-color);"><EMAIL></strong>
                    </p>
                </div>
            </div>
            """)

        return interface

    def launch(self, share: bool = False, debug: bool = False):
        """Launch the web interface"""
        interface = self.create_interface()

        print(f"Launching CPMS Chatbot Web Interface...")
        print(f"Model Type: {self.model_type}")
        print(f"Access URL: http://localhost:{config.GRADIO_PORT}")

        interface.launch(
            server_name="0.0.0.0",
            server_port=config.GRADIO_PORT,
            share=share,
            debug=debug,
            show_error=True
        )

def main():
    """Main function to launch the web interface"""
    import argparse

    parser = argparse.ArgumentParser(description="CPMS Chatbot Web Interface")
    parser.add_argument(
        "--model-type",
        choices=["gguf", "huggingface", "finetuned"],
        default="gguf",
        help="Type of model to use"
    )
    parser.add_argument(
        "--optimized",
        action="store_true",
        default=True,
        help="Use optimized RAG system for faster responses (default: True)"
    )
    parser.add_argument(
        "--standard",
        action="store_true",
        help="Use standard RAG system instead of optimized version"
    )
    parser.add_argument(
        "--share",
        action="store_true",
        help="Create a public link"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )

    args = parser.parse_args()

    # Determine whether to use optimized version
    use_optimized = not args.standard  # Use optimized by default unless --standard is specified

    print(f"🚀 Launching CPMS Web Interface")
    print(f"Model: {args.model_type}")
    print(f"Mode: {'Optimized (Fast)' if use_optimized else 'Standard'}")
    print(f"Target Response Time: {'< 10 seconds' if use_optimized else '30-40 seconds'}")

    # Create and launch interface
    web_interface = CPMSWebInterface(model_type=args.model_type, use_optimized=use_optimized)
    web_interface.launch(share=args.share, debug=args.debug)

if __name__ == "__main__":
    main()
