"""
Enhanced query processor that handles both general CPMS queries and pensioner-specific queries
"""
import re
import time
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from backend.services.pensioner_service import pensioner_service
from backend.models.pensioner_models import Pensioner

class EnhancedQueryProcessor:
    """Enhanced query processor for pensioner-specific and general queries"""
    
    def __init__(self):
        # Define patterns for pensioner-specific queries
        self.pensioner_query_patterns = {
            'lc_expiry': [
                r'lc.*expiry|life.*certificate.*expiry|when.*lc.*expire',
                r'lc.*date|life.*certificate.*date',
                r'certificate.*expiry|certificate.*expire'
            ],
            'pension_breakup': [
                r'pension.*breakup|pension.*breakdown|pension.*details',
                r'last.*month.*pension|monthly.*pension.*details',
                r'pension.*amount|pension.*calculation'
            ],
            'commutation_restoration': [
                r'commutation.*restoration|commutation.*date',
                r'when.*commutation.*restore|commutation.*restore.*date'
            ],
            'da_rate': [
                r'da.*rate|dearness.*allowance.*rate|current.*da',
                r'da.*percentage|dearness.*allowance.*percentage'
            ],
            'additional_pension': [
                r'additional.*pension|additional.*pension.*rate',
                r'extra.*pension|supplementary.*pension'
            ],
            'family_pension': [
                r'family.*pension|normal.*family.*pension|enhanced.*family.*pension',
                r'family.*pension.*rate|family.*pension.*amount'
            ],
            'fma': [
                r'fma|fixed.*medical.*allowance|medical.*allowance',
                r'fma.*amount|fma.*rate'
            ],
            'mobile_change': [
                r'change.*mobile|mobile.*change|update.*mobile',
                r'mobile.*number.*change|phone.*number.*change'
            ],
            'sampann_migration': [
                r'sampann.*migration|migration.*date|sampann.*date',
                r'when.*migrated.*sampann|sampann.*migration.*status'
            ]
        }
    
    def detect_query_type(self, query: str) -> Optional[str]:
        """
        Detect if the query is pensioner-specific and return the query type
        """
        query_lower = query.lower()
        
        for query_type, patterns in self.pensioner_query_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return query_type
        
        return None
    
    def process_pensioner_query(self, query: str, pensioner: Pensioner, db: Session) -> Dict[str, Any]:
        """
        Process pensioner-specific query and return structured response
        """
        start_time = time.time()
        query_type = self.detect_query_type(query)
        
        if not query_type:
            return {
                "success": False,
                "message": "I couldn't understand your specific pension query. Please try asking about LC expiry, pension breakup, DA rate, etc.",
                "is_pensioner_query": False
            }
        
        try:
            # Route to appropriate service method
            if query_type == 'lc_expiry':
                result = pensioner_service.get_lc_expiry_date(pensioner, db)
            elif query_type == 'pension_breakup':
                result = pensioner_service.get_pension_breakup(pensioner, db)
            elif query_type == 'commutation_restoration':
                result = pensioner_service.get_commutation_restoration_date(pensioner, db)
            elif query_type == 'da_rate':
                result = pensioner_service.get_current_da_rate(pensioner, db)
            elif query_type == 'additional_pension':
                result = pensioner_service.get_additional_pension_rate(pensioner, db)
            elif query_type == 'family_pension':
                result = pensioner_service.get_family_pension_info(pensioner, db)
            elif query_type == 'fma':
                result = pensioner_service.get_fma_info(pensioner, db)
            elif query_type == 'mobile_change':
                result = pensioner_service.get_mobile_change_info(pensioner, db)
            elif query_type == 'sampann_migration':
                result = pensioner_service.get_sampann_migration_info(pensioner, db)
            else:
                result = {
                    "success": False,
                    "message": "Query type not implemented yet."
                }
            
            processing_time = time.time() - start_time
            
            # Log the query
            pensioner_service.log_query(
                pensioner_id=pensioner.id,
                query_text=query,
                query_type=query_type,
                response_text=result.get("message", ""),
                processing_time=processing_time,
                session_id=f"chat_{pensioner.id}_{int(time.time())}",
                db=db
            )
            
            # Format response for chatbot
            response = {
                "success": result["success"],
                "message": self._format_pensioner_response(result, query_type),
                "is_pensioner_query": True,
                "query_type": query_type,
                "processing_time": processing_time
            }
            
            if result.get("data"):
                response["data"] = result["data"]
            
            return response
            
        except Exception as e:
            return {
                "success": False,
                "message": "I'm having trouble accessing your pension information right now. Please try again later or contact the helpline.",
                "is_pensioner_query": True,
                "error": str(e)
            }
    
    def _format_pensioner_response(self, result: Dict[str, Any], query_type: str) -> str:
        """
        Format pensioner response for better readability in chat
        """
        if not result["success"]:
            return result["message"]
        
        message = result["message"]
        data = result.get("data", {})
        
        if query_type == 'lc_expiry' and data:
            if data.get("status") == "expired":
                message += f"\n⚠️ Your LC has expired {abs(data['days_remaining'])} days ago. Please submit it immediately."
            elif data.get("status") == "expiring_soon":
                message += f"\n⚠️ Your LC expires in {data['days_remaining']} days. Please submit it soon."
            else:
                message += f"\n✅ Your LC is valid for {data['days_remaining']} more days."
        
        elif query_type == 'pension_breakup' and data:
            message += f"\n\n💰 Pension Breakdown:\n"
            message += f"• Basic Pension: ₹{data.get('basic_pension', 0):,.2f}\n"
            message += f"• DA Amount: ₹{data.get('da_amount', 0):,.2f}\n"
            message += f"• Medical Allowance: ₹{data.get('medical_allowance', 0):,.2f}\n"
            message += f"• Additional Pension: ₹{data.get('additional_pension', 0):,.2f}\n"
            message += f"• Total Pension: ₹{data.get('total_pension', 0):,.2f}"
        
        elif query_type == 'da_rate' and data:
            message += f"\n\n📊 DA Information:\n"
            message += f"• Current DA Rate: {data.get('current_da_rate', 0)}%\n"
            message += f"• Your DA Amount: ₹{data.get('your_da_amount', 0):,.2f}"
        
        elif query_type == 'family_pension' and data:
            message += f"\n\n👨‍👩‍👧‍👦 Family Pension:\n"
            message += f"• Normal Family Pension: ₹{data.get('normal_family_pension', 0):,.2f}\n"
            message += f"• Enhanced Family Pension: ₹{data.get('enhanced_family_pension', 0):,.2f}"
        
        elif query_type == 'mobile_change' and data:
            message += f"\n\n📱 Required Documents:\n"
            for doc in data.get('required_documents', []):
                message += f"• {doc}\n"
            message += f"\n📞 Helpline: {data.get('contact_info', {}).get('helpline', 'N/A')}"
        
        return message
    
    def is_pensioner_query(self, query: str) -> bool:
        """
        Check if the query is pensioner-specific
        """
        return self.detect_query_type(query) is not None
    
    def get_query_suggestions(self, pensioner: Pensioner = None) -> Dict[str, Any]:
        """
        Get query suggestions for pensioner
        """
        general_suggestions = [
            "What is CPMS?",
            "How to submit Life Certificate?",
            "CPMS helpline number",
            "How to register on CPMS portal?"
        ]
        
        pensioner_suggestions = [
            "When does my LC expire?",
            "Show my pension breakup",
            "What is the current DA rate?",
            "When will my commutation be restored?",
            "What is my family pension amount?",
            "How to change my mobile number?",
            "When was I migrated to Sampann?"
        ]
        
        return {
            "general_queries": general_suggestions,
            "personal_queries": pensioner_suggestions if pensioner else []
        }

# Global enhanced query processor instance
enhanced_query_processor = EnhancedQueryProcessor()
