#!/usr/bin/env python3
"""
Restore legitimate CPMS questions that were incorrectly removed from cache
"""
import shelve
import sys
import os
from pathlib import Path

# Add workspace root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from backend.config import config

def restore_legitimate_questions():
    """Restore legitimate CPMS questions that were incorrectly removed"""
    
    # These are legitimate CPMS questions that were incorrectly removed
    legitimate_questions_to_restore = [
        "What is the life certificate procedure?",
        "what is life certificate procedure?", 
        "life certificate"
    ]
    
    print("🔄 Restoring legitimate CPMS questions...")
    
    try:
        cache_path = str(config.PERSISTENT_CACHE_PATH)
        
        with shelve.open(cache_path, flag='w') as cache:
            # Check which questions are missing
            missing_questions = []
            for question in legitimate_questions_to_restore:
                if question not in cache:
                    missing_questions.append(question)
            
            if missing_questions:
                print(f"📋 Found {len(missing_questions)} missing legitimate questions:")
                for q in missing_questions:
                    print(f"  - {q}")
                    
                print("\n⚠️ These questions were incorrectly removed and need to be re-cached.")
                print("💡 They will be automatically re-cached when asked again.")
                
                return missing_questions
            else:
                print("✅ All legitimate questions are present in cache")
                return []
                
    except Exception as e:
        print(f"❌ Error checking cache: {e}")
        return []

def show_current_cache_status():
    """Show current cache status after cleanup"""
    try:
        cache_path = str(config.PERSISTENT_CACHE_PATH)
        
        with shelve.open(cache_path, flag='r') as cache:
            questions = list(cache.keys())
            
        print(f"\n📊 Current Cache Status:")
        print(f"Total questions: {len(questions)}")
        
        # Categorize questions
        cpms_questions = []
        questionable = []
        
        for question in questions:
            question_lower = question.lower()
            cpms_keywords = ['pension', 'cpms', 'cca', 'form', 'login', 'disbursement', 'grievance', 'tax']
            
            if any(keyword in question_lower for keyword in cpms_keywords):
                cpms_questions.append(question)
            else:
                questionable.append(question)
        
        print(f"CPMS-related questions: {len(cpms_questions)}")
        print(f"Questionable questions: {len(questionable)}")
        
        if questionable:
            print(f"\n🤔 Questionable questions still in cache:")
            for q in questionable:
                print(f"  - {q}")
                
        return {
            'total': len(questions),
            'cpms': cpms_questions,
            'questionable': questionable
        }
        
    except Exception as e:
        print(f"❌ Error reading cache: {e}")
        return None

def main():
    """Main function"""
    print("🔄 CPMS Cache Restoration Tool")
    print("=" * 40)
    
    # Check and restore legitimate questions
    missing = restore_legitimate_questions()
    
    # Show current status
    status = show_current_cache_status()
    
    if missing:
        print(f"\n💡 Recommendation:")
        print(f"The following legitimate questions were removed and will need to be re-cached:")
        for q in missing:
            print(f"  - {q}")
        print(f"\nThey will be automatically re-cached when users ask these questions again.")
    
    print(f"\n✅ Cache restoration check complete!")

if __name__ == "__main__":
    main()
