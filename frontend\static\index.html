<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPMS - Comprehensive Pension Management System</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
    <link rel="icon" type="image/png" href="assets/favicon.png">
    <meta name="description" content="CPMS AI-powered assistant for pension management queries and procedures">
</head>
<body>
    <!-- Professional Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">🏛️</div>
            <div class="loading-text">CPMS</div>
            <div class="loading-spinner"></div>
            <div class="loading-status">Connecting to backend...</div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app" class="app hidden">
        <!-- Professional Header with refined styling -->
        <header class="header">
            <div class="header-content">
                <div class="header-left">
                    <div class="logo">
                        <span class="logo-icon">🏛️</span>
                        <div class="logo-text">
                            <h1>CPMS</h1>
                            <p>Comprehensive Pension Management System</p>
                        </div>
                    </div>
                </div>
                <div class="header-right">
                    <div class="connection-status" id="connection-status">
                        <div class="status-indicator" id="status-indicator"></div>
                        <span id="status-text">Connecting...</span>
                    </div>
                    <button class="header-btn" id="settings-btn" title="Settings" aria-label="Open Settings">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="header-btn" id="info-btn" title="System Info" aria-label="View System Information">
                        <i class="fas fa-info-circle"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Professional Sidebar with card styling -->
            <aside class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <h3><i class="fas fa-lightbulb"></i> Quick Questions</h3>
                    <button class="sidebar-toggle" id="sidebar-toggle" aria-label="Toggle sidebar">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
                <div class="suggestions-container" id="suggestions-container">
                    <div class="suggestions-loading">
                        <div class="spinner-small"></div>
                        <span>Loading suggestions...</span>
                    </div>
                </div>
                <div class="sidebar-footer">
                    <button class="clear-chat-btn" id="clear-chat-btn" aria-label="Clear chat history">
                        <i class="fas fa-trash"></i>
                        Clear Chat
                    </button>
                </div>
            </aside>

            <!-- Chat Area -->
            <section class="chat-section">
                <div class="chat-container">
                    <div class="chat-messages" id="chat-messages">
                        <div class="welcome-message">
                            <div class="welcome-icon">🏛️</div>
                            <h2>Welcome to CPMS Assistant</h2>
                            <p>I'm here to help you with pension management queries and CPMS procedures. Ask me anything!</p>
                            <div class="welcome-features">
                                <div class="feature">
                                    <i class="fas fa-search"></i>
                                    <span>Instant Answers</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>Secure & Private</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-clock"></i>
                                    <span>24/7 Available</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Typing Indicator -->
                    <div class="typing-indicator hidden" id="typing-indicator">
                        <div class="typing-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="typing-bubble">
                            <div class="typing-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Professional Input Area - Sticky footer -->
                <div class="input-area">
                    <div class="input-container">
                        <div class="input-wrapper">
                            <textarea
                                id="message-input"
                                placeholder="Ask me anything about CPMS..."
                                rows="1"
                                maxlength="2000"
                                aria-label="Message input"
                            ></textarea>
                            <div class="input-actions">
                                <button class="input-btn" id="attach-btn" title="Attach file" disabled aria-label="Attach file">
                                    <i class="fas fa-paperclip"></i>
                                </button>
                                <button class="send-btn" id="send-btn" title="Send message" aria-label="Send message">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                        <div class="input-footer">
                            <div class="char-counter">
                                <span id="char-count">0</span>/2000
                            </div>
                            <div class="input-hint">
                                Press <kbd>Enter</kbd> to send, <kbd>Shift+Enter</kbd> for new line
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modals -->
    <!-- Settings Modal -->
    <div class="modal hidden" id="settings-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-cog"></i> Settings</h3>
                <button class="modal-close" data-modal="settings-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label for="theme-select">Theme</label>
                    <select id="theme-select">
                        <option value="light">Light</option>
                        <option value="dark">Dark</option>
                        <option value="auto">Auto</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label for="font-size-select">Font Size</label>
                    <select id="font-size-select">
                        <option value="small">Small</option>
                        <option value="medium" selected>Medium</option>
                        <option value="large">Large</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="sound-enabled" checked>
                        <span class="checkmark"></span>
                        Enable notification sounds
                    </label>
                </div>
                <div class="setting-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="auto-scroll" checked>
                        <span class="checkmark"></span>
                        Auto-scroll to new messages
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- System Info Modal -->
    <div class="modal hidden" id="info-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-info-circle"></i> System Information</h3>
                <button class="modal-close" data-modal="info-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="info-loading">
                    <div class="spinner-small"></div>
                    <span>Loading system information...</span>
                </div>
                <div class="system-info hidden" id="system-info-content">
                    <!-- System info will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
