#!/usr/bin/env python3
"""
Quick Cache Cleanup Tool
Remove unwanted questions from persistent cache with predefined filters
"""
import shelve
import sys
import os
from pathlib import Path

# Add workspace root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from backend.config import config

def remove_off_topic_questions():
    """Remove obviously off-topic questions from cache"""

    # Specific off-topic questions to remove (exact matches)
    off_topic_exact_matches = [
        'how to cook chicken',
        'who won ind vs pak?',
        'for my dog i need pension?',
        'Path to create the AAO User Login?'
    ]

    # Patterns that are clearly off-topic (but avoid CPMS false positives)
    off_topic_patterns = [
        # Single words (will use word boundaries)
        'cricket', 'football', 'basketball', 'tennis',
        'movie', 'film', 'song', 'music',
        'weather', 'rain', 'temperature', 'storm',
        'love', 'dating', 'marriage', 'wedding',
        'crypto', 'bitcoin', 'ethereum',
        'hack', 'hacking', 'cat', 'dog', 'pet',
        'cooking', 'recipe', 'food', 'kitchen','AAO User'

        # Multi-word phrases (will use substring matching)
        'sports match', 'stock market', 'crack password',
        'how to cook', 'ind vs pak', 'who won',
        'entertainment news', 'celebrity gossip','AAO User Login'
    ]

    try:
        cache_path = str(config.PERSISTENT_CACHE_PATH)
        print(f"🧹 Cleaning off-topic questions from cache...")

        removed_questions = []

        with shelve.open(cache_path, flag='w') as cache:
            questions_to_remove = []

            # Find exact matches first
            for question in list(cache.keys()):
                if question in off_topic_exact_matches:
                    questions_to_remove.append(question)
                    continue

                # Check patterns but exclude CPMS-related terms
                question_lower = question.lower()

                # Skip if it contains CPMS-related keywords
                cpms_keywords = ['pension', 'cpms', 'cca', 'certificate', 'form', 'disbursement']
                is_cpms_related = any(keyword in question_lower for keyword in cpms_keywords)

                if not is_cpms_related:
                    import re
                    for pattern in off_topic_patterns:
                        # Use word boundaries for single words, substring for phrases
                        # This prevents "certificate" from being flagged because it contains "cat"
                        if ' ' in pattern:
                            # Multi-word phrases - use substring matching
                            if pattern in question_lower:
                                questions_to_remove.append(question)
                                break
                        else:
                            # Single words - use word boundaries to avoid false matches
                            # \b ensures "cat" only matches whole word "cat", not "certificate"
                            word_pattern = r'\b' + re.escape(pattern) + r'\b'
                            if re.search(word_pattern, question_lower):
                                questions_to_remove.append(question)
                                break

            # Remove them
            for question in questions_to_remove:
                del cache[question]
                removed_questions.append(question)
                print(f"🗑️ Removed: {question}")

        print(f"\n✅ Cleanup complete! Removed {len(removed_questions)} off-topic questions")

        if removed_questions:
            print("\n📋 Removed questions:")
            for q in removed_questions:
                print(f"  - {q}")

        return removed_questions

    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        return []

def remove_duplicate_questions():
    """Remove duplicate or very similar questions"""
    try:
        cache_path = str(config.PERSISTENT_CACHE_PATH)
        print(f"🔍 Finding duplicate questions...")

        removed_questions = []

        with shelve.open(cache_path, flag='w') as cache:
            questions = list(cache.keys())
            questions_to_remove = []

            # Find duplicates (case-insensitive, ignoring punctuation)
            seen_normalized = {}

            for question in questions:
                # Normalize: lowercase, remove punctuation, strip whitespace
                normalized = ''.join(c.lower() for c in question if c.isalnum() or c.isspace()).strip()
                normalized = ' '.join(normalized.split())  # Normalize whitespace

                if normalized in seen_normalized:
                    # This is a duplicate
                    original = seen_normalized[normalized]
                    print(f"🔄 Duplicate found:")
                    print(f"   Original: {original}")
                    print(f"   Duplicate: {question}")
                    questions_to_remove.append(question)
                else:
                    seen_normalized[normalized] = question

            # Remove duplicates
            for question in questions_to_remove:
                del cache[question]
                removed_questions.append(question)

        print(f"\n✅ Removed {len(removed_questions)} duplicate questions")
        return removed_questions

    except Exception as e:
        print(f"❌ Error removing duplicates: {e}")
        return []

def list_questionable_entries():
    """List questions that might need manual review"""
    try:
        cache_path = str(config.PERSISTENT_CACHE_PATH)

        with shelve.open(cache_path, flag='r') as cache:
            questions = list(cache.keys())

        # Patterns that might indicate questionable content
        questionable_patterns = [
            'how to', 'what is', 'where', 'when', 'why',
            'step', 'procedure', 'process'
        ]

        questionable = []
        for question in questions:
            question_lower = question.lower()
            # Very short questions
            if len(question.strip()) < 10:
                questionable.append(('Too short', question))
            # Questions with special characters
            elif any(c in question for c in ['\\', '\x80', '\x91']):
                questionable.append(('Special chars', question))
            # Very long questions
            elif len(question) > 200:
                questionable.append(('Too long', question))

        if questionable:
            print(f"\n🤔 Found {len(questionable)} questionable entries:")
            print("=" * 80)
            for reason, question in questionable:
                print(f"[{reason}] {question}")
        else:
            print("✅ No questionable entries found")

        return questionable

    except Exception as e:
        print(f"❌ Error listing questionable entries: {e}")
        return []

def main():
    """Main menu for quick cleanup operations"""
    print("🧹 CPMS Cache Quick Cleanup Tool")
    print("=" * 40)

    while True:
        print("\n📋 Quick Actions:")
        print("1. Remove off-topic questions")
        print("2. Remove duplicate questions")
        print("3. List questionable entries")
        print("4. Show cache statistics")
        print("5. Exit")

        choice = input("\n🔢 Enter your choice (1-5): ").strip()

        if choice == '1':
            print("\n⚠️ This will remove obviously off-topic questions!")
            confirm = input("Continue? (y/N): ").strip().lower()
            if confirm == 'y':
                remove_off_topic_questions()
            else:
                print("❌ Operation cancelled")

        elif choice == '2':
            print("\n⚠️ This will remove duplicate questions!")
            confirm = input("Continue? (y/N): ").strip().lower()
            if confirm == 'y':
                remove_duplicate_questions()
            else:
                print("❌ Operation cancelled")

        elif choice == '3':
            list_questionable_entries()

        elif choice == '4':
            try:
                cache_path = str(config.PERSISTENT_CACHE_PATH)
                with shelve.open(cache_path, flag='r') as cache:
                    total_questions = len(cache)
                print(f"\n📊 Cache Statistics:")
                print(f"Total cached questions: {total_questions}")

                # Calculate file sizes
                total_size = 0
                for ext in ['.dir', '.dat', '.bak']:
                    file_path = cache_path + ext
                    if os.path.exists(file_path):
                        size = os.path.getsize(file_path)
                        total_size += size
                        print(f"  {ext} file: {size:,} bytes")

                print(f"Total cache size: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")

            except Exception as e:
                print(f"❌ Error getting statistics: {e}")

        elif choice == '5':
            print("👋 Goodbye!")
            break

        else:
            print("❌ Invalid choice. Please enter 1-5.")

if __name__ == "__main__":
    main()
