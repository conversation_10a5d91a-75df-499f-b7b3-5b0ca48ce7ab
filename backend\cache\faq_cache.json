{"5e9133f000025362": {"query": "Who developed CPMS?", "response": "CPMS is an end-to-end software developed by the Department of Telecom.", "processing_time": 8.12, "sources": [{"source": "Web - FAQ", "relevance_score": 0.188, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": 0.188, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "model_type": "gguf", "timestamp": **********.9125552}, "e015c9d74c58d4f3": {"query": "What is the purpose of CPMS?", "response": "The Comprehensive Pension Management System (CPMS) is a software solution developed and implemented by the Department of Telecom to manage all pension-related activities, from sanction and authorization to disbursement. It enables pensioners to track their latest pension status online at www.dotpension.gov.in. After CPMS implementation, the Controller of Communication Accounts (CCA) acts as the pension disbursing authority, dealing with all pension-related activities on behalf of post offices and banks. This simplifies processes for pensioners by making CPMS a single window for managing their pensions.", "processing_time": 10.75, "sources": [{"source": "Web - FAQ", "relevance_score": 0.251, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": 0.251, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "model_type": "gguf", "timestamp": **********.7639077}, "59cc0deb906d31a4": {"query": "What are the benefits of CPMS?", "response": "The Comprehensive Pension Management System (CPMS) provides several benefits for pensioners. With CPMS, pensioners can track the latest status of their pensions online. The implementation of CPMS simplifies the process by making the Controller of Communication Accounts (CCA) the pension disbursing authority (PDA), eliminating the need for multiple interactions with different entities. This results in faster and more efficient pension disbursements directly to the designated bank accounts of pensioners.", "processing_time": 8.73, "sources": [{"source": "Web - FAQ", "relevance_score": 0.118, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": 0.118, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "model_type": "gguf", "timestamp": **********.5992892}, "9d2b3346ae3cdf43": {"query": "What is the full form of CPMS?", "response": "CPMS stands for Comprehensive Pension Management System. It is an end-to-end software implemented by the Department of Telecom that covers all activities related to pension sanction, authorization, disbursement, and enables pensioners to track their latest status.", "processing_time": 5.15, "sources": [{"source": "Web - FAQ", "relevance_score": 0.319, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": 0.319, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "model_type": "gguf", "timestamp": 1750485891.8592784}, "74ff78619f60a693": {"query": "How to access CPMS portal?", "response": "To access the CPMS portal, follow these steps:\n1. Open your preferred web browser (Google Chrome for CPMS or Internet Explorer Version 9 or 11 for DSC) and enter the URL www.dotpension.gov.in in the address bar to reach the login screen.\n2. Enter your User Name.\n3. Enter your Password.\n4. Enter the Captcha code correctly.\n5. Click on the \"Login\" button to access the CPMS portal.", "processing_time": 17.53, "sources": [{"source": "Unknown", "relevance_score": 0.429, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": 0.165, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in FAQ Index Scraped: 2025..."}], "model_type": "gguf", "timestamp": 1750485909.595503}, "bc3c1681508a77e9": {"query": "What is the CPMS website URL?", "response": "The CPMS website URL is www.dotpension.gov.in. To log in, enter your User Name and Password, then click the Login button after entering the Captcha code. The preferred web browsers for using CPMS are Google Chrome and for DSC, it is Internet Explorer Version 9 or 11.", "processing_time": 6.31, "sources": [{"source": "Unknown", "relevance_score": 0.162, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": -0.044, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in FAQ Index Scraped: 2025..."}], "model_type": "gguf", "timestamp": **********.0124593}, "7923162820c67ed9": {"query": "How to register on CPMS?", "response": "To register on the Comprehensive Pension Management System (CPMS), follow these steps:\n1. Enter www.dotpension.gov.in in your web browser to go to the login screen. Use Google Chrome for CPMS and Internet Explorer Version 9 or 11 for DSC.\n2. If you're an Administrator, create a new user account by logging in, navigating to Users > User Registration, clicking \"ADD NEW,\" filling out the details, and saving it.\n3. Assign roles to the newly created user (CCA) by logging in as Administrator, going to Users > Role Master, clicking \"ADD NEW,\" filling out the details, and saving it.\n4. Enter your User Name and Password, then enter the Captcha and click Login to access the CPMS application.", "processing_time": 22.34, "sources": [{"source": "Unknown", "relevance_score": 0.33, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Unknown", "relevance_score": 0.061, "content_preview": "a) Administrator User will create CCA user name/password for login. b) CCA or any officer nominated ..."}], "model_type": "gguf", "timestamp": **********.4602344}, "bb07d6e8b6795abb": {"query": "What are the login requirements for CPMS?", "response": "To login to the CPMS application, users must enter the URL www.dotpension.gov.in in their web browser, then input their User Name and Password, enter the Captcha, and click on the Login button. The preferred browsers for CPMS are Google Chrome and for DSC, Internet Explorer Version 9 or 11.\n\nFor administrative tasks, an Administrator User will create CCA usernames and passwords, assign roles to CCAs, and create DH logins with the necessary permissions assigned by AAO. The process involves logging in as an Administrator, navigating to Users > User Registration for CCA creation and Users > Role Master > Add New for role assignment. For DH login creation, AAO logs in as AAO, navigates to Users > User Registration, selects the DH role type, and saves the user details. After creating the login, AAO assigns module authorizations by clicking on the lock icon in the authorization column. Multiple DH users can be created, and their access can be edited as needed.", "processing_time": 23.18, "sources": [{"source": "Unknown", "relevance_score": 0.385, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Unknown", "relevance_score": 0.151, "content_preview": "a) Administrator User will create CCA user name/password for login. b) CCA or any officer nominated ..."}], "model_type": "gguf", "timestamp": **********.7479227}, "1954b268c735ca0f": {"query": "How to reset CPMS password?", "response": "To reset your CPMS password, follow these steps:\n1. Go to the CPMS login page by entering www.dotpension.gov.in in your web browser.\n2. Click on 'Forgot Password' and enter your User Name or Email Address associated with your account.\n3. You will receive an email containing a link to reset your password. Follow the instructions in the email to create a new password.\n4. Once you have created a new password, log in using your User Name, new Password, and Captcha code.\n\nAdditional tips:\n- The preferred web browser for CPMS is Google Chrome, while Internet Explorer Version 9 or 11 is recommended for DSC login.\n- For technical help, contact the CPMS helpdesk at 1800-113-5800.\n- If you encounter any issues, refer to the FAQs for pensioners on the CPMS portal.", "processing_time": 26.38, "sources": [{"source": "Unknown", "relevance_score": 0.204, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - Home", "relevance_score": -0.003, "content_preview": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/..."}], "model_type": "gguf", "timestamp": **********.2386081}, "c1908f3a5a32615e": {"query": "What to do if I forgot my CPMS login credentials?", "response": "If you have forgotten your CPMS login credentials (User Name or Password), you can follow these steps:\n1. Go to the CPMS website at www.dotpension.gov.in.\n2. Click on the 'Forgot Password' link below the login screen.\n3. Enter your registered E-mail address and click on 'Send'.\n4. Your password will be sent to your E-mail address. If you no longer have access to that email, please contact the CPMS helpdesk at sampann.cpms-dot-at-gov-in or call them at 1800-113-5800 for assistance.\nRemember, it's important to keep your login credentials secure and not share them with anyone.", "processing_time": 15.12, "sources": [{"source": "Unknown", "relevance_score": 0.209, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - Home", "relevance_score": 0.12, "content_preview": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/..."}], "model_type": "gguf", "timestamp": **********.4636967}, "40befa9cbafce824": {"query": "How to create CPMS account?", "response": "To create a CPMS account, follow these steps:\n1. Enter the URL www.dotpension.gov.in in your web browser to reach the login screen. Preferred browsers are Google Chrome for CPMS and Internet Explorer Version 9 or 11 for DSC.\n2. Log in as an Administrator.\n3. Navigate to Users > User Registration, then click \"ADD NEW\" to create a new user. Fill in the required details and save it.\n4. Assign roles to the newly created user under Users > Role Master by clicking \"ADD NEW\", filling in the necessary information, and saving it.\n\nThis process creates an account for a CCA (Central Civilian Authority) or any officer nominated by them to log in to the CPMS application.", "processing_time": 21.63, "sources": [{"source": "Unknown", "relevance_score": 0.327, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Unknown", "relevance_score": 0.267, "content_preview": "a) Administrator User will create CCA user name/password for login. b) CCA or any officer nominated ..."}], "model_type": "gguf", "timestamp": **********.194446}, "69f6c9200d670428": {"query": "How to track pension application?", "response": "To track the status of your pension application, you can log in to the CPMS (Central Pension Management System) pensioners' portal at https://cpms.gov.in/. Once logged in, navigate to the 'Pensioner Corner' tab and select 'View Application Status'. This will allow you to view the current status of your application, as well as any relevant updates or communications from the authorities. For technical assistance or queries related to the portal, contact the CPMS helpdesk at support[dot]cpms-dot[at]nic[dot]in or call 1800-113-5800.", "processing_time": 21.79, "sources": [{"source": "Web - About Us", "relevance_score": 0.317, "content_preview": "\n\nList:\n1. Direct disbursement of pension on timely basis without intermediaries\n2. Single window sy..."}, {"source": "Web - About Us", "relevance_score": 0.317, "content_preview": "\n\nList:\n1. Direct disbursement of pension on timely basis without intermediaries\n2. Single window sy..."}], "model_type": "gguf", "timestamp": 1750486047.290857}, "0d15c1f6346ae4ac": {"query": "How to apply for family pension?", "response": "To apply for family pension, the following details need to be provided on the personal detail page: title, first name, middle name, last name, type of retirement, height, father's/husband's name, mother's name, date of birth, gender, Aadhaar Number, PAN Number, FP mobile number, and FP email ID. Additionally, mandatory fields in the Other Details section include designation at the time of death, group, date of joining, date of death, date of superannuation, and government accommodation provided by Directorate of Estates/BSNL during service (yes or no). Once all information is entered and saved, an instance will be created. The HoO should then be advised to submit these details along with relevant documents. Family pensioners are also required to provide a mobile phone number for updates on the pension process and disbursement.", "processing_time": 23.96, "sources": [{"source": "Unknown", "relevance_score": 0.202, "content_preview": " Following information need to be entered on personal detail page: a. Title b. First Name c. Middle..."}, {"source": "Unknown", "relevance_score": 0.187, "content_preview": " Following information need to be entered on personal detail page: s. Title t. First Name u. Middle..."}], "model_type": "gguf", "timestamp": **********.4553547}, "96d02ee4421263ab": {"query": "What is the pension calculation method?", "response": "The pension calculation method for compulsory retirement involves providing the Date of Medical Report at the time of creating the retiree profile. At the Form 7 level, a careful assessment is required when entering the percentage to reduce the pension and DCRG. The system calculates the impact on the pension automatically. For revisions, there are five categories, including revision in the rate of DA which can result in an adjustment to the DCRG and pension payable.", "processing_time": 18.43, "sources": [{"source": "Unknown", "relevance_score": 0.309, "content_preview": "In form 7 in case of compulsory retirement, at the time of creating the retiree profile, the Date of..."}, {"source": "Unknown", "relevance_score": 0.17, "content_preview": " Again login with AO Pension go to the Masters Generate ID Card.  Here Fill the PPO number of pen..."}], "model_type": "gguf", "timestamp": **********.99223}, "831e9cbe96a38f86": {"query": "How to get pension certificate?", "response": "To obtain a pension certificate, you must first complete the Know Your Pensioner (KYP) form which is available for download on the Comprehensive Pension Management System (CPMS) website. After filling out the form, submit your Life Certificate at any Central Civil Accounts Office (CCA). The guidelines for document submission can be found in circular 3 dated June 23, 2021, and circular 5 dated March 4, 2021. Additionally, you may need to provide Income Tax Declarations as per the guidelines outlined in circular 4 dated August 22, 2019. Once your application is processed, a pension certificate will be issued to you. If you require technical assistance or have administrative/training queries, please contact the CPMS helpdesk at support.cpms-at-nic.in or call 1800-113-5800. For feedback and policies related matters, kindly visit the Department of Telecommunication Jeevan Pramaan portal.", "processing_time": 33.24, "sources": [{"source": "Web - Circulars", "relevance_score": 0.275, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home RetireeCornerCir S..."}, {"source": "Web - Circulars", "relevance_score": 0.275, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home RetireeCornerCir S..."}], "model_type": "gguf", "timestamp": **********.3383696}, "937ade14a2f7660c": {"query": "What is commutation of pension?", "response": "Commutation of pension refers to the process of converting a portion of one's monthly pension into a lump sum payment, as permitted by certain rules. This form (Form I-A) is used for applying for such commutation without undergoing a medical examination. The application must be submitted at least three months before retirement and includes specific details such as the applicant's name, date of birth, date of retirement, and the percentage of monthly pension proposed to be commuted, among others. After submission, an acknowledgement is received confirming receipt of the application.", "processing_time": 19.92, "sources": [{"source": "PDF - Form1A_COP", "relevance_score": 0.215, "content_preview": "FORM I-A FORM OF APPLICATION FOR COMMUTATION OF A PERCENTAGE OF SUPERANNUATION PENSION WITHOUT s MED..."}, {"source": "PDF - Form1A_COP", "relevance_score": 0.215, "content_preview": "FORM I-A FORM OF APPLICATION FOR COMMUTATION OF A PERCENTAGE OF SUPERANNUATION PENSION WITHOUT s MED..."}], "model_type": "gguf", "timestamp": **********.3708777}, "9c444bec2bf8403d": {"query": "How to apply for commutation?", "response": "To apply for the commutation of a percentage of your pension without undergoing a medical examination, follow these steps:\n1. Complete Part I of Form I-A application form available in the Central Civil Services (Pension) Rules, 1972.\n2. Submit it to the Head of Office at least three months before retirement with all necessary particulars like name, father's/husband's name, designation at retirement, department/ministry, date of birth, date of retirement, class of pension, percentage of monthly pension proposed to be commuted, and bank account details.\n3. The Head of Office will acknowledge your application with their signature and date.\n4. Keep a copy for your records.", "processing_time": 13.81, "sources": [{"source": "PDF - Form1A_COP", "relevance_score": 0.138, "content_preview": "FORM I-A FORM OF APPLICATION FOR COMMUTATION OF A PERCENTAGE OF SUPERANNUATION PENSION WITHOUT s MED..."}, {"source": "PDF - Form1A_COP", "relevance_score": 0.138, "content_preview": "FORM I-A FORM OF APPLICATION FOR COMMUTATION OF A PERCENTAGE OF SUPERANNUATION PENSION WITHOUT s MED..."}], "model_type": "gguf", "timestamp": **********.29469}, "5a10dbb76ac4399a": {"query": "When will pension arrears be paid?", "response": "According to the CPMS documentation provided, after the monthly bill is approved by the AO and sent for payment through PFMS, arrears are processed faster. However, the exact timing of when pension arrears will be paid isn't specified in the information given. If you have additional questions or need further clarification, please contact CPMS helpdesk at support.cpms-at-nic.in or call 1800-113-5800 for technical assistance. For administrative and training queries, email dycgcabait-dot-gov-dot-in. Additionally, the Pensioners' Portal offers a feedback feature and contact information for policies and other concerns.", "processing_time": 22.5, "sources": [{"source": "Unknown", "relevance_score": 0.167, "content_preview": " While processing the case for monthly bill the income tax deducted shall be filled and accordingly..."}, {"source": "Web - About Us", "relevance_score": 0.145, "content_preview": "\n\nList:\n1. Direct disbursement of pension on timely basis without intermediaries\n2. Single window sy..."}], "model_type": "gguf", "timestamp": **********.8987904}, "56603ed5e3002515": {"query": "How to update bank account information?", "response": "To update your bank account information on the CPMS platform, follow these steps:\n\n1. Log in using your PAN number as the username.\n2. Click on your profile picture and select \"Edit Profile.\"\n3. Choose the \"Bank Account\" option to edit or modify it.\n4. Enter your new bank details and select either your registered email address or the entered mobile number to receive an OTP for verification.\n5. Receive and enter the OTP, then save the changes to update your bank account information.", "processing_time": 18.21, "sources": [{"source": "Unknown", "relevance_score": 0.033, "content_preview": "For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefit..."}, {"source": "Unknown", "relevance_score": -0.089, "content_preview": "Upon selecting ‘Mobile Number’, the following screen will be displayed (5.12)) Fig(5.12) Retiree can..."}], "model_type": "gguf", "timestamp": **********.3163943}, "b52e79ab27dfdf58": {"query": "What bank details are required for pension?", "response": "The CPMS documentation above does not mention any bank details for pension. However, it does state that Family Pensioners are required to provide their Aadhaar Number and FP Mobile Number. These details might be used for communication purposes or to facilitate the disbursement of pensionary benefits. For specific information on how to submit bank account details for pension, Family Pensioners should refer to the instructions provided by their respective HoO (Head of Office).", "processing_time": 33.79, "sources": [{"source": "Unknown", "relevance_score": 0.176, "content_preview": " Following information need to be entered on personal detail page: a. Title b. First Name c. Middle..."}, {"source": "Unknown", "relevance_score": 0.17, "content_preview": " DH to verify and fill the required information in Form 12. For each claimant who has been nominate..."}], "model_type": "gguf", "timestamp": **********.218288}, "9db1a4a24d6060bf": {"query": "How to verify bank account in CPMS?", "response": "To verify a bank account in CPMS, follow these steps:\n1. Log in to the CPMS application using your User Name and Password.\n2. Go to the 'Form 12' tab and click on the Edit button next to the nominee's name whose bank details you want to update.\n3. Enter the correct Bank Account number, IFSC code, and other required information.\n4. Obtain a signed Bank Undertaking from the bank authorities and the Family Pensioner.\n5. Scan and upload the signed Bank Undertaking on CPMS.\n6. Save your changes.\n\nFor claimants who are only eligible for Gratuity, you need to fill and upload the Mandate form generated in the portal instead of a Bank Undertaking.", "processing_time": 68.87, "sources": [{"source": "Unknown", "relevance_score": 0.106, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Unknown", "relevance_score": 0.067, "content_preview": " DH to verify and fill the required information in Form 12. For each claimant who has been nominate..."}], "model_type": "gguf", "timestamp": **********.1968875}, "ee7818b6e0ba8377": {"query": "What if pension is not credited to my account?", "response": "If your pension is not credited to the account mentioned in this letter of authority and undertaking, you should contact the Pr.CCA or CCA office directly for assistance. They can help investigate any potential issues with the direct disbursement of your pension through SAMPANN to your bank account. If you need further guidance, please let me know.", "processing_time": 24.32, "sources": [{"source": "PDF - Bank undertaking", "relevance_score": 0.129, "content_preview": "ANNEXURE 1 Pensioner s letter of Authority and Undertaking To, Date Pr.CCA CCA .. Sir, I hereby opt ..."}, {"source": "PDF - Bank undertaking", "relevance_score": 0.129, "content_preview": "ANNEXURE 1 Pensioner s letter of Authority and Undertaking To, Date Pr.CCA CCA .. Sir, I hereby opt ..."}], "model_type": "gguf", "timestamp": **********.6243448}, "0ccca4df54ec44a6": {"query": "How to change bank branch for pension?", "response": "To change the bank branch for your pension, you will need to follow the revision process outlined in the document. Specifically, under category 2 (Revision on account of Withheld Amount), you should request a sanction for the release of funds to your new bank account. Once approved and processed by the Pension Department, you can then make the necessary changes to your pension payments through your new bank branch. Note that any impact on your monthly pension amount will need to be calculated and paid separately.", "processing_time": 17.16, "sources": [{"source": "Unknown", "relevance_score": 0.036, "content_preview": " Again login with AO Pension go to the Masters Generate ID Card.  Here Fill the PPO number of pen..."}, {"source": "PDF - Bank undertaking", "relevance_score": 0.007, "content_preview": "ANNEXURE 1 Pensioner s letter of Authority and Undertaking To, Date Pr.CCA CCA .. Sir, I hereby opt ..."}], "model_type": "gguf", "timestamp": **********.891895}, "d4dc31a03f6ea84f": {"query": "How to submit life certificate?", "response": "To submit a life certificate as a Sampann pensioner, you have two options: digital or physical. For digital submission, follow the instructions provided by the authorities to certify your identity online. Alternatively, for physical submission, print and sign the prescribed form, then mail it to the Central Civil Accounts Office (CCA). Upon receipt and verification, your pension payment will resume if suspended due to non-submission.", "processing_time": 15.15, "sources": [{"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.403, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}, {"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.403, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}], "model_type": "gguf", "timestamp": **********.2501671}, "27988a0acf85ea02": {"query": "When to submit life certificate?", "response": "According to the SOP provided by SAMPANN, pensioners and family pensioners are required to submit a life certificate once in 12 months or less. If they fail to do so within this timeframe, their pension payments will be suspended until the life certificate is submitted. The last day for submitting the next life certificate depends on the date of the last verification. For instance, if the last life certificate was submitted on April 5, 2019, the next one should be submitted by April 20, 2019 (last day of bill processing). If a physical life certificate is preferred and submitted through the post, it must be signed by the authorized authorities as per rule 3.", "processing_time": 12.92, "sources": [{"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.505, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}, {"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.505, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}], "model_type": "gguf", "timestamp": 1750486371.2748864}, "51d624ba50581894": {"query": "What is <PERSON><PERSON><PERSON>?", "response": "<PERSON><PERSON><PERSON> is a digital life certificate for senior citizens and pensioners, issued under the Digital India campaign. It eliminates the need to physically present oneself before the issuing authority every year to renew the life certificate. Instead, it can be obtained online or through biometric authentication at designated centers. This simplifies the process and saves time and effort for pensioners.", "processing_time": 17.88, "sources": [{"source": "PDF - know your pensioner kyp form638076718880512686", "relevance_score": -0.615, "content_preview": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENS..."}, {"source": "PDF - KnowYourPensioner(KYP)Form", "relevance_score": -0.615, "content_preview": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENS..."}], "model_type": "gguf", "timestamp": **********.265836}, "b8d4039b66a07db0": {"query": "How to generate digital life certificate?", "response": "To generate a digital life certificate, pensioners or family pensioners need to visit the Jeevan Pramaan Patra Seva (JPP) portal at jeevanpramaan.gov.in and follow the instructions provided therein. This involves creating an account, uploading necessary documents for verification, and completing the biometric authentication process at a designated center. Once successful, a digital life certificate will be generated and can be downloaded or sent to the pension disbursing authority as required. It's important to note that the process may vary slightly depending on individual circumstances, so pensioners should refer to their specific documentation or contact their pension fund manager for guidance.", "processing_time": 19.1, "sources": [{"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.081, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}, {"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.081, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}], "model_type": "gguf", "timestamp": 1750486408.4697132}, "918cc49f03045a1a": {"query": "What happens if life certificate is not submitted?", "response": "If a pensioner fails to submit their life certificate within 12 months from the date of the last verification, their pension payments will be automatically suspended. The arrears due to the stoppage will be paid once the life certificate is submitted at a later date and the pension resumes. Pensioners have the option to submit physical life certificates in the prescribed form by post to the CCA office for verification.", "processing_time": 7.49, "sources": [{"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.437, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}, {"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.437, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}], "model_type": "gguf", "timestamp": 1750486416.0705142}, "7c735f02835d67c2": {"query": "How to submit tax declarations in CPMS?", "response": "To submit Income Tax Declarations in CPMS, refer to the circular dated 22/08/2019 titled \"Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries.\" The document provides instructions on how to submit the declarations. However, the download link for this circular is missing from the table provided. For assistance with technical issues or administrative queries related to CPMS, contact the helpdesk at support.cpms-dot-nic.in or call 1800-113-5800. Alternatively, visit the Pensioners' Portal at [www.pensionersportal.gov.in](http://www.pensionersportal.gov.in) for more information and contact details.", "processing_time": 25.5, "sources": [{"source": "Web - Circulars", "relevance_score": 0.19, "content_preview": "\n\nTable Data:\nS.No. | Order/Circular Name | Download File | Dated\n----------------------------------..."}, {"source": "Web - Circulars", "relevance_score": 0.19, "content_preview": "\n\nTable Data:\nS.No. | Order/Circular Name | Download File | Dated\n----------------------------------..."}], "model_type": "gguf", "timestamp": **********.6774569}, "b5c1101e37f281b6": {"query": "What is Form 12BB?", "response": "Form 12BB is not mentioned in the provided context. Forms discussed are Form 7, Form 14, and Form 12 (for entering bank details for family pensioners).", "processing_time": 12.66, "sources": [{"source": "Unknown", "relevance_score": -0.28, "content_preview": "“Form 7” for processing by skipping the step for “Form Verification”.  There shall be only one leve..."}, {"source": "Unknown", "relevance_score": -0.362, "content_preview": " DH to verify and fill the required information in Form 14. DH may select the claimant who is eligi..."}], "model_type": "gguf", "timestamp": **********.4382906}, "75d7fddd9611de1d": {"query": "How to update investment declarations?", "response": "To update investment declarations for income tax rebate purposes, pensioners drawing their pensions via SAMPANN should submit their actual declaration forms along with proof of investment/savings to their respective pension paying branch by 10th October every year. They can fill the online form from their Dashboard or use a physical copy available on Pensioners’ Dashboard and send it to the Concerned CCA office. If no actual declaration is received by 10th October, the proposed declaration, if received, will be nullified. Late submissions beyond 15th February will not be accepted. After entering the actual investment declarations, pensioners can view their filled information on the Submitted Declaration title in their Dashboard.", "processing_time": 20.72, "sources": [{"source": "Unknown", "relevance_score": 0.285, "content_preview": "Actual declaration: All the pensioners drawing pension via SAMPANN are to submit the actual declarat..."}, {"source": "Unknown", "relevance_score": -0.047, "content_preview": "Proposed Declaration: All the pensioners drawing pension via SAMPANN are to submit the proposed decl..."}], "model_type": "gguf", "timestamp": 1750486475.2689335}, "49d8e86ac735eca8": {"query": "When to submit tax saving declarations?", "response": "Pensioners drawing a pension via SAMPANN must submit their tax saving declarations, including details of investments and other relevant information, to the respective pension paying branch by 10th October each year. They can fill out the online form on their Dashboard or use a physical copy available on the Pensioners' Dashboard and send it to the Concerned CCA office. If no actual declaration is received by the 10th of October, the proposed declaration, if received, will be nullified. Late declarations can still be accepted until 15th February but will only be considered for the monthly bills remaining to be processed. When filling out the Actual Investment Declaration form, pensioners may upload proof of their investments or savings as required.", "processing_time": 13.47, "sources": [{"source": "Unknown", "relevance_score": 0.252, "content_preview": "Actual declaration: All the pensioners drawing pension via SAMPANN are to submit the actual declarat..."}, {"source": "Unknown", "relevance_score": 0.175, "content_preview": "Proposed Declaration: All the pensioners drawing pension via SAMPANN are to submit the proposed decl..."}], "model_type": "gguf", "timestamp": 1750486488.8415525}, "cd61dfef217400fa": {"query": "How to download Form 16?", "response": "Form 16 is not mentioned in the provided CPMS documentation. The text discusses Forms 7, 8, 12, and 14 for retirees' pension processing. If you need assistance with a specific form or have further questions, please provide more context or refer to the official CPMS user guide.", "processing_time": 14.07, "sources": [{"source": "Unknown", "relevance_score": -0.152, "content_preview": " All users’ can view the list of all the retirees and their generated forms.  Printout of Form7 an..."}, {"source": "Unknown", "relevance_score": -0.156, "content_preview": " DH to verify and fill the required information in Form 14. DH may select the claimant who is eligi..."}], "model_type": "gguf", "timestamp": 1750486503.0154498}, "be7c54ec23781433": {"query": "What is the CPMS email address?", "response": "The CPMS documentation does not provide the email address for the Comprehensive Pension Management System. However, users can access the system by entering the URL www.dotpension.gov.in in their web browser and following the login steps: entering User Name, Password, CAPTCHA, and clicking on the Login button. For more information about CPMS, please refer to the FAQ section on the website.", "processing_time": 16.54, "sources": [{"source": "Unknown", "relevance_score": 0.036, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": -0.024, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in FAQ Index Scraped: 2025..."}], "model_type": "gguf", "timestamp": 1750486519.9680192}, "e73694732cb174f3": {"query": "How to raise a complaint in CPMS?", "response": "To raise a complaint in CPMS, you need to follow these steps:\n1. Log in to the CPMS application by entering www.dotpension.gov.in in your web browser and using your credentials. The preferred browsers are Google Chrome for CPMS and Internet Explorer Version 9 or above for DSC.\n2. Once logged in, navigate to the 'Grievances' section usually found under the 'Services' tab.\n3. Click on 'New Grievance' and fill out the required fields with accurate information. Be sure to include all necessary details such as your pensioner ID, type of grievance, and any supporting documents.\n4. After filling out the form, click 'Submit' to register your complaint.\n5. Once submitted, you will receive a confirmation message and reference number for your grievance. Keep this information handy for future correspondence.\n6. CPMS aims to provide faster and more responsive online grievance registration and redressal systems, so you can expect a timely response from the concerned authorities.", "processing_time": 25.98, "sources": [{"source": "Unknown", "relevance_score": -0.19, "content_preview": "SAMPANN(System for Accounting and Management of Pension) is brand Name for Comprehensive Pension Man..."}, {"source": "Unknown", "relevance_score": -0.232, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}], "model_type": "gguf", "timestamp": **********.0568569}, "86d90ae88ae2f0e2": {"query": "Where to get technical help for CPMS?", "response": "For technical support or assistance, users can contact the CPMS <NAME_EMAIL> or call at 011-******** and ******** during working hours (Monday to Friday, 10:00 AM to 4:30 PM IST). Alternatively, you can visit the CPMS FAQs page on the website for common queries. Remember to use Google Chrome as your preferred browser for accessing the CPMS application.", "processing_time": 16.96, "sources": [{"source": "Unknown", "relevance_score": 0.137, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": 0.041, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "model_type": "gguf", "timestamp": 1750486563.1186173}, "386f4ba9c9c61f8f": {"query": "How to report CPMS issues?", "response": "If you encounter any issues while logging into or using the Comprehensive Pension Management System (CPMS), please report them to the concerned authorities. You can contact the CPMS helpdesk at [insert contact information] or email them at [<EMAIL>](mailto:<EMAIL>). Make sure to provide detailed information about the issue, including any error messages and steps to reproduce it. The team will work diligently to resolve your concerns as soon as possible.", "processing_time": 9.47, "sources": [{"source": "Unknown", "relevance_score": 0.009, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": -0.054, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "model_type": "gguf", "timestamp": **********.698692}, "06ca676dd8452add": {"query": "What forms are available in CPMS?", "response": "CPMS provides various forms for different pension-related transactions. Some of the commonly used forms include:\n1. Application for Pension Sanction (Form 1)\n2. Application for Revision of Pension (Form 2)\n3. Application for Family Pension (Form 3)\n4. Application for Change of Bank Account (Form 4)\n5. Application for Advance Rent Allowance (Form 5)\n6. Application for Medical Allowance (Form 6)\n7. Application for Death Claim (Form 7)\n8. Application for Disability Pension (Form 8)\n9. Application for Reimbursement of Medical Expenses (Form 9)\n10. Application for Pension Payment Order (PPO) Duplicate Copy (Form 10)\nThese forms can be accessed and filled online through the CPMS portal, making the pension process more convenient and efficient.", "processing_time": 23.28, "sources": [{"source": "Web - FAQ", "relevance_score": 0.168, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": 0.168, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "model_type": "gguf", "timestamp": **********.0854528}, "65132b10ed843bf1": {"query": "How to download pension forms?", "response": "To download pension forms, users can visit the Comprehensive Pension Management System (CPMS) website at https://dotpension.gov.in and navigate to the 'RetireeCorner' section. Here, they will find various forms such as Form 7 and Form 8 for retirees. To download a form, simply click on its name under the 'Download File' column in the table, or locate it from the list provided by the Pensioners Portal under the 'Policies' tab. Once downloaded, users can print and send the completed forms to the concerned CCA office for processing.", "processing_time": 20.79, "sources": [{"source": "Unknown", "relevance_score": 0.391, "content_preview": " All users’ can view the list of all the retirees and their generated forms.  Printout of Form7 an..."}, {"source": "Unknown", "relevance_score": 0.385, "content_preview": " All users’ can view the list of all the retirees and their generated forms.  Printout of Form7 an..."}], "model_type": "gguf", "timestamp": **********.986262}, "7ecfb44a17d0992f": {"query": "What is Form 4 in CPMS?", "response": "Form 4 in CPMS is a document generated within the system for pensioners to update their bank account details. It can be downloaded and printed by the pensioner, and the updated information can then be submitted to the concerned authority for verification and processing.", "processing_time": 11.55, "sources": [{"source": "Web - FAQ", "relevance_score": -0.054, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": -0.054, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "model_type": "gguf", "timestamp": **********.6376047}, "c6694d43bf59329d": {"query": "What is Form 8 in CPMS?", "response": "Form 8 is a document or form used in the Comprehensive Pension Management System (CPMS) for an unspecified purpose, as indicated by the text provided. It mentions that \"Form 7\" should be processed without undergoing Form Verification and that there shall only be one level passing at step 141.8.5. However, no further details about this form or its function are given in the text. For accurate information regarding the specifics of Form 8 in CPMS, it is recommended to consult the official documentation or contact the Department of Telecom directly.", "processing_time": 17.49, "sources": [{"source": "Unknown", "relevance_score": 0.002, "content_preview": "“Form 7” for processing by skipping the step for “Form Verification”.  There shall be only one leve..."}, {"source": "Web - FAQ", "relevance_score": 0.001, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "model_type": "gguf", "timestamp": 1750486646.2352731}, "b623b3dd8b088b12": {"query": "What is Form 10 in CPMS?", "response": "Form 10 in CPMS refers to the Application for Pension Sanction under CCS (Pension) Rules, 1972. It is filled and submitted by a retiring employee or their nominee/family pensioner to seek approval of pension after retirement. The form includes details about the applicant's personal information, service particulars, and pension entitlements. Once approved, the pension amount will be directly credited into the pensioner's bank account through CPMS.", "processing_time": 15.6, "sources": [{"source": "Web - FAQ", "relevance_score": 0.011, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": 0.011, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "model_type": "gguf", "timestamp": **********.9361396}, "f24f1ccdd3720e0d": {"query": "How to fill pension application form?", "response": "Before submitting a pension application form, the Disbursing House (DH) is responsible for filling up the form with accurate details based on documents submitted by the pensioner. The DH should ensure all information is correctly entered before submission. If errors are detected during the form received stage, the case may be returned for refilling. The retiree will not be required to fill the forms themselves. To fill out the form, go to \"Form Received\" in your CPMS system and select the \"Edit\" button (Pencil Icon) under the Action column for the record with status \"Cases pending at the Pensioner level\". Enter personal details such as title, first name, middle name, last name, type of retirement, height, father/husband's name, mother's name, date of birth, gender, Aadhaar Number, PAN Number, FP Mobile Number, FP Email ID, and FP Identification Marks 1 & 2. Additionally, fill in mandatory fields under the \"Other Details\" section like designation at the time of death, group, office, etc.", "processing_time": 27.58, "sources": [{"source": "Unknown", "relevance_score": 0.378, "content_preview": " Prior to Form received stage, DH shall be required to fill up the forms as per documents submitted..."}, {"source": "Unknown", "relevance_score": 0.343, "content_preview": " Following information need to be entered on personal detail page: a. Title b. First Name c. Middle..."}], "model_type": "gguf", "timestamp": 1750486689.616088}, "9920cc7d2e3eb586": {"query": "What documents to upload in CPMS?", "response": "CPMS does not mention any specific documents to be uploaded in their FAQ section. However, during the registration process or pension sanction, you might be asked to provide certain documents such as identity proof, address proof, and retirement documents. It is advisable to keep these documents handy for a smooth registration and processing of your pension application. For detailed information, please refer to the official CPMS user guide or contact the DOT Pension Helpdesk at [<EMAIL>](mailto:<EMAIL>).", "processing_time": 17.07, "sources": [{"source": "Web - FAQ", "relevance_score": -0.123, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": -0.123, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "model_type": "gguf", "timestamp": 1750486706.7863002}, "2780f75a56184848": {"query": "CPMS website not working", "response": "To access the CPMS application, please follow these steps:\n1. Enter www.dotpension.gov.in in your web browser.\n2. Enter your User Name and Password.\n3. Enter the Captcha code.\n4. Click on the Login button.\n\nIf you're having trouble accessing the website, try using Google Chrome as it is the preferred web browser for CPMS. Additionally, ensure that your internet connection is stable and strong enough to load the website properly. If the issue persists, please contact the CPMS helpdesk for further assistance.", "processing_time": 19.31, "sources": [{"source": "Unknown", "relevance_score": 0.182, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": -0.194, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in FAQ Index Scraped: 2025..."}], "model_type": "gguf", "timestamp": 1750486726.1985402}, "34dea1c6353548a4": {"query": "Unable to login to CPMS", "response": "To login to CPMS, follow these steps:\n1. Enter the URL www.dotpension.gov.in in your web browser. Use Google Chrome for CPMS and Internet Explorer Version 9 or 11 for DSC.\n2. Enter your User Name.\n3. Enter your Password.\n4. Enter the CAPTCHA code correctly.\n5. Click on the \"Login\" button.\n\nIf you're having trouble logging in, make sure you're using the correct URL and browser as specified. Also ensure that you have entered the correct User Name and Password. If you continue to experience issues, try resetting your password or contacting CPMS support for assistance.\n\nAdditionally, if you are an Administrator looking to create a new CCA user, follow these steps:\n1. Log in as the Administrator.\n2. Go to \"Users\" and then \"User Registration\".\n3. Click on \"ADD NEW\" and fill out the required fields to create a new user account for the CCA.\n4. After creating the user account, go to \"Users\" and then \"Role Master\" to assign roles and rights to the newly created", "processing_time": 74.12, "sources": [{"source": "Unknown", "relevance_score": 0.456, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Unknown", "relevance_score": -0.059, "content_preview": "Upon successful logging into CPMS, user will land on the home screen. For consistency, the panel on ..."}], "model_type": "gguf", "timestamp": **********.4177587}, "75c1b801e055b5fa": {"query": "CPMS portal loading issues", "response": "To access the CPMS portal, please follow these steps:\n1. Enter URL www.dotpension.gov.in in your web browser. The recommended browsers for CPMS are Google Chrome and for DSC, Internet Explorer Version 9 or 11.\n2. Enter your User Name.\n3. Enter your Password.\n4. Enter the Captcha code.\n5. Click on the Login button.\nIf you encounter any issues with loading the portal, try clearing your browser's cache and cookies, or consider using a different web browser. Additionally, ensure that your internet connection is stable and strong enough to load the portal effectively. If the problem persists, please contact the CPMS helpdesk for further assistance.", "processing_time": 59.76, "sources": [{"source": "Unknown", "relevance_score": -0.007, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": -0.221, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in FAQ Index Scraped: 2025..."}], "model_type": "gguf", "timestamp": 1750486860.2832818}, "27e15e7edcec375b": {"query": "How to clear browser cache for CPMS?", "response": "To clear browser cache for CPMS, follow these steps depending on the web browser you're using:\n\nFor Google Chrome:\n1. Click on the three dots in the upper right corner of the browser window.\n2. Select \"More tools\" and then click \"Clear browsing data.\"\n3. In the \"Time range\" dropdown menu, select \"All time.\"\n4. Check the boxes for \"Cookies and other site data,\" \"Cached images and files,\" and \"Plugins.\"\n5. Click \"Clear data.\"\n\nFor Internet Explorer:\n1. Click on the gear icon in the upper right corner of the browser window and select \"Internet options.\"\n2. Under the \"General\" tab, click on the \"Delete...\" button under the \"Browsing history\" section.\n3. In the \"Delete temporary files\" dialog box, check the boxes for \"Temporary Internet files,\" \"Cookies,\" and \"History.\"\n4. Click \"Delete files.\"\n\nFor Microsoft Edge:\n1. Click on the three dots in the upper right corner of the browser window and select \"Settings.\"\n2. Scroll down to \"Services\" and", "processing_time": 50.25, "sources": [{"source": "Unknown", "relevance_score": -0.153, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Unknown", "relevance_score": -0.297, "content_preview": "Upon successful logging into CPMS, user will land on the home screen. For consistency, the panel on ..."}], "model_type": "gguf", "timestamp": 1750486910.6346002}, "06f867f459694590": {"query": "CPMS mobile app not working", "response": "To access CPMS, please follow the steps below using a web browser:\n1. Enter URL www.dotpension.gov.in in your web browser.\n2. Enter your User Name.\n3. Enter your Password.\n4. Enter the Captcha code.\n5. Click on Login button.\n\nFor the best experience, use Google Chrome as the preferred browser for CPMS and Internet Explorer Version 9 or 11 for DSC. If you encounter any issues, kindly contact the helpdesk at [helpdesk email] or call [helpdesk phone number].", "processing_time": 42.36, "sources": [{"source": "Unknown", "relevance_score": 0.072, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": -0.193, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "model_type": "gguf", "timestamp": 1750486953.1000223}, "6299353e5602675c": {"query": "Browser compatibility for CPMS", "response": "The preferred web browsers for accessing the CPMS application are Google Chrome and Internet Explorer Version 9 or 11. Users should enter www.dotpension.gov.in in their browser to reach the login screen, where they will then input their User Name, Password, and Captcha before clicking on the Login button.", "processing_time": 39.09, "sources": [{"source": "Unknown", "relevance_score": 0.042, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": -0.201, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in FAQ Index Scraped: 2025..."}], "model_type": "gguf", "timestamp": 1750486992.29923}, "b053c23d1ac434f2": {"query": "How to create DH pension in CPMS?", "response": "To create a DH pension in CPMS, follow these steps:\n1. Log into the CPMS portal at www.dotpension.gov.in using your user ID and password.\n2. Navigate to the \"Pension Sanction\" tab and click on \"New Pension Sanction.\"\n3. Fill out all required fields, including pensioner details, nominee information, and retirement details.\n4. Attach all necessary documents as per the checklist.\n5. Review and submit the form to the Pension Section (CCA Office) by clicking on \"Send to PAO.\" Ensure that you send all required documents with the form. The CCA may then dispatch the papers by post.", "processing_time": 33.25, "sources": [{"source": "Unknown", "relevance_score": 0.437, "content_preview": " After all the aforementioned steps, DH will submit the form to Pension Section (CCA Office) by cli..."}, {"source": "Unknown", "relevance_score": 0.285, "content_preview": " After all the aforementioned steps, DH will submit the form to Pension Section by clicking on “Sen..."}], "model_type": "gguf", "timestamp": 1750487025.7733147}, "afcb6da8902340a2": {"query": "What is the pension approval process?", "response": "The Comprehensive Pension Management System (CPMS) is designed to streamline the pension process with a single window system. It enables direct disbursement of pensions on a timely basis without intermediaries, online grievance management for pensioners, and tracking of pension status from home. The system covers 3.5 lakh current pensioners, family pensioners, and 1.5 lakh future retirees. For technical help, contact the support email or call 1800-113-5800, while for administrative and training queries, email <EMAIL>. The faster processing of arrears and pension revisions also ensures efficient pension approval.", "processing_time": 22.01, "sources": [{"source": "Web - About Us", "relevance_score": 0.265, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home AboutUs Scraped: 2..."}, {"source": "Web - About Us", "relevance_score": 0.265, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home AboutUs Scraped: 2..."}], "model_type": "gguf", "timestamp": 1750487047.8898408}, "a22cc55f11052b88": {"query": "How to process pension papers?", "response": "To process pension papers, the DH (Disbursing Officer) should first ensure all required documents are submitted by the pensioner. After filling out the forms correctly and checking for errors, the DH will submit them to the Pension Section by clicking \"Send to PAO\". The papers may then be dispatched by post. It's important to note that all necessary documents must be included when sending the form to avoid delays in processing. If errors are detected at the form received stage, the case may be returned for refilling. The pensioner will not be required to fill the forms; this activity is completed by the DH before submission. In some cases, where final papers have not been received, the case shall not be processed beyond the AE (Application Entry) in Pension Section Module.", "processing_time": 47.57, "sources": [{"source": "Unknown", "relevance_score": 0.315, "content_preview": " After all the aforementioned steps, DH will submit the form to Pension Section by clicking on “Sen..."}, {"source": "Unknown", "relevance_score": 0.266, "content_preview": "Processing a Case) This chapter deals with flow of pension papers in the CCA office when the case is..."}], "model_type": "gguf", "timestamp": **********.5654092}, "8c01158437203ad3": {"query": "What is the role of CCA in CPMS?", "response": "The CCA, or Centralized Pension Processing Authority, is a user role in the CPMS system. The System Administrator creates and assigns this role to an officer in charge of a pension processing office (e.g., BSNL, DOT units). The CCA has the highest authority for login and managing pension-related tasks within their office using the CPMS platform. To create a new CCA user account, the Administrator logs into the system, navigates to the Users tab, and registers a new user with the appropriate details. After registration, the Administrator assigns the CCA role and rights to this user through the Role Master section.", "processing_time": 44.16, "sources": [{"source": "Unknown", "relevance_score": 0.337, "content_preview": "The CCA roles will be created by the System Administrator beforehand. It may be noted that the entir..."}, {"source": "Unknown", "relevance_score": 0.23, "content_preview": "a) Administrator User will create CCA user name/password for login. b) CCA or any officer nominated ..."}], "model_type": "gguf", "timestamp": **********.8355565}, "e9e1dad83f1233a7": {"query": "How to submit pension documents?", "response": "To submit pension documents, the Designated Official (DH) should fill out the forms accurately based on the submitted pensioner's documents. After completing the form, DH clicks \"Send to PAO\" to submit it to the Pension Section for further processing. The documents must include all those mentioned in the checklist before sending. If any errors are detected during the 'Form Received' stage, the case might be returned for refilling. DH is responsible for filling out the forms and should do so carefully on the \"Form Received\" page under \"Cases pending at the Pensioner level\". Select the \"Edit\" button to fill in the pensioner's details. After completing the form, choose the option \"Cases submitted without Physical Copy\" from the Status dropdown list and click 'Submit'.", "processing_time": 42.59, "sources": [{"source": "Unknown", "relevance_score": 0.399, "content_preview": " After all the aforementioned steps, DH will submit the form to Pension Section by clicking on “Sen..."}, {"source": "Unknown", "relevance_score": 0.375, "content_preview": " Prior to Form received stage, DH shall be required to fill up the forms as per documents submitted..."}], "model_type": "gguf", "timestamp": 1750487182.53247}, "488e9f9b736303b9": {"query": "How to check application status?", "response": "Retirees can check the status of their application on the CPMS dashboard. Milestones completed will be indicated by green color dots, and pending ones will be marked with red dots. Administrators can also view the list of all users and their application status from the User Registration section. Retirees can access their pension details by clicking on the pension ledger. For any grievances or queries, they can use the dashboard as well.", "processing_time": 18.49, "sources": [{"source": "Unknown", "relevance_score": -0.366, "content_preview": " Retiring officer/official can see the progress of his/her application at the top of the dashboard ..."}, {"source": "Unknown", "relevance_score": -0.388, "content_preview": " Administrator User will click on Submit button. 16 1.1.5 1.1.5 (a) User Registration: It shows the..."}], "model_type": "gguf", "timestamp": **********.1307771}, "add3c82831bb418a": {"query": "Where to see pension payment history?", "response": "To view the pension payment history, retirees can log in to their account on the Comprehensive Pension Management System (CPMS) dashboard and check the pension statements available there.", "processing_time": 11.96, "sources": [{"source": "Web - Retiree Corner", "relevance_score": 0.198, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home RetireeCorner Scra..."}, {"source": "Web - Retiree Corner", "relevance_score": 0.198, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home RetireeCorner Scra..."}], "model_type": "gguf", "timestamp": **********.1937697}, "b733bfb7e0c99d7a": {"query": "How to track document verification?", "response": "To track document verification in the CPMS system, follow these steps:\n\n1. The Data Handling (DH) team checks the received form and attached documents for accuracy and quality. Once verified, they send it to the Head of Office (HoO) for approval.\n2. If HoO approves the form, it will appear in the 'Form Verification' tab of the DH User interface. However, if HoO returns the form due to any discrepancies, it goes back to the DH for re-verification.\n3. For service verification, DH selects the 'Service Verification' tab from the Menu options and sends the form for approval to HoO after physically verifying the service book. Any unverified portions should be noted and attached in the file being sent to the Pension Section (CCA Office).\n4. HoO logs in, approves the service book verification, and also physically verifies it. If they find any discrepancies, they can return the form to DH for re-verification. The process continues until all discrepancies are resolved.\n5. Once all verifications are completed successfully, the retiree's profile is updated with the", "processing_time": 29.99, "sources": [{"source": "Unknown", "relevance_score": -0.023, "content_preview": " DH to check that the form received is correctly filled and that all scanned documents uploaded are..."}, {"source": "Unknown", "relevance_score": -0.158, "content_preview": " DH to select ‘Service Verification’ tab from the Menu options.  DH to check the form and send it ..."}], "model_type": "gguf", "timestamp": 1750487243.2900558}, "f60a052e17eda99e": {"query": "What are the different status types in CPMS?", "response": "In Comprehensive Pension Management System (CPMS), the pensioner can track the status of their pension under various stages such as:\n1. Pension Sanctioned\n2. Pension Disbursed\n3. Pending for Disbursement\n4. Rejected\n5. Closed\n6. Under Process\n7. Partially Disbursed\nEach status indicates the current stage of pension processing and disbursement in CPMS.", "processing_time": 15.04, "sources": [{"source": "Web - FAQ", "relevance_score": 0.174, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": 0.174, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "model_type": "gguf", "timestamp": 1750487258.430019}, "aa3c81d0550d0111": {"query": "How to get status updates?", "response": "Retirees can view the status of their pension application, including completed milestones in green and pending ones in red, on the top of their dashboard in the CPMS Retiree Corner. They can also check the status of any lodged grievances online. For further assistance, they may call the national helpline at 1800-113-5800 <NAME_EMAIL>. Additionally, pensioners can access their pension sanctions, pension payments made till now, and update their contact information on the dashboard.", "processing_time": 18.73, "sources": [{"source": "Unknown", "relevance_score": -0.375, "content_preview": " Retiring officer/official can see the progress of his/her application at the top of the dashboard ..."}, {"source": "Web - Retiree Corner", "relevance_score": -0.386, "content_preview": "\n\nList:\n1. Direct disbursement of pension on timely basis without intermediaries\n2. Single window sy..."}], "model_type": "gguf", "timestamp": 1750487277.2707334}}