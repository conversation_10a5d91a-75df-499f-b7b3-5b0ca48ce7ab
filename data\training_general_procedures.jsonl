{"instruction": "What is CPMS?", "input": "", "output": "Comprehensive Pension Management System (CPMS) ACTUAL DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 10th October every year) Name of the pensioner : P. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under the Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank (...", "source": "PDF - ActualDeclaration"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Comprehensive Pension Management System (CPMS) ACTUAL DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 10th October every year) Name of the pensioner : P. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under the Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank (...", "source": "PDF - ActualDeclaration"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Comprehensive Pension Management System (CPMS) ACTUAL DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 10th October every year) Name of the pensioner : P. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under the Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank (...", "source": "PDF - ActualDeclaration"}
{"instruction": "When is pension credited?", "input": "", "output": "Comprehensive Pension Management System (CPMS) ACTUAL DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 10th October every year) Name of the pensioner : P. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under the Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank (...", "source": "PDF - ActualDeclaration"}
{"instruction": "How is pension paid?", "input": "", "output": "Comprehensive Pension Management System (CPMS) ACTUAL DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 10th October every year) Name of the pensioner : P. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under the Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank (...", "source": "PDF - ActualDeclaration"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Comprehensive Pension Management System (CPMS) ACTUAL DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 10th October every year) Name of the pensioner : P. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under the Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank (...", "source": "PDF - ActualDeclaration"}
{"instruction": "How to change bank account?", "input": "", "output": "INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under the Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank ( 5 years) : NSC interest reinvested : Housing Loan principal repaid : Life Insurance premium paid : ULIP investment approved for tax rebate : 2019 -Comprehensive Pension Management System (CPMS) Comprehensive Pension Ma...", "source": "PDF - ActualDeclaration"}
{"instruction": "Can I change my bank details?", "input": "", "output": "INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under the Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank ( 5 years) : NSC interest reinvested : Housing Loan principal repaid : Life Insurance premium paid : ULIP investment approved for tax rebate : 2019 -Comprehensive Pension Management System (CPMS) Comprehensive Pension Ma...", "source": "PDF - ActualDeclaration"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under the Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank ( 5 years) : NSC interest reinvested : Housing Loan principal repaid : Life Insurance premium paid : ULIP investment approved for tax rebate : 2019 -Comprehensive Pension Management System (CPMS) Comprehensive Pension Ma...", "source": "PDF - ActualDeclaration"}
{"instruction": "Is TDS deducted from pension?", "input": "", "output": "Comprehensive Pension Management System (CPMS) ACTUAL DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 10th October every year) Name of the pensioner : P. ) (i) Income from Previous Other Employer(s) : (ii) Tax Deducted by Previous Other Employer(s) : (iii) Income from Other Sources : (iv) Income from House Property (Income Loss): Total : B. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : P...", "source": "PDF - ActualDeclaration"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "Comprehensive Pension Management System (CPMS) ACTUAL DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 10th October every year) Name of the pensioner : P. ) (i) Income from Previous Other Employer(s) : (ii) Tax Deducted by Previous Other Employer(s) : (iii) Income from Other Sources : (iv) Income from House Property (Income Loss): Total : B. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : P...", "source": "PDF - ActualDeclaration"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "Here are the step-by-step instructions:\n\nComprehensive Pension Management System (CPMS) ACTUAL DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 10th October every year) Name of the pensioner : P. ) (i) Income from Previous Other Employer(s) : (ii) Tax Deducted by Previous Other Employer(s) : (iii) Income from Other Sources : (iv) Income from House Property (Income Loss): Total : B. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : P...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "PDF - ActualDeclaration", "enhanced": true}
{"instruction": "Where to get Form 16?", "input": "", "output": "Comprehensive Pension Management System (CPMS) ACTUAL DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 10th October every year) Name of the pensioner : P. ) (i) Income from Previous Other Employer(s) : (ii) Tax Deducted by Previous Other Employer(s) : (iii) Income from Other Sources : (iv) Income from House Property (Income Loss): Total : B. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : P...", "source": "PDF - ActualDeclaration"}
{"instruction": "When is pension credited?", "input": "", "output": "ANNEXURE 1 Pensioner s letter of Authority and Undertaking To, Date Pr. Sir, I hereby opt to draw my Pension through a Bank Account under the direct disbursement of telecom pension by DOT through SAMPANN. I hereby authorize the bank to receive my monthly Pension on my behalf and credit the same to my account as per particulars given as follows: a", "source": "PDF - Bank undertaking"}
{"instruction": "How is pension paid?", "input": "", "output": "ANNEXURE 1 Pensioner s letter of Authority and Undertaking To, Date Pr. Sir, I hereby opt to draw my Pension through a Bank Account under the direct disbursement of telecom pension by DOT through SAMPANN. I hereby authorize the bank to receive my monthly Pension on my behalf and credit the same to my account as per particulars given as follows: a", "source": "PDF - Bank undertaking"}
{"instruction": "When do I receive my pension?", "input": "", "output": "ANNEXURE 1 Pensioner s letter of Authority and Undertaking To, Date Pr. Sir, I hereby opt to draw my Pension through a Bank Account under the direct disbursement of telecom pension by DOT through SAMPANN. I hereby authorize the bank to receive my monthly Pension on my behalf and credit the same to my account as per particulars given as follows: a", "source": "PDF - Bank undertaking"}
{"instruction": "How to apply for family pension?", "input": "", "output": "Address of the Pensioner : Family Pensioners only:- 5. Name of the Family Pensioner : 2", "source": "PDF - Bank undertaking"}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "Address of the Pensioner : Family Pensioners only:- 5. Name of the Family Pensioner : 2", "source": "PDF - Bank undertaking"}
{"instruction": "When does family pension start?", "input": "", "output": "Address of the Pensioner : Family Pensioners only:- 5. Name of the Family Pensioner : 2", "source": "PDF - Bank undertaking"}
{"instruction": "How to change bank account?", "input": "", "output": "Sir, I hereby opt to draw my Pension through a Bank Account under the direct disbursement of telecom pension by DOT through SAMPANN. I hereby authorize the bank to receive my monthly Pension on my behalf and credit the same to my account as per particulars given as follows: a. Name of the Bank b", "source": "PDF - Bank undertaking"}
{"instruction": "Can I change my bank details?", "input": "", "output": "Sir, I hereby opt to draw my Pension through a Bank Account under the direct disbursement of telecom pension by DOT through SAMPANN. I hereby authorize the bank to receive my monthly Pension on my behalf and credit the same to my account as per particulars given as follows: a. Name of the Bank b", "source": "PDF - Bank undertaking"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "Sir, I hereby opt to draw my Pension through a Bank Account under the direct disbursement of telecom pension by DOT through SAMPANN. I hereby authorize the bank to receive my monthly Pension on my behalf and credit the same to my account as per particulars given as follows: a. Name of the Bank b", "source": "PDF - Bank undertaking"}
{"instruction": "What is CPMS?", "input": "", "output": "An initiative under Department of Telecommunications(DoT), and developed by the Controller General of Communication Accounts(CGCA), the Comprehensive Pension Management System or SAMPANN is an integrated Pension application for telecom pensioners. cpms-dot gov. Moreover, the pensioner can also view the monthly statements for pension and gratuity slip in his account dashboard in the CPMS website at dotpension", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "An initiative under Department of Telecommunications(DoT), and developed by the Controller General of Communication Accounts(CGCA), the Comprehensive Pension Management System or SAMPANN is an integrated Pension application for telecom pensioners. cpms-dot gov. Moreover, the pensioner can also view the monthly statements for pension and gratuity slip in his account dashboard in the CPMS website at dotpension", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "An initiative under Department of Telecommunications(DoT), and developed by the Controller General of Communication Accounts(CGCA), the Comprehensive Pension Management System or SAMPANN is an integrated Pension application for telecom pensioners. cpms-dot gov. Moreover, the pensioner can also view the monthly statements for pension and gratuity slip in his account dashboard in the CPMS website at dotpension", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "What will be requirements of uploading the scanned photographs and signature in the CPMS? Reply: The following Do s and Don ts must be fulfilled by the pensioner in order to upload the scanned photographs and pictures on the CPMS portal which is: - Dos DON Ts Paste upload joint colour photograph (or Do not paste or upload black and white separate photographs) in the box meant for photograph. What if the pensioner loses the copy of the PPO? Reply: The Pensioner can download the same from his her ...", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "What will be requirements of uploading the scanned photographs and signature in the CPMS? Reply: The following Do s and Don ts must be fulfilled by the pensioner in order to upload the scanned photographs and pictures on the CPMS portal which is: - Dos DON Ts Paste upload joint colour photograph (or Do not paste or upload black and white separate photographs) in the box meant for photograph. What if the pensioner loses the copy of the PPO? Reply: The Pensioner can download the same from his her ...", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "Here are the step-by-step instructions:\n\nWhat will be requirements of uploading the scanned photographs and signature in the CPMS? Reply: The following Do s and Don ts must be fulfilled by the pensioner in order to upload the scanned photographs and pictures on the CPMS portal which is: - Dos DON Ts Paste upload joint colour photograph (or Do not paste or upload black and white separate photographs) in the box meant for photograph. What if the pensioner loses the copy of the PPO? Reply: The Pensioner can download the same from his her ...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "PDF - CPMS-FAQForPensioners", "enhanced": true}
{"instruction": "What is the CPMS website?", "input": "", "output": "What will be requirements of uploading the scanned photographs and signature in the CPMS? Reply: The following Do s and Don ts must be fulfilled by the pensioner in order to upload the scanned photographs and pictures on the CPMS portal which is: - Dos DON Ts Paste upload joint colour photograph (or Do not paste or upload black and white separate photographs) in the box meant for photograph. What if the pensioner loses the copy of the PPO? Reply: The Pensioner can download the same from his her ...", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "When is pension credited?", "input": "", "output": "An initiative under Department of Telecommunications(DoT), and developed by the Controller General of Communication Accounts(CGCA), the Comprehensive Pension Management System or SAMPANN is an integrated Pension application for telecom pensioners. This will provide an integrated approach with online facilities to its pensioners to make pension management more convenient. What are the contact points available to the Pensioner? Reply: The following contact points will be available to the pensioner...", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "How is pension paid?", "input": "", "output": "An initiative under Department of Telecommunications(DoT), and developed by the Controller General of Communication Accounts(CGCA), the Comprehensive Pension Management System or SAMPANN is an integrated Pension application for telecom pensioners. This will provide an integrated approach with online facilities to its pensioners to make pension management more convenient. What are the contact points available to the Pensioner? Reply: The following contact points will be available to the pensioner...", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "When do I receive my pension?", "input": "", "output": "An initiative under Department of Telecommunications(DoT), and developed by the Controller General of Communication Accounts(CGCA), the Comprehensive Pension Management System or SAMPANN is an integrated Pension application for telecom pensioners. This will provide an integrated approach with online facilities to its pensioners to make pension management more convenient. What are the contact points available to the Pensioner? Reply: The following contact points will be available to the pensioner...", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "How to lodge a grievance?", "input": "", "output": "What are the contact points available to the Pensioner? Reply: The following contact points will be available to the pensioners in case he she faces any trouble or has any grievance. - All India Toll-Free Helpline no. in Sampann Mobile app Online grievance redressal facility through the pensioners account in dotpension", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "What is the helpline number?", "input": "", "output": "What are the contact points available to the Pensioner? Reply: The following contact points will be available to the pensioners in case he she faces any trouble or has any grievance. - All India Toll-Free Helpline no. in Sampann Mobile app Online grievance redressal facility through the pensioners account in dotpension", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "How to contact support?", "input": "", "output": "What are the contact points available to the Pensioner? Reply: The following contact points will be available to the pensioners in case he she faces any trouble or has any grievance. - All India Toll-Free Helpline no. in Sampann Mobile app Online grievance redressal facility through the pensioners account in dotpension", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "What is PPO?", "input": "", "output": "For submission of DLC, the pensioner requires to be physically present to facilitate Biometric verification, and provide the PPO Mobile number Aadhaar Number at the CCAs Pensioners Service Centre Banks HPOs CSCs Aadhaar centers for filling online in the following format. (Name of CCA which has issued the PPO) Step 4 Modes for submission of Life certificates: Digitally- The Digital Life Certificate will travel online and is not required to be physically submitted. In the case of Family Pension an...", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "How to get PPO?", "input": "", "output": "For submission of DLC, the pensioner requires to be physically present to facilitate Biometric verification, and provide the PPO Mobile number Aadhaar Number at the CCAs Pensioners Service Centre Banks HPOs CSCs Aadhaar centers for filling online in the following format. (Name of CCA which has issued the PPO) Step 4 Modes for submission of Life certificates: Digitally- The Digital Life Certificate will travel online and is not required to be physically submitted. In the case of Family Pension an...", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "For submission of DLC, the pensioner requires to be physically present to facilitate Biometric verification, and provide the PPO Mobile number Aadhaar Number at the CCAs Pensioners Service Centre Banks HPOs CSCs Aadhaar centers for filling online in the following format. (Name of CCA which has issued the PPO) Step 4 Modes for submission of Life certificates: Digitally- The Digital Life Certificate will travel online and is not required to be physically submitted. In the case of Family Pension an...", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "How to apply for family pension?", "input": "", "output": "In the case of Family Pension and in the event of death of the Family Pensioner Pensioner which office should be the dependant approach? Reply: In the event of the death of the Pensioner Family pensioner, if the name of claimant is mentioned in the PPO or any revised Pension authority issued by the CCA office, then the claimant need not present himself herself to the O o Pr. Death certificate of pensioner 2. STEP 2 Step 3: Name of pensioner: Name of claimant PPO Number: same as earlier PPO numbe...", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "In the case of Family Pension and in the event of death of the Family Pensioner Pensioner which office should be the dependant approach? Reply: In the event of the death of the Pensioner Family pensioner, if the name of claimant is mentioned in the PPO or any revised Pension authority issued by the CCA office, then the claimant need not present himself herself to the O o Pr. Death certificate of pensioner 2. STEP 2 Step 3: Name of pensioner: Name of claimant PPO Number: same as earlier PPO numbe...", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "When does family pension start?", "input": "", "output": "In the case of Family Pension and in the event of death of the Family Pensioner Pensioner which office should be the dependant approach? Reply: In the event of the death of the Pensioner Family pensioner, if the name of claimant is mentioned in the PPO or any revised Pension authority issued by the CCA office, then the claimant need not present himself herself to the O o Pr. Death certificate of pensioner 2. STEP 2 Step 3: Name of pensioner: Name of claimant PPO Number: same as earlier PPO numbe...", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "How to change bank account?", "input": "", "output": "An initiative under Department of Telecommunications(DoT), and developed by the Controller General of Communication Accounts(CGCA), the Comprehensive Pension Management System or SAMPANN is an integrated Pension application for telecom pensioners. in Sampann Mobile app Online grievance redressal facility through the pensioners account in dotpension. CSCs banks for Life certification only 2", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "Can I change my bank details?", "input": "", "output": "An initiative under Department of Telecommunications(DoT), and developed by the Controller General of Communication Accounts(CGCA), the Comprehensive Pension Management System or SAMPANN is an integrated Pension application for telecom pensioners. in Sampann Mobile app Online grievance redressal facility through the pensioners account in dotpension. CSCs banks for Life certification only 2", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "An initiative under Department of Telecommunications(DoT), and developed by the Controller General of Communication Accounts(CGCA), the Comprehensive Pension Management System or SAMPANN is an integrated Pension application for telecom pensioners. in Sampann Mobile app Online grievance redressal facility through the pensioners account in dotpension. CSCs banks for Life certification only 2", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "Is TDS deducted from pension?", "input": "", "output": "How will Pensioner get the Pension Slip and Gratuity? Reply: Pensioners will continue to receive the Pension Slip on their registered mobile number and e-mail ID indicating basic pension, commuted pension, IDA, Income Tax deduction and pension credited in the registered Bank Account. The new system will provide additional benefits of online services for monitoring DLC, Tax functions, grievances, information etc", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "How will Pensioner get the Pension Slip and Gratuity? Reply: Pensioners will continue to receive the Pension Slip on their registered mobile number and e-mail ID indicating basic pension, commuted pension, IDA, Income Tax deduction and pension credited in the registered Bank Account. The new system will provide additional benefits of online services for monitoring DLC, Tax functions, grievances, information etc", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "Where to get Form 16?", "input": "", "output": "How will Pensioner get the Pension Slip and Gratuity? Reply: Pensioners will continue to receive the Pension Slip on their registered mobile number and e-mail ID indicating basic pension, commuted pension, IDA, Income Tax deduction and pension credited in the registered Bank Account. The new system will provide additional benefits of online services for monitoring DLC, Tax functions, grievances, information etc", "source": "PDF - CPMS-FAQForPensioners"}
{"instruction": "What is CPMS?", "input": "", "output": "Comprehensive Pension Management System User Manual (Version 6. 2) System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 6. 2 Wednesday, August 7, 2019 0 Comprehensive Pension Management System User Manual (Version 6", "source": "PDF - CPMS-UserManual"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Comprehensive Pension Management System User Manual (Version 6. 2) System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 6. 2 Wednesday, August 7, 2019 0 Comprehensive Pension Management System User Manual (Version 6", "source": "PDF - CPMS-UserManual"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Comprehensive Pension Management System User Manual (Version 6. 2) System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 6. 2 Wednesday, August 7, 2019 0 Comprehensive Pension Management System User Manual (Version 6", "source": "PDF - CPMS-UserManual"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "These include digital payments, online receipts, direct payment of GPF for BSNL employees, registration of pensioners through Jeevan Pramaan Portal for Life Certification, etc. 1 Unit wise Login Verticals creation in CPMS. 1 Administrator and CCA login", "source": "PDF - CPMS-UserManual"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "These include digital payments, online receipts, direct payment of GPF for BSNL employees, registration of pensioners through Jeevan Pramaan Portal for Life Certification, etc. 1 Unit wise Login Verticals creation in CPMS. 1 Administrator and CCA login", "source": "PDF - CPMS-UserManual"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "Here are the step-by-step instructions:\n\nThese include digital payments, online receipts, direct payment of GPF for BSNL employees, registration of pensioners through Jeevan Pramaan Portal for Life Certification, etc. 1 Unit wise Login Verticals creation in CPMS. 1 Administrator and CCA login\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "PDF - CPMS-UserManual", "enhanced": true}
{"instruction": "What is the CPMS website?", "input": "", "output": "These include digital payments, online receipts, direct payment of GPF for BSNL employees, registration of pensioners through Jeevan Pramaan Portal for Life Certification, etc. 1 Unit wise Login Verticals creation in CPMS. 1 Administrator and CCA login", "source": "PDF - CPMS-UserManual"}
{"instruction": "When is pension credited?", "input": "", "output": "Comprehensive Pension Management System User Manual (Version 6. 2) System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 6. 2 Wednesday, August 7, 2019 0 Comprehensive Pension Management System User Manual (Version 6", "source": "PDF - CPMS-UserManual"}
{"instruction": "How is pension paid?", "input": "", "output": "Comprehensive Pension Management System User Manual (Version 6. 2) System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 6. 2 Wednesday, August 7, 2019 0 Comprehensive Pension Management System User Manual (Version 6", "source": "PDF - CPMS-UserManual"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Comprehensive Pension Management System User Manual (Version 6. 2) System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 6. 2 Wednesday, August 7, 2019 0 Comprehensive Pension Management System User Manual (Version 6", "source": "PDF - CPMS-UserManual"}
{"instruction": "How to lodge a grievance?", "input": "", "output": "These includes delays in disbursement of the first pension due to time taken in physical movement of PPO from sanctioning authorities to banks or to Post Offices, delay in payment of arrears of pension after revisions, wrong disbursement of pension, difficulty and delay in redressal of pensioners grievances, non-refund of excess payment by Banks Post Offices to the Telecom Department, paper based ineffective audit, etc. CCAs CCAs (in the field) for looking after their interests and settling any ...", "source": "PDF - CPMS-UserManual"}
{"instruction": "What is the helpline number?", "input": "", "output": "These includes delays in disbursement of the first pension due to time taken in physical movement of PPO from sanctioning authorities to banks or to Post Offices, delay in payment of arrears of pension after revisions, wrong disbursement of pension, difficulty and delay in redressal of pensioners grievances, non-refund of excess payment by Banks Post Offices to the Telecom Department, paper based ineffective audit, etc. CCAs CCAs (in the field) for looking after their interests and settling any ...", "source": "PDF - CPMS-UserManual"}
{"instruction": "How to contact support?", "input": "", "output": "These includes delays in disbursement of the first pension due to time taken in physical movement of PPO from sanctioning authorities to banks or to Post Offices, delay in payment of arrears of pension after revisions, wrong disbursement of pension, difficulty and delay in redressal of pensioners grievances, non-refund of excess payment by Banks Post Offices to the Telecom Department, paper based ineffective audit, etc. CCAs CCAs (in the field) for looking after their interests and settling any ...", "source": "PDF - CPMS-UserManual"}
{"instruction": "What is PPO?", "input": "", "output": "These includes delays in disbursement of the first pension due to time taken in physical movement of PPO from sanctioning authorities to banks or to Post Offices, delay in payment of arrears of pension after revisions, wrong disbursement of pension, difficulty and delay in redressal of pensioners grievances, non-refund of excess payment by Banks Post Offices to the Telecom Department, paper based ineffective audit, etc. 4 Revision of Pension to FP (No eligible family member mentioned in PPO). 5 ...", "source": "PDF - CPMS-UserManual"}
{"instruction": "How to get PPO?", "input": "", "output": "These includes delays in disbursement of the first pension due to time taken in physical movement of PPO from sanctioning authorities to banks or to Post Offices, delay in payment of arrears of pension after revisions, wrong disbursement of pension, difficulty and delay in redressal of pensioners grievances, non-refund of excess payment by Banks Post Offices to the Telecom Department, paper based ineffective audit, etc. 4 Revision of Pension to FP (No eligible family member mentioned in PPO). 5 ...", "source": "PDF - CPMS-UserManual"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "These includes delays in disbursement of the first pension due to time taken in physical movement of PPO from sanctioning authorities to banks or to Post Offices, delay in payment of arrears of pension after revisions, wrong disbursement of pension, difficulty and delay in redressal of pensioners grievances, non-refund of excess payment by Banks Post Offices to the Telecom Department, paper based ineffective audit, etc. 4 Revision of Pension to FP (No eligible family member mentioned in PPO). 5 ...", "source": "PDF - CPMS-UserManual"}
{"instruction": "How to apply for family pension?", "input": "", "output": "2 Family Pension Case. 2 Service Verification (Family Pension case). 2 Family Pension Case", "source": "PDF - CPMS-UserManual"}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "2 Family Pension Case. 2 Service Verification (Family Pension case). 2 Family Pension Case", "source": "PDF - CPMS-UserManual"}
{"instruction": "When does family pension start?", "input": "", "output": "2 Family Pension Case. 2 Service Verification (Family Pension case). 2 Family Pension Case", "source": "PDF - CPMS-UserManual"}
{"instruction": "How to change bank account?", "input": "", "output": "2) System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 6. 5 lakh DoT and BSNL retirees is being sanctioned and authorized by the Controller of Communication Accounts (CCA) Offices. The pension is thereafter disbursed on commission basis by intermediary Agents-Banks and Post Offices to the pensioners", "source": "PDF - CPMS-UserManual"}
{"instruction": "Can I change my bank details?", "input": "", "output": "2) System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 6. 5 lakh DoT and BSNL retirees is being sanctioned and authorized by the Controller of Communication Accounts (CCA) Offices. The pension is thereafter disbursed on commission basis by intermediary Agents-Banks and Post Offices to the pensioners", "source": "PDF - CPMS-UserManual"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "2) System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 6. 5 lakh DoT and BSNL retirees is being sanctioned and authorized by the Controller of Communication Accounts (CCA) Offices. The pension is thereafter disbursed on commission basis by intermediary Agents-Banks and Post Offices to the pensioners", "source": "PDF - CPMS-UserManual"}
{"instruction": "Is TDS deducted from pension?", "input": "", "output": "2 02-08-2019 Income Tax Module 1 Comprehensive Pension Management System User Manual (Version 6. Income Tax Module. Income Tax Processing", "source": "PDF - CPMS-UserManual"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "2 02-08-2019 Income Tax Module 1 Comprehensive Pension Management System User Manual (Version 6. Income Tax Module. Income Tax Processing", "source": "PDF - CPMS-UserManual"}
{"instruction": "Where to get Form 16?", "input": "", "output": "2 02-08-2019 Income Tax Module 1 Comprehensive Pension Management System User Manual (Version 6. Income Tax Module. Income Tax Processing", "source": "PDF - CPMS-UserManual"}
{"instruction": "When is pension credited?", "input": "", "output": "FORM I-A FORM OF APPLICATION FOR COMMUTATION OF A PERCENTAGE OF SUPERANNUATION PENSION WITHOUT s MEDICAL EXAMINATION IF NOT APPLIED FOR IN FORM OF CENTRAL CIVIL SERVICES (PENSION) RULES, 1972 [see Rules 5(2), 12,13(3), (3A), (3B), 14(1) and 15(3)] (To be submitted in duplicate at least three months before the date of retirement) PART I To The. (Here indicate the designation and full address of the Head of Office) Subject:- Commutation of pension without medical examination. Sir, I desire to comm...", "source": "PDF - Form1A_COP"}
{"instruction": "How is pension paid?", "input": "", "output": "FORM I-A FORM OF APPLICATION FOR COMMUTATION OF A PERCENTAGE OF SUPERANNUATION PENSION WITHOUT s MEDICAL EXAMINATION IF NOT APPLIED FOR IN FORM OF CENTRAL CIVIL SERVICES (PENSION) RULES, 1972 [see Rules 5(2), 12,13(3), (3A), (3B), 14(1) and 15(3)] (To be submitted in duplicate at least three months before the date of retirement) PART I To The. (Here indicate the designation and full address of the Head of Office) Subject:- Commutation of pension without medical examination. Sir, I desire to comm...", "source": "PDF - Form1A_COP"}
{"instruction": "When do I receive my pension?", "input": "", "output": "FORM I-A FORM OF APPLICATION FOR COMMUTATION OF A PERCENTAGE OF SUPERANNUATION PENSION WITHOUT s MEDICAL EXAMINATION IF NOT APPLIED FOR IN FORM OF CENTRAL CIVIL SERVICES (PENSION) RULES, 1972 [see Rules 5(2), 12,13(3), (3A), (3B), 14(1) and 15(3)] (To be submitted in duplicate at least three months before the date of retirement) PART I To The. (Here indicate the designation and full address of the Head of Office) Subject:- Commutation of pension without medical examination. Sir, I desire to comm...", "source": "PDF - Form1A_COP"}
{"instruction": "What is PPO?", "input": "", "output": "The pension papers of the applicant completed in all respects were forwarded under this Ministry Department Office Letter No , dated It is requested that the payment of commuted value of pension may be authorised through the Pension Payment Order which may be issued at least one month before the retirement of the applicant", "source": "PDF - Form1A_COP"}
{"instruction": "How to get PPO?", "input": "", "output": "The pension papers of the applicant completed in all respects were forwarded under this Ministry Department Office Letter No , dated It is requested that the payment of commuted value of pension may be authorised through the Pension Payment Order which may be issued at least one month before the retirement of the applicant", "source": "PDF - Form1A_COP"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "The pension papers of the applicant completed in all respects were forwarded under this Ministry Department Office Letter No , dated It is requested that the payment of commuted value of pension may be authorised through the Pension Payment Order which may be issued at least one month before the retirement of the applicant", "source": "PDF - Form1A_COP"}
{"instruction": "How to change bank account?", "input": "", "output": "Details of Bank account to which monthly pension shall be credited: (i) Name of Bank and Branch (ii) Account No. PART III Forwarded to the Accounts Officer (here indicate the address and designation) with the remarks that - (i) the particulars furnished by the applicant in Part I have been verified and are correct; (ii) the applicant is eligible to get a percentage of his pension commuted without medical examination; (iii) Amount of pension authorised", "source": "PDF - Form1A_COP"}
{"instruction": "Can I change my bank details?", "input": "", "output": "Details of Bank account to which monthly pension shall be credited: (i) Name of Bank and Branch (ii) Account No. PART III Forwarded to the Accounts Officer (here indicate the address and designation) with the remarks that - (i) the particulars furnished by the applicant in Part I have been verified and are correct; (ii) the applicant is eligible to get a percentage of his pension commuted without medical examination; (iii) Amount of pension authorised", "source": "PDF - Form1A_COP"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "Details of Bank account to which monthly pension shall be credited: (i) Name of Bank and Branch (ii) Account No. PART III Forwarded to the Accounts Officer (here indicate the address and designation) with the remarks that - (i) the particulars furnished by the applicant in Part I have been verified and are correct; (ii) the applicant is eligible to get a percentage of his pension commuted without medical examination; (iii) Amount of pension authorised", "source": "PDF - Form1A_COP"}
{"instruction": "What is CPMS?", "input": "", "output": "Comprehensive Pension Management System (CPMS) PROPOSED DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 15th April every year) Name of the pensioner : P. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under The Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank (...", "source": "PDF - implementation of income tax module_sampann (1)637020839208032024"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Comprehensive Pension Management System (CPMS) PROPOSED DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 15th April every year) Name of the pensioner : P. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under The Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank (...", "source": "PDF - implementation of income tax module_sampann (1)637020839208032024"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Comprehensive Pension Management System (CPMS) PROPOSED DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 15th April every year) Name of the pensioner : P. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under The Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank (...", "source": "PDF - implementation of income tax module_sampann (1)637020839208032024"}
{"instruction": "When is pension credited?", "input": "", "output": "Comprehensive Pension Management System (CPMS) PROPOSED DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 15th April every year) Name of the pensioner : P. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under The Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank (...", "source": "PDF - implementation of income tax module_sampann (1)637020839208032024"}
{"instruction": "How is pension paid?", "input": "", "output": "Comprehensive Pension Management System (CPMS) PROPOSED DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 15th April every year) Name of the pensioner : P. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under The Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank (...", "source": "PDF - implementation of income tax module_sampann (1)637020839208032024"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Comprehensive Pension Management System (CPMS) PROPOSED DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 15th April every year) Name of the pensioner : P. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under The Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank (...", "source": "PDF - implementation of income tax module_sampann (1)637020839208032024"}
{"instruction": "How to change bank account?", "input": "", "output": "INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under The Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank ( 5 years) : NSC interest reinvested : Housing Loan principal repaid : Life Insurance premium paid : ULIP investment approved for tax rebate : 2019 -Comprehensive Pension Management System (CPMS) Comprehensive Pension Ma...", "source": "PDF - implementation of income tax module_sampann (1)637020839208032024"}
{"instruction": "Can I change my bank details?", "input": "", "output": "INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under The Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank ( 5 years) : NSC interest reinvested : Housing Loan principal repaid : Life Insurance premium paid : ULIP investment approved for tax rebate : 2019 -Comprehensive Pension Management System (CPMS) Comprehensive Pension Ma...", "source": "PDF - implementation of income tax module_sampann (1)637020839208032024"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Public Provident Fund (PPF) : Mutual Fund ELSS approved under The Act : National Savings Scheme (NSS) : National Savings Certificate (NSC) : FD under Tax Savings Scheme with Scheduled Bank ( 5 years) : NSC interest reinvested : Housing Loan principal repaid : Life Insurance premium paid : ULIP investment approved for tax rebate : 2019 -Comprehensive Pension Management System (CPMS) Comprehensive Pension Ma...", "source": "PDF - implementation of income tax module_sampann (1)637020839208032024"}
{"instruction": "Is TDS deducted from pension?", "input": "", "output": "Comprehensive Pension Management System (CPMS) PROPOSED DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 15th April every year) Name of the pensioner : P. ) (i) Income from Previous Other Employer(s) : (ii)Tax Deducted by Previous Other Employer(s) : (iii) Income from Other Sources : (iv) Income from House Property (Income Loss): Total : B. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Pu...", "source": "PDF - implementation of income tax module_sampann (1)637020839208032024"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "Comprehensive Pension Management System (CPMS) PROPOSED DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 15th April every year) Name of the pensioner : P. ) (i) Income from Previous Other Employer(s) : (ii)Tax Deducted by Previous Other Employer(s) : (iii) Income from Other Sources : (iv) Income from House Property (Income Loss): Total : B. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Pu...", "source": "PDF - implementation of income tax module_sampann (1)637020839208032024"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "Here are the step-by-step instructions:\n\nComprehensive Pension Management System (CPMS) PROPOSED DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 15th April every year) Name of the pensioner : P. ) (i) Income from Previous Other Employer(s) : (ii)Tax Deducted by Previous Other Employer(s) : (iii) Income from Other Sources : (iv) Income from House Property (Income Loss): Total : B. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Pu...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "PDF - implementation of income tax module_sampann (1)637020839208032024", "enhanced": true}
{"instruction": "Where to get Form 16?", "input": "", "output": "Comprehensive Pension Management System (CPMS) PROPOSED DECLARATION OF INVESTMENT AND OTHER INFORMATION FOR AVAILING INCOME TAX REBATE BY PENSIONERS (to be submitted by 15th April every year) Name of the pensioner : P. ) (i) Income from Previous Other Employer(s) : (ii)Tax Deducted by Previous Other Employer(s) : (iii) Income from Other Sources : (iv) Income from House Property (Income Loss): Total : B. INVESTMENT UNDER SEC 80C Children Education Tuition Fee : Infrastructure Bond(U S 80CCF) : Pu...", "source": "PDF - implementation of income tax module_sampann (1)637020839208032024"}
{"instruction": "When is pension credited?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. 3 Pensioner s Code New PPO No. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if ...", "source": "PDF - know your pensioner kyp form638076718880512686"}
{"instruction": "How is pension paid?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. 3 Pensioner s Code New PPO No. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if ...", "source": "PDF - know your pensioner kyp form638076718880512686"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. 3 Pensioner s Code New PPO No. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if ...", "source": "PDF - know your pensioner kyp form638076718880512686"}
{"instruction": "What is PPO?", "input": "", "output": "Description Details 1 Name 2 PPO No. 3 Pensioner s Code New PPO No. PPO No", "source": "PDF - know your pensioner kyp form638076718880512686"}
{"instruction": "How to get PPO?", "input": "", "output": "Description Details 1 Name 2 PPO No. 3 Pensioner s Code New PPO No. PPO No", "source": "PDF - know your pensioner kyp form638076718880512686"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "Description Details 1 Name 2 PPO No. 3 Pensioner s Code New PPO No. PPO No", "source": "PDF - know your pensioner kyp form638076718880512686"}
{"instruction": "How to apply for family pension?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if Pensioner expired) 1 Pensioner ...", "source": "PDF - know your pensioner kyp form638076718880512686"}
{"instruction": "How to apply for family pension?", "input": "", "output": "Here are the step-by-step instructions:\n\nPhoto GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if Pensioner expired) 1 Pensioner ...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "PDF - know your pensioner kyp form638076718880512686", "enhanced": true}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if Pensioner expired) 1 Pensioner ...", "source": "PDF - know your pensioner kyp form638076718880512686"}
{"instruction": "When does family pension start?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if Pensioner expired) 1 Pensioner ...", "source": "PDF - know your pensioner kyp form638076718880512686"}
{"instruction": "How to change bank account?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. Email ID BANK POST OFFICE DETAILS 1. Name of Bank IFSC 2", "source": "PDF - know your pensioner kyp form638076718880512686"}
{"instruction": "How to change bank account?", "input": "", "output": "Here are the step-by-step instructions:\n\nPhoto GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. Email ID BANK POST OFFICE DETAILS 1. Name of Bank IFSC 2\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "PDF - know your pensioner kyp form638076718880512686", "enhanced": true}
{"instruction": "Can I change my bank details?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. Email ID BANK POST OFFICE DETAILS 1. Name of Bank IFSC 2", "source": "PDF - know your pensioner kyp form638076718880512686"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. Email ID BANK POST OFFICE DETAILS 1. Name of Bank IFSC 2", "source": "PDF - know your pensioner kyp form638076718880512686"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "Here are the step-by-step instructions:\n\nPhoto GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. Email ID BANK POST OFFICE DETAILS 1. Name of Bank IFSC 2\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "PDF - know your pensioner kyp form638076718880512686", "enhanced": true}
{"instruction": "When is pension credited?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. 3 Pensioner s Code New PPO No. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if ...", "source": "PDF - KnowYourPensioner(KYP)Form"}
{"instruction": "How is pension paid?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. 3 Pensioner s Code New PPO No. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if ...", "source": "PDF - KnowYourPensioner(KYP)Form"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. 3 Pensioner s Code New PPO No. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if ...", "source": "PDF - KnowYourPensioner(KYP)Form"}
{"instruction": "What is PPO?", "input": "", "output": "Description Details 1 Name 2 PPO No. 3 Pensioner s Code New PPO No. PPO No", "source": "PDF - KnowYourPensioner(KYP)Form"}
{"instruction": "How to get PPO?", "input": "", "output": "Description Details 1 Name 2 PPO No. 3 Pensioner s Code New PPO No. PPO No", "source": "PDF - KnowYourPensioner(KYP)Form"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "Description Details 1 Name 2 PPO No. 3 Pensioner s Code New PPO No. PPO No", "source": "PDF - KnowYourPensioner(KYP)Form"}
{"instruction": "How to apply for family pension?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if Pensioner expired) 1 Pensioner ...", "source": "PDF - KnowYourPensioner(KYP)Form"}
{"instruction": "How to apply for family pension?", "input": "", "output": "Here are the step-by-step instructions:\n\nPhoto GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if Pensioner expired) 1 Pensioner ...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "PDF - KnowYourPensioner(KYP)Form", "enhanced": true}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if Pensioner expired) 1 Pensioner ...", "source": "PDF - KnowYourPensioner(KYP)Form"}
{"instruction": "When does family pension start?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. NAME RELATION DOB Contact No No (i) (ii) (iii) (iv) (v) FAMILY PENSIONER S DETAILS (To be filled by Family Pensioner if Pensioner expired) 1 Pensioner ...", "source": "PDF - KnowYourPensioner(KYP)Form"}
{"instruction": "How to change bank account?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. Email ID BANK POST OFFICE DETAILS 1. Name of Bank IFSC 2", "source": "PDF - KnowYourPensioner(KYP)Form"}
{"instruction": "How to change bank account?", "input": "", "output": "Here are the step-by-step instructions:\n\nPhoto GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. Email ID BANK POST OFFICE DETAILS 1. Name of Bank IFSC 2\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "PDF - KnowYourPensioner(KYP)Form", "enhanced": true}
{"instruction": "Can I change my bank details?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. Email ID BANK POST OFFICE DETAILS 1. Name of Bank IFSC 2", "source": "PDF - KnowYourPensioner(KYP)Form"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. Email ID BANK POST OFFICE DETAILS 1. Name of Bank IFSC 2", "source": "PDF - KnowYourPensioner(KYP)Form"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "Here are the step-by-step instructions:\n\nPhoto GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENSIONER (KYP) FORM Category of Pensioner Pensioner (tick the correct option) Family Pensioner Pension Disbursing Authority Directly through CCA office Directly through Bank Directly through Post office Pensioner details (to be filled by Pensioner) S. Email ID BANK POST OFFICE DETAILS 1. Name of Bank IFSC 2\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "PDF - KnowYourPensioner(KYP)Form", "enhanced": true}
{"instruction": "When is pension credited?", "input": "", "output": "CCAs CCAs Subject: Submission of LC DLC by DoT pensioners (SAMPANN FMS) at any CCA office. It has been observed that SAMPANN FMS pensioners may face difficulties in submitting their Physical Life Certificates when they reside in a circle other than the circle which issued the PPO which could lead to delays and thereby stoppage of pension. Additionally, in case of Digital Life Certificate (DLC) via Jeevan Pramaan, it has been observed that sometimes the DLC is submitted to a CCA office other than...", "source": "PDF - lc dlc submission at any cca office (2)637600546902818537"}
{"instruction": "How is pension paid?", "input": "", "output": "CCAs CCAs Subject: Submission of LC DLC by DoT pensioners (SAMPANN FMS) at any CCA office. It has been observed that SAMPANN FMS pensioners may face difficulties in submitting their Physical Life Certificates when they reside in a circle other than the circle which issued the PPO which could lead to delays and thereby stoppage of pension. Additionally, in case of Digital Life Certificate (DLC) via Jeevan Pramaan, it has been observed that sometimes the DLC is submitted to a CCA office other than...", "source": "PDF - lc dlc submission at any cca office (2)637600546902818537"}
{"instruction": "When do I receive my pension?", "input": "", "output": "CCAs CCAs Subject: Submission of LC DLC by DoT pensioners (SAMPANN FMS) at any CCA office. It has been observed that SAMPANN FMS pensioners may face difficulties in submitting their Physical Life Certificates when they reside in a circle other than the circle which issued the PPO which could lead to delays and thereby stoppage of pension. Additionally, in case of Digital Life Certificate (DLC) via Jeevan Pramaan, it has been observed that sometimes the DLC is submitted to a CCA office other than...", "source": "PDF - lc dlc submission at any cca office (2)637600546902818537"}
{"instruction": "What is PPO?", "input": "", "output": "It has been observed that SAMPANN FMS pensioners may face difficulties in submitting their Physical Life Certificates when they reside in a circle other than the circle which issued the PPO which could lead to delays and thereby stoppage of pension", "source": "PDF - lc dlc submission at any cca office (2)637600546902818537"}
{"instruction": "How to get PPO?", "input": "", "output": "It has been observed that SAMPANN FMS pensioners may face difficulties in submitting their Physical Life Certificates when they reside in a circle other than the circle which issued the PPO which could lead to delays and thereby stoppage of pension", "source": "PDF - lc dlc submission at any cca office (2)637600546902818537"}
{"instruction": "How to get PPO?", "input": "", "output": "Here are the step-by-step instructions:\n\nIt has been observed that SAMPANN FMS pensioners may face difficulties in submitting their Physical Life Certificates when they reside in a circle other than the circle which issued the PPO which could lead to delays and thereby stoppage of pension\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "PDF - lc dlc submission at any cca office (2)637600546902818537", "enhanced": true}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "It has been observed that SAMPANN FMS pensioners may face difficulties in submitting their Physical Life Certificates when they reside in a circle other than the circle which issued the PPO which could lead to delays and thereby stoppage of pension", "source": "PDF - lc dlc submission at any cca office (2)637600546902818537"}
{"instruction": "How to change bank account?", "input": "", "output": "Government of India Ministry of Communications Department of Telecommunications Offlce of Controller General of Communicatlon Accounts NICF Campus, Ghitorni, New Delhi - 110047", "source": "PDF - lc dlc submission at any cca office (2)637600546902818537"}
{"instruction": "Can I change my bank details?", "input": "", "output": "Government of India Ministry of Communications Department of Telecommunications Offlce of Controller General of Communicatlon Accounts NICF Campus, Ghitorni, New Delhi - 110047", "source": "PDF - lc dlc submission at any cca office (2)637600546902818537"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "Government of India Ministry of Communications Department of Telecommunications Offlce of Controller General of Communicatlon Accounts NICF Campus, Ghitorni, New Delhi - 110047", "source": "PDF - lc dlc submission at any cca office (2)637600546902818537"}
{"instruction": "When is pension credited?", "input": "", "output": "All additions alterations are to be communicated by the Government servant pensioner along with the supporting documents and the changes shall be recorded in this Form under the signature of Head of Office in column (7). The details of all members of family (whether eligible for family pension or not) including spouse, all children, parents parents in law and disabled siblings (brothers and sisters) may be given. The pensioner shall intimate the details of change in family structure after retire...", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "How is pension paid?", "input": "", "output": "All additions alterations are to be communicated by the Government servant pensioner along with the supporting documents and the changes shall be recorded in this Form under the signature of Head of Office in column (7). The details of all members of family (whether eligible for family pension or not) including spouse, all children, parents parents in law and disabled siblings (brothers and sisters) may be given. The pensioner shall intimate the details of change in family structure after retire...", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "When do I receive my pension?", "input": "", "output": "All additions alterations are to be communicated by the Government servant pensioner along with the supporting documents and the changes shall be recorded in this Form under the signature of Head of Office in column (7). The details of all members of family (whether eligible for family pension or not) including spouse, all children, parents parents in law and disabled siblings (brothers and sisters) may be given. The pensioner shall intimate the details of change in family structure after retire...", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "What is PPO?", "input": "", "output": "All additions alterations are to be communicated by the Government servant pensioner along with the supporting documents and the changes shall be recorded in this Form under the signature of Head of Office in column (7). FORM 8 (See Rule 63(1) and 79(2) Application by a Government servant pensioner or his her spouse for including co-authorisation of names of permanently disabled child dependent parents disabled sibling as family pensioner in the Pension Payment Order Photograph(s) of the Family ...", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "How to get PPO?", "input": "", "output": "All additions alterations are to be communicated by the Government servant pensioner along with the supporting documents and the changes shall be recorded in this Form under the signature of Head of Office in column (7). FORM 8 (See Rule 63(1) and 79(2) Application by a Government servant pensioner or his her spouse for including co-authorisation of names of permanently disabled child dependent parents disabled sibling as family pensioner in the Pension Payment Order Photograph(s) of the Family ...", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "All additions alterations are to be communicated by the Government servant pensioner along with the supporting documents and the changes shall be recorded in this Form under the signature of Head of Office in column (7). FORM 8 (See Rule 63(1) and 79(2) Application by a Government servant pensioner or his her spouse for including co-authorisation of names of permanently disabled child dependent parents disabled sibling as family pensioner in the Pension Payment Order Photograph(s) of the Family ...", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "How to apply for family pension?", "input": "", "output": "The details of all members of family (whether eligible for family pension or not) including spouse, all children, parents parents in law and disabled siblings (brothers and sisters) may be given. FORM 8 (See Rule 63(1) and 79(2) Application by a Government servant pensioner or his her spouse for including co-authorisation of names of permanently disabled child dependent parents disabled sibling as family pensioner in the Pension Payment Order Photograph(s) of the Family member(s) to be co-author...", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "The details of all members of family (whether eligible for family pension or not) including spouse, all children, parents parents in law and disabled siblings (brothers and sisters) may be given. FORM 8 (See Rule 63(1) and 79(2) Application by a Government servant pensioner or his her spouse for including co-authorisation of names of permanently disabled child dependent parents disabled sibling as family pensioner in the Pension Payment Order Photograph(s) of the Family member(s) to be co-author...", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "When does family pension start?", "input": "", "output": "The details of all members of family (whether eligible for family pension or not) including spouse, all children, parents parents in law and disabled siblings (brothers and sisters) may be given. FORM 8 (See Rule 63(1) and 79(2) Application by a Government servant pensioner or his her spouse for including co-authorisation of names of permanently disabled child dependent parents disabled sibling as family pensioner in the Pension Payment Order Photograph(s) of the Family member(s) to be co-author...", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "How to change bank account?", "input": "", "output": "All additions alterations are to be communicated by the Government servant pensioner along with the supporting documents and the changes shall be recorded in this Form under the signature of Head of Office in column (7). The fact regarding disability or change of marital status of a family member should also be indicated in the Remarks column. The pensioner shall intimate the details of change in family structure after retirement in Form 5", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "Can I change my bank details?", "input": "", "output": "All additions alterations are to be communicated by the Government servant pensioner along with the supporting documents and the changes shall be recorded in this Form under the signature of Head of Office in column (7). The fact regarding disability or change of marital status of a family member should also be indicated in the Remarks column. The pensioner shall intimate the details of change in family structure after retirement in Form 5", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "All additions alterations are to be communicated by the Government servant pensioner along with the supporting documents and the changes shall be recorded in this Form under the signature of Head of Office in column (7). The fact regarding disability or change of marital status of a family member should also be indicated in the Remarks column. The pensioner shall intimate the details of change in family structure after retirement in Form 5", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "Is TDS deducted from pension?", "input": "", "output": "Last Income Tax Return failing which Certificate from SDM failing which any other document regarding income in support of the claim for family pension. Last Income Tax Return failing which Certificate from SDM failing which any other document regarding income in support of the claim for family pension (Not applicable in the case of spouse). Signature or left hand thumb impression of the claimant guardian Mobile Telephone No Permanent Account Number for Income Tax (PAN)", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "Last Income Tax Return failing which Certificate from SDM failing which any other document regarding income in support of the claim for family pension. Last Income Tax Return failing which Certificate from SDM failing which any other document regarding income in support of the claim for family pension (Not applicable in the case of spouse). Signature or left hand thumb impression of the claimant guardian Mobile Telephone No Permanent Account Number for Income Tax (PAN)", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "Where to get Form 16?", "input": "", "output": "Last Income Tax Return failing which Certificate from SDM failing which any other document regarding income in support of the claim for family pension. Last Income Tax Return failing which Certificate from SDM failing which any other document regarding income in support of the claim for family pension (Not applicable in the case of spouse). Signature or left hand thumb impression of the claimant guardian Mobile Telephone No Permanent Account Number for Income Tax (PAN)", "source": "PDF - PensionForms_Form4_Form8_Form10_Form12"}
{"instruction": "What is CPMS?", "input": "", "output": "CPMS stands for Comprehensive Pension Management System. It is a centralized web-based solution developed by the Department of Telecommunications (DoT) for end-to-end pension processing, from sanction to disbursement. CPMS is also known by the brand name SAMPANN (System for Accounting and Management of Pension), which was developed under the Digital India initiative to bring processing, sanctioning, authorization and disbursement under a common online platform for pensioners of the Telecom Department.", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "What is SAMPANN?", "input": "", "output": "SAMPANN (System for Accounting and Management of Pension) is a Comprehensive Pension Management System developed by the Department of Telecommunications (DoT) under the Digital India initiative. It is designed to enable end-to-end processing, sanctioning, authorization, and disbursement of pension through a single online platform for pensioners of the Department of Telecommunications. SAMPANN is now also being extended to BSNL units such as SSAs, Circle Offices, and the Corporate Office to handle pension cases of BSNL employees who opted for the Voluntary Retirement Scheme (VRS) in 2019.", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "What does SAMPANN stand for?", "input": "", "output": "SAMPANN stands for System for Accounting and Management of Pension. It is a comprehensive pension management system developed by the Department of Telecommunications (DoT) under the Digital India initiative for end-to-end processing, sanctioning, authorization, and disbursement of pension through a single online platform.", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "SAMPANN full form", "input": "", "output": "SAMPANN full form is System for Accounting and Management of Pension. It is a Comprehensive Pension Management System developed by the Department of Telecommunications (DoT) under the Digital India initiative to enable end-to-end processing, sanctioning, authorization, and disbursement of pension through a single online platform for pensioners of the Department of Telecommunications.", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "CPMS stands for Comprehensive Pension Management System. It is also known by the brand name SAMPANN (System for Accounting and Management of Pension). CPMS/SAMPANN was developed by the Department of Telecommunications (DoT) under the Digital India initiative to bring processing, sanctioning, authorization and disbursement under a common online platform for pensioners of the Telecom Department.", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "The Comprehensive Pension Management System (CPMS), also known by the brand name SAMPANN (System for Accounting and Management of Pension), is a digital platform developed by the Department of Telecommunications (DoT) under the Digital India initiative. It enables end-to-end processing, sanctioning, authorization, and disbursement of pension through a single online platform for pensioners of the Department of Telecommunications. SAMPANN has been extended to BSNL units including SSAs, Circle Offices, and the Corporate Office to handle pension cases of BSNL employees under the Voluntary Retirement Scheme (VRS) 2019. The system provides direct pension disbursement, online grievance management, status tracking, and transparent pension processing.", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "1 Comprehensive Pension Management System (CPMS) CPMS is a web portal for Pension Processing, Direct Disbursement, Accounting and Auditing of Pension and Pensionary Benefits to Pensioners of Department of Telecommunication. 3 Launching Logging into CPMS Users have to take following steps in order to login in to CPMS application. in in web browser to go to login screen", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "1 Comprehensive Pension Management System (CPMS) CPMS is a web portal for Pension Processing, Direct Disbursement, Accounting and Auditing of Pension and Pensionary Benefits to Pensioners of Department of Telecommunication. 3 Launching Logging into CPMS Users have to take following steps in order to login in to CPMS application. in in web browser to go to login screen", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "What is the CPMS website?", "input": "", "output": "1 Comprehensive Pension Management System (CPMS) CPMS is a web portal for Pension Processing, Direct Disbursement, Accounting and Auditing of Pension and Pensionary Benefits to Pensioners of Department of Telecommunication. 3 Launching Logging into CPMS Users have to take following steps in order to login in to CPMS application. in in web browser to go to login screen", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "When is pension credited?", "input": "", "output": "1) BSNL Voluntary Retirement Scheme 2019 System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 1. 1) Executive Summary Sanchar Pension, Seedha Vitaran Department of Telecommunications (DoT) launched a Comprehensive Pension Management System (CPMS) with the brand name SAMPANN which brings processing, sanctioning, authorisation and disbursement under a common online platform for the pensioners of the Telecom Department. This...", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "How is pension paid?", "input": "", "output": "1) BSNL Voluntary Retirement Scheme 2019 System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 1. 1) Executive Summary Sanchar Pension, Seedha Vitaran Department of Telecommunications (DoT) launched a Comprehensive Pension Management System (CPMS) with the brand name SAMPANN which brings processing, sanctioning, authorisation and disbursement under a common online platform for the pensioners of the Telecom Department. This...", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "When do I receive my pension?", "input": "", "output": "1) BSNL Voluntary Retirement Scheme 2019 System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 1. 1) Executive Summary Sanchar Pension, Seedha Vitaran Department of Telecommunications (DoT) launched a Comprehensive Pension Management System (CPMS) with the brand name SAMPANN which brings processing, sanctioning, authorisation and disbursement under a common online platform for the pensioners of the Telecom Department. This...", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "How to lodge a grievance?", "input": "", "output": "Creating a faster and more responsive online grievance registration and redressal system", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "What is the helpline number?", "input": "", "output": "Creating a faster and more responsive online grievance registration and redressal system", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "How to contact support?", "input": "", "output": "Creating a faster and more responsive online grievance registration and redressal system", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "How to apply for family pension?", "input": "", "output": "DH select the person eligible for family pension from the list of family members from the dropdown shown in Fig 2", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "How to apply for family pension?", "input": "", "output": "Here are the step-by-step instructions:\n\nDH select the person eligible for family pension from the list of family members from the dropdown shown in Fig 2\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1", "enhanced": true}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "DH select the person eligible for family pension from the list of family members from the dropdown shown in Fig 2", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "When does family pension start?", "input": "", "output": "DH select the person eligible for family pension from the list of family members from the dropdown shown in Fig 2", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "How to change bank account?", "input": "", "output": "1) BSNL Voluntary Retirement Scheme 2019 System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 1. 1 Comprehensive Pension Management System (CPMS) CPMS is a web portal for Pension Processing, Direct Disbursement, Accounting and Auditing of Pension and Pensionary Benefits to Pensioners of Department of Telecommunication. It has been designed with the following objectives: Direct Credit of pensionary benefits and pension to ...", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "Can I change my bank details?", "input": "", "output": "1) BSNL Voluntary Retirement Scheme 2019 System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 1. 1 Comprehensive Pension Management System (CPMS) CPMS is a web portal for Pension Processing, Direct Disbursement, Accounting and Auditing of Pension and Pensionary Benefits to Pensioners of Department of Telecommunication. It has been designed with the following objectives: Direct Credit of pensionary benefits and pension to ...", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "1) BSNL Voluntary Retirement Scheme 2019 System for Accounting and Management of Pension (SAMPANN) COMPREHENSIVE PENSION MANAGEMENT SYSTEM User Manual Volume I Version: 1. 1 Comprehensive Pension Management System (CPMS) CPMS is a web portal for Pension Processing, Direct Disbursement, Accounting and Auditing of Pension and Pensionary Benefits to Pensioners of Department of Telecommunication. It has been designed with the following objectives: Direct Credit of pensionary benefits and pension to ...", "source": "PDF - SAMPANNUserManual-BSNLVRS2019-HoO1.1"}
{"instruction": "When is pension credited?", "input": "", "output": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. First Pension: No additional document shall be required for discharge of first pension and so physical presence will not be required at that time. Life certificate: Every pensioner Family pensioner shall be required to get himself verified (Submission of Digital Life certificate or duly signed Life certificate) once in 12 months or less, during any month of the year", "source": "PDF - submission of documents by sampann beneficiaries637020838284494332"}
{"instruction": "How is pension paid?", "input": "", "output": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. First Pension: No additional document shall be required for discharge of first pension and so physical presence will not be required at that time. Life certificate: Every pensioner Family pensioner shall be required to get himself verified (Submission of Digital Life certificate or duly signed Life certificate) once in 12 months or less, during any month of the year", "source": "PDF - submission of documents by sampann beneficiaries637020838284494332"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. First Pension: No additional document shall be required for discharge of first pension and so physical presence will not be required at that time. Life certificate: Every pensioner Family pensioner shall be required to get himself verified (Submission of Digital Life certificate or duly signed Life certificate) once in 12 months or less, during any month of the year", "source": "PDF - submission of documents by sampann beneficiaries637020838284494332"}
{"instruction": "What is PPO?", "input": "", "output": "For submission of DLC, the pensioner requires to be physically present to generate Biometric verification, and provide the PPO Mobile number Aadhaar Number at the CCAs Pensioners Service Centre Banks HPOs CSCs Aadhaar centers for completing the following procedures: Step 1: The pensioner s Aadhaar Number and Mobile number is to be entered in the online application form after which the pensioner receives an OTP (Fig 1) which is to be fed in the system (Fig 2) (Fig 1) (Fig 2) Step 2: If correct OT...", "source": "PDF - submission of documents by sampann beneficiaries637020838284494332"}
{"instruction": "How to get PPO?", "input": "", "output": "For submission of DLC, the pensioner requires to be physically present to generate Biometric verification, and provide the PPO Mobile number Aadhaar Number at the CCAs Pensioners Service Centre Banks HPOs CSCs Aadhaar centers for completing the following procedures: Step 1: The pensioner s Aadhaar Number and Mobile number is to be entered in the online application form after which the pensioner receives an OTP (Fig 1) which is to be fed in the system (Fig 2) (Fig 1) (Fig 2) Step 2: If correct OT...", "source": "PDF - submission of documents by sampann beneficiaries637020838284494332"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "For submission of DLC, the pensioner requires to be physically present to generate Biometric verification, and provide the PPO Mobile number Aadhaar Number at the CCAs Pensioners Service Centre Banks HPOs CSCs Aadhaar centers for completing the following procedures: Step 1: The pensioner s Aadhaar Number and Mobile number is to be entered in the online application form after which the pensioner receives an OTP (Fig 1) which is to be fed in the system (Fig 2) (Fig 1) (Fig 2) Step 2: If correct OT...", "source": "PDF - submission of documents by sampann beneficiaries637020838284494332"}
{"instruction": "How to apply for family pension?", "input": "", "output": "Life certificate: Every pensioner Family pensioner shall be required to get himself verified (Submission of Digital Life certificate or duly signed Life certificate) once in 12 months or less, during any month of the year. Non remarriage Certificate: A declaration shall be obtained half-yearly from all recipients of family pension whose pension is terminable on their marriage or remarriage. However, in case of widow recipient of family pension, such declaration will be obtained only on the 1st o...", "source": "PDF - submission of documents by sampann beneficiaries637020838284494332"}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "Life certificate: Every pensioner Family pensioner shall be required to get himself verified (Submission of Digital Life certificate or duly signed Life certificate) once in 12 months or less, during any month of the year. Non remarriage Certificate: A declaration shall be obtained half-yearly from all recipients of family pension whose pension is terminable on their marriage or remarriage. However, in case of widow recipient of family pension, such declaration will be obtained only on the 1st o...", "source": "PDF - submission of documents by sampann beneficiaries637020838284494332"}
{"instruction": "When does family pension start?", "input": "", "output": "Life certificate: Every pensioner Family pensioner shall be required to get himself verified (Submission of Digital Life certificate or duly signed Life certificate) once in 12 months or less, during any month of the year. Non remarriage Certificate: A declaration shall be obtained half-yearly from all recipients of family pension whose pension is terminable on their marriage or remarriage. However, in case of widow recipient of family pension, such declaration will be obtained only on the 1st o...", "source": "PDF - submission of documents by sampann beneficiaries637020838284494332"}
{"instruction": "How to change bank account?", "input": "", "output": "Digital life certificate can be generated at Banks Post Offices which have Jeevan Pramaan Facility. In addition, the Pensioners Service Centre at SSAs, Pensioners Lounge at CCA Offices, banks and CSCs(Customer Service Centres) will facilitate online submission of Digital Life Certificate for the pensioner and the same can be delivered through Post Hand. For submission of DLC, the pensioner requires to be physically present to generate Biometric verification, and provide the PPO Mobile number Aad...", "source": "PDF - submission of documents by sampann beneficiaries637020838284494332"}
{"instruction": "Can I change my bank details?", "input": "", "output": "Digital life certificate can be generated at Banks Post Offices which have Jeevan Pramaan Facility. In addition, the Pensioners Service Centre at SSAs, Pensioners Lounge at CCA Offices, banks and CSCs(Customer Service Centres) will facilitate online submission of Digital Life Certificate for the pensioner and the same can be delivered through Post Hand. For submission of DLC, the pensioner requires to be physically present to generate Biometric verification, and provide the PPO Mobile number Aad...", "source": "PDF - submission of documents by sampann beneficiaries637020838284494332"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "Digital life certificate can be generated at Banks Post Offices which have Jeevan Pramaan Facility. In addition, the Pensioners Service Centre at SSAs, Pensioners Lounge at CCA Offices, banks and CSCs(Customer Service Centres) will facilitate online submission of Digital Life Certificate for the pensioner and the same can be delivered through Post Hand. For submission of DLC, the pensioner requires to be physically present to generate Biometric verification, and provide the PPO Mobile number Aad...", "source": "PDF - submission of documents by sampann beneficiaries637020838284494332"}
{"instruction": "What is CPMS?", "input": "", "output": "Faster processing of arrears and revision of pension\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies", "source": "Web - About Us"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Faster processing of arrears and revision of pension\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies", "source": "Web - About Us"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Faster processing of arrears and revision of pension\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies", "source": "Web - About Us"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "Faster processing of arrears and revision of pension\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies", "source": "Web - About Us"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "Faster processing of arrears and revision of pension\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies", "source": "Web - About Us"}
{"instruction": "What is the CPMS website?", "input": "", "output": "Faster processing of arrears and revision of pension\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies", "source": "Web - About Us"}
{"instruction": "When is pension credited?", "input": "", "output": "Direct disbursement of pension on timely basis without intermediaries\n2. Single window system for complete pension process\n3. Online grievance management for the pensioners reduces paper work\n4", "source": "Web - About Us"}
{"instruction": "How is pension paid?", "input": "", "output": "Direct disbursement of pension on timely basis without intermediaries\n2. Single window system for complete pension process\n3. Online grievance management for the pensioners reduces paper work\n4", "source": "Web - About Us"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Direct disbursement of pension on timely basis without intermediaries\n2. Single window system for complete pension process\n3. Online grievance management for the pensioners reduces paper work\n4", "source": "Web - About Us"}
{"instruction": "How to lodge a grievance?", "input": "", "output": "Online grievance management for the pensioners reduces paper work\n4", "source": "Web - About Us"}
{"instruction": "What is the helpline number?", "input": "", "output": "Online grievance management for the pensioners reduces paper work\n4", "source": "Web - About Us"}
{"instruction": "How to contact support?", "input": "", "output": "Online grievance management for the pensioners reduces paper work\n4", "source": "Web - About Us"}
{"instruction": "What is PPO?", "input": "", "output": "Faster processing of arrears and revision of pension\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies", "source": "Web - About Us"}
{"instruction": "How to get PPO?", "input": "", "output": "Faster processing of arrears and revision of pension\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies", "source": "Web - About Us"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "Faster processing of arrears and revision of pension\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• Feedback\n• |\n• Contact us\n• |\n• Policies", "source": "Web - About Us"}
{"instruction": "How to apply for family pension?", "input": "", "output": "5 lakh current pensioners/family pensioners and 1", "source": "Web - About Us"}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "5 lakh current pensioners/family pensioners and 1", "source": "Web - About Us"}
{"instruction": "When does family pension start?", "input": "", "output": "5 lakh current pensioners/family pensioners and 1", "source": "Web - About Us"}
{"instruction": "How to change bank account?", "input": "", "output": "Tracking of pension status from home encourages transparency and accountability\n5", "source": "Web - About Us"}
{"instruction": "Can I change my bank details?", "input": "", "output": "Tracking of pension status from home encourages transparency and accountability\n5", "source": "Web - About Us"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "Tracking of pension status from home encourages transparency and accountability\n5", "source": "Web - About Us"}
{"instruction": "What is CPMS?", "input": "", "output": "cpms-dot[at]nic[dot]in. List:\n• Track pension\n• View ePPO\n• Lodge and track grievances\n• View monthly Statement\n• Stay Updated\n\n\nList:\n• All India Toll-free helpline: 1800-113-5800\n• Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help :  dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n...", "source": "Web - Retiree Corner"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "cpms-dot[at]nic[dot]in. List:\n• Track pension\n• View ePPO\n• Lodge and track grievances\n• View monthly Statement\n• Stay Updated\n\n\nList:\n• All India Toll-free helpline: 1800-113-5800\n• Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help :  dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n...", "source": "Web - Retiree Corner"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "cpms-dot[at]nic[dot]in. List:\n• Track pension\n• View ePPO\n• Lodge and track grievances\n• View monthly Statement\n• Stay Updated\n\n\nList:\n• All India Toll-free helpline: 1800-113-5800\n• Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help :  dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n...", "source": "Web - Retiree Corner"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "List:\n• Track pension\n• View ePPO\n• Lodge and track grievances\n• View monthly Statement\n• Stay Updated\n\n\nList:\n• All India Toll-free helpline: 1800-113-5800\n• Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help :  dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• F...", "source": "Web - Retiree Corner"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "List:\n• Track pension\n• View ePPO\n• Lodge and track grievances\n• View monthly Statement\n• Stay Updated\n\n\nList:\n• All India Toll-free helpline: 1800-113-5800\n• Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help :  dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• F...", "source": "Web - Retiree Corner"}
{"instruction": "What is the CPMS website?", "input": "", "output": "List:\n• Track pension\n• View ePPO\n• Lodge and track grievances\n• View monthly Statement\n• Stay Updated\n\n\nList:\n• All India Toll-free helpline: 1800-113-5800\n• Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help :  dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunication\n• |\n• Jeevan Pramaan\n• |\n• F...", "source": "Web - Retiree Corner"}
{"instruction": "When is pension credited?", "input": "", "output": "Direct disbursement of pension on timely basis without intermediaries\n2. Single window system for complete pension process\n3. Online grievance management for the pensioners reducing paper work\n4", "source": "Web - Retiree Corner"}
{"instruction": "How is pension paid?", "input": "", "output": "Direct disbursement of pension on timely basis without intermediaries\n2. Single window system for complete pension process\n3. Online grievance management for the pensioners reducing paper work\n4", "source": "Web - Retiree Corner"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Direct disbursement of pension on timely basis without intermediaries\n2. Single window system for complete pension process\n3. Online grievance management for the pensioners reducing paper work\n4", "source": "Web - Retiree Corner"}
{"instruction": "How to lodge a grievance?", "input": "", "output": "Online grievance management for the pensioners reducing paper work\n4. Lodge Grievance: Pensioner can lodge grievance online and check its status also. Alternately the Pensioner can always call the national Helpline 1800-113-5800 or mail his grievances/query to sampann", "source": "Web - Retiree Corner"}
{"instruction": "What is the helpline number?", "input": "", "output": "Online grievance management for the pensioners reducing paper work\n4. Lodge Grievance: Pensioner can lodge grievance online and check its status also. Alternately the Pensioner can always call the national Helpline 1800-113-5800 or mail his grievances/query to sampann", "source": "Web - Retiree Corner"}
{"instruction": "How to contact support?", "input": "", "output": "Online grievance management for the pensioners reducing paper work\n4. Lodge Grievance: Pensioner can lodge grievance online and check its status also. Alternately the Pensioner can always call the national Helpline 1800-113-5800 or mail his grievances/query to sampann", "source": "Web - Retiree Corner"}
{"instruction": "What is PPO?", "input": "", "output": "ePPO (Digitally signed PPO), Gratuity sanction and sanction of revision of pension on the dashboard, along with details of commutation payment and arrears. List:\n• Track pension\n• View ePPO\n• Lodge and track grievances\n• View monthly Statement\n• Stay Updated\n\n\nList:\n• All India Toll-free helpline: 1800-113-5800\n• Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Admi...", "source": "Web - Retiree Corner"}
{"instruction": "How to get PPO?", "input": "", "output": "ePPO (Digitally signed PPO), Gratuity sanction and sanction of revision of pension on the dashboard, along with details of commutation payment and arrears. List:\n• Track pension\n• View ePPO\n• Lodge and track grievances\n• View monthly Statement\n• Stay Updated\n\n\nList:\n• All India Toll-free helpline: 1800-113-5800\n• Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Admi...", "source": "Web - Retiree Corner"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "ePPO (Digitally signed PPO), Gratuity sanction and sanction of revision of pension on the dashboard, along with details of commutation payment and arrears. List:\n• Track pension\n• View ePPO\n• Lodge and track grievances\n• View monthly Statement\n• Stay Updated\n\n\nList:\n• All India Toll-free helpline: 1800-113-5800\n• Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Admi...", "source": "Web - Retiree Corner"}
{"instruction": "How to change bank account?", "input": "", "output": "Tracking of pension status from home encourages transparency and accountability\n5", "source": "Web - Retiree Corner"}
{"instruction": "Can I change my bank details?", "input": "", "output": "Tracking of pension status from home encourages transparency and accountability\n5", "source": "Web - Retiree Corner"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "Tracking of pension status from home encourages transparency and accountability\n5", "source": "Web - Retiree Corner"}
{"instruction": "What is CPMS?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "What is the CPMS website?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "When is pension credited?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "How is pension paid?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "When do I receive my pension?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "What is PPO?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "How to get PPO?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "How to apply for family pension?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "When does family pension start?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "Is TDS deducted from pension?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "Where to get Form 16?", "input": "", "output": "| Order/Circular Name | Download File | Dated\n---------------------------------------------------\n1 | Know Your Pensioner KYP Form |  | 26/12/2022\n2 | Submission of LC by Pensioners at any CCA Office |  | 23/06/2021\n3 | Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries |  | 22/08/2019\n4 | Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries |  | 22/08/2019\n5 | Submission of Family Pension Claim Form and Commutation Forms directly to ...", "source": "Web - Circulars"}
{"instruction": "What is CPMS?", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "What is the CPMS website?", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "When is pension credited?", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "How is pension paid?", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "When do I receive my pension?", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "What is PPO?", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "How to get PPO?", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "How to change bank account?", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "Can I change my bank details?", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "1\nBank Undertaking\nFaq's For Pensioner\nActual declaration for Investments and Savings\nProposed declaration for Investments and Savings\nForm 1A to apply for Commutation of Pension\nKnow Your Pensioner (KYP) Form\nPension Forms - Form 4, Form 8, Form 10  Form 12\n\n\nList:\n• Helpdesk\n• Email : support[dot]cpms-dot[at]nic[dot]in\n• |\n• For Technical Help : 1800-113-5800\n• |\n• For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\n\n\nList:\n• Pensioners' Portal\n• |\n• Department of Telecommunica...", "source": "Web - Manual"}
{"instruction": "What is CPMS?", "input": "", "output": "Table Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n2) | How to get con...", "source": "Web - FAQ"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Table Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n2) | How to get con...", "source": "Web - FAQ"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Table Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n2) | How to get con...", "source": "Web - FAQ"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n2) | How to get connected to CPMS portal?\n\n\nAns : \nRetirees can get connected by logging on to www. •\tThe pensioner will be able to access and take print-out of details of pension payment ...", "source": "Web - FAQ"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n2) | How to get connected to CPMS portal?\n\n\nAns : \nRetirees can get connected by logging on to www. •\tThe pensioner will be able to access and take print-out of details of pension payment ...", "source": "Web - FAQ"}
{"instruction": "What is the CPMS website?", "input": "", "output": "-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n2) | How to get connected to CPMS portal?\n\n\nAns : \nRetirees can get connected by logging on to www. •\tThe pensioner will be able to access and take print-out of details of pension payment ...", "source": "Web - FAQ"}
{"instruction": "When is pension credited?", "input": "", "output": "Table Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. It covers all activities starting from pension sanction, authorization, and disbursement. It also enables the pensioner to track latest status of pension", "source": "Web - FAQ"}
{"instruction": "How is pension paid?", "input": "", "output": "Table Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. It covers all activities starting from pension sanction, authorization, and disbursement. It also enables the pensioner to track latest status of pension", "source": "Web - FAQ"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Table Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. It covers all activities starting from pension sanction, authorization, and disbursement. It also enables the pensioner to track latest status of pension", "source": "Web - FAQ"}
{"instruction": "How to lodge a grievance?", "input": "", "output": "•\t Lodge a grievance from the CPMS portal itself. In case of any grievance, he/she can lodge the same from his dashboard itself. 20) | Which office will the pensioner approach for grievance resolution?\n\n\nAns : \nGrievance can be lodged from the Pensioner’s dashboard online", "source": "Web - FAQ"}
{"instruction": "What is the helpline number?", "input": "", "output": "•\t Lodge a grievance from the CPMS portal itself. In case of any grievance, he/she can lodge the same from his dashboard itself. 20) | Which office will the pensioner approach for grievance resolution?\n\n\nAns : \nGrievance can be lodged from the Pensioner’s dashboard online", "source": "Web - FAQ"}
{"instruction": "How to contact support?", "input": "", "output": "•\t Lodge a grievance from the CPMS portal itself. In case of any grievance, he/she can lodge the same from his dashboard itself. 20) | Which office will the pensioner approach for grievance resolution?\n\n\nAns : \nGrievance can be lodged from the Pensioner’s dashboard online", "source": "Web - FAQ"}
{"instruction": "What is PPO?", "input": "", "output": "7) | Will the pensioner get a PPO in new system?\n\n\nAns : \nA digitally signed PPO shall be sent to Pensioners dashboard. •\tView PPO and sanction orders\n11) | What is to be done in case the pensioner wants to change the state in which his bank branch is located?\n\n\nAns : \nThe pensioners covered under CPMS needs to submit application in prescribed form along with details of bank account and bank undertaking to the CCA office. There will be no need for recalling PPO from Bank and transferring pension...", "source": "Web - FAQ"}
{"instruction": "How to get PPO?", "input": "", "output": "7) | Will the pensioner get a PPO in new system?\n\n\nAns : \nA digitally signed PPO shall be sent to Pensioners dashboard. •\tView PPO and sanction orders\n11) | What is to be done in case the pensioner wants to change the state in which his bank branch is located?\n\n\nAns : \nThe pensioners covered under CPMS needs to submit application in prescribed form along with details of bank account and bank undertaking to the CCA office. There will be no need for recalling PPO from Bank and transferring pension...", "source": "Web - FAQ"}
{"instruction": "How to get PPO?", "input": "", "output": "Here are the step-by-step instructions:\n\n7) | Will the pensioner get a PPO in new system?\n\n\nAns : \nA digitally signed PPO shall be sent to Pensioners dashboard. •\tView PPO and sanction orders\n11) | What is to be done in case the pensioner wants to change the state in which his bank branch is located?\n\n\nAns : \nThe pensioners covered under CPMS needs to submit application in prescribed form along with details of bank account and bank undertaking to the CCA office. There will be no need for recalling PPO from Bank and transferring pension...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - FAQ", "enhanced": true}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "7) | Will the pensioner get a PPO in new system?\n\n\nAns : \nA digitally signed PPO shall be sent to Pensioners dashboard. •\tView PPO and sanction orders\n11) | What is to be done in case the pensioner wants to change the state in which his bank branch is located?\n\n\nAns : \nThe pensioners covered under CPMS needs to submit application in prescribed form along with details of bank account and bank undertaking to the CCA office. There will be no need for recalling PPO from Bank and transferring pension...", "source": "Web - FAQ"}
{"instruction": "How to apply for family pension?", "input": "", "output": "12) | In case of Family Pension, which office should the dependents approach?\n\n\nAns : \nIn case of unfortunate death of the employee/pensioner, the Death Certificate along with Life certificate (or DLC) of family pensioner may be sent to CCA office. 13) | When does the family pension commence?\n\n\nAns : \nOnce the Death Certificate is received, the pension can either start in the same month or the next. Family pensioner need not visit CCA office for commencing disbursement of family pension", "source": "Web - FAQ"}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "12) | In case of Family Pension, which office should the dependents approach?\n\n\nAns : \nIn case of unfortunate death of the employee/pensioner, the Death Certificate along with Life certificate (or DLC) of family pensioner may be sent to CCA office. 13) | When does the family pension commence?\n\n\nAns : \nOnce the Death Certificate is received, the pension can either start in the same month or the next. Family pensioner need not visit CCA office for commencing disbursement of family pension", "source": "Web - FAQ"}
{"instruction": "When does family pension start?", "input": "", "output": "12) | In case of Family Pension, which office should the dependents approach?\n\n\nAns : \nIn case of unfortunate death of the employee/pensioner, the Death Certificate along with Life certificate (or DLC) of family pensioner may be sent to CCA office. 13) | When does the family pension commence?\n\n\nAns : \nOnce the Death Certificate is received, the pension can either start in the same month or the next. Family pensioner need not visit CCA office for commencing disbursement of family pension", "source": "Web - FAQ"}
{"instruction": "How to change bank account?", "input": "", "output": "3) | What changes after CPMS implementation?\n\n\nAns : \nThe pensioner will continue to be credited to the designated bank account of the pensioner after implementation of CPMS. However, Controller of Communication Accounts (CCA) becomes the pension disbursing authority (PDA) in place of Post Offices and CPPC of Banks. 4) | How does the Pensioner benefit from implementation of CPMS?\n\n\nAns : \nCPMS implementation will benefit the pensioner in the following ways:\r\n•\tDirect transfer of pension by the C...", "source": "Web - FAQ"}
{"instruction": "Can I change my bank details?", "input": "", "output": "3) | What changes after CPMS implementation?\n\n\nAns : \nThe pensioner will continue to be credited to the designated bank account of the pensioner after implementation of CPMS. However, Controller of Communication Accounts (CCA) becomes the pension disbursing authority (PDA) in place of Post Offices and CPPC of Banks. 4) | How does the Pensioner benefit from implementation of CPMS?\n\n\nAns : \nCPMS implementation will benefit the pensioner in the following ways:\r\n•\tDirect transfer of pension by the C...", "source": "Web - FAQ"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "3) | What changes after CPMS implementation?\n\n\nAns : \nThe pensioner will continue to be credited to the designated bank account of the pensioner after implementation of CPMS. However, Controller of Communication Accounts (CCA) becomes the pension disbursing authority (PDA) in place of Post Offices and CPPC of Banks. 4) | How does the Pensioner benefit from implementation of CPMS?\n\n\nAns : \nCPMS implementation will benefit the pensioner in the following ways:\r\n•\tDirect transfer of pension by the C...", "source": "Web - FAQ"}
{"instruction": "Is TDS deducted from pension?", "input": "", "output": "•\tTDS and Form-16 will be provided by the CCA without any intervention from the pensioner. 17) | Whether Income Tax (TDS) will be deducted at source?\n\n\nAns : \nYes, income tax is deducted at source for all pensioner (not family pensioners), as per provisions of Income Tax Act and instructions issued by the Income Tax Department. 18) | Who is responsible for deduction of Income Tax at source from pension payment?\n\n\nAns : \nPDA section of CCA office is responsible for deducting the TDS on Pension", "source": "Web - FAQ"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "•\tTDS and Form-16 will be provided by the CCA without any intervention from the pensioner. 17) | Whether Income Tax (TDS) will be deducted at source?\n\n\nAns : \nYes, income tax is deducted at source for all pensioner (not family pensioners), as per provisions of Income Tax Act and instructions issued by the Income Tax Department. 18) | Who is responsible for deduction of Income Tax at source from pension payment?\n\n\nAns : \nPDA section of CCA office is responsible for deducting the TDS on Pension", "source": "Web - FAQ"}
{"instruction": "Where to get Form 16?", "input": "", "output": "•\tTDS and Form-16 will be provided by the CCA without any intervention from the pensioner. 17) | Whether Income Tax (TDS) will be deducted at source?\n\n\nAns : \nYes, income tax is deducted at source for all pensioner (not family pensioners), as per provisions of Income Tax Act and instructions issued by the Income Tax Department. 18) | Who is responsible for deduction of Income Tax at source from pension payment?\n\n\nAns : \nPDA section of CCA office is responsible for deducting the TDS on Pension", "source": "Web - FAQ"}
{"instruction": "What is CPMS?", "input": "", "output": "LPD and Penalty)\nUser manual for Deactivation module\nUser manual for Enhanced Monthly Bill Enhancements\nUser manual for Family and Migrated Pensioner Dashboard\nUser manual for FMA Update\nUser manual for Income tax related changes for family pensioners\nUser manual for logic of Minimum Pension\nUser manual for Part IV utility\nUser Manual for Pay related revision for BSNL VRS 2019 cases\nUser manual for Pensioner Pension Slip Generation\nUser Manual for Return Delete feature in Revision Module\nUser Ma...", "source": "Web - Training"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "LPD and Penalty)\nUser manual for Deactivation module\nUser manual for Enhanced Monthly Bill Enhancements\nUser manual for Family and Migrated Pensioner Dashboard\nUser manual for FMA Update\nUser manual for Income tax related changes for family pensioners\nUser manual for logic of Minimum Pension\nUser manual for Part IV utility\nUser Manual for Pay related revision for BSNL VRS 2019 cases\nUser manual for Pensioner Pension Slip Generation\nUser Manual for Return Delete feature in Revision Module\nUser Ma...", "source": "Web - Training"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "LPD and Penalty)\nUser manual for Deactivation module\nUser manual for Enhanced Monthly Bill Enhancements\nUser manual for Family and Migrated Pensioner Dashboard\nUser manual for FMA Update\nUser manual for Income tax related changes for family pensioners\nUser manual for logic of Minimum Pension\nUser manual for Part IV utility\nUser Manual for Pay related revision for BSNL VRS 2019 cases\nUser manual for Pensioner Pension Slip Generation\nUser Manual for Return Delete feature in Revision Module\nUser Ma...", "source": "Web - Training"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "LPD and Penalty)\nUser manual for Deactivation module\nUser manual for Enhanced Monthly Bill Enhancements\nUser manual for Family and Migrated Pensioner Dashboard\nUser manual for FMA Update\nUser manual for Income tax related changes for family pensioners\nUser manual for logic of Minimum Pension\nUser manual for Part IV utility\nUser Manual for Pay related revision for BSNL VRS 2019 cases\nUser manual for Pensioner Pension Slip Generation\nUser Manual for Return Delete feature in Revision Module\nUser Ma...", "source": "Web - Training"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "LPD and Penalty)\nUser manual for Deactivation module\nUser manual for Enhanced Monthly Bill Enhancements\nUser manual for Family and Migrated Pensioner Dashboard\nUser manual for FMA Update\nUser manual for Income tax related changes for family pensioners\nUser manual for logic of Minimum Pension\nUser manual for Part IV utility\nUser Manual for Pay related revision for BSNL VRS 2019 cases\nUser manual for Pensioner Pension Slip Generation\nUser Manual for Return Delete feature in Revision Module\nUser Ma...", "source": "Web - Training"}
{"instruction": "What is the CPMS website?", "input": "", "output": "LPD and Penalty)\nUser manual for Deactivation module\nUser manual for Enhanced Monthly Bill Enhancements\nUser manual for Family and Migrated Pensioner Dashboard\nUser manual for FMA Update\nUser manual for Income tax related changes for family pensioners\nUser manual for logic of Minimum Pension\nUser manual for Part IV utility\nUser Manual for Pay related revision for BSNL VRS 2019 cases\nUser manual for Pensioner Pension Slip Generation\nUser Manual for Return Delete feature in Revision Module\nUser Ma...", "source": "Web - Training"}
{"instruction": "When is pension credited?", "input": "", "output": "Table Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...", "source": "Web - Training"}
{"instruction": "How is pension paid?", "input": "", "output": "Table Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...", "source": "Web - Training"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Table Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...", "source": "Web - Training"}
{"instruction": "What is PPO?", "input": "", "output": "Table Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...", "source": "Web - Training"}
{"instruction": "How to get PPO?", "input": "", "output": "Table Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...", "source": "Web - Training"}
{"instruction": "How to get PPO?", "input": "", "output": "Here are the step-by-step instructions:\n\nTable Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Training", "enhanced": true}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "Table Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...", "source": "Web - Training"}
{"instruction": "How to apply for family pension?", "input": "", "output": "Table Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...", "source": "Web - Training"}
{"instruction": "How to apply for family pension?", "input": "", "output": "Here are the step-by-step instructions:\n\nTable Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Training", "enhanced": true}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "Table Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...", "source": "Web - Training"}
{"instruction": "When does family pension start?", "input": "", "output": "Table Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...", "source": "Web - Training"}
{"instruction": "How to change bank account?", "input": "", "output": "Table Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...", "source": "Web - Training"}
{"instruction": "How to change bank account?", "input": "", "output": "Here are the step-by-step instructions:\n\nTable Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Training", "enhanced": true}
{"instruction": "Can I change my bank details?", "input": "", "output": "Table Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...", "source": "Web - Training"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "Table Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...", "source": "Web - Training"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "Here are the step-by-step instructions:\n\nTable Data:\n\n\nHoO Module Training Presentation\nHoO Module Training Video\nRetiree Form Filling Training Presentation\nRetiree Form Filling Training Video\nRevised Process Flow BSNL VRS\nManual for Logic of Arrear Calculation on Voucher date\nMigration_Family Revision User Manual\nMigration_Pay Revision User Manual\nMinor Corrections in ePPO\nProcess of Generation of Pension Sleep through SAMPANN mobile App\nUser Manual failed Cancelled Bill handling in SAMPANN\nUser manual for Additional Quantum Pension\nU...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Training", "enhanced": true}
{"instruction": "Is TDS deducted from pension?", "input": "", "output": "LPD and Penalty)\nUser manual for Deactivation module\nUser manual for Enhanced Monthly Bill Enhancements\nUser manual for Family and Migrated Pensioner Dashboard\nUser manual for FMA Update\nUser manual for Income tax related changes for family pensioners\nUser manual for logic of Minimum Pension\nUser manual for Part IV utility\nUser Manual for Pay related revision for BSNL VRS 2019 cases\nUser manual for Pensioner Pension Slip Generation\nUser Manual for Return Delete feature in Revision Module\nUser Ma...", "source": "Web - Training"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "LPD and Penalty)\nUser manual for Deactivation module\nUser manual for Enhanced Monthly Bill Enhancements\nUser manual for Family and Migrated Pensioner Dashboard\nUser manual for FMA Update\nUser manual for Income tax related changes for family pensioners\nUser manual for logic of Minimum Pension\nUser manual for Part IV utility\nUser Manual for Pay related revision for BSNL VRS 2019 cases\nUser manual for Pensioner Pension Slip Generation\nUser Manual for Return Delete feature in Revision Module\nUser Ma...", "source": "Web - Training"}
{"instruction": "Where to get Form 16?", "input": "", "output": "LPD and Penalty)\nUser manual for Deactivation module\nUser manual for Enhanced Monthly Bill Enhancements\nUser manual for Family and Migrated Pensioner Dashboard\nUser manual for FMA Update\nUser manual for Income tax related changes for family pensioners\nUser manual for logic of Minimum Pension\nUser manual for Part IV utility\nUser Manual for Pay related revision for BSNL VRS 2019 cases\nUser manual for Pensioner Pension Slip Generation\nUser Manual for Return Delete feature in Revision Module\nUser Ma...", "source": "Web - Training"}
{"instruction": "What is CPMS?", "input": "", "output": "Table Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate genera...", "source": "Web - Whats New"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Table Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate genera...", "source": "Web - Whats New"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Table Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate genera...", "source": "Web - Whats New"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "Table Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate genera...", "source": "Web - Whats New"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "Table Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate genera...", "source": "Web - Whats New"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "Here are the step-by-step instructions:\n\nTable Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate genera...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Whats New", "enhanced": true}
{"instruction": "What is the CPMS website?", "input": "", "output": "Table Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate genera...", "source": "Web - Whats New"}
{"instruction": "When is pension credited?", "input": "", "output": "Table Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate genera...", "source": "Web - Whats New"}
{"instruction": "How is pension paid?", "input": "", "output": "Table Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate genera...", "source": "Web - Whats New"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Table Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate genera...", "source": "Web - Whats New"}
{"instruction": "What is PPO?", "input": "", "output": "Table Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate genera...", "source": "Web - Whats New"}
{"instruction": "How to get PPO?", "input": "", "output": "Table Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate genera...", "source": "Web - Whats New"}
{"instruction": "How to get PPO?", "input": "", "output": "Here are the step-by-step instructions:\n\nTable Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate genera...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Whats New", "enhanced": true}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "Table Data:\n\n\nProcess of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) | Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) | SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) | SAMPANN Login Creation for Family and Migrated Pensioners (English) | Generation of Pension Slip in SAMPANN Hindi | Generation of Pension Slip in SAMPANN English | Process of  Life Certificate/Digital Life certificate genera...", "source": "Web - Whats New"}
{"instruction": "What is CPMS?", "input": "", "output": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/ SAMPANN Contact details for access by pensioners. CPMS portal at\nwww. in\nToll Free number – 1800-113-5800\n(Timings – 9:30 AM to 6:00 PM except on weekends & gazetted  holidays)\nCentralized helpdesk Mail Id – sampann[dot]cpms-dot[at]gov[dot]in\nAll CCA Contact Details -\nClick here\nSAMPANN APP -  Mobile application available on google play store\nFAQ For Pensioners -\nClick here\nClose\nContact us\nEmail...", "source": "Web - Home"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/ SAMPANN Contact details for access by pensioners. CPMS portal at\nwww. in\nToll Free number – 1800-113-5800\n(Timings – 9:30 AM to 6:00 PM except on weekends & gazetted  holidays)\nCentralized helpdesk Mail Id – sampann[dot]cpms-dot[at]gov[dot]in\nAll CCA Contact Details -\nClick here\nSAMPANN APP -  Mobile application available on google play store\nFAQ For Pensioners -\nClick here\nClose\nContact us\nEmail...", "source": "Web - Home"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/ SAMPANN Contact details for access by pensioners. CPMS portal at\nwww. in\nToll Free number – 1800-113-5800\n(Timings – 9:30 AM to 6:00 PM except on weekends & gazetted  holidays)\nCentralized helpdesk Mail Id – sampann[dot]cpms-dot[at]gov[dot]in\nAll CCA Contact Details -\nClick here\nSAMPANN APP -  Mobile application available on google play store\nFAQ For Pensioners -\nClick here\nClose\nContact us\nEmail...", "source": "Web - Home"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/ SAMPANN Contact details for access by pensioners. CPMS portal at\nwww. यदि आपका जीवन प्रमाण पत्र 31 मई, 2025 को या उससे पहले समाप्त हो गया है, तो कृपया पेंशन रोकने से बचने के लिए 20 जून, 2025 तक या उससे पहले जीवन प्रमाण पत्र जमा करें। इसके अलावा, यदि आपका जीवन प्रमाण पत्र आगामी महीनों में समाप्त हो रहा है, तो कृपया निर्बाध पेंशन भुगतान के लिए समाप्ति से पहले इसे जमा करना सुनिश्चित करें।\nInformatio...", "source": "Web - Home"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/ SAMPANN Contact details for access by pensioners. CPMS portal at\nwww. यदि आपका जीवन प्रमाण पत्र 31 मई, 2025 को या उससे पहले समाप्त हो गया है, तो कृपया पेंशन रोकने से बचने के लिए 20 जून, 2025 तक या उससे पहले जीवन प्रमाण पत्र जमा करें। इसके अलावा, यदि आपका जीवन प्रमाण पत्र आगामी महीनों में समाप्त हो रहा है, तो कृपया निर्बाध पेंशन भुगतान के लिए समाप्ति से पहले इसे जमा करना सुनिश्चित करें।\nInformatio...", "source": "Web - Home"}
{"instruction": "What is the CPMS website?", "input": "", "output": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/ SAMPANN Contact details for access by pensioners. CPMS portal at\nwww. यदि आपका जीवन प्रमाण पत्र 31 मई, 2025 को या उससे पहले समाप्त हो गया है, तो कृपया पेंशन रोकने से बचने के लिए 20 जून, 2025 तक या उससे पहले जीवन प्रमाण पत्र जमा करें। इसके अलावा, यदि आपका जीवन प्रमाण पत्र आगामी महीनों में समाप्त हो रहा है, तो कृपया निर्बाध पेंशन भुगतान के लिए समाप्ति से पहले इसे जमा करना सुनिश्चित करें।\nInformatio...", "source": "Web - Home"}
{"instruction": "When is pension credited?", "input": "", "output": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/ SAMPANN Contact details for access by pensioners. dotpension. in\nToll Free number – 1800-113-5800\n(Timings – 9:30 AM to 6:00 PM except on weekends & gazetted  holidays)\nCentralized helpdesk Mail Id – sampann[dot]cpms-dot[at]gov[dot]in\nAll CCA Contact Details -\nClick here\nSAMPANN APP -  Mobile application available on google play store\nFAQ For Pensioners -\nClick here\nClose\nContact us\nEmail : suppo...", "source": "Web - Home"}
{"instruction": "How is pension paid?", "input": "", "output": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/ SAMPANN Contact details for access by pensioners. dotpension. in\nToll Free number – 1800-113-5800\n(Timings – 9:30 AM to 6:00 PM except on weekends & gazetted  holidays)\nCentralized helpdesk Mail Id – sampann[dot]cpms-dot[at]gov[dot]in\nAll CCA Contact Details -\nClick here\nSAMPANN APP -  Mobile application available on google play store\nFAQ For Pensioners -\nClick here\nClose\nContact us\nEmail : suppo...", "source": "Web - Home"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/ SAMPANN Contact details for access by pensioners. dotpension. in\nToll Free number – 1800-113-5800\n(Timings – 9:30 AM to 6:00 PM except on weekends & gazetted  holidays)\nCentralized helpdesk Mail Id – sampann[dot]cpms-dot[at]gov[dot]in\nAll CCA Contact Details -\nClick here\nSAMPANN APP -  Mobile application available on google play store\nFAQ For Pensioners -\nClick here\nClose\nContact us\nEmail : suppo...", "source": "Web - Home"}
{"instruction": "What is PPO?", "input": "", "output": "in\nToll Free number – 1800-113-5800\n(Timings – 9:30 AM to 6:00 PM except on weekends & gazetted  holidays)\nCentralized helpdesk Mail Id – sampann[dot]cpms-dot[at]gov[dot]in\nAll CCA Contact Details -\nClick here\nSAMPANN APP -  Mobile application available on google play store\nFAQ For Pensioners -\nClick here\nClose\nContact us\nEmail : support[dot]cpms-dot[at]nic[dot]in\nFor Technical Help : 1800-113-5800\nFor Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\nClose\nAlert\nTo know your Ident...", "source": "Web - Home"}
{"instruction": "How to get PPO?", "input": "", "output": "in\nToll Free number – 1800-113-5800\n(Timings – 9:30 AM to 6:00 PM except on weekends & gazetted  holidays)\nCentralized helpdesk Mail Id – sampann[dot]cpms-dot[at]gov[dot]in\nAll CCA Contact Details -\nClick here\nSAMPANN APP -  Mobile application available on google play store\nFAQ For Pensioners -\nClick here\nClose\nContact us\nEmail : support[dot]cpms-dot[at]nic[dot]in\nFor Technical Help : 1800-113-5800\nFor Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\nClose\nAlert\nTo know your Ident...", "source": "Web - Home"}
{"instruction": "How to get PPO?", "input": "", "output": "Here are the step-by-step instructions:\n\nin\nToll Free number – 1800-113-5800\n(Timings – 9:30 AM to 6:00 PM except on weekends & gazetted  holidays)\nCentralized helpdesk Mail Id – sampann[dot]cpms-dot[at]gov[dot]in\nAll CCA Contact Details -\nClick here\nSAMPANN APP -  Mobile application available on google play store\nFAQ For Pensioners -\nClick here\nClose\nContact us\nEmail : support[dot]cpms-dot[at]nic[dot]in\nFor Technical Help : 1800-113-5800\nFor Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\nClose\nAlert\nTo know your Ident...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Home", "enhanced": true}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "in\nToll Free number – 1800-113-5800\n(Timings – 9:30 AM to 6:00 PM except on weekends & gazetted  holidays)\nCentralized helpdesk Mail Id – sampann[dot]cpms-dot[at]gov[dot]in\nAll CCA Contact Details -\nClick here\nSAMPANN APP -  Mobile application available on google play store\nFAQ For Pensioners -\nClick here\nClose\nContact us\nEmail : support[dot]cpms-dot[at]nic[dot]in\nFor Technical Help : 1800-113-5800\nFor Administrative and Training Help : dycgcabait-dot[at]gov[dot]in\nClose\nAlert\nTo know your Ident...", "source": "Web - Home"}
{"instruction": "How to change bank account?", "input": "", "output": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/ SAMPANN Contact details for access by pensioners. यदि आपका जीवन प्रमाण पत्र 31 मई, 2025 को या उससे पहले समाप्त हो गया है, तो कृपया पेंशन रोकने से बचने के लिए 20 जून, 2025 तक या उससे पहले जीवन प्रमाण पत्र जमा करें। इसके अलावा, यदि आपका जीवन प्रमाण पत्र आगामी महीनों में समाप्त हो रहा है, तो कृपया निर्बाध पेंशन भुगतान के लिए समाप्ति से पहले इसे जमा करना सुनिश्चित करें।\nInformation\nContact Us\nKnow Yo...", "source": "Web - Home"}
{"instruction": "Can I change my bank details?", "input": "", "output": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/ SAMPANN Contact details for access by pensioners. यदि आपका जीवन प्रमाण पत्र 31 मई, 2025 को या उससे पहले समाप्त हो गया है, तो कृपया पेंशन रोकने से बचने के लिए 20 जून, 2025 तक या उससे पहले जीवन प्रमाण पत्र जमा करें। इसके अलावा, यदि आपका जीवन प्रमाण पत्र आगामी महीनों में समाप्त हो रहा है, तो कृपया निर्बाध पेंशन भुगतान के लिए समाप्ति से पहले इसे जमा करना सुनिश्चित करें।\nInformation\nContact Us\nKnow Yo...", "source": "Web - Home"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/ SAMPANN Contact details for access by pensioners. यदि आपका जीवन प्रमाण पत्र 31 मई, 2025 को या उससे पहले समाप्त हो गया है, तो कृपया पेंशन रोकने से बचने के लिए 20 जून, 2025 तक या उससे पहले जीवन प्रमाण पत्र जमा करें। इसके अलावा, यदि आपका जीवन प्रमाण पत्र आगामी महीनों में समाप्त हो रहा है, तो कृपया निर्बाध पेंशन भुगतान के लिए समाप्ति से पहले इसे जमा करना सुनिश्चित करें।\nInformation\nContact Us\nKnow Yo...", "source": "Web - Home"}
{"instruction": "Is TDS deducted from pension?", "input": "", "output": "यदि आपका जीवन प्रमाण पत्र 31 मई, 2025 को या उससे पहले समाप्त हो गया है, तो कृपया पेंशन रोकने से बचने के लिए 20 जून, 2025 तक या उससे पहले जीवन प्रमाण पत्र जमा करें। इसके अलावा, यदि आपका जीवन प्रमाण पत्र आगामी महीनों में समाप्त हो रहा है, तो कृपया निर्बाध पेंशन भुगतान के लिए समाप्ति से पहले इसे जमा करना सुनिश्चित करें।\nInformation\nContact Us\nKnow Your Pensioner (KYP) Form\nKnow Your PPO No\nHelp line\nSelect Your Tax Regime\nLife Certificate Validity Check\n440931\nPensioners Benefited\n********\nTransact...", "source": "Web - Home"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "यदि आपका जीवन प्रमाण पत्र 31 मई, 2025 को या उससे पहले समाप्त हो गया है, तो कृपया पेंशन रोकने से बचने के लिए 20 जून, 2025 तक या उससे पहले जीवन प्रमाण पत्र जमा करें। इसके अलावा, यदि आपका जीवन प्रमाण पत्र आगामी महीनों में समाप्त हो रहा है, तो कृपया निर्बाध पेंशन भुगतान के लिए समाप्ति से पहले इसे जमा करना सुनिश्चित करें।\nInformation\nContact Us\nKnow Your Pensioner (KYP) Form\nKnow Your PPO No\nHelp line\nSelect Your Tax Regime\nLife Certificate Validity Check\n440931\nPensioners Benefited\n********\nTransact...", "source": "Web - Home"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "Here are the step-by-step instructions:\n\nयदि आपका जीवन प्रमाण पत्र 31 मई, 2025 को या उससे पहले समाप्त हो गया है, तो कृपया पेंशन रोकने से बचने के लिए 20 जून, 2025 तक या उससे पहले जीवन प्रमाण पत्र जमा करें। इसके अलावा, यदि आपका जीवन प्रमाण पत्र आगामी महीनों में समाप्त हो रहा है, तो कृपया निर्बाध पेंशन भुगतान के लिए समाप्ति से पहले इसे जमा करना सुनिश्चित करें।\nInformation\nContact Us\nKnow Your Pensioner (KYP) Form\nKnow Your PPO No\nHelp line\nSelect Your Tax Regime\nLife Certificate Validity Check\n440931\nPensioners Benefited\n********\nTransact...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Home", "enhanced": true}
{"instruction": "Where to get Form 16?", "input": "", "output": "यदि आपका जीवन प्रमाण पत्र 31 मई, 2025 को या उससे पहले समाप्त हो गया है, तो कृपया पेंशन रोकने से बचने के लिए 20 जून, 2025 तक या उससे पहले जीवन प्रमाण पत्र जमा करें। इसके अलावा, यदि आपका जीवन प्रमाण पत्र आगामी महीनों में समाप्त हो रहा है, तो कृपया निर्बाध पेंशन भुगतान के लिए समाप्ति से पहले इसे जमा करना सुनिश्चित करें।\nInformation\nContact Us\nKnow Your Pensioner (KYP) Form\nKnow Your PPO No\nHelp line\nSelect Your Tax Regime\nLife Certificate Validity Check\n440931\nPensioners Benefited\n********\nTransact...", "source": "Web - Home"}
{"instruction": "What is CPMS?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. Faster processing of arrears and revision of pension UL List 2: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 3: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "source": "Web - About Us"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. Faster processing of arrears and revision of pension UL List 2: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 3: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "source": "Web - About Us"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. Faster processing of arrears and revision of pension UL List 2: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 3: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "source": "Web - About Us"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "Faster processing of arrears and revision of pension UL List 2: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 3: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "source": "Web - About Us"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "Faster processing of arrears and revision of pension UL List 2: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 3: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "source": "Web - About Us"}
{"instruction": "What is the CPMS website?", "input": "", "output": "Faster processing of arrears and revision of pension UL List 2: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 3: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "source": "Web - About Us"}
{"instruction": "When is pension credited?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home AboutUs Scraped: 2025-06-10 11:55:20 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reduces paper work Tracking of pension status from home encourages transparency and accountability Covers 3. 5 lakh current pensioners family pensioners and 1", "source": "Web - About Us"}
{"instruction": "How is pension paid?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home AboutUs Scraped: 2025-06-10 11:55:20 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reduces paper work Tracking of pension status from home encourages transparency and accountability Covers 3. 5 lakh current pensioners family pensioners and 1", "source": "Web - About Us"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home AboutUs Scraped: 2025-06-10 11:55:20 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reduces paper work Tracking of pension status from home encourages transparency and accountability Covers 3. 5 lakh current pensioners family pensioners and 1", "source": "Web - About Us"}
{"instruction": "How to lodge a grievance?", "input": "", "output": "in Home AboutUs Scraped: 2025-06-10 11:55:20 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reduces paper work Tracking of pension status from home encourages transparency and accountability Covers 3", "source": "Web - About Us"}
{"instruction": "What is the helpline number?", "input": "", "output": "in Home AboutUs Scraped: 2025-06-10 11:55:20 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reduces paper work Tracking of pension status from home encourages transparency and accountability Covers 3", "source": "Web - About Us"}
{"instruction": "How to contact support?", "input": "", "output": "in Home AboutUs Scraped: 2025-06-10 11:55:20 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reduces paper work Tracking of pension status from home encourages transparency and accountability Covers 3", "source": "Web - About Us"}
{"instruction": "What is PPO?", "input": "", "output": "Faster processing of arrears and revision of pension UL List 2: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 3: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "source": "Web - About Us"}
{"instruction": "How to get PPO?", "input": "", "output": "Faster processing of arrears and revision of pension UL List 2: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 3: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "source": "Web - About Us"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "Faster processing of arrears and revision of pension UL List 2: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 3: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "source": "Web - About Us"}
{"instruction": "How to apply for family pension?", "input": "", "output": "5 lakh current pensioners family pensioners and 1", "source": "Web - About Us"}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "5 lakh current pensioners family pensioners and 1", "source": "Web - About Us"}
{"instruction": "When does family pension start?", "input": "", "output": "5 lakh current pensioners family pensioners and 1", "source": "Web - About Us"}
{"instruction": "How to change bank account?", "input": "", "output": "in Home AboutUs Scraped: 2025-06-10 11:55:20 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reduces paper work Tracking of pension status from home encourages transparency and accountability Covers 3", "source": "Web - About Us"}
{"instruction": "Can I change my bank details?", "input": "", "output": "in Home AboutUs Scraped: 2025-06-10 11:55:20 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reduces paper work Tracking of pension status from home encourages transparency and accountability Covers 3", "source": "Web - About Us"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "in Home AboutUs Scraped: 2025-06-10 11:55:20 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reduces paper work Tracking of pension status from home encourages transparency and accountability Covers 3", "source": "Web - About Us"}
{"instruction": "What is CPMS?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03...", "source": "Web - Circulars"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03...", "source": "Web - Circulars"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03...", "source": "Web - Circulars"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03 2021 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot...", "source": "Web - Circulars"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03 2021 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot...", "source": "Web - Circulars"}
{"instruction": "What is the CPMS website?", "input": "", "output": "Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03 2021 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot...", "source": "Web - Circulars"}
{"instruction": "When is pension credited?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03...", "source": "Web - Circulars"}
{"instruction": "How is pension paid?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03...", "source": "Web - Circulars"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03...", "source": "Web - Circulars"}
{"instruction": "What is PPO?", "input": "", "output": "Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03 2021 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot...", "source": "Web - Circulars"}
{"instruction": "How to get PPO?", "input": "", "output": "Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03 2021 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot...", "source": "Web - Circulars"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03 2021 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot...", "source": "Web - Circulars"}
{"instruction": "How to apply for family pension?", "input": "", "output": "Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03 2021 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot...", "source": "Web - Circulars"}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03 2021 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot...", "source": "Web - Circulars"}
{"instruction": "When does family pension start?", "input": "", "output": "Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03 2021 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot...", "source": "Web - Circulars"}
{"instruction": "Is TDS deducted from pension?", "input": "", "output": "Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03 2021 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot...", "source": "Web - Circulars"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03 2021 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot...", "source": "Web - Circulars"}
{"instruction": "Where to get Form 16?", "input": "", "output": "Order Circular Name Download File Dated 1 Know Your Pensioner KYP Form 26 12 2022 2 Submission of LC by Pensioners at any CCA Office 23 06 2021 3 Guidelines on submission of documents such as Life Certificate by SAMPANN beneficiaries 22 08 2019 4 Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries 22 08 2019 5 Submission of Family Pension Claim Form and Commutation Forms directly to CCA Offices 04 03 2021 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot...", "source": "Web - Circulars"}
{"instruction": "When is pension credited?", "input": "", "output": "Title: No title found URL: https: dotpension", "source": "Web - Contact"}
{"instruction": "How is pension paid?", "input": "", "output": "Title: No title found URL: https: dotpension", "source": "Web - Contact"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Title: No title found URL: https: dotpension", "source": "Web - Contact"}
{"instruction": "What is CPMS?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in FAQ Index Scraped: 2025-06-10 11:55:29 TABLES Table 1: Headers: 1) What is CPMS? Ans : Comprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. 2) How to get connected to CPMS portal? Ans : Retirees can get connected by logging on to www", "source": "Web - FAQ"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in FAQ Index Scraped: 2025-06-10 11:55:29 TABLES Table 1: Headers: 1) What is CPMS? Ans : Comprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. 2) How to get connected to CPMS portal? Ans : Retirees can get connected by logging on to www", "source": "Web - FAQ"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in FAQ Index Scraped: 2025-06-10 11:55:29 TABLES Table 1: Headers: 1) What is CPMS? Ans : Comprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. 2) How to get connected to CPMS portal? Ans : Retirees can get connected by logging on to www", "source": "Web - FAQ"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "2) How to get connected to CPMS portal? Ans : Retirees can get connected by logging on to www. The pensioner will be able to access and take print-out of details of pension payment by logging into CPMS with the login ID provided to the pensioner. 9) Can the pensioner login to CPMS? Ans : Yes", "source": "Web - FAQ"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "2) How to get connected to CPMS portal? Ans : Retirees can get connected by logging on to www. The pensioner will be able to access and take print-out of details of pension payment by logging into CPMS with the login ID provided to the pensioner. 9) Can the pensioner login to CPMS? Ans : Yes", "source": "Web - FAQ"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "Here are the step-by-step instructions:\n\n2) How to get connected to CPMS portal? Ans : Retirees can get connected by logging on to www. The pensioner will be able to access and take print-out of details of pension payment by logging into CPMS with the login ID provided to the pensioner. 9) Can the pensioner login to CPMS? Ans : Yes\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - FAQ", "enhanced": true}
{"instruction": "What is the CPMS website?", "input": "", "output": "2) How to get connected to CPMS portal? Ans : Retirees can get connected by logging on to www. The pensioner will be able to access and take print-out of details of pension payment by logging into CPMS with the login ID provided to the pensioner. 9) Can the pensioner login to CPMS? Ans : Yes", "source": "Web - FAQ"}
{"instruction": "When is pension credited?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in FAQ Index Scraped: 2025-06-10 11:55:29 TABLES Table 1: Headers: 1) What is CPMS? Ans : Comprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. It covers all activities starting from pension sanction, authorization, and disbursement", "source": "Web - FAQ"}
{"instruction": "How is pension paid?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in FAQ Index Scraped: 2025-06-10 11:55:29 TABLES Table 1: Headers: 1) What is CPMS? Ans : Comprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. It covers all activities starting from pension sanction, authorization, and disbursement", "source": "Web - FAQ"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in FAQ Index Scraped: 2025-06-10 11:55:29 TABLES Table 1: Headers: 1) What is CPMS? Ans : Comprehensive Pension Management System (CPMS) is an end-to-end software developed and implemented by the Department of Telecom. It covers all activities starting from pension sanction, authorization, and disbursement", "source": "Web - FAQ"}
{"instruction": "How to lodge a grievance?", "input": "", "output": "Lodge a grievance from the CPMS portal itself. In case of any grievance, he she can lodge the same from his dashboard itself. 20) Which office will the pensioner approach for grievance resolution? Ans : Grievance can be lodged from the Pensioner s dashboard online", "source": "Web - FAQ"}
{"instruction": "What is the helpline number?", "input": "", "output": "Lodge a grievance from the CPMS portal itself. In case of any grievance, he she can lodge the same from his dashboard itself. 20) Which office will the pensioner approach for grievance resolution? Ans : Grievance can be lodged from the Pensioner s dashboard online", "source": "Web - FAQ"}
{"instruction": "How to contact support?", "input": "", "output": "Lodge a grievance from the CPMS portal itself. In case of any grievance, he she can lodge the same from his dashboard itself. 20) Which office will the pensioner approach for grievance resolution? Ans : Grievance can be lodged from the Pensioner s dashboard online", "source": "Web - FAQ"}
{"instruction": "What is PPO?", "input": "", "output": "7) Will the pensioner get a PPO in new system? Ans : A digitally signed PPO shall be sent to Pensioners dashboard. View PPO and sanction orders 11) What is to be done in case the pensioner wants to change the state in which his bank branch is located? Ans : The pensioners covered under CPMS needs to submit application in prescribed form along with details of bank account and bank undertaking to the CCA office. There will be no need for recalling PPO from Bank and transferring pension case from o...", "source": "Web - FAQ"}
{"instruction": "How to get PPO?", "input": "", "output": "7) Will the pensioner get a PPO in new system? Ans : A digitally signed PPO shall be sent to Pensioners dashboard. View PPO and sanction orders 11) What is to be done in case the pensioner wants to change the state in which his bank branch is located? Ans : The pensioners covered under CPMS needs to submit application in prescribed form along with details of bank account and bank undertaking to the CCA office. There will be no need for recalling PPO from Bank and transferring pension case from o...", "source": "Web - FAQ"}
{"instruction": "How to get PPO?", "input": "", "output": "Here are the step-by-step instructions:\n\n7) Will the pensioner get a PPO in new system? Ans : A digitally signed PPO shall be sent to Pensioners dashboard. View PPO and sanction orders 11) What is to be done in case the pensioner wants to change the state in which his bank branch is located? Ans : The pensioners covered under CPMS needs to submit application in prescribed form along with details of bank account and bank undertaking to the CCA office. There will be no need for recalling PPO from Bank and transferring pension case from o...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - FAQ", "enhanced": true}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "7) Will the pensioner get a PPO in new system? Ans : A digitally signed PPO shall be sent to Pensioners dashboard. View PPO and sanction orders 11) What is to be done in case the pensioner wants to change the state in which his bank branch is located? Ans : The pensioners covered under CPMS needs to submit application in prescribed form along with details of bank account and bank undertaking to the CCA office. There will be no need for recalling PPO from Bank and transferring pension case from o...", "source": "Web - FAQ"}
{"instruction": "How to apply for family pension?", "input": "", "output": "12) In case of Family Pension, which office should the dependents approach? Ans : In case of unfortunate death of the employee pensioner, the Death Certificate along with Life certificate (or DLC) of family pensioner may be sent to CCA office. 13) When does the family pension commence? Ans : Once the Death Certificate is received, the pension can either start in the same month or the next. Family pensioner need not visit CCA office for commencing disbursement of family pension", "source": "Web - FAQ"}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "12) In case of Family Pension, which office should the dependents approach? Ans : In case of unfortunate death of the employee pensioner, the Death Certificate along with Life certificate (or DLC) of family pensioner may be sent to CCA office. 13) When does the family pension commence? Ans : Once the Death Certificate is received, the pension can either start in the same month or the next. Family pensioner need not visit CCA office for commencing disbursement of family pension", "source": "Web - FAQ"}
{"instruction": "When does family pension start?", "input": "", "output": "12) In case of Family Pension, which office should the dependents approach? Ans : In case of unfortunate death of the employee pensioner, the Death Certificate along with Life certificate (or DLC) of family pensioner may be sent to CCA office. 13) When does the family pension commence? Ans : Once the Death Certificate is received, the pension can either start in the same month or the next. Family pensioner need not visit CCA office for commencing disbursement of family pension", "source": "Web - FAQ"}
{"instruction": "How to change bank account?", "input": "", "output": "3) What changes after CPMS implementation? Ans : The pensioner will continue to be credited to the designated bank account of the pensioner after implementation of CPMS. However, Controller of Communication Accounts (CCA) becomes the pension disbursing authority (PDA) in place of Post Offices and CPPC of Banks. 4) How does the Pensioner benefit from implementation of CPMS? Ans : CPMS implementation will benefit the pensioner in the following ways: Direct transfer of pension by the CCA to the pen...", "source": "Web - FAQ"}
{"instruction": "Can I change my bank details?", "input": "", "output": "3) What changes after CPMS implementation? Ans : The pensioner will continue to be credited to the designated bank account of the pensioner after implementation of CPMS. However, Controller of Communication Accounts (CCA) becomes the pension disbursing authority (PDA) in place of Post Offices and CPPC of Banks. 4) How does the Pensioner benefit from implementation of CPMS? Ans : CPMS implementation will benefit the pensioner in the following ways: Direct transfer of pension by the CCA to the pen...", "source": "Web - FAQ"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "3) What changes after CPMS implementation? Ans : The pensioner will continue to be credited to the designated bank account of the pensioner after implementation of CPMS. However, Controller of Communication Accounts (CCA) becomes the pension disbursing authority (PDA) in place of Post Offices and CPPC of Banks. 4) How does the Pensioner benefit from implementation of CPMS? Ans : CPMS implementation will benefit the pensioner in the following ways: Direct transfer of pension by the CCA to the pen...", "source": "Web - FAQ"}
{"instruction": "Is TDS deducted from pension?", "input": "", "output": "TDS and Form-16 will be provided by the CCA without any intervention from the pensioner. 17) Whether Income Tax (TDS) will be deducted at source? Ans : Yes, income tax is deducted at source for all pensioner (not family pensioners), as per provisions of Income Tax Act and instructions issued by the Income Tax Department. 18) Who is responsible for deduction of Income Tax at source from pension payment? Ans : PDA section of CCA office is responsible for deducting the TDS on Pension", "source": "Web - FAQ"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "TDS and Form-16 will be provided by the CCA without any intervention from the pensioner. 17) Whether Income Tax (TDS) will be deducted at source? Ans : Yes, income tax is deducted at source for all pensioner (not family pensioners), as per provisions of Income Tax Act and instructions issued by the Income Tax Department. 18) Who is responsible for deduction of Income Tax at source from pension payment? Ans : PDA section of CCA office is responsible for deducting the TDS on Pension", "source": "Web - FAQ"}
{"instruction": "Where to get Form 16?", "input": "", "output": "TDS and Form-16 will be provided by the CCA without any intervention from the pensioner. 17) Whether Income Tax (TDS) will be deducted at source? Ans : Yes, income tax is deducted at source for all pensioner (not family pensioners), as per provisions of Income Tax Act and instructions issued by the Income Tax Department. 18) Who is responsible for deduction of Income Tax at source from pension payment? Ans : PDA section of CCA office is responsible for deducting the TDS on Pension", "source": "Web - FAQ"}
{"instruction": "What is CPMS?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Scraped: 2025-06-10 11:55:39 Forgot Password Your password will be send on your E-mail address Close Save changes Help line CPMS SAMPANN Contact details for access by pensioners. CPMS portal at www", "source": "Web - Home"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Scraped: 2025-06-10 11:55:39 Forgot Password Your password will be send on your E-mail address Close Save changes Help line CPMS SAMPANN Contact details for access by pensioners. CPMS portal at www", "source": "Web - Home"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Scraped: 2025-06-10 11:55:39 Forgot Password Your password will be send on your E-mail address Close Save changes Help line CPMS SAMPANN Contact details for access by pensioners. CPMS portal at www", "source": "Web - Home"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "in Scraped: 2025-06-10 11:55:39 Forgot Password Your password will be send on your E-mail address Close Save changes Help line CPMS SAMPANN Contact details for access by pensioners. CPMS portal at www. यद आपक ज वन प रम ण पत र 31 मई, 2025 क य उसस पहल सम प त ह गय ह , त क पय प शन र कन स बचन क ल ए 20 ज न, 2025 तक य उसस पहल ज वन प रम ण पत र जम कर इसक अल व , यद आपक ज वन प रम ण पत र आग म मह न म सम प त ह रह ह , त क पय न र ब ध प शन भ गत न क ल ए सम प त स पहल इस जम करन स न श च त कर Information Contact Us K...", "source": "Web - Home"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "in Scraped: 2025-06-10 11:55:39 Forgot Password Your password will be send on your E-mail address Close Save changes Help line CPMS SAMPANN Contact details for access by pensioners. CPMS portal at www. यद आपक ज वन प रम ण पत र 31 मई, 2025 क य उसस पहल सम प त ह गय ह , त क पय प शन र कन स बचन क ल ए 20 ज न, 2025 तक य उसस पहल ज वन प रम ण पत र जम कर इसक अल व , यद आपक ज वन प रम ण पत र आग म मह न म सम प त ह रह ह , त क पय न र ब ध प शन भ गत न क ल ए सम प त स पहल इस जम करन स न श च त कर Information Contact Us K...", "source": "Web - Home"}
{"instruction": "What is the CPMS website?", "input": "", "output": "in Scraped: 2025-06-10 11:55:39 Forgot Password Your password will be send on your E-mail address Close Save changes Help line CPMS SAMPANN Contact details for access by pensioners. CPMS portal at www. यद आपक ज वन प रम ण पत र 31 मई, 2025 क य उसस पहल सम प त ह गय ह , त क पय प शन र कन स बचन क ल ए 20 ज न, 2025 तक य उसस पहल ज वन प रम ण पत र जम कर इसक अल व , यद आपक ज वन प रम ण पत र आग म मह न म सम प त ह रह ह , त क पय न र ब ध प शन भ गत न क ल ए सम प त स पहल इस जम करन स न श च त कर Information Contact Us K...", "source": "Web - Home"}
{"instruction": "When is pension credited?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Scraped: 2025-06-10 11:55:39 Forgot Password Your password will be send on your E-mail address Close Save changes Help line CPMS SAMPANN Contact details for access by pensioners. dotpension", "source": "Web - Home"}
{"instruction": "How is pension paid?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Scraped: 2025-06-10 11:55:39 Forgot Password Your password will be send on your E-mail address Close Save changes Help line CPMS SAMPANN Contact details for access by pensioners. dotpension", "source": "Web - Home"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Scraped: 2025-06-10 11:55:39 Forgot Password Your password will be send on your E-mail address Close Save changes Help line CPMS SAMPANN Contact details for access by pensioners. dotpension", "source": "Web - Home"}
{"instruction": "What is PPO?", "input": "", "output": "in Toll Free number 1800-113-5800 (Timings 9:30 AM to 6:00 PM except on weekends gazetted holidays) Centralized helpdesk Mail Id sampann[dot]cpms-dot[at]gov[dot]in All CCA Contact Details - Click here SAMPANN APP - Mobile application available on google play store FAQ For Pensioners - Click here Close Contact us Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in Close Alert To know your Identification ...", "source": "Web - Home"}
{"instruction": "How to get PPO?", "input": "", "output": "in Toll Free number 1800-113-5800 (Timings 9:30 AM to 6:00 PM except on weekends gazetted holidays) Centralized helpdesk Mail Id sampann[dot]cpms-dot[at]gov[dot]in All CCA Contact Details - Click here SAMPANN APP - Mobile application available on google play store FAQ For Pensioners - Click here Close Contact us Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in Close Alert To know your Identification ...", "source": "Web - Home"}
{"instruction": "How to get PPO?", "input": "", "output": "Here are the step-by-step instructions:\n\nin Toll Free number 1800-113-5800 (Timings 9:30 AM to 6:00 PM except on weekends gazetted holidays) Centralized helpdesk Mail Id sampann[dot]cpms-dot[at]gov[dot]in All CCA Contact Details - Click here SAMPANN APP - Mobile application available on google play store FAQ For Pensioners - Click here Close Contact us Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in Close Alert To know your Identification ...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Home", "enhanced": true}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "in Toll Free number 1800-113-5800 (Timings 9:30 AM to 6:00 PM except on weekends gazetted holidays) Centralized helpdesk Mail Id sampann[dot]cpms-dot[at]gov[dot]in All CCA Contact Details - Click here SAMPANN APP - Mobile application available on google play store FAQ For Pensioners - Click here Close Contact us Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in Close Alert To know your Identification ...", "source": "Web - Home"}
{"instruction": "How to change bank account?", "input": "", "output": "in Scraped: 2025-06-10 11:55:39 Forgot Password Your password will be send on your E-mail address Close Save changes Help line CPMS SAMPANN Contact details for access by pensioners. यद आपक ज वन प रम ण पत र 31 मई, 2025 क य उसस पहल सम प त ह गय ह , त क पय प शन र कन स बचन क ल ए 20 ज न, 2025 तक य उसस पहल ज वन प रम ण पत र जम कर इसक अल व , यद आपक ज वन प रम ण पत र आग म मह न म सम प त ह रह ह , त क पय न र ब ध प शन भ गत न क ल ए सम प त स पहल इस जम करन स न श च त कर Information Contact Us Know Your Pensioner (...", "source": "Web - Home"}
{"instruction": "Can I change my bank details?", "input": "", "output": "in Scraped: 2025-06-10 11:55:39 Forgot Password Your password will be send on your E-mail address Close Save changes Help line CPMS SAMPANN Contact details for access by pensioners. यद आपक ज वन प रम ण पत र 31 मई, 2025 क य उसस पहल सम प त ह गय ह , त क पय प शन र कन स बचन क ल ए 20 ज न, 2025 तक य उसस पहल ज वन प रम ण पत र जम कर इसक अल व , यद आपक ज वन प रम ण पत र आग म मह न म सम प त ह रह ह , त क पय न र ब ध प शन भ गत न क ल ए सम प त स पहल इस जम करन स न श च त कर Information Contact Us Know Your Pensioner (...", "source": "Web - Home"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "in Scraped: 2025-06-10 11:55:39 Forgot Password Your password will be send on your E-mail address Close Save changes Help line CPMS SAMPANN Contact details for access by pensioners. यद आपक ज वन प रम ण पत र 31 मई, 2025 क य उसस पहल सम प त ह गय ह , त क पय प शन र कन स बचन क ल ए 20 ज न, 2025 तक य उसस पहल ज वन प रम ण पत र जम कर इसक अल व , यद आपक ज वन प रम ण पत र आग म मह न म सम प त ह रह ह , त क पय न र ब ध प शन भ गत न क ल ए सम प त स पहल इस जम करन स न श च त कर Information Contact Us Know Your Pensioner (...", "source": "Web - Home"}
{"instruction": "Is TDS deducted from pension?", "input": "", "output": "यद आपक ज वन प रम ण पत र 31 मई, 2025 क य उसस पहल सम प त ह गय ह , त क पय प शन र कन स बचन क ल ए 20 ज न, 2025 तक य उसस पहल ज वन प रम ण पत र जम कर इसक अल व , यद आपक ज वन प रम ण पत र आग म मह न म सम प त ह रह ह , त क पय न र ब ध प शन भ गत न क ल ए सम प त स पहल इस जम करन स न श च त कर Information Contact Us Know Your Pensioner (KYP) Form Know Your PPO No Help line Select Your Tax Regime Life Certificate Validity Check 440931 Pensioners Benefited ******** Transactions Made Helpdesk Email : support[dot]cpms-d...", "source": "Web - Home"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "यद आपक ज वन प रम ण पत र 31 मई, 2025 क य उसस पहल सम प त ह गय ह , त क पय प शन र कन स बचन क ल ए 20 ज न, 2025 तक य उसस पहल ज वन प रम ण पत र जम कर इसक अल व , यद आपक ज वन प रम ण पत र आग म मह न म सम प त ह रह ह , त क पय न र ब ध प शन भ गत न क ल ए सम प त स पहल इस जम करन स न श च त कर Information Contact Us Know Your Pensioner (KYP) Form Know Your PPO No Help line Select Your Tax Regime Life Certificate Validity Check 440931 Pensioners Benefited ******** Transactions Made Helpdesk Email : support[dot]cpms-d...", "source": "Web - Home"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "Here are the step-by-step instructions:\n\nयद आपक ज वन प रम ण पत र 31 मई, 2025 क य उसस पहल सम प त ह गय ह , त क पय प शन र कन स बचन क ल ए 20 ज न, 2025 तक य उसस पहल ज वन प रम ण पत र जम कर इसक अल व , यद आपक ज वन प रम ण पत र आग म मह न म सम प त ह रह ह , त क पय न र ब ध प शन भ गत न क ल ए सम प त स पहल इस जम करन स न श च त कर Information Contact Us Know Your Pensioner (KYP) Form Know Your PPO No Help line Select Your Tax Regime Life Certificate Validity Check 440931 Pensioners Benefited ******** Transactions Made Helpdesk Email : support[dot]cpms-d...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Home", "enhanced": true}
{"instruction": "Where to get Form 16?", "input": "", "output": "यद आपक ज वन प रम ण पत र 31 मई, 2025 क य उसस पहल सम प त ह गय ह , त क पय प शन र कन स बचन क ल ए 20 ज न, 2025 तक य उसस पहल ज वन प रम ण पत र जम कर इसक अल व , यद आपक ज वन प रम ण पत र आग म मह न म सम प त ह रह ह , त क पय न र ब ध प शन भ गत न क ल ए सम प त स पहल इस जम करन स न श च त कर Information Contact Us Know Your Pensioner (KYP) Form Know Your PPO No Help line Select Your Tax Regime Life Certificate Validity Check 440931 Pensioners Benefited ******** Transactions Made Helpdesk Email : support[dot]cpms-d...", "source": "Web - Home"}
{"instruction": "What is CPMS?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. 1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in...", "source": "Web - Manual"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. 1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in...", "source": "Web - Manual"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. 1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in...", "source": "Web - Manual"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 2: Pensioners Portal Department of Telecommunication Jeevan Pr...", "source": "Web - Manual"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 2: Pensioners Portal Department of Telecommunication Jeevan Pr...", "source": "Web - Manual"}
{"instruction": "What is the CPMS website?", "input": "", "output": "1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 2: Pensioners Portal Department of Telecommunication Jeevan Pr...", "source": "Web - Manual"}
{"instruction": "When is pension credited?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. 1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in...", "source": "Web - Manual"}
{"instruction": "How is pension paid?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. 1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in...", "source": "Web - Manual"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. 1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in...", "source": "Web - Manual"}
{"instruction": "What is PPO?", "input": "", "output": "1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 2: Pensioners Portal Department of Telecommunication Jeevan Pr...", "source": "Web - Manual"}
{"instruction": "How to get PPO?", "input": "", "output": "1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 2: Pensioners Portal Department of Telecommunication Jeevan Pr...", "source": "Web - Manual"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 2: Pensioners Portal Department of Telecommunication Jeevan Pr...", "source": "Web - Manual"}
{"instruction": "How to change bank account?", "input": "", "output": "1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 2: Pensioners Portal Department of Telecommunication Jeevan Pr...", "source": "Web - Manual"}
{"instruction": "Can I change my bank details?", "input": "", "output": "1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 2: Pensioners Portal Department of Telecommunication Jeevan Pr...", "source": "Web - Manual"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "1 Bank Undertaking Faq s For Pensioner Actual declaration for Investments and Savings Proposed declaration for Investments and Savings Form 1A to apply for Commutation of Pension Know Your Pensioner (KYP) Form Pension Forms - Form 4, Form 8, Form 10 Form 12 LISTS UL List 1: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 2: Pensioners Portal Department of Telecommunication Jeevan Pr...", "source": "Web - Manual"}
{"instruction": "What is CPMS?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. cpms-dot[at]nic[dot]in. UL List 3: Track pension View ePPO Lodge and track grievances View monthly Statement Stay Updated UL List 4: All India Toll-free helpline: 1800-113-5800 Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in UL List 5: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 6: Pensioner...", "source": "Web - Retiree Corner"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. cpms-dot[at]nic[dot]in. UL List 3: Track pension View ePPO Lodge and track grievances View monthly Statement Stay Updated UL List 4: All India Toll-free helpline: 1800-113-5800 Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in UL List 5: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 6: Pensioner...", "source": "Web - Retiree Corner"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. cpms-dot[at]nic[dot]in. UL List 3: Track pension View ePPO Lodge and track grievances View monthly Statement Stay Updated UL List 4: All India Toll-free helpline: 1800-113-5800 Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in UL List 5: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 6: Pensioner...", "source": "Web - Retiree Corner"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "UL List 3: Track pension View ePPO Lodge and track grievances View monthly Statement Stay Updated UL List 4: All India Toll-free helpline: 1800-113-5800 Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in UL List 5: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 6: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "source": "Web - Retiree Corner"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "UL List 3: Track pension View ePPO Lodge and track grievances View monthly Statement Stay Updated UL List 4: All India Toll-free helpline: 1800-113-5800 Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in UL List 5: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 6: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "source": "Web - Retiree Corner"}
{"instruction": "What is the CPMS website?", "input": "", "output": "UL List 3: Track pension View ePPO Lodge and track grievances View monthly Statement Stay Updated UL List 4: All India Toll-free helpline: 1800-113-5800 Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in UL List 5: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Training Help : dycgcabait-dot[at]gov[dot]in UL List 6: Pensioners Portal Department of Telecommunication Jeevan Pramaan Feedback Contact us Policies", "source": "Web - Retiree Corner"}
{"instruction": "When is pension credited?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home RetireeCorner Scraped: 2025-06-10 11:55:22 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reducing paper work Tracking of pension status from home encourages transparency and accountability Faster processing of arrears and revision of pension OL List 2: Pension Sanction: Pensioner ca...", "source": "Web - Retiree Corner"}
{"instruction": "How is pension paid?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home RetireeCorner Scraped: 2025-06-10 11:55:22 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reducing paper work Tracking of pension status from home encourages transparency and accountability Faster processing of arrears and revision of pension OL List 2: Pension Sanction: Pensioner ca...", "source": "Web - Retiree Corner"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home RetireeCorner Scraped: 2025-06-10 11:55:22 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reducing paper work Tracking of pension status from home encourages transparency and accountability Faster processing of arrears and revision of pension OL List 2: Pension Sanction: Pensioner ca...", "source": "Web - Retiree Corner"}
{"instruction": "How to lodge a grievance?", "input": "", "output": "in Home RetireeCorner Scraped: 2025-06-10 11:55:22 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reducing paper work Tracking of pension status from home encourages transparency and accountability Faster processing of arrears and revision of pension OL List 2: Pension Sanction: Pensioner can view the pension sanctions i. Lodge Grievance: Pensioner can lodge gr...", "source": "Web - Retiree Corner"}
{"instruction": "What is the helpline number?", "input": "", "output": "in Home RetireeCorner Scraped: 2025-06-10 11:55:22 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reducing paper work Tracking of pension status from home encourages transparency and accountability Faster processing of arrears and revision of pension OL List 2: Pension Sanction: Pensioner can view the pension sanctions i. Lodge Grievance: Pensioner can lodge gr...", "source": "Web - Retiree Corner"}
{"instruction": "How to contact support?", "input": "", "output": "in Home RetireeCorner Scraped: 2025-06-10 11:55:22 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reducing paper work Tracking of pension status from home encourages transparency and accountability Faster processing of arrears and revision of pension OL List 2: Pension Sanction: Pensioner can view the pension sanctions i. Lodge Grievance: Pensioner can lodge gr...", "source": "Web - Retiree Corner"}
{"instruction": "What is PPO?", "input": "", "output": "ePPO (Digitally signed PPO), Gratuity sanction and sanction of revision of pension on the dashboard, along with details of commutation payment and arrears. UL List 3: Track pension View ePPO Lodge and track grievances View monthly Statement Stay Updated UL List 4: All India Toll-free helpline: 1800-113-5800 Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in UL List 5: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Trai...", "source": "Web - Retiree Corner"}
{"instruction": "How to get PPO?", "input": "", "output": "ePPO (Digitally signed PPO), Gratuity sanction and sanction of revision of pension on the dashboard, along with details of commutation payment and arrears. UL List 3: Track pension View ePPO Lodge and track grievances View monthly Statement Stay Updated UL List 4: All India Toll-free helpline: 1800-113-5800 Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in UL List 5: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Trai...", "source": "Web - Retiree Corner"}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "ePPO (Digitally signed PPO), Gratuity sanction and sanction of revision of pension on the dashboard, along with details of commutation payment and arrears. UL List 3: Track pension View ePPO Lodge and track grievances View monthly Statement Stay Updated UL List 4: All India Toll-free helpline: 1800-113-5800 Centralized helpdesk Email id- sampann[dot]cpms-dot[at]gov[dot]in UL List 5: Helpdesk Email : support[dot]cpms-dot[at]nic[dot]in For Technical Help : 1800-113-5800 For Administrative and Trai...", "source": "Web - Retiree Corner"}
{"instruction": "How to change bank account?", "input": "", "output": "in Home RetireeCorner Scraped: 2025-06-10 11:55:22 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reducing paper work Tracking of pension status from home encourages transparency and accountability Faster processing of arrears and revision of pension OL List 2: Pension Sanction: Pensioner can view the pension sanctions i", "source": "Web - Retiree Corner"}
{"instruction": "Can I change my bank details?", "input": "", "output": "in Home RetireeCorner Scraped: 2025-06-10 11:55:22 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reducing paper work Tracking of pension status from home encourages transparency and accountability Faster processing of arrears and revision of pension OL List 2: Pension Sanction: Pensioner can view the pension sanctions i", "source": "Web - Retiree Corner"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "in Home RetireeCorner Scraped: 2025-06-10 11:55:22 LISTS OL List 1: Direct disbursement of pension on timely basis without intermediaries Single window system for complete pension process Online grievance management for the pensioners reducing paper work Tracking of pension status from home encourages transparency and accountability Faster processing of arrears and revision of pension OL List 2: Pension Sanction: Pensioner can view the pension sanctions i", "source": "Web - Retiree Corner"}
{"instruction": "What is CPMS?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. LPD and Penalty) User manual for Deactivation module User manual for Enhanced Monthly Bill Enhancements User manual for Family and Migrated Pensioner Dashboard User manual for FMA Update User manual for Income tax related changes for family pensioners User manual for logic of Minimum Pension User manual for Part IV utility User Manual for Pay related revision for BSNL VRS 2019 cases User manual for Pensioner Pension Slip Gene...", "source": "Web - Training"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. LPD and Penalty) User manual for Deactivation module User manual for Enhanced Monthly Bill Enhancements User manual for Family and Migrated Pensioner Dashboard User manual for FMA Update User manual for Income tax related changes for family pensioners User manual for logic of Minimum Pension User manual for Part IV utility User Manual for Pay related revision for BSNL VRS 2019 cases User manual for Pensioner Pension Slip Gene...", "source": "Web - Training"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. LPD and Penalty) User manual for Deactivation module User manual for Enhanced Monthly Bill Enhancements User manual for Family and Migrated Pensioner Dashboard User manual for FMA Update User manual for Income tax related changes for family pensioners User manual for logic of Minimum Pension User manual for Part IV utility User Manual for Pay related revision for BSNL VRS 2019 cases User manual for Pensioner Pension Slip Gene...", "source": "Web - Training"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "LPD and Penalty) User manual for Deactivation module User manual for Enhanced Monthly Bill Enhancements User manual for Family and Migrated Pensioner Dashboard User manual for FMA Update User manual for Income tax related changes for family pensioners User manual for logic of Minimum Pension User manual for Part IV utility User Manual for Pay related revision for BSNL VRS 2019 cases User manual for Pensioner Pension Slip Generation User Manual for Return Delete feature in Revision Module User Ma...", "source": "Web - Training"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "LPD and Penalty) User manual for Deactivation module User manual for Enhanced Monthly Bill Enhancements User manual for Family and Migrated Pensioner Dashboard User manual for FMA Update User manual for Income tax related changes for family pensioners User manual for logic of Minimum Pension User manual for Part IV utility User Manual for Pay related revision for BSNL VRS 2019 cases User manual for Pensioner Pension Slip Generation User Manual for Return Delete feature in Revision Module User Ma...", "source": "Web - Training"}
{"instruction": "What is the CPMS website?", "input": "", "output": "LPD and Penalty) User manual for Deactivation module User manual for Enhanced Monthly Bill Enhancements User manual for Family and Migrated Pensioner Dashboard User manual for FMA Update User manual for Income tax related changes for family pensioners User manual for logic of Minimum Pension User manual for Part IV utility User Manual for Pay related revision for BSNL VRS 2019 cases User manual for Pensioner Pension Slip Generation User Manual for Return Delete feature in Revision Module User Ma...", "source": "Web - Training"}
{"instruction": "When is pension credited?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sle...", "source": "Web - Training"}
{"instruction": "How is pension paid?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sle...", "source": "Web - Training"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sle...", "source": "Web - Training"}
{"instruction": "What is PPO?", "input": "", "output": "in Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sleep through SAMPANN mobile App User Manual failed Cancelled Bill handlin...", "source": "Web - Training"}
{"instruction": "How to get PPO?", "input": "", "output": "in Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sleep through SAMPANN mobile App User Manual failed Cancelled Bill handlin...", "source": "Web - Training"}
{"instruction": "How to get PPO?", "input": "", "output": "Here are the step-by-step instructions:\n\nin Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sleep through SAMPANN mobile App User Manual failed Cancelled Bill handlin...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Training", "enhanced": true}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "in Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sleep through SAMPANN mobile App User Manual failed Cancelled Bill handlin...", "source": "Web - Training"}
{"instruction": "How to apply for family pension?", "input": "", "output": "in Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sleep through SAMPANN mobile App User Manual failed Cancelled Bill handlin...", "source": "Web - Training"}
{"instruction": "How to apply for family pension?", "input": "", "output": "Here are the step-by-step instructions:\n\nin Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sleep through SAMPANN mobile App User Manual failed Cancelled Bill handlin...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Training", "enhanced": true}
{"instruction": "What documents are needed for family pension?", "input": "", "output": "in Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sleep through SAMPANN mobile App User Manual failed Cancelled Bill handlin...", "source": "Web - Training"}
{"instruction": "When does family pension start?", "input": "", "output": "in Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sleep through SAMPANN mobile App User Manual failed Cancelled Bill handlin...", "source": "Web - Training"}
{"instruction": "How to change bank account?", "input": "", "output": "in Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sleep through SAMPANN mobile App User Manual failed Cancelled Bill handlin...", "source": "Web - Training"}
{"instruction": "How to change bank account?", "input": "", "output": "Here are the step-by-step instructions:\n\nin Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sleep through SAMPANN mobile App User Manual failed Cancelled Bill handlin...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Training", "enhanced": true}
{"instruction": "Can I change my bank details?", "input": "", "output": "in Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sleep through SAMPANN mobile App User Manual failed Cancelled Bill handlin...", "source": "Web - Training"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "in Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sleep through SAMPANN mobile App User Manual failed Cancelled Bill handlin...", "source": "Web - Training"}
{"instruction": "What is the process to update bank information?", "input": "", "output": "Here are the step-by-step instructions:\n\nin Home Training Scraped: 2025-06-10 11:55:31 TABLES Table 1: Headers: HoO Module Training Presentation HoO Module Training Video Retiree Form Filling Training Presentation Retiree Form Filling Training Video Revised Process Flow BSNL VRS Manual for Logic of Arrear Calculation on Voucher date Migration_Family Revision User Manual Migration_Pay Revision User Manual Minor Corrections in ePPO Process of Generation of Pension Sleep through SAMPANN mobile App User Manual failed Cancelled Bill handlin...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Training", "enhanced": true}
{"instruction": "Is TDS deducted from pension?", "input": "", "output": "LPD and Penalty) User manual for Deactivation module User manual for Enhanced Monthly Bill Enhancements User manual for Family and Migrated Pensioner Dashboard User manual for FMA Update User manual for Income tax related changes for family pensioners User manual for logic of Minimum Pension User manual for Part IV utility User Manual for Pay related revision for BSNL VRS 2019 cases User manual for Pensioner Pension Slip Generation User Manual for Return Delete feature in Revision Module User Ma...", "source": "Web - Training"}
{"instruction": "How to submit tax declarations?", "input": "", "output": "LPD and Penalty) User manual for Deactivation module User manual for Enhanced Monthly Bill Enhancements User manual for Family and Migrated Pensioner Dashboard User manual for FMA Update User manual for Income tax related changes for family pensioners User manual for logic of Minimum Pension User manual for Part IV utility User Manual for Pay related revision for BSNL VRS 2019 cases User manual for Pensioner Pension Slip Generation User Manual for Return Delete feature in Revision Module User Ma...", "source": "Web - Training"}
{"instruction": "Where to get Form 16?", "input": "", "output": "LPD and Penalty) User manual for Deactivation module User manual for Enhanced Monthly Bill Enhancements User manual for Family and Migrated Pensioner Dashboard User manual for FMA Update User manual for Income tax related changes for family pensioners User manual for logic of Minimum Pension User manual for Part IV utility User Manual for Pay related revision for BSNL VRS 2019 cases User manual for Pensioner Pension Slip Generation User Manual for Return Delete feature in Revision Module User Ma...", "source": "Web - Training"}
{"instruction": "What is CPMS?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMP...", "source": "Web - Whats New"}
{"instruction": "What does CPMS stand for?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMP...", "source": "Web - Whats New"}
{"instruction": "Tell me about the Comprehensive Pension Management System", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMP...", "source": "Web - Whats New"}
{"instruction": "How can I login to CPMS?", "input": "", "output": "in Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMPANN Hindi Generation of Pension Slip in SAMPANN English Process of Life...", "source": "Web - Whats New"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "in Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMPANN Hindi Generation of Pension Slip in SAMPANN English Process of Life...", "source": "Web - Whats New"}
{"instruction": "How to access CPMS portal?", "input": "", "output": "Here are the step-by-step instructions:\n\nin Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMPANN Hindi Generation of Pension Slip in SAMPANN English Process of Life...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Whats New", "enhanced": true}
{"instruction": "What is the CPMS website?", "input": "", "output": "in Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMPANN Hindi Generation of Pension Slip in SAMPANN English Process of Life...", "source": "Web - Whats New"}
{"instruction": "When is pension credited?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMP...", "source": "Web - Whats New"}
{"instruction": "How is pension paid?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMP...", "source": "Web - Whats New"}
{"instruction": "When do I receive my pension?", "input": "", "output": "Title: Comprehensive Pension Management System URL: https: dotpension. in Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMP...", "source": "Web - Whats New"}
{"instruction": "What is PPO?", "input": "", "output": "in Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMPANN Hindi Generation of Pension Slip in SAMPANN English Process of Life...", "source": "Web - Whats New"}
{"instruction": "How to get PPO?", "input": "", "output": "in Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMPANN Hindi Generation of Pension Slip in SAMPANN English Process of Life...", "source": "Web - Whats New"}
{"instruction": "How to get PPO?", "input": "", "output": "Here are the step-by-step instructions:\n\nin Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMPANN Hindi Generation of Pension Slip in SAMPANN English Process of Life...\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "Web - Whats New", "enhanced": true}
{"instruction": "Where can I find my Pension Payment Order?", "input": "", "output": "in Home WhatsNew Scraped: 2025-06-10 11:55:33 TABLES Table 1: Headers: Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(Hindi) Process of raising request of transfer of pension through SAMPANN Pensioner Dashboard-(English) SAMPANN Login Creation for Family and Migrated Pensioners (Hindi) SAMPANN Login Creation for Family and Migrated Pensioners (English) Generation of Pension Slip in SAMPANN Hindi Generation of Pension Slip in SAMPANN English Process of Life...", "source": "Web - Whats New"}
{"instruction": "What is CPMS?", "input": "", "output": "CPMS stands for Comprehensive Pension Management System. It is an end-to-end software developed and implemented by the Department of Telecom that covers all activities starting from pension sanction, authorization, and disbursement. It also enables pensioners to track the latest status of their pension.", "source": "Manual"}
{"instruction": "How can I contact CPMS support?", "input": "", "output": "You can contact CPMS support through: 1) Toll-free helpline: 1800-113-5800, 2) Email: <EMAIL>, 3) Technical support: <EMAIL>, 4) Administrative help: <EMAIL>", "source": "Manual"}
{"instruction": "When is pension credited to my account?", "input": "", "output": "Pension is credited to your account on the last working day of the month. In case of arrears, the amount will be credited out of cycle on the earliest possible day.", "source": "Manual"}
{"instruction": "How to revision of pension to fp (eligible family member mentioned in ppo) ....................... 188?", "input": "", "output": "For this CPMS procedure:\n\n   11.............................................................................................................................. 195 11.Profile Authentication ....................................................................................................... 195\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Revision of Pension to FP (Eligible family member mentioned in PPO) ....................... 188", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What are the steps for revision of pension to fp (eligible family member mentioned in ppo) ....................... 188?", "input": "", "output": "For this CPMS procedure:\n\n   11.............................................................................................................................. 195 11.Profile Authentication ....................................................................................................... 195\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Revision of Pension to FP (Eligible family member mentioned in PPO) ....................... 188", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to complete revision of pension to fp (eligible family member mentioned in ppo) ....................... 188?", "input": "", "output": "For this CPMS procedure:\n\n   11.............................................................................................................................. 195 11.Profile Authentication ....................................................................................................... 195\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Revision of Pension to FP (Eligible family member mentioned in PPO) ....................... 188", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to launching& logging into cpms?", "input": "", "output": "For this CPMS procedure:\n\n   Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpension.gov.in in web browser* to go to login screen. (*the preferred web browser for CPMS use is Google Chrome and for DSC, it is Internet Explorer Version 9 or 11). 2. Enter User Name. 12 3. Enter Password. 4. Enter Captcha. 5. Click on Login button.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Launching& Logging into CPMS", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What are the steps for launching& logging into cpms?", "input": "", "output": "For this CPMS procedure:\n\n   Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpension.gov.in in web browser* to go to login screen. (*the preferred web browser for CPMS use is Google Chrome and for DSC, it is Internet Explorer Version 9 or 11). 2. Enter User Name. 12 3. Enter Password. 4. Enter Captcha. 5. Click on Login button.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Launching& Logging into CPMS", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to complete launching& logging into cpms?", "input": "", "output": "For this CPMS procedure:\n\n   Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpension.gov.in in web browser* to go to login screen. (*the preferred web browser for CPMS use is Google Chrome and for DSC, it is Internet Explorer Version 9 or 11). 2. Enter User Name. 12 3. Enter Password. 4. Enter Captcha. 5. Click on Login button.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Launching& Logging into CPMS", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What is this procedure?", "input": "", "output": "For this CPMS procedure:\n\n   Administrator logins screen will show the following options to work with. (1.1.1) 14 1.1.1 Masters: This allows the Administrator User to manage the master data of different modules of CPMS. (1.1.2) 1.1.2 Users: User section consists of user roles, access rights, user registration and DSC approval. It allows the Administrator User to assign and modify roles, access rights and grant DSC approval to different users. (1.1.3) 15 1.1.3 Role Master: Administrator User can see the different roles available in the Role Master and can also add a new role (1.1.4)  Administrator Users Role Master 1.1.4 Role Rights: Administrator User can assign the access rights to the available roles (created by admin) (1.1.5)  Administrator Users Role Rights Click on Action icon of the respective roles to give the access rights.  Administrator User can select the respective role from the dropdown and assign the access of modules to it by clicking on check boxes next to the module name. Fig\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What is this procedure?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   Administrator logins screen will show the following options to work with. (1.1.1) 14 1.1.1 Masters: This allows the Administrator User to manage the master data of different modules of CPMS. (1.1.2) 1.1.2 Users: User section consists of user roles, access rights, user registration and DSC approval. It allows the Administrator User to assign and modify roles, access rights and grant DSC approval to different users. (1.1.3) 15 1.1.3 Role Master: Administrator User can see the different roles available in the Role Master and can also add a new role (1.1.4)  Administrator Users Role Master 1.1.4 Role Rights: Administrator User can assign the access rights to the available roles (created by admin) (1.1.5)  Administrator Users Role Rights Click on Action icon of the respective roles to give the access rights.  Administrator User can select the respective role from the dropdown and assign the access of modules to it by clicking on check boxes next to the module name. Fig\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What are the steps?", "input": "", "output": "For this CPMS procedure:\n\n   Administrator logins screen will show the following options to work with. (1.1.1) 14 1.1.1 Masters: This allows the Administrator User to manage the master data of different modules of CPMS. (1.1.2) 1.1.2 Users: User section consists of user roles, access rights, user registration and DSC approval. It allows the Administrator User to assign and modify roles, access rights and grant DSC approval to different users. (1.1.3) 15 1.1.3 Role Master: Administrator User can see the different roles available in the Role Master and can also add a new role (1.1.4)  Administrator Users Role Master 1.1.4 Role Rights: Administrator User can assign the access rights to the available roles (created by admin) (1.1.5)  Administrator Users Role Rights Click on Action icon of the respective roles to give the access rights.  Administrator User can select the respective role from the dropdown and assign the access of modules to it by clicking on check boxes next to the module name. Fig\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What are the steps?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   Administrator logins screen will show the following options to work with. (1.1.1) 14 1.1.1 Masters: This allows the Administrator User to manage the master data of different modules of CPMS. (1.1.2) 1.1.2 Users: User section consists of user roles, access rights, user registration and DSC approval. It allows the Administrator User to assign and modify roles, access rights and grant DSC approval to different users. (1.1.3) 15 1.1.3 Role Master: Administrator User can see the different roles available in the Role Master and can also add a new role (1.1.4)  Administrator Users Role Master 1.1.4 Role Rights: Administrator User can assign the access rights to the available roles (created by admin) (1.1.5)  Administrator Users Role Rights Click on Action icon of the respective roles to give the access rights.  Administrator User can select the respective role from the dropdown and assign the access of modules to it by clicking on check boxes next to the module name. Fig\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "How to complete this process?", "input": "", "output": "For this CPMS procedure:\n\n   Administrator logins screen will show the following options to work with. (1.1.1) 14 1.1.1 Masters: This allows the Administrator User to manage the master data of different modules of CPMS. (1.1.2) 1.1.2 Users: User section consists of user roles, access rights, user registration and DSC approval. It allows the Administrator User to assign and modify roles, access rights and grant DSC approval to different users. (1.1.3) 15 1.1.3 Role Master: Administrator User can see the different roles available in the Role Master and can also add a new role (1.1.4)  Administrator Users Role Master 1.1.4 Role Rights: Administrator User can assign the access rights to the available roles (created by admin) (1.1.5)  Administrator Users Role Rights Click on Action icon of the respective roles to give the access rights.  Administrator User can select the respective role from the dropdown and assign the access of modules to it by clicking on check boxes next to the module name. Fig\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to complete this process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   Administrator logins screen will show the following options to work with. (1.1.1) 14 1.1.1 Masters: This allows the Administrator User to manage the master data of different modules of CPMS. (1.1.2) 1.1.2 Users: User section consists of user roles, access rights, user registration and DSC approval. It allows the Administrator User to assign and modify roles, access rights and grant DSC approval to different users. (1.1.3) 15 1.1.3 Role Master: Administrator User can see the different roles available in the Role Master and can also add a new role (1.1.4)  Administrator Users Role Master 1.1.4 Role Rights: Administrator User can assign the access rights to the available roles (created by admin) (1.1.5)  Administrator Users Role Rights Click on Action icon of the respective roles to give the access rights.  Administrator User can select the respective role from the dropdown and assign the access of modules to it by clicking on check boxes next to the module name. Fig\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What is this procedure?", "input": "", "output": "For this CPMS procedure:\n\n    Administrator User will click on Submit button. 16 1.1.5 1.1.5 (a) User Registration: It shows the list of all the existing users of the application along with the details. Administrator User can also add new user from this screen. (1.1.6) 17 1.1.6 Reports: This allows the Administrator User to generate the reports as per requirements.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What is this procedure?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n    Administrator User will click on Submit button. 16 1.1.5 1.1.5 (a) User Registration: It shows the list of all the existing users of the application along with the details. Administrator User can also add new user from this screen. (1.1.6) 17 1.1.6 Reports: This allows the Administrator User to generate the reports as per requirements.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What are the steps?", "input": "", "output": "For this CPMS procedure:\n\n    Administrator User will click on Submit button. 16 1.1.5 1.1.5 (a) User Registration: It shows the list of all the existing users of the application along with the details. Administrator User can also add new user from this screen. (1.1.6) 17 1.1.6 Reports: This allows the Administrator User to generate the reports as per requirements.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What are the steps?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n    Administrator User will click on Submit button. 16 1.1.5 1.1.5 (a) User Registration: It shows the list of all the existing users of the application along with the details. Administrator User can also add new user from this screen. (1.1.6) 17 1.1.6 Reports: This allows the Administrator User to generate the reports as per requirements.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "How to complete this process?", "input": "", "output": "For this CPMS procedure:\n\n    Administrator User will click on Submit button. 16 1.1.5 1.1.5 (a) User Registration: It shows the list of all the existing users of the application along with the details. Administrator User can also add new user from this screen. (1.1.6) 17 1.1.6 Reports: This allows the Administrator User to generate the reports as per requirements.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to complete this process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n    Administrator User will click on Submit button. 16 1.1.5 1.1.5 (a) User Registration: It shows the list of all the existing users of the application along with the details. Administrator User can also add new user from this screen. (1.1.6) 17 1.1.6 Reports: This allows the Administrator User to generate the reports as per requirements.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What is this procedure?", "input": "", "output": "For this CPMS procedure:\n\n   a) Administrator User will create CCA user name/password for login. b) CCA or any officer nominated by CCA shall be the highest authority in CCA office for login in CPMS. c) Path to create the CCA user: Login as Administrator  UsersUser Registration Click on “ADD NEW” (fill the user detail and save it).(1.3) 19 1.3 d) In next step Administrator User will assign roles to CCA. e) Path to assign the CCA Role and Rights: Login as Administrator  Users Role Master Click on “ADD NEW” (fill the user detail and save it).. 1.4&1.5) 1.4 20 1.5\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What are the steps?", "input": "", "output": "For this CPMS procedure:\n\n   a) Administrator User will create CCA user name/password for login. b) CCA or any officer nominated by CCA shall be the highest authority in CCA office for login in CPMS. c) Path to create the CCA user: Login as Administrator  UsersUser Registration Click on “ADD NEW” (fill the user detail and save it).(1.3) 19 1.3 d) In next step Administrator User will assign roles to CCA. e) Path to assign the CCA Role and Rights: Login as Administrator  Users Role Master Click on “ADD NEW” (fill the user detail and save it).. 1.4&1.5) 1.4 20 1.5\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to complete this process?", "input": "", "output": "For this CPMS procedure:\n\n   a) Administrator User will create CCA user name/password for login. b) CCA or any officer nominated by CCA shall be the highest authority in CCA office for login in CPMS. c) Path to create the CCA user: Login as Administrator  UsersUser Registration Click on “ADD NEW” (fill the user detail and save it).(1.3) 19 1.3 d) In next step Administrator User will assign roles to CCA. e) Path to assign the CCA Role and Rights: Login as Administrator  Users Role Master Click on “ADD NEW” (fill the user detail and save it).. 1.4&1.5) 1.4 20 1.5\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What is this procedure?", "input": "", "output": "For this CPMS procedure:\n\n   a) CCA will create the login of HoO. b) HoO is the second level login after CCA. c) Path to create the HoO User Login: Login as CCA Users User Registration (Select the Role Type as HoO, fill the user detail and save it).(1.6 (A)) d) For side channel (Telecom Circle) HoO creation, please refer Annexure 1. e) After creating the login, CCA will assign the authorization of modules to HoO by clicking on the Lock icon in the authorization column. (1.6 (i) (A)) f) CCA will subsequently assign rights. . 1.6 (i)(A)) g) CCA will enter/edit details of the HoO. (1.7 (A)) 1.6 (A) 21 Assign Access Rights 1.6 (i) (A) Edit user details 1.7 (A)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What is this procedure?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) CCA will create the login of HoO. b) HoO is the second level login after CCA. c) Path to create the HoO User Login: Login as CCA Users User Registration (Select the Role Type as HoO, fill the user detail and save it).(1.6 (A)) d) For side channel (Telecom Circle) HoO creation, please refer Annexure 1. e) After creating the login, CCA will assign the authorization of modules to HoO by clicking on the Lock icon in the authorization column. (1.6 (i) (A)) f) CCA will subsequently assign rights. . 1.6 (i)(A)) g) CCA will enter/edit details of the HoO. (1.7 (A)) 1.6 (A) 21 Assign Access Rights 1.6 (i) (A) Edit user details 1.7 (A)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What are the steps?", "input": "", "output": "For this CPMS procedure:\n\n   a) CCA will create the login of HoO. b) HoO is the second level login after CCA. c) Path to create the HoO User Login: Login as CCA Users User Registration (Select the Role Type as HoO, fill the user detail and save it).(1.6 (A)) d) For side channel (Telecom Circle) HoO creation, please refer Annexure 1. e) After creating the login, CCA will assign the authorization of modules to HoO by clicking on the Lock icon in the authorization column. (1.6 (i) (A)) f) CCA will subsequently assign rights. . 1.6 (i)(A)) g) CCA will enter/edit details of the HoO. (1.7 (A)) 1.6 (A) 21 Assign Access Rights 1.6 (i) (A) Edit user details 1.7 (A)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What are the steps?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) CCA will create the login of HoO. b) HoO is the second level login after CCA. c) Path to create the HoO User Login: Login as CCA Users User Registration (Select the Role Type as HoO, fill the user detail and save it).(1.6 (A)) d) For side channel (Telecom Circle) HoO creation, please refer Annexure 1. e) After creating the login, CCA will assign the authorization of modules to HoO by clicking on the Lock icon in the authorization column. (1.6 (i) (A)) f) CCA will subsequently assign rights. . 1.6 (i)(A)) g) CCA will enter/edit details of the HoO. (1.7 (A)) 1.6 (A) 21 Assign Access Rights 1.6 (i) (A) Edit user details 1.7 (A)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "How to complete this process?", "input": "", "output": "For this CPMS procedure:\n\n   a) CCA will create the login of HoO. b) HoO is the second level login after CCA. c) Path to create the HoO User Login: Login as CCA Users User Registration (Select the Role Type as HoO, fill the user detail and save it).(1.6 (A)) d) For side channel (Telecom Circle) HoO creation, please refer Annexure 1. e) After creating the login, CCA will assign the authorization of modules to HoO by clicking on the Lock icon in the authorization column. (1.6 (i) (A)) f) CCA will subsequently assign rights. . 1.6 (i)(A)) g) CCA will enter/edit details of the HoO. (1.7 (A)) 1.6 (A) 21 Assign Access Rights 1.6 (i) (A) Edit user details 1.7 (A)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to complete this process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) CCA will create the login of HoO. b) HoO is the second level login after CCA. c) Path to create the HoO User Login: Login as CCA Users User Registration (Select the Role Type as HoO, fill the user detail and save it).(1.6 (A)) d) For side channel (Telecom Circle) HoO creation, please refer Annexure 1. e) After creating the login, CCA will assign the authorization of modules to HoO by clicking on the Lock icon in the authorization column. (1.6 (i) (A)) f) CCA will subsequently assign rights. . 1.6 (i)(A)) g) CCA will enter/edit details of the HoO. (1.7 (A)) 1.6 (A) 21 Assign Access Rights 1.6 (i) (A) Edit user details 1.7 (A)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What is this procedure?", "input": "", "output": "For this CPMS procedure:\n\n   a) DH is the third level login and subordinate to HoO. b) HoO will create the login for DH. 22 c) Path to create the DH User Login: Login as HoO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.8 (A) and 1.9 (A)) d) After creating the login, HoO will assign the authorization of modules to DH by clicking on the Lock icon in the authorization column. 1.9 (i) (A) 1.8 (A) Edit user details 1.9 (A) 23 Assign Access Rights 1.9 (i) (A)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What is this procedure?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) DH is the third level login and subordinate to HoO. b) HoO will create the login for DH. 22 c) Path to create the DH User Login: Login as HoO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.8 (A) and 1.9 (A)) d) After creating the login, HoO will assign the authorization of modules to DH by clicking on the Lock icon in the authorization column. 1.9 (i) (A) 1.8 (A) Edit user details 1.9 (A) 23 Assign Access Rights 1.9 (i) (A)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What are the steps?", "input": "", "output": "For this CPMS procedure:\n\n   a) DH is the third level login and subordinate to HoO. b) HoO will create the login for DH. 22 c) Path to create the DH User Login: Login as HoO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.8 (A) and 1.9 (A)) d) After creating the login, HoO will assign the authorization of modules to DH by clicking on the Lock icon in the authorization column. 1.9 (i) (A) 1.8 (A) Edit user details 1.9 (A) 23 Assign Access Rights 1.9 (i) (A)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What are the steps?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) DH is the third level login and subordinate to HoO. b) HoO will create the login for DH. 22 c) Path to create the DH User Login: Login as HoO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.8 (A) and 1.9 (A)) d) After creating the login, HoO will assign the authorization of modules to DH by clicking on the Lock icon in the authorization column. 1.9 (i) (A) 1.8 (A) Edit user details 1.9 (A) 23 Assign Access Rights 1.9 (i) (A)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "How to complete this process?", "input": "", "output": "For this CPMS procedure:\n\n   a) DH is the third level login and subordinate to HoO. b) HoO will create the login for DH. 22 c) Path to create the DH User Login: Login as HoO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.8 (A) and 1.9 (A)) d) After creating the login, HoO will assign the authorization of modules to DH by clicking on the Lock icon in the authorization column. 1.9 (i) (A) 1.8 (A) Edit user details 1.9 (A) 23 Assign Access Rights 1.9 (i) (A)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to complete this process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) DH is the third level login and subordinate to HoO. b) HoO will create the login for DH. 22 c) Path to create the DH User Login: Login as HoO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.8 (A) and 1.9 (A)) d) After creating the login, HoO will assign the authorization of modules to DH by clicking on the Lock icon in the authorization column. 1.9 (i) (A) 1.8 (A) Edit user details 1.9 (A) 23 Assign Access Rights 1.9 (i) (A)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What is this procedure?", "input": "", "output": "For this CPMS procedure:\n\n   a) AO is the second level login and subordinate to CCA in Pension Section. b) CCA will create the login for AO to work in Pension Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (1.10 and 1.11) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. 1.11 (a) e) CCA will subsequently assign rights. (1.11 (a)) f) Multiple AO Users can be created. 24 1.10 Edit user details 1.11 25 1.11 (a)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What is this procedure?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) AO is the second level login and subordinate to CCA in Pension Section. b) CCA will create the login for AO to work in Pension Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (1.10 and 1.11) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. 1.11 (a) e) CCA will subsequently assign rights. (1.11 (a)) f) Multiple AO Users can be created. 24 1.10 Edit user details 1.11 25 1.11 (a)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What are the steps?", "input": "", "output": "For this CPMS procedure:\n\n   a) AO is the second level login and subordinate to CCA in Pension Section. b) CCA will create the login for AO to work in Pension Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (1.10 and 1.11) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. 1.11 (a) e) CCA will subsequently assign rights. (1.11 (a)) f) Multiple AO Users can be created. 24 1.10 Edit user details 1.11 25 1.11 (a)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What are the steps?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) AO is the second level login and subordinate to CCA in Pension Section. b) CCA will create the login for AO to work in Pension Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (1.10 and 1.11) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. 1.11 (a) e) CCA will subsequently assign rights. (1.11 (a)) f) Multiple AO Users can be created. 24 1.10 Edit user details 1.11 25 1.11 (a)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "How to complete this process?", "input": "", "output": "For this CPMS procedure:\n\n   a) AO is the second level login and subordinate to CCA in Pension Section. b) CCA will create the login for AO to work in Pension Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (1.10 and 1.11) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. 1.11 (a) e) CCA will subsequently assign rights. (1.11 (a)) f) Multiple AO Users can be created. 24 1.10 Edit user details 1.11 25 1.11 (a)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to complete this process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) AO is the second level login and subordinate to CCA in Pension Section. b) CCA will create the login for AO to work in Pension Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (1.10 and 1.11) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. 1.11 (a) e) CCA will subsequently assign rights. (1.11 (a)) f) Multiple AO Users can be created. 24 1.10 Edit user details 1.11 25 1.11 (a)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What is this procedure?", "input": "", "output": "For this CPMS procedure:\n\n   a) AAO is the third level login and subordinate to AO in Pension Section. b) AO will create the login for AAO to work in Pension Section. (1.12 and 1.13) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (1.12 and 1.13) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. 1.13 (a) e) AO will subsequently assign rights. (1.13 (a)) f) Multiple AAO Users can be created. 1.12 26 Edit user details (1.13) Assign Access Rights 1.13 (a)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What is this procedure?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) AAO is the third level login and subordinate to AO in Pension Section. b) AO will create the login for AAO to work in Pension Section. (1.12 and 1.13) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (1.12 and 1.13) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. 1.13 (a) e) AO will subsequently assign rights. (1.13 (a)) f) Multiple AAO Users can be created. 1.12 26 Edit user details (1.13) Assign Access Rights 1.13 (a)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What are the steps?", "input": "", "output": "For this CPMS procedure:\n\n   a) AAO is the third level login and subordinate to AO in Pension Section. b) AO will create the login for AAO to work in Pension Section. (1.12 and 1.13) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (1.12 and 1.13) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. 1.13 (a) e) AO will subsequently assign rights. (1.13 (a)) f) Multiple AAO Users can be created. 1.12 26 Edit user details (1.13) Assign Access Rights 1.13 (a)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What are the steps?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) AAO is the third level login and subordinate to AO in Pension Section. b) AO will create the login for AAO to work in Pension Section. (1.12 and 1.13) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (1.12 and 1.13) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. 1.13 (a) e) AO will subsequently assign rights. (1.13 (a)) f) Multiple AAO Users can be created. 1.12 26 Edit user details (1.13) Assign Access Rights 1.13 (a)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "How to complete this process?", "input": "", "output": "For this CPMS procedure:\n\n   a) AAO is the third level login and subordinate to AO in Pension Section. b) AO will create the login for AAO to work in Pension Section. (1.12 and 1.13) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (1.12 and 1.13) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. 1.13 (a) e) AO will subsequently assign rights. (1.13 (a)) f) Multiple AAO Users can be created. 1.12 26 Edit user details (1.13) Assign Access Rights 1.13 (a)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to complete this process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) AAO is the third level login and subordinate to AO in Pension Section. b) AO will create the login for AAO to work in Pension Section. (1.12 and 1.13) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (1.12 and 1.13) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. 1.13 (a) e) AO will subsequently assign rights. (1.13 (a)) f) Multiple AAO Users can be created. 1.12 26 Edit user details (1.13) Assign Access Rights 1.13 (a)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What is this procedure?", "input": "", "output": "For this CPMS procedure:\n\n   a) DH is the third level login and subordinate to AAO. b) AAO will create the login for DH. c) Path to create the DH User Login: Login as AAO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.14 and 1.15) d) After creating the login, AAO will assign the authorization of modules to DH by clicking on Lock icon in the authorization column. 1.15 (i) e) Multiple DH Users can be created. 27 1.14 Edit user details 1.15 28 Assign Access Rights 1.15 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What is this procedure?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) DH is the third level login and subordinate to AAO. b) AAO will create the login for DH. c) Path to create the DH User Login: Login as AAO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.14 and 1.15) d) After creating the login, AAO will assign the authorization of modules to DH by clicking on Lock icon in the authorization column. 1.15 (i) e) Multiple DH Users can be created. 27 1.14 Edit user details 1.15 28 Assign Access Rights 1.15 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What are the steps?", "input": "", "output": "For this CPMS procedure:\n\n   a) DH is the third level login and subordinate to AAO. b) AAO will create the login for DH. c) Path to create the DH User Login: Login as AAO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.14 and 1.15) d) After creating the login, AAO will assign the authorization of modules to DH by clicking on Lock icon in the authorization column. 1.15 (i) e) Multiple DH Users can be created. 27 1.14 Edit user details 1.15 28 Assign Access Rights 1.15 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What are the steps?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) DH is the third level login and subordinate to AAO. b) AAO will create the login for DH. c) Path to create the DH User Login: Login as AAO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.14 and 1.15) d) After creating the login, AAO will assign the authorization of modules to DH by clicking on Lock icon in the authorization column. 1.15 (i) e) Multiple DH Users can be created. 27 1.14 Edit user details 1.15 28 Assign Access Rights 1.15 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "How to complete this process?", "input": "", "output": "For this CPMS procedure:\n\n   a) DH is the third level login and subordinate to AAO. b) AAO will create the login for DH. c) Path to create the DH User Login: Login as AAO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.14 and 1.15) d) After creating the login, AAO will assign the authorization of modules to DH by clicking on Lock icon in the authorization column. 1.15 (i) e) Multiple DH Users can be created. 27 1.14 Edit user details 1.15 28 Assign Access Rights 1.15 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to complete this process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) DH is the third level login and subordinate to AAO. b) AAO will create the login for DH. c) Path to create the DH User Login: Login as AAO Users User Registration (Select the Role Type as DH, fill the user detail and save it). (1.14 and 1.15) d) After creating the login, AAO will assign the authorization of modules to DH by clicking on Lock icon in the authorization column. 1.15 (i) e) Multiple DH Users can be created. 27 1.14 Edit user details 1.15 28 Assign Access Rights 1.15 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What is this procedure?", "input": "", "output": "For this CPMS procedure:\n\n   a) AO is the second level login and subordinate to CCA in Pension Disbursement Section. b) CCA will create the login for AO to work in Pension Disbursement Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (1.16 and 1.17) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. 1.17 (i) e) CCA will subsequently assign rights. (1.17 (i)) f) Multiple AO Users can be created. 1.16 29 Edit user details (1.17) Assign Access Rights 1.17 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What is this procedure?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) AO is the second level login and subordinate to CCA in Pension Disbursement Section. b) CCA will create the login for AO to work in Pension Disbursement Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (1.16 and 1.17) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. 1.17 (i) e) CCA will subsequently assign rights. (1.17 (i)) f) Multiple AO Users can be created. 1.16 29 Edit user details (1.17) Assign Access Rights 1.17 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What are the steps?", "input": "", "output": "For this CPMS procedure:\n\n   a) AO is the second level login and subordinate to CCA in Pension Disbursement Section. b) CCA will create the login for AO to work in Pension Disbursement Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (1.16 and 1.17) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. 1.17 (i) e) CCA will subsequently assign rights. (1.17 (i)) f) Multiple AO Users can be created. 1.16 29 Edit user details (1.17) Assign Access Rights 1.17 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What are the steps?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) AO is the second level login and subordinate to CCA in Pension Disbursement Section. b) CCA will create the login for AO to work in Pension Disbursement Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (1.16 and 1.17) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. 1.17 (i) e) CCA will subsequently assign rights. (1.17 (i)) f) Multiple AO Users can be created. 1.16 29 Edit user details (1.17) Assign Access Rights 1.17 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "How to complete this process?", "input": "", "output": "For this CPMS procedure:\n\n   a) AO is the second level login and subordinate to CCA in Pension Disbursement Section. b) CCA will create the login for AO to work in Pension Disbursement Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (1.16 and 1.17) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. 1.17 (i) e) CCA will subsequently assign rights. (1.17 (i)) f) Multiple AO Users can be created. 1.16 29 Edit user details (1.17) Assign Access Rights 1.17 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to complete this process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) AO is the second level login and subordinate to CCA in Pension Disbursement Section. b) CCA will create the login for AO to work in Pension Disbursement Section. c) Path to create the AO User Login: Login as CCA Users User Registration (Select the Role Type as AO, fill the user detail and save it). (1.16 and 1.17) d) After creating the login, CCA will assign the authorization of modules to AO by clicking on Lock icon in the authorization column. 1.17 (i) e) CCA will subsequently assign rights. (1.17 (i)) f) Multiple AO Users can be created. 1.16 29 Edit user details (1.17) Assign Access Rights 1.17 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What is this procedure?", "input": "", "output": "For this CPMS procedure:\n\n   a) AAO is the third level login and subordinate to AO in Pension Disbursement Section. b) AO will create the login for AAO to work in Pension Disbursement Section. (1.18 and 1.19) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (1.18 and 1.19) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. 1.19 (i) e) AO will subsequently assign rights. (1.19 (i)) 30 f) Multiple AAO Users can be created. (1.18) Edit user details (1.19) 31 Assign Access Rights 1.19 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What is this procedure?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) AAO is the third level login and subordinate to AO in Pension Disbursement Section. b) AO will create the login for AAO to work in Pension Disbursement Section. (1.18 and 1.19) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (1.18 and 1.19) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. 1.19 (i) e) AO will subsequently assign rights. (1.19 (i)) 30 f) Multiple AAO Users can be created. (1.18) Edit user details (1.19) 31 Assign Access Rights 1.19 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What are the steps?", "input": "", "output": "For this CPMS procedure:\n\n   a) AAO is the third level login and subordinate to AO in Pension Disbursement Section. b) AO will create the login for AAO to work in Pension Disbursement Section. (1.18 and 1.19) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (1.18 and 1.19) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. 1.19 (i) e) AO will subsequently assign rights. (1.19 (i)) 30 f) Multiple AAO Users can be created. (1.18) Edit user details (1.19) 31 Assign Access Rights 1.19 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What are the steps?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) AAO is the third level login and subordinate to AO in Pension Disbursement Section. b) AO will create the login for AAO to work in Pension Disbursement Section. (1.18 and 1.19) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (1.18 and 1.19) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. 1.19 (i) e) AO will subsequently assign rights. (1.19 (i)) 30 f) Multiple AAO Users can be created. (1.18) Edit user details (1.19) 31 Assign Access Rights 1.19 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "How to complete this process?", "input": "", "output": "For this CPMS procedure:\n\n   a) AAO is the third level login and subordinate to AO in Pension Disbursement Section. b) AO will create the login for AAO to work in Pension Disbursement Section. (1.18 and 1.19) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (1.18 and 1.19) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. 1.19 (i) e) AO will subsequently assign rights. (1.19 (i)) 30 f) Multiple AAO Users can be created. (1.18) Edit user details (1.19) 31 Assign Access Rights 1.19 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to complete this process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n   a) AAO is the third level login and subordinate to AO in Pension Disbursement Section. b) AO will create the login for AAO to work in Pension Disbursement Section. (1.18 and 1.19) c) Path to create the AAO User Login: Login as AO Users User Registration (Select the Role Type as AAO, fill the user detail and save it). (1.18 and 1.19) d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column. 1.19 (i) e) AO will subsequently assign rights. (1.19 (i)) 30 f) Multiple AAO Users can be created. (1.18) Edit user details (1.19) 31 Assign Access Rights 1.19 (i)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "How to hoo unit- side channel (steps for?", "input": "", "output": "For this CPMS procedure:\n\n   Processing a Case) This chapter deals with flow of pension papers in the CCA office when the case is processed through Side channel on behalf of the HOO unit and the pensioner to enable creation of digital profile of pensioner. 137\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - HoO Unit- Side channel (Steps for", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What are the steps for hoo unit- side channel (steps for?", "input": "", "output": "For this CPMS procedure:\n\n   Processing a Case) This chapter deals with flow of pension papers in the CCA office when the case is processed through Side channel on behalf of the HOO unit and the pensioner to enable creation of digital profile of pensioner. 137\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - HoO Unit- Side channel (Steps for", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to complete hoo unit- side channel (steps for?", "input": "", "output": "For this CPMS procedure:\n\n   Processing a Case) This chapter deals with flow of pension papers in the CCA office when the case is processed through Side channel on behalf of the HOO unit and the pensioner to enable creation of digital profile of pensioner. 137\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - HoO Unit- Side channel (Steps for", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What is this procedure?", "input": "", "output": "For this CPMS procedure:\n\n    It may be noted while the case is processed via side channel, the timelines related to lose their relevance.  Based on the S/B received in office, same may be filled in the module by DH. If some portion remains unverified it may be filled in remarks column.  There shall be only one level passing. 139\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What is this procedure?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n    It may be noted while the case is processed via side channel, the timelines related to lose their relevance.  Based on the S/B received in office, same may be filled in the module by DH. If some portion remains unverified it may be filled in remarks column.  There shall be only one level passing. 139\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "What are the steps?", "input": "", "output": "For this CPMS procedure:\n\n    It may be noted while the case is processed via side channel, the timelines related to lose their relevance.  Based on the S/B received in office, same may be filled in the module by DH. If some portion remains unverified it may be filled in remarks column.  There shall be only one level passing. 139\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "What are the steps?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n    It may be noted while the case is processed via side channel, the timelines related to lose their relevance.  Based on the S/B received in office, same may be filled in the module by DH. If some portion remains unverified it may be filled in remarks column.  There shall be only one level passing. 139\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
{"instruction": "How to complete this process?", "input": "", "output": "For this CPMS procedure:\n\n    It may be noted while the case is processed via side channel, the timelines related to lose their relevance.  Based on the S/B received in office, same may be filled in the module by DH. If some portion remains unverified it may be filled in remarks column.  There shall be only one level passing. 139\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE"}
{"instruction": "How to complete this process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor this CPMS procedure:\n\n    It may be noted while the case is processed via side channel, the timelines related to lose their relevance.  Based on the S/B received in office, same may be filled in the module by DH. If some portion remains unverified it may be filled in remarks column.  There shall be only one level passing. 139\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "GENERAL_PROCEDURE", "enhanced": true}
