# 🔌 CPMS Chatbot - True Frontend/Backend Separation

## 🎯 API-Based Architecture

Now you can run the frontend and backend as **completely separate services**:

- **Backend**: REST API server providing chatbot functionality
- **Frontend**: Web UI client that connects to the backend API

## 🚀 Commands to Run Separately

### 1️⃣ Start Backend API Server (Run First)
```bash
python run_backend.py
```
**What it does:**
- Starts FastAPI server on `http://localhost:8000`
- Loads the RAG system and models
- Provides REST API endpoints
- Serves `/docs` for API documentation

### 2️⃣ Start Frontend UI Client (Run Second)
```bash
python run_frontend.py
```
**What it does:**
- Starts Gradio web interface on `http://localhost:7861`
- Connects to backend API via HTTP requests
- Provides the chat interface
- Shows "API Mode" in the header

## 📡 API Endpoints

The backend provides these REST API endpoints:

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/` | GET | API information |
| `/health` | GET | Health check and system status |
| `/query` | POST | Process chat queries |
| `/suggestions` | GET | Get suggested questions |
| `/stats` | GET | Get detailed system statistics |
| `/clear-cache` | POST | Clear system caches |
| `/docs` | GET | Interactive API documentation |

## 🔧 Advanced Usage

### Custom API URL
```bash
# Run backend on different port
python backend/api/server.py --port 9000

# Connect frontend to custom backend
python run_frontend.py --api-url http://localhost:9000
```

### API Documentation
Visit `http://localhost:8000/docs` when backend is running to see interactive API docs.

### Direct API Testing
```bash
# Health check
curl http://localhost:8000/health

# Send a query
curl -X POST http://localhost:8000/query \
  -H "Content-Type: application/json" \
  -d '{"message": "What is CPMS?", "include_sources": true}'
```

## 🏗️ Architecture Benefits

### ✅ True Separation
- Frontend and backend run as independent processes
- Communication only via HTTP REST API
- Can be deployed on different servers

### ✅ Scalability
- Multiple frontend instances can connect to one backend
- Backend can serve multiple different frontends
- Easy to load balance and scale horizontally

### ✅ Development Flexibility
- Frontend team can work independently
- Backend team can work independently
- Different technologies can be used for each

### ✅ Deployment Options
- Deploy backend and frontend separately
- Use different hosting providers
- Scale components independently

## 🔄 Migration Paths

### Option 1: API Mode (New)
```bash
# Terminal 1: Backend
python run_backend.py

# Terminal 2: Frontend
python run_frontend.py
```

### Option 2: Integrated Mode (Original)
```bash
# Single process with direct imports
python launch_chatbot.py
```

## 🛠️ Development Workflow

### Backend Development
1. Make changes to backend code
2. Restart: `python run_backend.py`
3. Test API: `http://localhost:8000/docs`

### Frontend Development
1. Make changes to frontend code
2. Restart: `python run_frontend.py`
3. Backend keeps running unchanged

## 📊 Monitoring

### Backend Logs
- FastAPI server logs all requests
- RAG system performance metrics
- Error tracking and debugging

### Frontend Status
- Connection status to backend
- Real-time API communication
- Error handling and user feedback

## 🔒 Security Considerations

### Current Setup (Development)
- CORS enabled for all origins
- No authentication required
- Suitable for local development

### Production Recommendations
- Configure specific CORS origins
- Add API authentication (JWT tokens)
- Use HTTPS for all communication
- Implement rate limiting

## 🧪 Testing the Separation

### Test Backend Only
```bash
# Start backend
python run_backend.py

# Test in another terminal
curl http://localhost:8000/health
```

### Test Frontend Connection
```bash
# With backend running, start frontend
python run_frontend.py

# Should show "Connected to backend API" message
```

## 🎉 Benefits Achieved

1. **✅ Complete Separation**: Frontend and backend are independent services
2. **✅ API-First Design**: RESTful API with proper documentation
3. **✅ Flexible Deployment**: Can run on different servers/containers
4. **✅ Development Independence**: Teams can work separately
5. **✅ Technology Freedom**: Can replace frontend with any framework
6. **✅ Scalability Ready**: Easy to scale and load balance

## 🚀 Quick Start Summary

```bash
# Terminal 1: Start Backend
python run_backend.py
# Wait for "Backend API ready to serve requests!"

# Terminal 2: Start Frontend  
python run_frontend.py
# Wait for "Connected to backend API"

# Open browser: http://localhost:7861
```

The chatbot now runs as a true client-server application with complete frontend/backend separation!
