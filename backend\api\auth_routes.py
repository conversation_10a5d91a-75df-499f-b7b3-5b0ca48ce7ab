"""
Authentication API routes for pensioner login
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from typing import Optional

from backend.database.database import get_db
from backend.services.auth_service import auth_service
from backend.models.pensioner_models import Pensioner

# Create router
router = APIRouter(prefix="/auth", tags=["Authentication"])

# Security scheme
security = HTTPBearer()

# Pydantic models for requests/responses
class LoginRequest(BaseModel):
    eppo_number: str = Field(..., min_length=8, max_length=20, description="EPPO Number")
    mobile_number: str = Field(..., min_length=10, max_length=15, description="Mobile Number")

class OTPVerificationRequest(BaseModel):
    session_id: int = Field(..., description="OTP Session ID")
    otp: str = Field(..., min_length=6, max_length=6, description="6-digit OTP")

class LoginResponse(BaseModel):
    success: bool
    message: str
    session_id: Optional[int] = None
    expires_in: Optional[int] = None
    error_code: Optional[str] = None

class TokenResponse(BaseModel):
    success: bool
    message: str
    access_token: Optional[str] = None
    token_type: Optional[str] = None
    expires_in: Optional[int] = None
    pensioner: Optional[dict] = None
    error_code: Optional[str] = None

class PensionerInfo(BaseModel):
    id: int
    eppo_number: str
    name: str
    mobile_number: str

@router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest, db: Session = Depends(get_db)):
    """
    Initiate login process with EPPO number and mobile number
    Sends OTP to registered mobile number
    """
    try:
        result = auth_service.authenticate_pensioner(
            eppo_number=request.eppo_number.strip().upper(),
            mobile_number=request.mobile_number.strip(),
            db=db
        )
        
        return LoginResponse(**result)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service temporarily unavailable"
        )

@router.post("/verify-otp", response_model=TokenResponse)
async def verify_otp(request: OTPVerificationRequest, db: Session = Depends(get_db)):
    """
    Verify OTP and get access token
    """
    try:
        result = auth_service.verify_otp(
            session_id=request.session_id,
            otp=request.otp.strip(),
            db=db
        )
        
        if not result["success"]:
            # Return appropriate HTTP status for different error types
            if result.get("error_code") == "INVALID_OTP":
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=result["message"]
                )
            elif result.get("error_code") == "OTP_EXPIRED":
                raise HTTPException(
                    status_code=status.HTTP_410_GONE,
                    detail=result["message"]
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=result["message"]
                )
        
        return TokenResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Verification service temporarily unavailable"
        )

@router.get("/me", response_model=PensionerInfo)
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """
    Get current authenticated pensioner information
    """
    try:
        token = credentials.credentials
        pensioner = auth_service.get_current_pensioner(token, db)
        
        if not pensioner:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token"
            )
        
        return PensionerInfo(
            id=pensioner.id,
            eppo_number=pensioner.eppo_number,
            name=pensioner.name,
            mobile_number=pensioner.mobile_number
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve user information"
        )

@router.post("/logout")
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """
    Logout user (invalidate token on client side)
    """
    # In a production system, you might want to maintain a blacklist of tokens
    # For now, we'll just return success and let the client handle token removal
    return {
        "success": True,
        "message": "Logged out successfully"
    }

# Dependency to get current authenticated pensioner
async def get_current_pensioner(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> Pensioner:
    """
    Dependency to get current authenticated pensioner
    Use this in protected routes
    """
    token = credentials.credentials
    pensioner = auth_service.get_current_pensioner(token, db)
    
    if not pensioner:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return pensioner

@router.get("/health")
async def auth_health():
    """
    Health check for authentication service
    """
    return {
        "status": "healthy",
        "service": "authentication",
        "timestamp": "2024-01-01T00:00:00Z"
    }
