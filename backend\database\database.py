"""
Database configuration and connection management
"""
import os
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from typing import Generator
import logging

# Database configuration
DATABASE_URL = os.getenv(
    "DATABASE_URL", 
    "sqlite:///./cpms_pensioner.db"  # Default to SQLite for development
)

# For production, use PostgreSQL:
# DATABASE_URL = "postgresql://username:password@localhost/cpms_pensioner"

# Create engine with appropriate settings
if DATABASE_URL.startswith("sqlite"):
    # SQLite specific settings
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=False  # Set to True for SQL debugging
    )
else:
    # PostgreSQL/other database settings
    engine = create_engine(
        DATABASE_URL,
        pool_pre_ping=True,
        pool_recycle=300,
        echo=False  # Set to True for SQL debugging
    )

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class for models
Base = declarative_base()

def get_db() -> Generator[Session, None, None]:
    """
    Dependency to get database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """
    Create all tables in the database
    """
    try:
        # Import all models to ensure they're registered
        from backend.models.pensioner_models import (
            Pensioner, PensionDetails, OTPSession, QueryLog, SystemConfiguration, Base as ModelBase
        )

        # Create all tables using the model's Base
        ModelBase.metadata.create_all(bind=engine)
        logging.info("✅ Database tables created successfully")
        return True
    except Exception as e:
        logging.error(f"❌ Error creating database tables: {e}")
        return False

def init_database():
    """
    Initialize database with default data
    """
    try:
        db = SessionLocal()
        
        # Import models
        from backend.models.pensioner_models import SystemConfiguration
        
        # Check if system configuration exists
        existing_config = db.query(SystemConfiguration).first()
        if not existing_config:
            # Add default system configurations
            default_configs = [
                {
                    "config_key": "current_da_rate",
                    "config_value": "50.0",
                    "description": "Current Dearness Allowance rate percentage"
                },
                {
                    "config_key": "fma_rate",
                    "config_value": "1000.0",
                    "description": "Fixed Medical Allowance amount"
                },
                {
                    "config_key": "otp_expiry_minutes",
                    "config_value": "10",
                    "description": "OTP expiry time in minutes"
                },
                {
                    "config_key": "max_otp_attempts",
                    "config_value": "3",
                    "description": "Maximum OTP verification attempts"
                },
                {
                    "config_key": "session_timeout_hours",
                    "config_value": "24",
                    "description": "User session timeout in hours"
                }
            ]
            
            for config in default_configs:
                db_config = SystemConfiguration(**config)
                db.add(db_config)
            
            db.commit()
            logging.info("✅ Default system configuration added")
        
        db.close()
        return True
    except Exception as e:
        logging.error(f"❌ Error initializing database: {e}")
        return False

def get_system_config(key: str, default_value: str = None) -> str:
    """
    Get system configuration value
    """
    try:
        db = SessionLocal()
        from backend.models.pensioner_models import SystemConfiguration
        
        config = db.query(SystemConfiguration).filter(
            SystemConfiguration.config_key == key,
            SystemConfiguration.is_active == True
        ).first()
        
        db.close()
        
        if config:
            return config.config_value
        return default_value
    except Exception as e:
        logging.error(f"❌ Error getting system config {key}: {e}")
        return default_value

def update_system_config(key: str, value: str) -> bool:
    """
    Update system configuration value
    """
    try:
        db = SessionLocal()
        from backend.models.pensioner_models import SystemConfiguration
        
        config = db.query(SystemConfiguration).filter(
            SystemConfiguration.config_key == key
        ).first()
        
        if config:
            config.config_value = value
        else:
            config = SystemConfiguration(config_key=key, config_value=value)
            db.add(config)
        
        db.commit()
        db.close()
        return True
    except Exception as e:
        logging.error(f"❌ Error updating system config {key}: {e}")
        return False

# Test database connection
def test_connection():
    """
    Test database connection
    """
    try:
        db = SessionLocal()
        # Try a simple query
        db.execute("SELECT 1")
        db.close()
        logging.info("✅ Database connection successful")
        return True
    except Exception as e:
        logging.error(f"❌ Database connection failed: {e}")
        return False

# Initialize logging
logging.basicConfig(level=logging.INFO)

# Auto-create tables on import (for development)
if __name__ == "__main__":
    print("Creating database tables...")
    if create_tables():
        print("Initializing database...")
        if init_database():
            print("Database setup complete!")
        else:
            print("Database initialization failed!")
    else:
        print("Table creation failed!")
