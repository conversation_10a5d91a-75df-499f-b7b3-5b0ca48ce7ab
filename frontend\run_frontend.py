#!/usr/bin/env python3
"""
CPMS Modern Frontend Launcher
Launch the new HTML/CSS/JS frontend for CPMS chatbot
"""
import sys
import os

# Add workspace root to Python path (go up one level from frontend folder)
workspace_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, workspace_root)

def main():
    """Launch the modern frontend web interface"""
    print("🎨 CPMS Modern Frontend Launcher")
    print("=" * 50)
    print("🚀 Starting modern HTML/CSS/JS frontend...")
    print("🔌 This will connect to backend API at: http://localhost:8001")
    print("🌐 Frontend will be available at: http://localhost:7861")
    print("⚠️  Make sure backend is running first: python run_backend.py")
    print("=" * 50)

    try:
        # Import and run the modern frontend server
        from frontend.server import main as run_server
        run_server()

    except KeyboardInterrupt:
        print("\n👋 Modern frontend stopped by user")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure you're in the correct directory")
        print("2. Check that frontend/server.py exists")
        print("3. Verify all static files are in place")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting modern frontend: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure backend is running: python run_backend.py")
        print("2. Check if backend API is accessible at http://localhost:8000")
        print("3. Verify port 7861 is not in use")
        print("4. Ensure all frontend static files exist")
        sys.exit(1)

def run_legacy():
    """Run the legacy Gradio frontend (fallback)"""
    print("\n🔄 Falling back to legacy Gradio frontend...")
    try:
        from frontend.interface.web_client import main as run_client
        run_client()
    except Exception as e:
        print(f"❌ Legacy frontend also failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
