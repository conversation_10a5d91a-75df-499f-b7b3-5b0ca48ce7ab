#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to ask the CPMS chatbot frequently asked questions to populate the cache
for faster responses. This will help improve response times for common queries.
"""

import sys
import os
import time
import json
from typing import List, Dict, Any

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from backend.core.ultra_optimized_rag_system import UltraOptimizedRAGSystem
    from backend.config import config
    print("✅ Successfully imported RAG system")
except ImportError as e:
    print(f"❌ Failed to import RAG system: {e}")
    sys.exit(1)

class CPMSFrequentQuestionAsker:
    def __init__(self):
        """Initialize the question asker with RAG system"""
        print("🔄 Initializing CPMS RAG System...")
        try:
            self.rag_system = UltraOptimizedRAGSystem(model_type="gguf")
            print("✅ RAG System initialized successfully!")
        except Exception as e:
            print(f"❌ Failed to initialize RAG system: {e}")
            print("🔄 Trying with HuggingFace model...")
            try:
                self.rag_system = UltraOptimizedRAGSystem(model_type="huggingface")
                print("✅ RAG System initialized with HuggingFace model!")
            except Exception as e2:
                print(f"❌ Failed to initialize with HuggingFace model: {e2}")
                raise e2

        self.results = []
        self.total_questions = 0
        self.cached_responses = 0

    def get_frequent_questions(self) -> List[Dict[str, str]]:
        """Get a comprehensive list of frequently asked questions about CPMS"""
        return [
            # Basic CPMS Information
            {"category": "Basic Info", "question": "What is CPMS?"},
            {"category": "Basic Info", "question": "What does CPMS stand for?"},
            {"category": "Basic Info", "question": "What is SAMPANN?"},
            {"category": "Basic Info", "question": "Who developed CPMS?"},
            {"category": "Basic Info", "question": "What is the purpose of CPMS?"},
            {"category": "Basic Info", "question": "What are the benefits of CPMS?"},
            {"category": "Basic Info", "question": "What is the full form of CPMS?"},

            # Login and Access
            {"category": "Login & Access", "question": "How to login to CPMS?"},
            {"category": "Login & Access", "question": "How to access CPMS portal?"},
            {"category": "Login & Access", "question": "What is the CPMS website URL?"},
            {"category": "Login & Access", "question": "How to register on CPMS?"},
            {"category": "Login & Access", "question": "What are the login requirements for CPMS?"},
            {"category": "Login & Access", "question": "How to reset CPMS password?"},
            {"category": "Login & Access", "question": "What to do if I forgot my CPMS login credentials?"},
            {"category": "Login & Access", "question": "How to create CPMS account?"},

            # Pension Related
            {"category": "Pension", "question": "When is pension credited?"},
            {"category": "Pension", "question": "How to check pension status?"},
            {"category": "Pension", "question": "How to track pension application?"},
            {"category": "Pension", "question": "What documents are required for pension?"},
            {"category": "Pension", "question": "How to apply for family pension?"},
            {"category": "Pension", "question": "What is the pension calculation method?"},
            {"category": "Pension", "question": "How to get pension certificate?"},
            {"category": "Pension", "question": "What is commutation of pension?"},
            {"category": "Pension", "question": "How to apply for commutation?"},
            {"category": "Pension", "question": "When will pension arrears be paid?"},

            # Bank and Payment Details
            {"category": "Banking", "question": "How to change bank account details in CPMS?"},
            {"category": "Banking", "question": "How to update bank account information?"},
            {"category": "Banking", "question": "What bank details are required for pension?"},
            {"category": "Banking", "question": "How to verify bank account in CPMS?"},
            {"category": "Banking", "question": "What if pension is not credited to my account?"},
            {"category": "Banking", "question": "How to change bank branch for pension?"},

            # Life Certificate and Verification
            {"category": "Life Certificate", "question": "What is life certificate?"},
            {"category": "Life Certificate", "question": "How to submit life certificate?"},
            {"category": "Life Certificate", "question": "When to submit life certificate?"},
            {"category": "Life Certificate", "question": "What is Jeevan Pramaan?"},
            {"category": "Life Certificate", "question": "How to generate digital life certificate?"},
            {"category": "Life Certificate", "question": "What happens if life certificate is not submitted?"},

            # Tax and Declarations
            {"category": "Tax", "question": "How to submit tax declarations in CPMS?"},
            {"category": "Tax", "question": "What is Form 12BB?"},
            {"category": "Tax", "question": "How to update investment declarations?"},
            {"category": "Tax", "question": "When to submit tax saving declarations?"},
            {"category": "Tax", "question": "How to download Form 16?"},

            # Grievances and Support
            {"category": "Support", "question": "How to lodge a grievance?"},
            {"category": "Support", "question": "What is the CPMS helpline number?"},
            {"category": "Support", "question": "How to contact CPMS support?"},
            {"category": "Support", "question": "What is the CPMS email address?"},
            {"category": "Support", "question": "How to raise a complaint in CPMS?"},
            {"category": "Support", "question": "Where to get technical help for CPMS?"},
            {"category": "Support", "question": "How to report CPMS issues?"},

            # Forms and Documents
            {"category": "Forms", "question": "What forms are available in CPMS?"},
            {"category": "Forms", "question": "How to download pension forms?"},
            {"category": "Forms", "question": "What is Form 4 in CPMS?"},
            {"category": "Forms", "question": "What is Form 8 in CPMS?"},
            {"category": "Forms", "question": "What is Form 10 in CPMS?"},
            {"category": "Forms", "question": "How to fill pension application form?"},
            {"category": "Forms", "question": "What documents to upload in CPMS?"},

            # Technical Issues
            {"category": "Technical", "question": "CPMS website not working"},
            {"category": "Technical", "question": "Unable to login to CPMS"},
            {"category": "Technical", "question": "CPMS portal loading issues"},
            {"category": "Technical", "question": "How to clear browser cache for CPMS?"},
            {"category": "Technical", "question": "CPMS mobile app not working"},
            {"category": "Technical", "question": "Browser compatibility for CPMS"},

            # Specific Procedures
            {"category": "Procedures", "question": "How to create AO login in CPMS?"},
            {"category": "Procedures", "question": "How to create DH pension in CPMS?"},
            {"category": "Procedures", "question": "What is the pension approval process?"},
            {"category": "Procedures", "question": "How to process pension papers?"},
            {"category": "Procedures", "question": "What is the role of CCA in CPMS?"},
            {"category": "Procedures", "question": "How to submit pension documents?"},

            # Status and Tracking
            {"category": "Status", "question": "How to check application status?"},
            {"category": "Status", "question": "Where to see pension payment history?"},
            {"category": "Status", "question": "How to track document verification?"},
            {"category": "Status", "question": "What are the different status types in CPMS?"},
            {"category": "Status", "question": "How to get status updates?"},

            # Updates and Changes
            {"category": "Updates", "question": "How to update personal details?"},
            {"category": "Updates", "question": "How to change address in CPMS?"},
            {"category": "Updates", "question": "How to update mobile number?"},
            {"category": "Updates", "question": "How to change email address?"},
            {"category": "Updates", "question": "How to update nominee details?"},
        ]

    def ask_question(self, question: str, category: str) -> Dict[str, Any]:
        """Ask a single question and return the result"""
        print(f"❓ Asking: {question}")

        start_time = time.time()
        try:
            result = self.rag_system.query(question, include_sources=True)
            end_time = time.time()

            response_time = round(end_time - start_time, 3)
            is_cached = result.get('cached', False)
            cache_type = result.get('cache_type', 'None')

            if is_cached:
                self.cached_responses += 1
                status = f"⚡ {cache_type} Hit"
            else:
                status = "🔍 Generated"

            print(f"   ✅ {status} | {response_time}s | {len(result['response'])} chars")

            return {
                'question': question,
                'category': category,
                'response': result['response'],
                'response_time': response_time,
                'cached': is_cached,
                'cache_type': cache_type,
                'sources': result.get('sources', []),
                'status': 'success'
            }

        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            return {
                'question': question,
                'category': category,
                'error': str(e),
                'status': 'error'
            }

    def ask_all_questions(self):
        """Ask all frequent questions and collect results"""
        questions = self.get_frequent_questions()
        self.total_questions = len(questions)

        print(f"🚀 Starting to ask {self.total_questions} frequent questions...")
        print("=" * 60)

        category_stats = {}

        for i, q_data in enumerate(questions, 1):
            question = q_data['question']
            category = q_data['category']

            print(f"\n[{i}/{self.total_questions}] Category: {category}")

            result = self.ask_question(question, category)
            self.results.append(result)

            # Update category stats
            if category not in category_stats:
                category_stats[category] = {'total': 0, 'cached': 0, 'errors': 0}

            category_stats[category]['total'] += 1
            if result['status'] == 'success' and result.get('cached', False):
                category_stats[category]['cached'] += 1
            elif result['status'] == 'error':
                category_stats[category]['errors'] += 1

            # Small delay to avoid overwhelming the system
            time.sleep(0.1)

        print("\n" + "=" * 60)
        print("📊 SUMMARY STATISTICS")
        print("=" * 60)

        successful_questions = len([r for r in self.results if r['status'] == 'success'])
        error_questions = len([r for r in self.results if r['status'] == 'error'])

        print(f"Total Questions Asked: {self.total_questions}")
        print(f"Successful Responses: {successful_questions}")
        print(f"Cached Responses: {self.cached_responses}")
        print(f"Cache Hit Rate: {(self.cached_responses/successful_questions*100):.1f}%")
        print(f"Errors: {error_questions}")

        print(f"\n📋 CATEGORY BREAKDOWN:")
        for category, stats in category_stats.items():
            cache_rate = (stats['cached']/stats['total']*100) if stats['total'] > 0 else 0
            print(f"  {category}: {stats['total']} questions, {stats['cached']} cached ({cache_rate:.1f}%), {stats['errors']} errors")

        # Calculate average response times
        successful_results = [r for r in self.results if r['status'] == 'success']
        if successful_results:
            avg_response_time = sum(r['response_time'] for r in successful_results) / len(successful_results)
            cached_times = [r['response_time'] for r in successful_results if r.get('cached', False)]
            uncached_times = [r['response_time'] for r in successful_results if not r.get('cached', False)]

            print(f"\n⏱️ RESPONSE TIME ANALYSIS:")
            print(f"  Average Response Time: {avg_response_time:.3f}s")
            if cached_times:
                print(f"  Average Cached Response: {sum(cached_times)/len(cached_times):.3f}s")
            if uncached_times:
                print(f"  Average Generated Response: {sum(uncached_times)/len(uncached_times):.3f}s")

    def save_results(self, filename: str = "frequent_questions_results.json"):
        """Save the results to a JSON file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump({
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'total_questions': self.total_questions,
                    'cached_responses': self.cached_responses,
                    'results': self.results
                }, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Results saved to: {filename}")
        except Exception as e:
            print(f"\n❌ Failed to save results: {e}")

    def verify_answers(self, sample_size: int = 5):
        """Verify a sample of answers for accuracy"""
        print(f"\n🔍 VERIFYING SAMPLE ANSWERS ({sample_size} questions)")
        print("=" * 60)

        successful_results = [r for r in self.results if r['status'] == 'success']
        if len(successful_results) < sample_size:
            sample_size = len(successful_results)

        import random
        sample_results = random.sample(successful_results, sample_size)

        for i, result in enumerate(sample_results, 1):
            print(f"\n[{i}/{sample_size}] Question: {result['question']}")
            print(f"Category: {result['category']}")
            print(f"Response Length: {len(result['response'])} characters")
            print(f"Sources: {len(result.get('sources', []))} sources")
            print(f"Cached: {'Yes' if result.get('cached', False) else 'No'}")

            # Show first 200 characters of response
            response_preview = result['response'][:200]
            if len(result['response']) > 200:
                response_preview += "..."
            print(f"Response Preview: {response_preview}")
            print("-" * 40)

def main():
    """Main function to run the frequent question asking process"""
    print("🤖 CPMS Frequent Questions Cache Population Tool")
    print("=" * 60)
    print("This tool will ask the chatbot frequently asked questions")
    print("to populate the cache for faster future responses.")
    print("=" * 60)

    try:
        asker = CPMSFrequentQuestionAsker()
        asker.ask_all_questions()
        asker.save_results()
        asker.verify_answers()

        print("\n🎉 PROCESS COMPLETED SUCCESSFULLY!")
        print("The chatbot cache has been populated with frequent questions.")
        print("Future queries on these topics should respond much faster!")

    except KeyboardInterrupt:
        print("\n\n⚠️ Process interrupted by user")
    except Exception as e:
        print(f"\n❌ Process failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
