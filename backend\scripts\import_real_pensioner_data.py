#!/usr/bin/env python3
"""
Import real pensioner data from EPPONUMBER.txt and CPMS_JEEVANPARAMA.csv
Replaces mock data with actual pensioner records
"""
import sys
import os
import csv
import re
from datetime import datetime, date
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from backend.database.database import SessionLocal, create_tables, init_database
from backend.models.pensioner_models import Pensioner, PensionDetails, SystemConfiguration
from sqlalchemy.exc import IntegrityError

def parse_date(date_str):
    """Parse date string in various formats"""
    if not date_str or date_str.strip() == '':
        return None
    
    date_str = date_str.strip()
    
    # Try different date formats
    formats = [
        '%Y-%m-%d',
        '%d-%m-%Y', 
        '%d/%m/%Y',
        '%Y-%m-%d %H:%M:%S.%f',
        '%Y-%m-%d %H:%M:%S'
    ]
    
    for fmt in formats:
        try:
            parsed_date = datetime.strptime(date_str, fmt).date()
            return parsed_date
        except ValueError:
            continue
    
    print(f"⚠️ Could not parse date: {date_str}")
    return None

def parse_eppo_txt_file(file_path):
    """Parse EPPONUMBER.txt file"""
    pensioners = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                try:
                    # Split by tab (assuming tab-separated)
                    fields = line.strip().split('\t')
                    
                    if len(fields) < 13:
                        print(f"⚠️ Line {line_num}: Insufficient fields ({len(fields)})")
                        continue
                    
                    # Extract relevant fields based on the actual structure
                    # Fields: ID, CIRCLE, EPPO_NUM, SOME_ID, STATUS, ..., TIMESTAMP, SOME_ID2, SOME_ID3, MOBILE, NAME, GENDER, DOB
                    eppo_number = fields[2].strip() if len(fields) > 2 else ""
                    mobile_number = fields[10].strip() if len(fields) > 10 else ""  # Mobile is at index 10
                    name = fields[11].strip() if len(fields) > 11 else ""           # Name is at index 11
                    gender = fields[12].strip() if len(fields) > 12 else ""         # Gender is at index 12
                    dob_str = fields[13].strip() if len(fields) > 13 else ""        # DOB is at index 13
                    
                    # Validate required fields
                    if not eppo_number or not mobile_number or not name:
                        print(f"⚠️ Line {line_num}: Missing required fields")
                        continue
                    
                    # Parse date of birth
                    date_of_birth = parse_date(dob_str)
                    if not date_of_birth:
                        # Use a default DOB if parsing fails
                        date_of_birth = date(1950, 1, 1)
                    
                    # Calculate retirement date (assuming retirement at 60)
                    retirement_year = date_of_birth.year + 60
                    date_of_retirement = date(retirement_year, date_of_birth.month, date_of_birth.day)
                    
                    # Clean mobile number (remove country code if present)
                    mobile_number = re.sub(r'[^\d]', '', mobile_number)
                    if len(mobile_number) > 10:
                        mobile_number = mobile_number[-10:]  # Take last 10 digits
                    
                    # Generate email
                    email_name = name.lower().replace(' ', '.').replace(',', '')
                    email = f"{email_name}@cpms.gov.in"
                    
                    pensioner_data = {
                        'eppo_number': eppo_number,
                        'name': name,
                        'mobile_number': mobile_number,
                        'email': email,
                        'date_of_birth': date_of_birth,
                        'date_of_retirement': date_of_retirement,
                        'is_active': True,
                        'gender': gender
                    }
                    
                    pensioners.append(pensioner_data)
                    
                except Exception as e:
                    print(f"⚠️ Error parsing line {line_num}: {e}")
                    continue
                    
    except Exception as e:
        print(f"❌ Error reading EPPONUMBER.txt: {e}")
        return []
    
    print(f"✅ Parsed {len(pensioners)} pensioners from EPPONUMBER.txt")
    return pensioners

def parse_jeevanparama_csv(file_path):
    """Parse CPMS_JEEVANPARAMA.csv file"""
    pensioners = []
    
    try:
        with open(file_path, 'r', encoding='utf-8-sig') as file:  # utf-8-sig to handle BOM
            # Read first line to determine delimiter
            first_line = file.readline()
            file.seek(0)
            
            # Detect delimiter
            delimiter = ',' if ',' in first_line else '\t'
            
            reader = csv.reader(file, delimiter=delimiter)
            
            for row_num, row in enumerate(reader, 1):
                try:
                    if len(row) < 12:
                        print(f"⚠️ Row {row_num}: Insufficient fields ({len(row)})")
                        continue
                    
                    # Extract fields based on CSV structure
                    # Fields: ID, CIRCLE, EPPO_NUM, SOME_ID, STATUS, ..., TIMESTAMP, SOME_ID2, SOME_ID3, MOBILE, NAME, GENDER, DOB
                    eppo_number = row[2].strip() if len(row) > 2 else ""
                    mobile_number = row[10].strip() if len(row) > 10 else ""  # Mobile is at index 10
                    name = row[11].strip() if len(row) > 11 else ""           # Name is at index 11
                    gender = row[12].strip() if len(row) > 12 else ""         # Gender is at index 12
                    dob_str = row[13].strip() if len(row) > 13 else ""        # DOB is at index 13
                    
                    # Validate required fields
                    if not eppo_number or not mobile_number or not name:
                        continue
                    
                    # Parse date of birth
                    date_of_birth = parse_date(dob_str)
                    if not date_of_birth:
                        date_of_birth = date(1950, 1, 1)
                    
                    # Calculate retirement date
                    retirement_year = date_of_birth.year + 60
                    date_of_retirement = date(retirement_year, date_of_birth.month, date_of_birth.day)
                    
                    # Clean mobile number
                    mobile_number = re.sub(r'[^\d]', '', mobile_number)
                    if len(mobile_number) > 10:
                        mobile_number = mobile_number[-10:]
                    
                    # Generate email
                    email_name = name.lower().replace(' ', '.').replace(',', '')
                    email = f"{email_name}@cpms.gov.in"
                    
                    pensioner_data = {
                        'eppo_number': eppo_number,
                        'name': name,
                        'mobile_number': mobile_number,
                        'email': email,
                        'date_of_birth': date_of_birth,
                        'date_of_retirement': date_of_retirement,
                        'is_active': True,
                        'gender': gender
                    }
                    
                    pensioners.append(pensioner_data)
                    
                except Exception as e:
                    print(f"⚠️ Error parsing row {row_num}: {e}")
                    continue
                    
    except Exception as e:
        print(f"❌ Error reading CPMS_JEEVANPARAMA.csv: {e}")
        return []
    
    print(f"✅ Parsed {len(pensioners)} pensioners from CPMS_JEEVANPARAMA.csv")
    return pensioners

def generate_pension_details(pensioner_id, base_pension=20000):
    """Generate realistic pension details for a pensioner"""
    import random
    
    # Generate realistic pension amounts
    basic_pension = base_pension + random.randint(-5000, 10000)
    da_rate = 50.0  # Current DA rate
    da_amount = basic_pension * (da_rate / 100)
    medical_allowance = 1000.0
    additional_pension_rate = random.choice([0, 10, 20])  # 0%, 10%, or 20%
    additional_pension = basic_pension * (additional_pension_rate / 100)
    total_pension = basic_pension + da_amount + medical_allowance + additional_pension
    
    # Random dates for various fields
    current_year = datetime.now().year
    lc_expiry = date(current_year, 12, 31)
    lc_last_submitted = date(current_year - 1, 11, 15)
    
    return PensionDetails(
        pensioner_id=pensioner_id,
        lc_expiry_date=lc_expiry,
        lc_last_submitted=lc_last_submitted,
        lc_status=random.choice(["verified", "pending", "submitted"]),
        basic_pension=basic_pension,
        da_amount=da_amount,
        medical_allowance=medical_allowance,
        additional_pension=additional_pension,
        total_pension=total_pension,
        last_pension_month=f"{current_year}-{random.randint(1, 12):02d}",
        current_da_rate=da_rate,
        da_effective_date=date(current_year, 1, 1),
        additional_pension_rate=additional_pension_rate,
        additional_pension_effective_date=date(current_year - 1, 1, 1),
        normal_family_pension=basic_pension * 0.5,
        enhanced_family_pension=basic_pension * 0.75,
        fma_amount=1000.0,
        fma_effective_date=date(current_year - 1, 1, 1),
        sampann_migration_date=date(2021, random.randint(1, 12), random.randint(1, 28)),
        sampann_migration_status=random.choice(["completed", "pending"]),
        bank_name=random.choice(["State Bank of India", "Punjab National Bank", "Bank of Baroda", "Canara Bank"]),
        account_number=f"{random.randint(***********, ***********)}",
        ifsc_code=f"SBIN000{random.randint(1000, 9999)}"
    )

def clear_existing_data(db):
    """Clear existing pensioner data"""
    try:
        # Delete in correct order due to foreign key constraints
        db.query(PensionDetails).delete()
        db.query(Pensioner).delete()
        db.commit()
        print("✅ Existing pensioner data cleared")
        return True
    except Exception as e:
        print(f"❌ Error clearing existing data: {e}")
        db.rollback()
        return False

def import_pensioners_to_db(pensioners_data):
    """Import pensioner data to database"""
    db = SessionLocal()

    try:
        # Clear existing data
        if not clear_existing_data(db):
            return False

        imported_count = 0
        skipped_count = 0

        for i, pensioner_data in enumerate(pensioners_data):
            try:
                # Create pensioner record
                pensioner = Pensioner(
                    eppo_number=pensioner_data['eppo_number'],
                    name=pensioner_data['name'],
                    mobile_number=pensioner_data['mobile_number'],
                    email=pensioner_data['email'],
                    date_of_birth=pensioner_data['date_of_birth'],
                    date_of_retirement=pensioner_data['date_of_retirement'],
                    is_active=pensioner_data['is_active']
                )

                db.add(pensioner)
                db.flush()  # Get the ID without committing

                # Create pension details
                pension_details = generate_pension_details(pensioner.id)
                db.add(pension_details)

                imported_count += 1

                # Commit in batches of 50
                if imported_count % 50 == 0:
                    db.commit()
                    print(f"✅ Imported {imported_count} pensioners...")

            except IntegrityError as e:
                db.rollback()
                print(f"⚠️ Skipping duplicate EPPO: {pensioner_data['eppo_number']}")
                skipped_count += 1
                continue
            except Exception as e:
                db.rollback()
                print(f"❌ Error importing pensioner {pensioner_data['eppo_number']}: {e}")
                skipped_count += 1
                continue

        # Final commit
        db.commit()

        print(f"\n🎉 Import completed!")
        print(f"✅ Successfully imported: {imported_count} pensioners")
        print(f"⚠️ Skipped: {skipped_count} records")

        return True

    except Exception as e:
        print(f"❌ Error during import: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def main():
    """Main function to import real pensioner data"""
    print("🏛️ CPMS Real Pensioner Data Import")
    print("=" * 50)

    # File paths
    eppo_file = workspace_root / "EPPONUMBER.txt"
    csv_file = workspace_root / "CPMS_JEEVANPARAMA.csv"

    # Check if files exist
    if not eppo_file.exists():
        print(f"❌ File not found: {eppo_file}")
        return

    if not csv_file.exists():
        print(f"❌ File not found: {csv_file}")
        return

    # Create tables if they don't exist
    print("📋 Setting up database...")
    if not create_tables():
        print("❌ Failed to create database tables")
        return

    if not init_database():
        print("❌ Failed to initialize database")
        return

    print("✅ Database setup complete")

    # Parse data files
    print("\n📖 Parsing data files...")

    eppo_pensioners = parse_eppo_txt_file(eppo_file)
    csv_pensioners = parse_jeevanparama_csv(csv_file)

    # Combine and deduplicate pensioners
    all_pensioners = {}

    # Add EPPO file pensioners
    for pensioner in eppo_pensioners:
        eppo = pensioner['eppo_number']
        if eppo not in all_pensioners:
            all_pensioners[eppo] = pensioner

    # Add CSV file pensioners (will overwrite if duplicate EPPO)
    for pensioner in csv_pensioners:
        eppo = pensioner['eppo_number']
        all_pensioners[eppo] = pensioner  # CSV data takes precedence

    unique_pensioners = list(all_pensioners.values())

    print(f"\n📊 Data Summary:")
    print(f"   EPPONUMBER.txt: {len(eppo_pensioners)} records")
    print(f"   CPMS_JEEVANPARAMA.csv: {len(csv_pensioners)} records")
    print(f"   Unique pensioners: {len(unique_pensioners)} records")

    if not unique_pensioners:
        print("❌ No valid pensioner data found")
        return

    # Import to database
    print(f"\n💾 Importing {len(unique_pensioners)} pensioners to database...")

    if import_pensioners_to_db(unique_pensioners):
        print("\n🎉 Real pensioner data import completed successfully!")
        print("\n📋 Sample imported pensioners:")

        # Show first 5 pensioners
        for i, pensioner in enumerate(unique_pensioners[:5]):
            print(f"   {i+1}. EPPO: {pensioner['eppo_number']}, "
                  f"Mobile: {pensioner['mobile_number']}, "
                  f"Name: {pensioner['name']}")

        if len(unique_pensioners) > 5:
            print(f"   ... and {len(unique_pensioners) - 5} more")

        print("\n🔧 Next steps:")
        print("1. Start the backend: python backend/run_backend.py")
        print("2. Start the frontend: python frontend/run_frontend.py")
        print("3. Test authentication with real EPPO numbers and mobile numbers")
        print("4. Check backend logs for OTP codes during testing")

    else:
        print("❌ Import failed")

if __name__ == "__main__":
    main()
