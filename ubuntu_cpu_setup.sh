#!/bin/bash

echo "🐧 CPMS Backend CPU-Only Setup for Ubuntu Server"
echo "================================================"

# Check if we're in the right directory
if [ ! -f "backend/requirements.txt" ]; then
    echo "❌ Please run this script from the CPMS_CHATBOT root directory"
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install system dependencies
echo "📦 Installing system dependencies..."
sudo apt update
sudo apt install -y build-essential cmake libpq-dev python3-dev

# Install CPU-only PyTorch first
echo "🔥 Installing CPU-only PyTorch..."
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

# Install other dependencies (excluding bitsandbytes)
echo "📦 Installing Python dependencies (CPU-only)..."
pip install transformers>=4.35.0
pip install accelerate>=0.24.0
pip install peft>=0.6.0
pip install datasets>=2.14.0
pip install trl>=0.7.0
pip install chromadb>=0.4.15
pip install sentence-transformers>=2.2.2
pip install langchain>=0.0.350
pip install langchain-community>=0.0.10
pip install faiss-cpu>=1.7.4
pip install PyPDF2>=3.0.1
pip install pdfplumber>=0.9.0
pip install pymupdf>=1.23.0
pip install beautifulsoup4>=4.12.2
pip install requests>=2.31.0
pip install pandas>=2.1.0
pip install numpy>=1.24.0
pip install flask>=3.0.0
pip install flask-cors>=4.0.0
pip install gradio>=4.0.0
pip install uvicorn>=0.24.0
pip install fastapi>=0.104.0
pip install pydantic>=2.0.0
pip install python-dotenv>=1.0.0
pip install tqdm>=4.66.0
pip install jsonlines>=4.0.0
pip install scikit-learn>=1.3.0
pip install matplotlib>=3.7.0
pip install seaborn>=0.12.0
pip install sqlalchemy>=2.0.0
pip install alembic>=1.12.0
pip install psycopg2-binary>=2.9.7
pip install "python-jose[cryptography]==3.3.0"
pip install "PyJWT[crypto]>=2.8.0"
pip install "passlib[bcrypt]>=1.7.4"
pip install python-multipart>=0.0.6
pip install cryptography>=41.0.0
pip install twilio>=8.10.0
pip install llama-cpp-python>=0.2.0
pip install ctransformers>=0.2.27

# Test imports
echo "✅ Testing critical imports..."
python3 -c "
import torch
print(f'✅ PyTorch {torch.__version__} (CPU-only)')

try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    print('✅ Transformers works')
except ImportError as e:
    print(f'❌ Transformers failed: {e}')

try:
    from jose import jwt
    print('✅ python-jose works')
except ImportError:
    try:
        import jwt
        print('✅ PyJWT works as fallback')
    except ImportError:
        print('❌ Both JWT libraries failed')

try:
    import faiss
    print('✅ FAISS-CPU works')
except ImportError:
    print('❌ FAISS failed')
"

# Initialize database
echo "🗄️ Initializing database..."
cd backend
python3 -c "
try:
    from database.database import create_tables, init_database
    create_tables()
    init_database()
    print('✅ Database initialized')
except Exception as e:
    print(f'❌ Database initialization failed: {e}')
"

# Create sample data
echo "👥 Creating sample pensioner data..."
python3 scripts/populate_sample_data.py

echo ""
echo "✅ CPU-Only Setup Complete!"
echo "==========================="
echo ""
echo "⚠️  IMPORTANT: This setup uses CPU-only mode"
echo "   - Model inference will be slower"
echo "   - No GPU acceleration available"
echo "   - Suitable for testing and light usage"
echo ""
echo "To start the backend server:"
echo "  cd backend"
echo "  python3 run_backend.py"
echo ""
echo "The server will be available at:"
echo "  - API: http://your-server-ip:8001"
echo "  - Docs: http://your-server-ip:8001/docs"
