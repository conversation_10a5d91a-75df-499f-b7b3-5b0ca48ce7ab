/**
 * CPMS Frontend Application
 * Main application logic and UI interactions
 */

class CPMSApp {
    constructor() {
        this.chatHistory = [];
        this.isTyping = false;
        this.settings = this.loadSettings();
        this.suggestions = [];

        // DOM elements
        this.elements = {};

        // Initialize app
        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        console.log('🚀 Initializing CPMS Frontend Application');

        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeApp());
        } else {
            this.initializeApp();
        }
    }

    /**
     * Initialize app after DOM is ready
     */
    async initializeApp() {
        try {
            // Cache DOM elements
            this.cacheElements();

            // Apply user settings
            this.applySettings();

            // Set up event listeners
            this.setupEventListeners();

            // Set up API connection monitoring
            this.setupApiMonitoring();

            // Initialize components
            await this.initializeComponents();

            // Hide loading screen and show app
            this.showApp();

            console.log('✅ CPMS Frontend Application initialized successfully');

        } catch (error) {
            console.error('❌ Failed to initialize application:', error);
            this.showError('Failed to initialize application. Please refresh the page.');
        }
    }

    /**
     * Cache frequently used DOM elements
     */
    cacheElements() {
        this.elements = {
            // Main containers
            loadingScreen: document.getElementById('loading-screen'),
            app: document.getElementById('app'),

            // Header elements
            connectionStatus: document.getElementById('connection-status'),
            statusIndicator: document.getElementById('status-indicator'),
            statusText: document.getElementById('status-text'),
            settingsBtn: document.getElementById('settings-btn'),
            infoBtn: document.getElementById('info-btn'),

            // Sidebar elements
            sidebar: document.getElementById('sidebar'),
            sidebarToggle: document.getElementById('sidebar-toggle'),
            suggestionsContainer: document.getElementById('suggestions-container'),
            clearChatBtn: document.getElementById('clear-chat-btn'),

            // Chat elements
            chatMessages: document.getElementById('chat-messages'),
            typingIndicator: document.getElementById('typing-indicator'),
            messageInput: document.getElementById('message-input'),
            sendBtn: document.getElementById('send-btn'),
            attachBtn: document.getElementById('attach-btn'),
            charCount: document.getElementById('char-count'),

            // Modals
            settingsModal: document.getElementById('settings-modal'),
            infoModal: document.getElementById('info-modal'),
            systemInfoContent: document.getElementById('system-info-content'),

            // Settings
            themeSelect: document.getElementById('theme-select'),
            fontSizeSelect: document.getElementById('font-size-select'),
            soundEnabled: document.getElementById('sound-enabled'),
            autoScroll: document.getElementById('auto-scroll')
        };

        // Validate critical elements
        const criticalElements = ['app', 'chatMessages', 'messageInput', 'sendBtn'];
        for (const elementId of criticalElements) {
            if (!this.elements[elementId]) {
                throw new Error(`Critical element not found: ${elementId}`);
            }
        }
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Message input events
        this.elements.messageInput.addEventListener('input', (e) => this.handleInputChange(e));
        this.elements.messageInput.addEventListener('keydown', (e) => this.handleInputKeydown(e));
        this.elements.messageInput.addEventListener('paste', (e) => this.handleInputPaste(e));

        // Send button
        this.elements.sendBtn.addEventListener('click', () => this.sendMessage());

        // Clear chat button
        if (this.elements.clearChatBtn) {
            this.elements.clearChatBtn.addEventListener('click', () => this.clearChat());
        }

        // Sidebar toggle
        if (this.elements.sidebarToggle) {
            this.elements.sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }

        // Header buttons
        if (this.elements.settingsBtn) {
            this.elements.settingsBtn.addEventListener('click', () => this.showModal('settings-modal'));
        }

        if (this.elements.infoBtn) {
            this.elements.infoBtn.addEventListener('click', () => this.showSystemInfo());
        }

        // Modal close buttons
        document.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modalId = e.target.closest('.modal-close').dataset.modal;
                if (modalId) {
                    this.hideModal(modalId);
                }
            });
        });

        // Modal backdrop clicks
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideModal(modal.id);
                }
            });
        });

        // Settings changes
        if (this.elements.themeSelect) {
            this.elements.themeSelect.addEventListener('change', (e) => this.changeTheme(e.target.value));
        }

        if (this.elements.fontSizeSelect) {
            this.elements.fontSizeSelect.addEventListener('change', (e) => this.changeFontSize(e.target.value));
        }

        if (this.elements.soundEnabled) {
            this.elements.soundEnabled.addEventListener('change', (e) => this.toggleSound(e.target.checked));
        }

        if (this.elements.autoScroll) {
            this.elements.autoScroll.addEventListener('change', (e) => this.toggleAutoScroll(e.target.checked));
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleGlobalKeydown(e));

        // Window events
        window.addEventListener('beforeunload', () => this.cleanup());
        window.addEventListener('resize', () => this.handleResize());

        // Visibility change (for pausing/resuming when tab is hidden)
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
    }

    /**
     * Set up API connection monitoring
     */
    setupApiMonitoring() {
        // Listen for connection status changes
        window.cpmsApi.onConnectionChange((connected, message) => {
            this.updateConnectionStatus(connected, message);
        });

        // Initial connection check
        this.checkConnection();
    }

    /**
     * Initialize components
     */
    async initializeComponents() {
        // Load suggestions
        await this.loadSuggestions();

        // Set up auto-resize for textarea
        this.setupTextareaAutoResize();

        // Initialize theme
        this.initializeTheme();

        // Focus message input
        if (this.elements.messageInput) {
            this.elements.messageInput.focus();
        }
    }

    /**
     * Show the main app and hide loading screen
     */
    showApp() {
        if (this.elements.loadingScreen) {
            this.elements.loadingScreen.style.opacity = '0';
            setTimeout(() => {
                this.elements.loadingScreen.classList.add('hidden');
                this.elements.app.classList.remove('hidden');

                // Animate app entrance
                this.elements.app.style.opacity = '0';
                this.elements.app.style.transform = 'translateY(20px)';

                requestAnimationFrame(() => {
                    this.elements.app.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
                    this.elements.app.style.opacity = '1';
                    this.elements.app.style.transform = 'translateY(0)';
                });
            }, 300);
        } else {
            this.elements.app.classList.remove('hidden');
        }
    }

    /**
     * Check API connection
     */
    async checkConnection() {
        this.updateLoadingStatus('Checking backend connection...');

        try {
            const result = await window.cpmsApi.healthCheck();
            if (result.success) {
                console.log('✅ Backend connection established');
                this.updateConnectionStatus(true, 'Connected to backend API');
            } else {
                console.warn('⚠️ Backend health check failed:', result.error);
                this.updateConnectionStatus(false, result.error);
            }
        } catch (error) {
            console.error('❌ Connection check failed:', error);
            this.updateConnectionStatus(false, 'Failed to connect to backend');
        }
    }

    /**
     * Update loading status text
     */
    updateLoadingStatus(status) {
        const loadingStatus = document.querySelector('.loading-status');
        if (loadingStatus) {
            loadingStatus.textContent = status;
        }
    }

    /**
     * Update connection status in UI
     */
    updateConnectionStatus(connected, message) {
        if (!this.elements.statusIndicator || !this.elements.statusText) return;

        this.elements.statusIndicator.className = 'status-indicator';

        if (connected) {
            this.elements.statusIndicator.classList.add('connected');
            this.elements.statusText.textContent = 'Connected';
        } else {
            this.elements.statusIndicator.classList.add('error');
            this.elements.statusText.textContent = 'Disconnected';
        }

        // Show detailed message in console
        if (message) {
            console.log(`🔌 Connection status: ${connected ? 'Connected' : 'Disconnected'} - ${message}`);
        }
    }

    /**
     * Load suggestions from API
     */
    async loadSuggestions() {
        if (!this.elements.suggestionsContainer) return;

        try {
            const result = await window.cpmsApi.getSuggestions();
            this.suggestions = result.data || [];
            this.renderSuggestions();
        } catch (error) {
            console.warn('Failed to load suggestions:', error);
            this.suggestions = window.cpmsApi.getFallbackSuggestions();
            this.renderSuggestions();
        }
    }

    /**
     * Render suggestions in sidebar
     */
    renderSuggestions() {
        if (!this.elements.suggestionsContainer) return;

        // Clear loading state
        this.elements.suggestionsContainer.innerHTML = '';

        if (this.suggestions.length === 0) {
            this.elements.suggestionsContainer.innerHTML = `
                <div class="suggestions-empty">
                    <p>No suggestions available</p>
                </div>
            `;
            return;
        }

        // Create suggestion buttons
        this.suggestions.forEach((suggestion, index) => {
            const button = document.createElement('button');
            button.className = 'suggestion-btn';
            button.textContent = suggestion;
            button.addEventListener('click', () => this.selectSuggestion(suggestion));

            // Add animation delay
            button.style.animationDelay = `${index * 0.1}s`;

            this.elements.suggestionsContainer.appendChild(button);
        });
    }

    /**
     * Handle suggestion selection
     */
    selectSuggestion(suggestion) {
        if (this.elements.messageInput) {
            this.elements.messageInput.value = suggestion;
            this.elements.messageInput.focus();
            this.updateCharCount();
            this.autoResizeTextarea();
        }
    }

    /**
     * Handle input change
     */
    handleInputChange(event) {
        this.updateCharCount();
        this.autoResizeTextarea();
        this.updateSendButtonState();
    }

    /**
     * Handle input keydown
     */
    handleInputKeydown(event) {
        // Send message on Enter (without Shift)
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            this.sendMessage();
        }

        // Handle other shortcuts
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case 'k':
                    event.preventDefault();
                    this.clearChat();
                    break;
                case '/':
                    event.preventDefault();
                    this.elements.messageInput.focus();
                    break;
            }
        }
    }

    /**
     * Handle input paste
     */
    handleInputPaste(event) {
        // Allow paste but update UI after
        setTimeout(() => {
            this.updateCharCount();
            this.autoResizeTextarea();
            this.updateSendButtonState();
        }, 0);
    }

    /**
     * Update character count
     */
    updateCharCount() {
        if (this.elements.charCount && this.elements.messageInput) {
            const count = this.elements.messageInput.value.length;
            this.elements.charCount.textContent = count;

            // Add warning class if approaching limit
            const maxLength = parseInt(this.elements.messageInput.getAttribute('maxlength')) || 2000;
            if (count > maxLength * 0.9) {
                this.elements.charCount.style.color = 'var(--warning-color)';
            } else if (count > maxLength * 0.8) {
                this.elements.charCount.style.color = 'var(--accent-color)';
            } else {
                this.elements.charCount.style.color = 'var(--text-muted)';
            }
        }
    }

    /**
     * Update send button state
     */
    updateSendButtonState() {
        if (this.elements.sendBtn && this.elements.messageInput) {
            const hasText = this.elements.messageInput.value.trim().length > 0;
            const isNotTyping = !this.isTyping;

            this.elements.sendBtn.disabled = !hasText || !isNotTyping;

            if (hasText && isNotTyping) {
                this.elements.sendBtn.style.opacity = '1';
                this.elements.sendBtn.style.transform = 'scale(1)';
            } else {
                this.elements.sendBtn.style.opacity = '0.5';
                this.elements.sendBtn.style.transform = 'scale(0.95)';
            }
        }
    }

    /**
     * Set up textarea auto-resize
     */
    setupTextareaAutoResize() {
        if (this.elements.messageInput) {
            this.autoResizeTextarea();
        }
    }

    /**
     * Auto-resize textarea based on content
     */
    autoResizeTextarea() {
        if (!this.elements.messageInput) return;

        const textarea = this.elements.messageInput;

        // Reset height to auto to get the correct scrollHeight
        textarea.style.height = 'auto';

        // Calculate new height
        const maxHeight = 120; // Max height in pixels
        const newHeight = Math.min(textarea.scrollHeight, maxHeight);

        // Set new height
        textarea.style.height = newHeight + 'px';

        // Add scrollbar if content exceeds max height
        if (textarea.scrollHeight > maxHeight) {
            textarea.style.overflowY = 'auto';
        } else {
            textarea.style.overflowY = 'hidden';
        }
    }

    /**
     * Send message to API
     */
    async sendMessage() {
        if (!this.elements.messageInput || this.isTyping) return;

        const message = this.elements.messageInput.value.trim();
        if (!message) return;

        // Clear input
        this.elements.messageInput.value = '';
        this.updateCharCount();
        this.autoResizeTextarea();
        this.updateSendButtonState();

        // Add user message to chat
        this.addMessage('user', message);

        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Send to API
            const result = await window.cpmsApi.sendQuery(message, true);

            if (result.success) {
                const response = result.data;
                this.addMessage('assistant', response.response, response.sources);

                // Play notification sound if enabled
                if (this.settings.soundEnabled) {
                    this.playNotificationSound();
                }
            } else {
                this.addMessage('assistant', `❌ Error: ${result.error}`, null, true);
            }
        } catch (error) {
            console.error('Failed to send message:', error);
            this.addMessage('assistant', '❌ Failed to send message. Please check your connection and try again.', null, true);
        } finally {
            this.hideTypingIndicator();
        }
    }

    /**
     * Add message to chat
     */
    addMessage(sender, content, sources = null, isError = false) {
        if (!this.elements.chatMessages) return;

        // Remove welcome message if it exists
        const welcomeMessage = this.elements.chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = `message ${sender}`;

        const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        let avatarIcon = sender === 'user' ? '👤' : '🤖';
        if (isError) avatarIcon = '⚠️';

        messageElement.innerHTML = `
            <div class="message-avatar">
                ${avatarIcon}
            </div>
            <div class="message-content">
                <div class="message-bubble">
                    ${this.formatMessageContent(content)}
                </div>
                <div class="message-time">${timestamp}</div>
                ${sources ? this.formatSources(sources) : ''}
            </div>
        `;

        // Add to chat
        this.elements.chatMessages.appendChild(messageElement);

        // Add to history
        this.chatHistory.push({
            sender,
            content,
            sources,
            timestamp: new Date(),
            isError
        });

        // Scroll to bottom if auto-scroll is enabled
        if (this.settings.autoScroll) {
            this.scrollToBottom();
        }

        // Animate message entrance
        messageElement.style.opacity = '0';
        messageElement.style.transform = 'translateY(20px)';

        requestAnimationFrame(() => {
            messageElement.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
            messageElement.style.opacity = '1';
            messageElement.style.transform = 'translateY(0)';
        });
    }

    /**
     * Format message content (handle markdown, links, etc.)
     */
    formatMessageContent(content) {
        if (!content) return '';

        // Escape HTML
        let formatted = content.replace(/</g, '&lt;').replace(/>/g, '&gt;');

        // Convert URLs to links
        formatted = formatted.replace(
            /(https?:\/\/[^\s]+)/g,
            '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>'
        );

        // Convert line breaks
        formatted = formatted.replace(/\n/g, '<br>');

        // Simple markdown-like formatting
        formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');
        formatted = formatted.replace(/`(.*?)`/g, '<code>$1</code>');

        return formatted;
    }

    /**
     * Format sources for display
     */
    formatSources(sources) {
        if (!sources || !Array.isArray(sources) || sources.length === 0) {
            return '';
        }

        const sourcesList = sources.map(source => `<li>${this.escapeHtml(source)}</li>`).join('');

        return `
            <div class="message-sources">
                <h4><i class="fas fa-book"></i> Sources</h4>
                <ul>${sourcesList}</ul>
            </div>
        `;
    }

    /**
     * Escape HTML characters
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Show typing indicator
     */
    showTypingIndicator() {
        this.isTyping = true;
        this.updateSendButtonState();

        if (this.elements.typingIndicator) {
            this.elements.typingIndicator.classList.remove('hidden');

            // Scroll to bottom to show typing indicator
            if (this.settings.autoScroll) {
                this.scrollToBottom();
            }
        }
    }

    /**
     * Hide typing indicator
     */
    hideTypingIndicator() {
        this.isTyping = false;
        this.updateSendButtonState();

        if (this.elements.typingIndicator) {
            this.elements.typingIndicator.classList.add('hidden');
        }
    }

    /**
     * Scroll chat to bottom
     */
    scrollToBottom() {
        if (this.elements.chatMessages) {
            this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
        }
    }

    /**
     * Clear chat history
     */
    clearChat() {
        if (!confirm('Are you sure you want to clear the chat history?')) {
            return;
        }

        this.chatHistory = [];

        if (this.elements.chatMessages) {
            this.elements.chatMessages.innerHTML = `
                <div class="welcome-message">
                    <div class="welcome-icon">🏛️</div>
                    <h2>Welcome to CPMS Assistant</h2>
                    <p>I'm here to help you with pension management queries and CPMS procedures. Ask me anything!</p>
                    <div class="welcome-features">
                        <div class="feature">
                            <i class="fas fa-search"></i>
                            <span>Instant Answers</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-shield-alt"></i>
                            <span>Secure & Private</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-clock"></i>
                            <span>24/7 Available</span>
                        </div>
                    </div>
                </div>
            `;
        }

        console.log('💬 Chat history cleared');
    }

    /**
     * Toggle sidebar visibility
     */
    toggleSidebar() {
        if (this.elements.sidebar) {
            this.elements.sidebar.classList.toggle('collapsed');

            // Update toggle button icon
            const icon = this.elements.sidebarToggle?.querySelector('i');
            if (icon) {
                if (this.elements.sidebar.classList.contains('collapsed')) {
                    icon.className = 'fas fa-chevron-right';
                } else {
                    icon.className = 'fas fa-chevron-left';
                }
            }
        }
    }

    /**
     * Show modal
     */
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');

            // Focus first focusable element
            const focusable = modal.querySelector('input, select, button, textarea');
            if (focusable) {
                setTimeout(() => focusable.focus(), 100);
            }
        }
    }

    /**
     * Hide modal
     */
    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    /**
     * Show system info modal
     */
    async showSystemInfo() {
        this.showModal('info-modal');

        // Show loading state
        const infoContent = this.elements.systemInfoContent;
        const infoLoading = this.elements.infoModal?.querySelector('.info-loading');

        if (infoLoading) infoLoading.classList.remove('hidden');
        if (infoContent) infoContent.classList.add('hidden');

        try {
            // Get system stats
            const result = await window.cpmsApi.getSystemStats();

            if (result.success && infoContent) {
                this.renderSystemInfo(result.data);
                infoContent.classList.remove('hidden');
            } else {
                this.renderSystemInfoError(result.error || 'Failed to load system information');
            }
        } catch (error) {
            console.error('Failed to load system info:', error);
            this.renderSystemInfoError('Failed to load system information');
        } finally {
            if (infoLoading) infoLoading.classList.add('hidden');
        }
    }

    /**
     * Render system information
     */
    renderSystemInfo(data) {
        if (!this.elements.systemInfoContent || !data) return;

        const stats = data.stats || {};

        this.elements.systemInfoContent.innerHTML = `
            <div class="info-card">
                <h4><i class="fas fa-server"></i> System Status</h4>
                <div class="info-item">
                    <span class="info-label">Status</span>
                    <span class="status-badge healthy">Healthy</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Model Type</span>
                    <span class="info-value">${stats.model_type || 'Unknown'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Total Documents</span>
                    <span class="info-value">${stats.vector_store?.total_documents || 0}</span>
                </div>
            </div>

            <div class="info-card">
                <h4><i class="fas fa-chart-bar"></i> Performance</h4>
                <div class="info-item">
                    <span class="info-label">Cache Hit Rate</span>
                    <span class="info-value">${stats.cache?.hit_rate || 'N/A'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Average Response Time</span>
                    <span class="info-value">${stats.performance?.avg_response_time || 'N/A'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Total Queries</span>
                    <span class="info-value">${stats.performance?.total_queries || 0}</span>
                </div>
            </div>

            <div class="info-card">
                <h4><i class="fas fa-memory"></i> Memory Usage</h4>
                <div class="info-item">
                    <span class="info-label">Model Memory</span>
                    <span class="info-value">${stats.memory?.model_memory || 'N/A'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Vector Store Memory</span>
                    <span class="info-value">${stats.memory?.vector_store_memory || 'N/A'}</span>
                </div>
            </div>
        `;
    }

    /**
     * Render system info error
     */
    renderSystemInfoError(error) {
        if (!this.elements.systemInfoContent) return;

        this.elements.systemInfoContent.innerHTML = `
            <div class="info-card">
                <h4><i class="fas fa-exclamation-triangle"></i> Error</h4>
                <p style="color: var(--error-color); margin: 0;">
                    ${this.escapeHtml(error)}
                </p>
            </div>
        `;
        this.elements.systemInfoContent.classList.remove('hidden');
    }

    /**
     * Load user settings
     */
    loadSettings() {
        const defaultSettings = {
            theme: 'light',
            fontSize: 'medium',
            soundEnabled: true,
            autoScroll: true
        };

        try {
            const saved = localStorage.getItem('cpms-settings');
            return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
        } catch (error) {
            console.warn('Failed to load settings:', error);
            return defaultSettings;
        }
    }

    /**
     * Save user settings
     */
    saveSettings() {
        try {
            localStorage.setItem('cpms-settings', JSON.stringify(this.settings));
        } catch (error) {
            console.warn('Failed to save settings:', error);
        }
    }

    /**
     * Apply user settings
     */
    applySettings() {
        // Apply theme
        this.applyTheme(this.settings.theme);

        // Apply font size
        this.applyFontSize(this.settings.fontSize);

        // Update UI elements
        if (this.elements.themeSelect) {
            this.elements.themeSelect.value = this.settings.theme;
        }

        if (this.elements.fontSizeSelect) {
            this.elements.fontSizeSelect.value = this.settings.fontSize;
        }

        if (this.elements.soundEnabled) {
            this.elements.soundEnabled.checked = this.settings.soundEnabled;
        }

        if (this.elements.autoScroll) {
            this.elements.autoScroll.checked = this.settings.autoScroll;
        }
    }

    /**
     * Initialize theme
     */
    initializeTheme() {
        // Auto theme detection
        if (this.settings.theme === 'auto') {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            this.applyTheme(prefersDark ? 'dark' : 'light');

            // Listen for system theme changes
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (this.settings.theme === 'auto') {
                    this.applyTheme(e.matches ? 'dark' : 'light');
                }
            });
        }
    }

    /**
     * Change theme
     */
    changeTheme(theme) {
        this.settings.theme = theme;
        this.saveSettings();

        if (theme === 'auto') {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            this.applyTheme(prefersDark ? 'dark' : 'light');
        } else {
            this.applyTheme(theme);
        }
    }

    /**
     * Apply theme
     */
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
    }

    /**
     * Change font size
     */
    changeFontSize(size) {
        this.settings.fontSize = size;
        this.saveSettings();
        this.applyFontSize(size);
    }

    /**
     * Apply font size
     */
    applyFontSize(size) {
        const root = document.documentElement;

        switch (size) {
            case 'small':
                root.style.fontSize = '14px';
                break;
            case 'large':
                root.style.fontSize = '18px';
                break;
            default: // medium
                root.style.fontSize = '16px';
                break;
        }
    }

    /**
     * Toggle sound notifications
     */
    toggleSound(enabled) {
        this.settings.soundEnabled = enabled;
        this.saveSettings();
    }

    /**
     * Toggle auto-scroll
     */
    toggleAutoScroll(enabled) {
        this.settings.autoScroll = enabled;
        this.saveSettings();
    }

    /**
     * Play notification sound
     */
    playNotificationSound() {
        if (!this.settings.soundEnabled) return;

        try {
            // Create a simple notification sound using Web Audio API
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        } catch (error) {
            console.warn('Failed to play notification sound:', error);
        }
    }

    /**
     * Handle global keyboard shortcuts
     */
    handleGlobalKeydown(event) {
        // Escape key to close modals
        if (event.key === 'Escape') {
            const openModal = document.querySelector('.modal:not(.hidden)');
            if (openModal) {
                this.hideModal(openModal.id);
                return;
            }
        }

        // Ctrl/Cmd + K to focus search
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            if (this.elements.messageInput) {
                this.elements.messageInput.focus();
            }
        }

        // Ctrl/Cmd + / to toggle sidebar
        if ((event.ctrlKey || event.metaKey) && event.key === '/') {
            event.preventDefault();
            this.toggleSidebar();
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        // Adjust layout for mobile
        if (window.innerWidth <= 768) {
            if (this.elements.sidebar && !this.elements.sidebar.classList.contains('collapsed')) {
                this.toggleSidebar();
            }
        }
    }

    /**
     * Handle visibility change (tab focus/blur)
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // Tab is hidden - pause any ongoing operations
            console.log('🔇 Tab hidden - pausing operations');
        } else {
            // Tab is visible - resume operations
            console.log('🔊 Tab visible - resuming operations');

            // Check connection status
            this.checkConnection();
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        console.error('❌ Application Error:', message);

        // Show error in UI
        if (this.elements.chatMessages) {
            this.addMessage('assistant', `❌ ${message}`, null, true);
        } else {
            // Fallback to alert if UI is not ready
            alert(`Error: ${message}`);
        }
    }

    /**
     * Cleanup on page unload
     */
    cleanup() {
        console.log('🧹 Cleaning up CPMS Frontend Application');

        // Cancel any pending API requests
        if (window.cpmsApi) {
            window.cpmsApi.cancelAllRequests();
        }

        // Save settings
        this.saveSettings();
    }
}

// Initialize the application when the script loads
document.addEventListener('DOMContentLoaded', () => {
    window.cpmsApp = new CPMSApp();
});

// Also initialize immediately if DOM is already ready
if (document.readyState !== 'loading') {
    window.cpmsApp = new CPMSApp();
}

// Export for debugging
if (typeof window !== 'undefined') {
    window.CPMSApp = CPMSApp;
}
