# Core ML and NLP libraries
torch>=2.0.0
transformers>=4.35.0
accelerate>=0.24.0
peft>=0.6.0
# bitsandbytes>=0.41.0  # Optional for GPU quantization
datasets>=2.14.0
trl>=0.7.0

# Vector database and embeddings
chromadb>=0.4.15
sentence-transformers>=2.2.2
langchain>=0.0.350
langchain-community>=0.0.10
faiss-cpu>=1.7.4

# PDF processing
PyPDF2>=3.0.1
pdfplumber>=0.9.0
pymupdf>=1.23.0

# Web scraping and data processing
beautifulsoup4>=4.12.2
requests>=2.31.0
pandas>=2.1.0
numpy>=1.24.0

# Model serving and API
flask>=3.0.0
flask-cors>=4.0.0
gradio>=4.0.0
uvicorn>=0.24.0
fastapi>=0.104.0
pydantic>=2.0.0
requests>=2.31.0

# Utilities
python-dotenv>=1.0.0
tqdm>=4.66.0
jsonlines>=4.0.0
scikit-learn>=1.3.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Database and Authentication
sqlalchemy>=2.0.0
alembic>=1.12.0
psycopg2-binary>=2.9.7
python-jose[cryptography]==3.3.0
PyJWT[crypto]>=2.8.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6
cryptography>=41.0.0

# SMS/OTP Services
twilio>=8.10.0
requests>=2.31.0

# Local model support
llama-cpp-python>=0.2.0
ctransformers>=0.2.27
