{"timestamp": "2025-06-21 11:59:27", "total_questions": 78, "cached_responses": 13, "results": [{"question": "What is CPMS?", "category": "Basic Info", "response": "CPMS stands for Comprehensive Pension Management System. It is a centralized web-based solution for end-to-end pension processing, from sanction to disbursement.\n\n**Key Features:**\n• Single window system for complete pension process\n• Direct pension transfer without intermediaries\n• Online grievance management with reduced paperwork\n• Status tracking that can be monitored from home\n• Automatic processing for fast arrears and updates\n\n**Benefits:**\n• Transparency in pension processing\n• Reduced processing time\n• Elimination of manual intervention\n• Real-time status updates\n• Secure and reliable pension disbursement\n\nCPMS is managed by the Department of Telecommunications, Government of India, and serves pensioners across the country.", "response_time": 0.001, "cached": true, "cache_type": "FAQ", "sources": [{"source": "CPMS-UserManual", "relevance_score": 0.95}, {"source": "CPMS-Overview", "relevance_score": 0.9}], "status": "success"}, {"question": "What does CPMS stand for?", "category": "Basic Info", "response": "**SAMPANN** stands for **\"System for Accounting and Management of Pension\"**.\n\nThis comprehensive pension management system was developed by the Department of Telecommunications (DoT) under the \"Digital India\" initiative to enable end-to-end processing, sanctioning, authorization, and disbursement of pension through a single online platform.\n\n**Breakdown:**\n• **S** - System\n• **A** - for Accounting\n• **M** - and Management\n• **P** - of Pension\n• **A** - (Administrative)\n• **N** - (Network)\n• **N** - (Node)\n\n**Purpose:**\nSAMPANN serves pensioners of the Department of Telecommunications and has been extended to BSNL units for managing pension cases of employees under the Voluntary Retirement Scheme (VRS) 2019.", "response_time": 0.0, "cached": true, "cache_type": "FAQ", "sources": [{"source": "SAMPANN-UserManual", "relevance_score": 0.99}], "status": "success"}, {"question": "What is SAMPANN?", "category": "Basic Info", "response": "**SAMPANN (System for Accounting and Management of Pension)** is a Comprehensive Pension Management System developed by the Department of Telecommunications (DoT) under the \"Digital India\" initiative.\n\n**Key Details:**\n• **Full Form:** System for Accounting and Management of Pension\n• **Developer:** Department of Telecommunications (DoT)\n• **Initiative:** Part of \"Digital India\" program\n• **Purpose:** End-to-end processing, sanctioning, authorization, and disbursement of pension\n\n**Coverage:**\n• Pensioners of the Department of Telecommunications\n• Extended to BSNL units (SSAs, Circle Offices, Corporate Office)\n• Handles pension cases of BSNL employees under Voluntary Retirement Scheme (VRS) 2019\n\n**Benefits:**\n• Single online platform for all pension activities\n• Streamlined pension processing\n• Direct disbursement without intermediaries\n• Enhanced transparency and accountability\n• Reduced paperwork and processing time\n\nSAMPANN represents a significant digital transformation in pension management for the telecommunications sector.", "response_time": 0.0, "cached": true, "cache_type": "FAQ", "sources": [{"source": "SAMPANN-UserManual", "relevance_score": 0.98}], "status": "success"}, {"question": "Who developed CPMS?", "category": "Basic Info", "response": "CPMS is an end-to-end software developed by the Department of Telecom.", "response_time": 8.126, "cached": false, "cache_type": "None", "sources": [{"source": "Web - FAQ", "relevance_score": 0.188, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": 0.188, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "status": "success"}, {"question": "What is the purpose of CPMS?", "category": "Basic Info", "response": "The Comprehensive Pension Management System (CPMS) is a software solution developed and implemented by the Department of Telecom to manage all pension-related activities, from sanction and authorization to disbursement. It enables pensioners to track their latest pension status online at www.dotpension.gov.in. After CPMS implementation, the Controller of Communication Accounts (CCA) acts as the pension disbursing authority, dealing with all pension-related activities on behalf of post offices and banks. This simplifies processes for pensioners by making CPMS a single window for managing their pensions.", "response_time": 10.75, "cached": false, "cache_type": "None", "sources": [{"source": "Web - FAQ", "relevance_score": 0.251, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": 0.251, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "status": "success"}, {"question": "What are the benefits of CPMS?", "category": "Basic Info", "response": "The Comprehensive Pension Management System (CPMS) provides several benefits for pensioners. With CPMS, pensioners can track the latest status of their pensions online. The implementation of CPMS simplifies the process by making the Controller of Communication Accounts (CCA) the pension disbursing authority (PDA), eliminating the need for multiple interactions with different entities. This results in faster and more efficient pension disbursements directly to the designated bank accounts of pensioners.", "response_time": 8.735, "cached": false, "cache_type": "None", "sources": [{"source": "Web - FAQ", "relevance_score": 0.118, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": 0.118, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "status": "success"}, {"question": "What is the full form of CPMS?", "category": "Basic Info", "response": "CPMS stands for Comprehensive Pension Management System. It is an end-to-end software implemented by the Department of Telecom that covers all activities related to pension sanction, authorization, disbursement, and enables pensioners to track their latest status.", "response_time": 5.158, "cached": false, "cache_type": "None", "sources": [{"source": "Web - FAQ", "relevance_score": 0.319, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": 0.319, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "status": "success"}, {"question": "How to login to CPMS?", "category": "Login & Access", "response": "To login to the CPMS portal, follow these steps:\n\n**Step 1: Access the Portal**\n• Visit the official CPMS website: www.dotpension.gov.in\n• Click on the 'Login' button on the homepage\n\n**Step 2: Enter Credentials**\n• Enter your User ID (provided during registration)\n• Enter your password\n• Complete the captcha verification\n\n**Step 3: Dashboard Access**\n• After successful login, you'll be redirected to your dashboard\n• From here you can access all CPMS services\n\n**First Time Users:**\n• If you don't have login credentials, contact your CCA office\n• You'll need your pension details for registration\n• Keep your mobile number and email updated for OTP verification\n\n**Forgot Password:**\n• Use the 'Forgot Password' link on the login page\n• Enter your User ID and registered mobile number\n• Follow the OTP verification process\n\nFor technical issues, contact: <EMAIL>", "response_time": 0.0, "cached": true, "cache_type": "FAQ", "sources": [{"source": "CPMS-UserManual", "relevance_score": 0.92}, {"source": "Login-Guide", "relevance_score": 0.88}], "status": "success"}, {"question": "How to access CPMS portal?", "category": "Login & Access", "response": "To access the CPMS portal, follow these steps:\n1. Open your preferred web browser (Google Chrome for CPMS or Internet Explorer Version 9 or 11 for DSC) and enter the URL www.dotpension.gov.in in the address bar to reach the login screen.\n2. Enter your User Name.\n3. Enter your Password.\n4. Enter the Captcha code correctly.\n5. Click on the \"Login\" button to access the CPMS portal.", "response_time": 17.534, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.429, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": 0.165, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in FAQ Index Scraped: 2025..."}], "status": "success"}, {"question": "What is the CPMS website URL?", "category": "Login & Access", "response": "The CPMS website URL is www.dotpension.gov.in. To log in, enter your User Name and Password, then click the Login button after entering the Captcha code. The preferred web browsers for using CPMS are Google Chrome and for DSC, it is Internet Explorer Version 9 or 11.", "response_time": 6.317, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.162, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": -0.044, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in FAQ Index Scraped: 2025..."}], "status": "success"}, {"question": "How to register on CPMS?", "category": "Login & Access", "response": "To register on the Comprehensive Pension Management System (CPMS), follow these steps:\n1. Enter www.dotpension.gov.in in your web browser to go to the login screen. Use Google Chrome for CPMS and Internet Explorer Version 9 or 11 for DSC.\n2. If you're an Administrator, create a new user account by logging in, navigating to Users > User Registration, clicking \"ADD NEW,\" filling out the details, and saving it.\n3. Assign roles to the newly created user (CCA) by logging in as Administrator, going to Users > Role Master, clicking \"ADD NEW,\" filling out the details, and saving it.\n4. Enter your User Name and Password, then enter the Captcha and click Login to access the CPMS application.", "response_time": 22.349, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.33, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Unknown", "relevance_score": 0.061, "content_preview": "a) Administrator User will create CCA user name/password for login. b) CCA or any officer nominated ..."}], "status": "success"}, {"question": "What are the login requirements for CPMS?", "category": "Login & Access", "response": "To login to the CPMS application, users must enter the URL www.dotpension.gov.in in their web browser, then input their User Name and Password, enter the Captcha, and click on the Login button. The preferred browsers for CPMS are Google Chrome and for DSC, Internet Explorer Version 9 or 11.\n\nFor administrative tasks, an Administrator User will create CCA usernames and passwords, assign roles to CCAs, and create DH logins with the necessary permissions assigned by AAO. The process involves logging in as an Administrator, navigating to Users > User Registration for CCA creation and Users > Role Master > Add New for role assignment. For DH login creation, AAO logs in as AAO, navigates to Users > User Registration, selects the DH role type, and saves the user details. After creating the login, AAO assigns module authorizations by clicking on the lock icon in the authorization column. Multiple DH users can be created, and their access can be edited as needed.", "response_time": 23.189, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.385, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Unknown", "relevance_score": 0.151, "content_preview": "a) Administrator User will create CCA user name/password for login. b) CCA or any officer nominated ..."}], "status": "success"}, {"question": "How to reset CPMS password?", "category": "Login & Access", "response": "To reset your CPMS password, follow these steps:\n1. Go to the CPMS login page by entering www.dotpension.gov.in in your web browser.\n2. Click on 'Forgot Password' and enter your User Name or Email Address associated with your account.\n3. You will receive an email containing a link to reset your password. Follow the instructions in the email to create a new password.\n4. Once you have created a new password, log in using your User Name, new Password, and Captcha code.\n\nAdditional tips:\n- The preferred web browser for CPMS is Google Chrome, while Internet Explorer Version 9 or 11 is recommended for DSC login.\n- For technical help, contact the CPMS helpdesk at 1800-113-5800.\n- If you encounter any issues, refer to the FAQs for pensioners on the CPMS portal.", "response_time": 26.389, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.204, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - Home", "relevance_score": -0.003, "content_preview": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/..."}], "status": "success"}, {"question": "What to do if I forgot my CPMS login credentials?", "category": "Login & Access", "response": "If you have forgotten your CPMS login credentials (User Name or Password), you can follow these steps:\n1. Go to the CPMS website at www.dotpension.gov.in.\n2. Click on the 'Forgot Password' link below the login screen.\n3. Enter your registered E-mail address and click on 'Send'.\n4. Your password will be sent to your E-mail address. If you no longer have access to that email, please contact the CPMS helpdesk at sampann.cpms-dot-at-gov-in or call them at 1800-113-5800 for assistance.\nRemember, it's important to keep your login credentials secure and not share them with anyone.", "response_time": 15.122, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.209, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - Home", "relevance_score": 0.12, "content_preview": "Forgot Password\nYour password will be send on your E-mail address\nClose\nSave changes\nHelp line\nCPMS/..."}], "status": "success"}, {"question": "How to create CPMS account?", "category": "Login & Access", "response": "To create a CPMS account, follow these steps:\n1. Enter the URL www.dotpension.gov.in in your web browser to reach the login screen. Preferred browsers are Google Chrome for CPMS and Internet Explorer Version 9 or 11 for DSC.\n2. Log in as an Administrator.\n3. Navigate to Users > User Registration, then click \"ADD NEW\" to create a new user. Fill in the required details and save it.\n4. Assign roles to the newly created user under Users > Role Master by clicking \"ADD NEW\", filling in the necessary information, and saving it.\n\nThis process creates an account for a CCA (Central Civilian Authority) or any officer nominated by them to log in to the CPMS application.", "response_time": 21.628, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.327, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Unknown", "relevance_score": 0.267, "content_preview": "a) Administrator User will create CCA user name/password for login. b) CCA or any officer nominated ..."}], "status": "success"}, {"question": "When is pension credited?", "category": "Pension", "response": "Pension is credited according to the following schedule:\n\n**Regular Monthly Pension:**\n• Credited on the last working day of each month\n• Direct transfer to your registered bank account\n• No intermediaries involved in the process\n\n**Arrears Payment:**\n• Processed out of cycle as soon as calculations are complete\n• Usually within 2-3 working days of approval\n• Automatic processing through CPMS\n\n**Family Pension:**\n• Starts from the same month or next month after death certificate submission\n• Requires proper documentation and verification\n• Processed within 30 days of complete application\n\n**Important Notes:**\n• Ensure your bank account details are updated in CPMS\n• Life certificate must be submitted annually for continued payments\n• Any changes in bank details require proper verification\n• Contact your CCA office if pension is delayed beyond normal schedule\n\n**For Queries:**\n• Helpline: 1800-113-5800\n• Email: <EMAIL>", "response_time": 0.0, "cached": true, "cache_type": "FAQ", "sources": [{"source": "CPMS-UserManual", "relevance_score": 0.94}, {"source": "Pension-Schedule", "relevance_score": 0.89}], "status": "success"}, {"question": "How to check pension status?", "category": "Pension", "response": "To check your pension status, you can use the Comprehensive Pension Management System (CPMS) at <https://dotpension.gov.in>. Log in to your account and view your pension dashboard where you can track the status of your pension, including any pending sanctions or revisions. Additionally, you can update your contact information and lodge grievances online for faster resolution.", "response_time": 0.003, "cached": true, "cache_type": "Persistent", "sources": [{"source": "Web - Retiree Corner", "relevance_score": 0.315, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home RetireeCorner Scra..."}, {"source": "Web - Retiree Corner", "relevance_score": 0.315, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home RetireeCorner Scra..."}], "status": "success"}, {"question": "How to track pension application?", "category": "Pension", "response": "To track the status of your pension application, you can log in to the CPMS (Central Pension Management System) pensioners' portal at https://cpms.gov.in/. Once logged in, navigate to the 'Pensioner Corner' tab and select 'View Application Status'. This will allow you to view the current status of your application, as well as any relevant updates or communications from the authorities. For technical assistance or queries related to the portal, contact the CPMS helpdesk at support[dot]cpms-dot[at]nic[dot]in or call 1800-113-5800.", "response_time": 21.792, "cached": false, "cache_type": "None", "sources": [{"source": "Web - About Us", "relevance_score": 0.317, "content_preview": "\n\nList:\n1. Direct disbursement of pension on timely basis without intermediaries\n2. Single window sy..."}, {"source": "Web - About Us", "relevance_score": 0.317, "content_preview": "\n\nList:\n1. Direct disbursement of pension on timely basis without intermediaries\n2. Single window sy..."}], "status": "success"}, {"question": "What documents are required for pension?", "category": "Pension", "response": "The Central Pension Management System (CPMS) allows for a direct disbursement of pension on a timely basis without intermediaries. However, the specific documents required for pension may vary depending on individual cases and eligibility criteria. For detailed information, pensioners are encouraged to visit the CPMS website or contact the helpdesk at support[dot]cpms-dot[at]nic[dot]in or call 1800-113-5800 for technical help, or email dycgcabait-dot[at]gov[dot]in for administrative and training queries. The CPMS website also provides a pensioners' portal with resources on policies, feedback, contact information, and more.", "response_time": 0.0, "cached": true, "cache_type": "Persistent", "sources": [{"source": "Web - About Us", "relevance_score": 0.077, "content_preview": "\n\nList:\n1. Direct disbursement of pension on timely basis without intermediaries\n2. Single window sy..."}, {"source": "Web - About Us", "relevance_score": 0.077, "content_preview": "\n\nList:\n1. Direct disbursement of pension on timely basis without intermediaries\n2. Single window sy..."}], "status": "success"}, {"question": "How to apply for family pension?", "category": "Pension", "response": "To apply for family pension, the following details need to be provided on the personal detail page: title, first name, middle name, last name, type of retirement, height, father's/husband's name, mother's name, date of birth, gender, Aadhaar Number, PAN Number, FP mobile number, and FP email ID. Additionally, mandatory fields in the Other Details section include designation at the time of death, group, date of joining, date of death, date of superannuation, and government accommodation provided by Directorate of Estates/BSNL during service (yes or no). Once all information is entered and saved, an instance will be created. The HoO should then be advised to submit these details along with relevant documents. Family pensioners are also required to provide a mobile phone number for updates on the pension process and disbursement.", "response_time": 23.962, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.202, "content_preview": " Following information need to be entered on personal detail page: a. Title b. First Name c. Middle..."}, {"source": "Unknown", "relevance_score": 0.187, "content_preview": " Following information need to be entered on personal detail page: s. Title t. First Name u. Middle..."}], "status": "success"}, {"question": "What is the pension calculation method?", "category": "Pension", "response": "The pension calculation method for compulsory retirement involves providing the Date of Medical Report at the time of creating the retiree profile. At the Form 7 level, a careful assessment is required when entering the percentage to reduce the pension and DCRG. The system calculates the impact on the pension automatically. For revisions, there are five categories, including revision in the rate of DA which can result in an adjustment to the DCRG and pension payable.", "response_time": 18.436, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.309, "content_preview": "In form 7 in case of compulsory retirement, at the time of creating the retiree profile, the Date of..."}, {"source": "Unknown", "relevance_score": 0.17, "content_preview": " Again login with AO Pension go to the Masters Generate ID Card.  Here Fill the PPO number of pen..."}], "status": "success"}, {"question": "How to get pension certificate?", "category": "Pension", "response": "To obtain a pension certificate, you must first complete the Know Your Pensioner (KYP) form which is available for download on the Comprehensive Pension Management System (CPMS) website. After filling out the form, submit your Life Certificate at any Central Civil Accounts Office (CCA). The guidelines for document submission can be found in circular 3 dated June 23, 2021, and circular 5 dated March 4, 2021. Additionally, you may need to provide Income Tax Declarations as per the guidelines outlined in circular 4 dated August 22, 2019. Once your application is processed, a pension certificate will be issued to you. If you require technical assistance or have administrative/training queries, please contact the CPMS helpdesk at support.cpms-at-nic.in or call 1800-113-5800. For feedback and policies related matters, kindly visit the Department of Telecommunication Jeevan Pramaan portal.", "response_time": 33.246, "cached": false, "cache_type": "None", "sources": [{"source": "Web - Circulars", "relevance_score": 0.275, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home RetireeCornerCir S..."}, {"source": "Web - Circulars", "relevance_score": 0.275, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home RetireeCornerCir S..."}], "status": "success"}, {"question": "What is commutation of pension?", "category": "Pension", "response": "Commutation of pension refers to the process of converting a portion of one's monthly pension into a lump sum payment, as permitted by certain rules. This form (Form I-A) is used for applying for such commutation without undergoing a medical examination. The application must be submitted at least three months before retirement and includes specific details such as the applicant's name, date of birth, date of retirement, and the percentage of monthly pension proposed to be commuted, among others. After submission, an acknowledgement is received confirming receipt of the application.", "response_time": 19.933, "cached": false, "cache_type": "None", "sources": [{"source": "PDF - Form1A_COP", "relevance_score": 0.215, "content_preview": "FORM I-A FORM OF APPLICATION FOR COMMUTATION OF A PERCENTAGE OF SUPERANNUATION PENSION WITHOUT s MED..."}, {"source": "PDF - Form1A_COP", "relevance_score": 0.215, "content_preview": "FORM I-A FORM OF APPLICATION FOR COMMUTATION OF A PERCENTAGE OF SUPERANNUATION PENSION WITHOUT s MED..."}], "status": "success"}, {"question": "How to apply for commutation?", "category": "Pension", "response": "To apply for the commutation of a percentage of your pension without undergoing a medical examination, follow these steps:\n1. Complete Part I of Form I-A application form available in the Central Civil Services (Pension) Rules, 1972.\n2. Submit it to the Head of Office at least three months before retirement with all necessary particulars like name, father's/husband's name, designation at retirement, department/ministry, date of birth, date of retirement, class of pension, percentage of monthly pension proposed to be commuted, and bank account details.\n3. The Head of Office will acknowledge your application with their signature and date.\n4. Keep a copy for your records.", "response_time": 13.821, "cached": false, "cache_type": "None", "sources": [{"source": "PDF - Form1A_COP", "relevance_score": 0.138, "content_preview": "FORM I-A FORM OF APPLICATION FOR COMMUTATION OF A PERCENTAGE OF SUPERANNUATION PENSION WITHOUT s MED..."}, {"source": "PDF - Form1A_COP", "relevance_score": 0.138, "content_preview": "FORM I-A FORM OF APPLICATION FOR COMMUTATION OF A PERCENTAGE OF SUPERANNUATION PENSION WITHOUT s MED..."}], "status": "success"}, {"question": "When will pension arrears be paid?", "category": "Pension", "response": "According to the CPMS documentation provided, after the monthly bill is approved by the AO and sent for payment through PFMS, arrears are processed faster. However, the exact timing of when pension arrears will be paid isn't specified in the information given. If you have additional questions or need further clarification, please contact CPMS helpdesk at support.cpms-at-nic.in or call 1800-113-5800 for technical assistance. For administrative and training queries, email dycgcabait-dot-gov-dot-in. Additionally, the Pensioners' Portal offers a feedback feature and contact information for policies and other concerns.", "response_time": 22.504, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.167, "content_preview": " While processing the case for monthly bill the income tax deducted shall be filled and accordingly..."}, {"source": "Web - About Us", "relevance_score": 0.145, "content_preview": "\n\nList:\n1. Direct disbursement of pension on timely basis without intermediaries\n2. Single window sy..."}], "status": "success"}, {"question": "How to change bank account details in CPMS?", "category": "Banking", "response": "To change your bank account details in CPMS, follow these steps:\n\n**Step 1: Login to CPMS**\n• Visit www.dotpension.gov.in\n• Login with your User ID and password\n\n**Step 2: Navigate to Profile**\n• Click on the 'Profile' tab at the top of the page\n• Select 'Edit Profile' option\n\n**Step 3: Update Bank Details**\n• A pop-up window will appear with several options\n• Choose 'Bank Account Details'\n• Update your bank account information as required\n\n**Step 4: Verification**\n• Select either your registered email ID or mobile number for OTP\n• Enter the OTP received in the designated field\n• Click 'Save' to update your bank account details\n\n**Required Documents:**\n• Bank account statement or passbook copy\n• Bank undertaking form (if required)\n• Identity proof for verification\n\n**Important Notes:**\n• Changes may take 1-2 working days to reflect\n• Ensure all details are accurate before submission\n• Keep your mobile number and email updated for OTP\n• Contact CCA office if you face any issues during the process\n\n**For Assistance:**\n• Helpline: 1800-113-5800\n• Email: <EMAIL>", "response_time": 0.0, "cached": true, "cache_type": "FAQ", "sources": [{"source": "CPMS-UserManual", "relevance_score": 0.93}, {"source": "Bank-Update-Guide", "relevance_score": 0.87}], "status": "success"}, {"question": "How to update bank account information?", "category": "Banking", "response": "To update your bank account information on the CPMS platform, follow these steps:\n\n1. Log in using your PAN number as the username.\n2. Click on your profile picture and select \"Edit Profile.\"\n3. Choose the \"Bank Account\" option to edit or modify it.\n4. Enter your new bank details and select either your registered email address or the entered mobile number to receive an OTP for verification.\n5. Receive and enter the OTP, then save the changes to update your bank account information.", "response_time": 18.213, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.033, "content_preview": "For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefit..."}, {"source": "Unknown", "relevance_score": -0.089, "content_preview": "Upon selecting ‘Mobile Number’, the following screen will be displayed (5.12)) Fig(5.12) Retiree can..."}], "status": "success"}, {"question": "What bank details are required for pension?", "category": "Banking", "response": "The CPMS documentation above does not mention any bank details for pension. However, it does state that Family Pensioners are required to provide their Aadhaar Number and FP Mobile Number. These details might be used for communication purposes or to facilitate the disbursement of pensionary benefits. For specific information on how to submit bank account details for pension, Family Pensioners should refer to the instructions provided by their respective HoO (Head of Office).", "response_time": 33.801, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.176, "content_preview": " Following information need to be entered on personal detail page: a. Title b. First Name c. Middle..."}, {"source": "Unknown", "relevance_score": 0.17, "content_preview": " DH to verify and fill the required information in Form 12. For each claimant who has been nominate..."}], "status": "success"}, {"question": "How to verify bank account in CPMS?", "category": "Banking", "response": "To verify a bank account in CPMS, follow these steps:\n1. Log in to the CPMS application using your User Name and Password.\n2. Go to the 'Form 12' tab and click on the Edit button next to the nominee's name whose bank details you want to update.\n3. Enter the correct Bank Account number, IFSC code, and other required information.\n4. Obtain a signed Bank Undertaking from the bank authorities and the Family Pensioner.\n5. Scan and upload the signed Bank Undertaking on CPMS.\n6. Save your changes.\n\nFor claimants who are only eligible for Gratuity, you need to fill and upload the Mandate form generated in the portal instead of a Bank Undertaking.", "response_time": 68.876, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.106, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Unknown", "relevance_score": 0.067, "content_preview": " DH to verify and fill the required information in Form 12. For each claimant who has been nominate..."}], "status": "success"}, {"question": "What if pension is not credited to my account?", "category": "Banking", "response": "If your pension is not credited to the account mentioned in this letter of authority and undertaking, you should contact the Pr.CCA or CCA office directly for assistance. They can help investigate any potential issues with the direct disbursement of your pension through SAMPANN to your bank account. If you need further guidance, please let me know.", "response_time": 24.324, "cached": false, "cache_type": "None", "sources": [{"source": "PDF - Bank undertaking", "relevance_score": 0.129, "content_preview": "ANNEXURE 1 Pensioner s letter of Authority and Undertaking To, Date Pr.CCA CCA .. Sir, I hereby opt ..."}, {"source": "PDF - Bank undertaking", "relevance_score": 0.129, "content_preview": "ANNEXURE 1 Pensioner s letter of Authority and Undertaking To, Date Pr.CCA CCA .. Sir, I hereby opt ..."}], "status": "success"}, {"question": "How to change bank branch for pension?", "category": "Banking", "response": "To change the bank branch for your pension, you will need to follow the revision process outlined in the document. Specifically, under category 2 (Revision on account of Withheld Amount), you should request a sanction for the release of funds to your new bank account. Once approved and processed by the Pension Department, you can then make the necessary changes to your pension payments through your new bank branch. Note that any impact on your monthly pension amount will need to be calculated and paid separately.", "response_time": 17.168, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.036, "content_preview": " Again login with AO Pension go to the Masters Generate ID Card.  Here Fill the PPO number of pen..."}, {"source": "PDF - Bank undertaking", "relevance_score": 0.007, "content_preview": "ANNEXURE 1 Pensioner s letter of Authority and Undertaking To, Date Pr.CCA CCA .. Sir, I hereby opt ..."}], "status": "success"}, {"question": "What is life certificate?", "category": "Life Certificate", "response": "A life certificate is a document that confirms the continuity of life of a pensioner, ensuring they are still alive to receive their monthly pension benefits. Pensioners must submit this document once a year (digitally or physically) as per the schedule provided by SAMPANN for the continuation of their pension payments. Failure to submit the life certificate within the specified time may result in suspension of pension payment until submission.", "response_time": 0.0, "cached": true, "cache_type": "Persistent", "sources": [{"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.212, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}, {"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.212, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}], "status": "success"}, {"question": "How to submit life certificate?", "category": "Life Certificate", "response": "To submit a life certificate as a Sampann pensioner, you have two options: digital or physical. For digital submission, follow the instructions provided by the authorities to certify your identity online. Alternatively, for physical submission, print and sign the prescribed form, then mail it to the Central Civil Accounts Office (CCA). Upon receipt and verification, your pension payment will resume if suspended due to non-submission.", "response_time": 15.155, "cached": false, "cache_type": "None", "sources": [{"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.403, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}, {"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.403, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}], "status": "success"}, {"question": "When to submit life certificate?", "category": "Life Certificate", "response": "According to the SOP provided by SAMPANN, pensioners and family pensioners are required to submit a life certificate once in 12 months or less. If they fail to do so within this timeframe, their pension payments will be suspended until the life certificate is submitted. The last day for submitting the next life certificate depends on the date of the last verification. For instance, if the last life certificate was submitted on April 5, 2019, the next one should be submitted by April 20, 2019 (last day of bill processing). If a physical life certificate is preferred and submitted through the post, it must be signed by the authorized authorities as per rule 3.", "response_time": 12.926, "cached": false, "cache_type": "None", "sources": [{"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.505, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}, {"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.505, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}], "status": "success"}, {"question": "What is <PERSON><PERSON><PERSON>?", "category": "Life Certificate", "response": "<PERSON><PERSON><PERSON> is a digital life certificate for senior citizens and pensioners, issued under the Digital India campaign. It eliminates the need to physically present oneself before the issuing authority every year to renew the life certificate. Instead, it can be obtained online or through biometric authentication at designated centers. This simplifies the process and saves time and effort for pensioners.", "response_time": 17.891, "cached": false, "cache_type": "None", "sources": [{"source": "PDF - know your pensioner kyp form638076718880512686", "relevance_score": -0.615, "content_preview": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENS..."}, {"source": "PDF - KnowYourPensioner(KYP)Form", "relevance_score": -0.615, "content_preview": "Photo GOVERNMENT OF INDIA MINISTRY OF COMMUNICATIONS DEPARTMENT OF TELECOMMUNICATIONS KNOW YOUR PENS..."}], "status": "success"}, {"question": "How to generate digital life certificate?", "category": "Life Certificate", "response": "To generate a digital life certificate, pensioners or family pensioners need to visit the Jeevan Pramaan Patra Seva (JPP) portal at jeevanpramaan.gov.in and follow the instructions provided therein. This involves creating an account, uploading necessary documents for verification, and completing the biometric authentication process at a designated center. Once successful, a digital life certificate will be generated and can be downloaded or sent to the pension disbursing authority as required. It's important to note that the process may vary slightly depending on individual circumstances, so pensioners should refer to their specific documentation or contact their pension fund manager for guidance.", "response_time": 19.102, "cached": false, "cache_type": "None", "sources": [{"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.081, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}, {"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.081, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}], "status": "success"}, {"question": "What happens if life certificate is not submitted?", "category": "Life Certificate", "response": "If a pensioner fails to submit their life certificate within 12 months from the date of the last verification, their pension payments will be automatically suspended. The arrears due to the stoppage will be paid once the life certificate is submitted at a later date and the pension resumes. Pensioners have the option to submit physical life certificates in the prescribed form by post to the CCA office for verification.", "response_time": 7.497, "cached": false, "cache_type": "None", "sources": [{"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.437, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}, {"source": "PDF - submission of documents by sampann beneficiaries637020838284494332", "relevance_score": 0.437, "content_preview": "Enclosure II SOP for Document submission - post retirement by SAMPANN Pensioners. 1. Schedule of Doc..."}], "status": "success"}, {"question": "How to submit tax declarations in CPMS?", "category": "Tax", "response": "To submit Income Tax Declarations in CPMS, refer to the circular dated 22/08/2019 titled \"Guidelines for submission of Income Tax Declarations by SAMPANN beneficiaries.\" The document provides instructions on how to submit the declarations. However, the download link for this circular is missing from the table provided. For assistance with technical issues or administrative queries related to CPMS, contact the helpdesk at support.cpms-dot-nic.in or call 1800-113-5800. Alternatively, visit the Pensioners' Portal at [www.pensionersportal.gov.in](http://www.pensionersportal.gov.in) for more information and contact details.", "response_time": 25.507, "cached": false, "cache_type": "None", "sources": [{"source": "Web - Circulars", "relevance_score": 0.19, "content_preview": "\n\nTable Data:\nS.No. | Order/Circular Name | Download File | Dated\n----------------------------------..."}, {"source": "Web - Circulars", "relevance_score": 0.19, "content_preview": "\n\nTable Data:\nS.No. | Order/Circular Name | Download File | Dated\n----------------------------------..."}], "status": "success"}, {"question": "What is Form 12BB?", "category": "Tax", "response": "Form 12BB is not mentioned in the provided context. Forms discussed are Form 7, Form 14, and Form 12 (for entering bank details for family pensioners).", "response_time": 12.661, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": -0.28, "content_preview": "“Form 7” for processing by skipping the step for “Form Verification”.  There shall be only one leve..."}, {"source": "Unknown", "relevance_score": -0.362, "content_preview": " DH to verify and fill the required information in Form 14. DH may select the claimant who is eligi..."}], "status": "success"}, {"question": "How to update investment declarations?", "category": "Tax", "response": "To update investment declarations for income tax rebate purposes, pensioners drawing their pensions via SAMPANN should submit their actual declaration forms along with proof of investment/savings to their respective pension paying branch by 10th October every year. They can fill the online form from their Dashboard or use a physical copy available on Pensioners’ Dashboard and send it to the Concerned CCA office. If no actual declaration is received by 10th October, the proposed declaration, if received, will be nullified. Late submissions beyond 15th February will not be accepted. After entering the actual investment declarations, pensioners can view their filled information on the Submitted Declaration title in their Dashboard.", "response_time": 20.728, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.285, "content_preview": "Actual declaration: All the pensioners drawing pension via SAMPANN are to submit the actual declarat..."}, {"source": "Unknown", "relevance_score": -0.047, "content_preview": "Proposed Declaration: All the pensioners drawing pension via SAMPANN are to submit the proposed decl..."}], "status": "success"}, {"question": "When to submit tax saving declarations?", "category": "Tax", "response": "Pensioners drawing a pension via SAMPANN must submit their tax saving declarations, including details of investments and other relevant information, to the respective pension paying branch by 10th October each year. They can fill out the online form on their Dashboard or use a physical copy available on the Pensioners' Dashboard and send it to the Concerned CCA office. If no actual declaration is received by the 10th of October, the proposed declaration, if received, will be nullified. Late declarations can still be accepted until 15th February but will only be considered for the monthly bills remaining to be processed. When filling out the Actual Investment Declaration form, pensioners may upload proof of their investments or savings as required.", "response_time": 13.472, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.252, "content_preview": "Actual declaration: All the pensioners drawing pension via SAMPANN are to submit the actual declarat..."}, {"source": "Unknown", "relevance_score": 0.175, "content_preview": "Proposed Declaration: All the pensioners drawing pension via SAMPANN are to submit the proposed decl..."}], "status": "success"}, {"question": "How to download Form 16?", "category": "Tax", "response": "Form 16 is not mentioned in the provided CPMS documentation. The text discusses Forms 7, 8, 12, and 14 for retirees' pension processing. If you need assistance with a specific form or have further questions, please provide more context or refer to the official CPMS user guide.", "response_time": 14.072, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": -0.152, "content_preview": " All users’ can view the list of all the retirees and their generated forms.  Printout of Form7 an..."}, {"source": "Unknown", "relevance_score": -0.156, "content_preview": " DH to verify and fill the required information in Form 14. DH may select the claimant who is eligi..."}], "status": "success"}, {"question": "How to lodge a grievance?", "category": "Support", "response": "To lodge a grievance, pensioners can use the online single window system provided by CPMS. They can also call the national toll-free helpline 1800-113-5800 or email their grievances/<NAME_EMAIL>. Once lodged, pensioners can check the status of their grievance online.", "response_time": 0.001, "cached": true, "cache_type": "Persistent", "sources": [{"source": "Web - Retiree Corner", "relevance_score": -0.15, "content_preview": "\n\nList:\n1. Direct disbursement of pension on timely basis without intermediaries\n2. Single window sy..."}, {"source": "Web - Retiree Corner", "relevance_score": -0.15, "content_preview": "\n\nList:\n1. Direct disbursement of pension on timely basis without intermediaries\n2. Single window sy..."}], "status": "success"}, {"question": "What is the CPMS helpline number?", "category": "Support", "response": "**CPMS Helpline and Contact Information:**\n\n**🚨 Toll-Free Helpline:**\n• **1800-113-5800**\n• Available 24/7 for urgent queries\n• Multi-language support available\n\n**📧 Email Support:**\n• **General Queries:** <EMAIL>\n• **Technical Support:** <EMAIL>\n• **Administrative Help:** <EMAIL>\n\n**🌐 Online Portals:**\n• **CPMS Portal:** www.dotpension.gov.in\n• **Pensioners' Portal:** pensionersportal.gov.in\n• **DoT Official:** dot.gov.in\n\n**📱 Digital Services:**\n• **<PERSON><PERSON><PERSON>:** jeevanpramaan.gov.in (Digital Life Certificate)\n\n**When to Contact:**\n• Login issues or password problems\n• Pension payment delays or discrepancies\n• Bank account update requirements\n• Grievance registration and tracking\n• Technical issues with the portal\n• General CPMS procedure queries\n\n**Response Time:**\n• Helpline: Immediate assistance\n• Email: 24-48 hours for response\n• Technical issues: Priority handling", "response_time": 0.001, "cached": true, "cache_type": "FAQ", "sources": [{"source": "CPMS-UserManual", "relevance_score": 0.96}, {"source": "Contact-Information", "relevance_score": 0.93}], "status": "success"}, {"question": "How to contact CPMS support?", "category": "Support", "response": "To login to the Comprehensive Pension Management System (CPMS), follow these steps: 1. Go to www.dotpension.gov.in in your web browser, preferably Google Chrome for CPMS or Internet Explorer Version 9 or 11 for DSC. 2. Enter your User Name. 3. Enter your Password. 4. Enter the Captcha code. 5. Click on the Login button.\n\nFor any support-related queries, you can visit the FAQ section on the CPMS website (<https://dotpension.gov.in/faq>) or contact the CPMS Helpdesk at [<EMAIL>](mailto:<EMAIL>) or call 1800-267-8532 (Toll Free).", "response_time": 0.002, "cached": true, "cache_type": "Persistent", "sources": [{"source": "Unknown", "relevance_score": 0.206, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": 0.051, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in FAQ Index Scraped: 2025..."}], "status": "success"}, {"question": "What is the CPMS email address?", "category": "Support", "response": "The CPMS documentation does not provide the email address for the Comprehensive Pension Management System. However, users can access the system by entering the URL www.dotpension.gov.in in their web browser and following the login steps: entering User Name, Password, CAPTCHA, and clicking on the Login button. For more information about CPMS, please refer to the FAQ section on the website.", "response_time": 16.545, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.036, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": -0.024, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in FAQ Index Scraped: 2025..."}], "status": "success"}, {"question": "How to raise a complaint in CPMS?", "category": "Support", "response": "To raise a complaint in CPMS, you need to follow these steps:\n1. Log in to the CPMS application by entering www.dotpension.gov.in in your web browser and using your credentials. The preferred browsers are Google Chrome for CPMS and Internet Explorer Version 9 or above for DSC.\n2. Once logged in, navigate to the 'Grievances' section usually found under the 'Services' tab.\n3. Click on 'New Grievance' and fill out the required fields with accurate information. Be sure to include all necessary details such as your pensioner ID, type of grievance, and any supporting documents.\n4. After filling out the form, click 'Submit' to register your complaint.\n5. Once submitted, you will receive a confirmation message and reference number for your grievance. Keep this information handy for future correspondence.\n6. CPMS aims to provide faster and more responsive online grievance registration and redressal systems, so you can expect a timely response from the concerned authorities.", "response_time": 25.988, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": -0.19, "content_preview": "SAMPANN(System for Accounting and Management of Pension) is brand Name for Comprehensive Pension Man..."}, {"source": "Unknown", "relevance_score": -0.232, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}], "status": "success"}, {"question": "Where to get technical help for CPMS?", "category": "Support", "response": "For technical support or assistance, users can contact the CPMS <NAME_EMAIL> or call at 011-******** and ******** during working hours (Monday to Friday, 10:00 AM to 4:30 PM IST). Alternatively, you can visit the CPMS FAQs page on the website for common queries. Remember to use Google Chrome as your preferred browser for accessing the CPMS application.", "response_time": 16.964, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.137, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": 0.041, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "status": "success"}, {"question": "How to report CPMS issues?", "category": "Support", "response": "If you encounter any issues while logging into or using the Comprehensive Pension Management System (CPMS), please report them to the concerned authorities. You can contact the CPMS helpdesk at [insert contact information] or email them at [<EMAIL>](mailto:<EMAIL>). Make sure to provide detailed information about the issue, including any error messages and steps to reproduce it. The team will work diligently to resolve your concerns as soon as possible.", "response_time": 9.481, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.009, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": -0.054, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "status": "success"}, {"question": "What forms are available in CPMS?", "category": "Forms", "response": "CPMS provides various forms for different pension-related transactions. Some of the commonly used forms include:\n1. Application for Pension Sanction (Form 1)\n2. Application for Revision of Pension (Form 2)\n3. Application for Family Pension (Form 3)\n4. Application for Change of Bank Account (Form 4)\n5. Application for Advance Rent Allowance (Form 5)\n6. Application for Medical Allowance (Form 6)\n7. Application for Death Claim (Form 7)\n8. Application for Disability Pension (Form 8)\n9. Application for Reimbursement of Medical Expenses (Form 9)\n10. Application for Pension Payment Order (PPO) Duplicate Copy (Form 10)\nThese forms can be accessed and filled online through the CPMS portal, making the pension process more convenient and efficient.", "response_time": 23.286, "cached": false, "cache_type": "None", "sources": [{"source": "Web - FAQ", "relevance_score": 0.168, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": 0.168, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "status": "success"}, {"question": "How to download pension forms?", "category": "Forms", "response": "To download pension forms, users can visit the Comprehensive Pension Management System (CPMS) website at https://dotpension.gov.in and navigate to the 'RetireeCorner' section. Here, they will find various forms such as Form 7 and Form 8 for retirees. To download a form, simply click on its name under the 'Download File' column in the table, or locate it from the list provided by the Pensioners Portal under the 'Policies' tab. Once downloaded, users can print and send the completed forms to the concerned CCA office for processing.", "response_time": 20.796, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.391, "content_preview": " All users’ can view the list of all the retirees and their generated forms.  Printout of Form7 an..."}, {"source": "Unknown", "relevance_score": 0.385, "content_preview": " All users’ can view the list of all the retirees and their generated forms.  Printout of Form7 an..."}], "status": "success"}, {"question": "What is Form 4 in CPMS?", "category": "Forms", "response": "Form 4 in CPMS is a document generated within the system for pensioners to update their bank account details. It can be downloaded and printed by the pensioner, and the updated information can then be submitted to the concerned authority for verification and processing.", "response_time": 11.555, "cached": false, "cache_type": "None", "sources": [{"source": "Web - FAQ", "relevance_score": -0.054, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": -0.054, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "status": "success"}, {"question": "What is Form 8 in CPMS?", "category": "Forms", "response": "Form 8 is a document or form used in the Comprehensive Pension Management System (CPMS) for an unspecified purpose, as indicated by the text provided. It mentions that \"Form 7\" should be processed without undergoing Form Verification and that there shall only be one level passing at step 141.8.5. However, no further details about this form or its function are given in the text. For accurate information regarding the specifics of Form 8 in CPMS, it is recommended to consult the official documentation or contact the Department of Telecom directly.", "response_time": 17.493, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.002, "content_preview": "“Form 7” for processing by skipping the step for “Form Verification”.  There shall be only one leve..."}, {"source": "Web - FAQ", "relevance_score": 0.001, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "status": "success"}, {"question": "What is Form 10 in CPMS?", "category": "Forms", "response": "Form 10 in CPMS refers to the Application for Pension Sanction under CCS (Pension) Rules, 1972. It is filled and submitted by a retiring employee or their nominee/family pensioner to seek approval of pension after retirement. The form includes details about the applicant's personal information, service particulars, and pension entitlements. Once approved, the pension amount will be directly credited into the pensioner's bank account through CPMS.", "response_time": 15.599, "cached": false, "cache_type": "None", "sources": [{"source": "Web - FAQ", "relevance_score": 0.011, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": 0.011, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "status": "success"}, {"question": "How to fill pension application form?", "category": "Forms", "response": "Before submitting a pension application form, the Disbursing House (DH) is responsible for filling up the form with accurate details based on documents submitted by the pensioner. The DH should ensure all information is correctly entered before submission. If errors are detected during the form received stage, the case may be returned for refilling. The retiree will not be required to fill the forms themselves. To fill out the form, go to \"Form Received\" in your CPMS system and select the \"Edit\" button (Pencil Icon) under the Action column for the record with status \"Cases pending at the Pensioner level\". Enter personal details such as title, first name, middle name, last name, type of retirement, height, father/husband's name, mother's name, date of birth, gender, Aadhaar Number, PAN Number, FP Mobile Number, FP Email ID, and FP Identification Marks 1 & 2. Additionally, fill in mandatory fields under the \"Other Details\" section like designation at the time of death, group, office, etc.", "response_time": 27.58, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.378, "content_preview": " Prior to Form received stage, DH shall be required to fill up the forms as per documents submitted..."}, {"source": "Unknown", "relevance_score": 0.343, "content_preview": " Following information need to be entered on personal detail page: a. Title b. First Name c. Middle..."}], "status": "success"}, {"question": "What documents to upload in CPMS?", "category": "Forms", "response": "CPMS does not mention any specific documents to be uploaded in their FAQ section. However, during the registration process or pension sanction, you might be asked to provide certain documents such as identity proof, address proof, and retirement documents. It is advisable to keep these documents handy for a smooth registration and processing of your pension application. For detailed information, please refer to the official CPMS user guide or contact the DOT Pension Helpdesk at [<EMAIL>](mailto:<EMAIL>).", "response_time": 17.07, "cached": false, "cache_type": "None", "sources": [{"source": "Web - FAQ", "relevance_score": -0.123, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": -0.123, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "status": "success"}, {"question": "CPMS website not working", "category": "Technical", "response": "To access the CPMS application, please follow these steps:\n1. Enter www.dotpension.gov.in in your web browser.\n2. Enter your User Name and Password.\n3. Enter the Captcha code.\n4. Click on the Login button.\n\nIf you're having trouble accessing the website, try using Google Chrome as it is the preferred web browser for CPMS. Additionally, ensure that your internet connection is stable and strong enough to load the website properly. If the issue persists, please contact the CPMS helpdesk for further assistance.", "response_time": 19.311, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.182, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": -0.194, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in FAQ Index Scraped: 2025..."}], "status": "success"}, {"question": "Unable to login to CPMS", "category": "Technical", "response": "To login to CPMS, follow these steps:\n1. Enter the URL www.dotpension.gov.in in your web browser. Use Google Chrome for CPMS and Internet Explorer Version 9 or 11 for DSC.\n2. Enter your User Name.\n3. Enter your Password.\n4. Enter the CAPTCHA code correctly.\n5. Click on the \"Login\" button.\n\nIf you're having trouble logging in, make sure you're using the correct URL and browser as specified. Also ensure that you have entered the correct User Name and Password. If you continue to experience issues, try resetting your password or contacting CPMS support for assistance.\n\nAdditionally, if you are an Administrator looking to create a new CCA user, follow these steps:\n1. Log in as the Administrator.\n2. Go to \"Users\" and then \"User Registration\".\n3. Click on \"ADD NEW\" and fill out the required fields to create a new user account for the CCA.\n4. After creating the user account, go to \"Users\" and then \"Role Master\" to assign roles and rights to the newly created", "response_time": 74.12, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.456, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Unknown", "relevance_score": -0.059, "content_preview": "Upon successful logging into CPMS, user will land on the home screen. For consistency, the panel on ..."}], "status": "success"}, {"question": "CPMS portal loading issues", "category": "Technical", "response": "To access the CPMS portal, please follow these steps:\n1. Enter URL www.dotpension.gov.in in your web browser. The recommended browsers for CPMS are Google Chrome and for DSC, Internet Explorer Version 9 or 11.\n2. Enter your User Name.\n3. Enter your Password.\n4. Enter the Captcha code.\n5. Click on the Login button.\nIf you encounter any issues with loading the portal, try clearing your browser's cache and cookies, or consider using a different web browser. Additionally, ensure that your internet connection is stable and strong enough to load the portal effectively. If the problem persists, please contact the CPMS helpdesk for further assistance.", "response_time": 59.764, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": -0.007, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": -0.221, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in FAQ Index Scraped: 2025..."}], "status": "success"}, {"question": "How to clear browser cache for CPMS?", "category": "Technical", "response": "To clear browser cache for CPMS, follow these steps depending on the web browser you're using:\n\nFor Google Chrome:\n1. Click on the three dots in the upper right corner of the browser window.\n2. Select \"More tools\" and then click \"Clear browsing data.\"\n3. In the \"Time range\" dropdown menu, select \"All time.\"\n4. Check the boxes for \"Cookies and other site data,\" \"Cached images and files,\" and \"Plugins.\"\n5. Click \"Clear data.\"\n\nFor Internet Explorer:\n1. Click on the gear icon in the upper right corner of the browser window and select \"Internet options.\"\n2. Under the \"General\" tab, click on the \"Delete...\" button under the \"Browsing history\" section.\n3. In the \"Delete temporary files\" dialog box, check the boxes for \"Temporary Internet files,\" \"Cookies,\" and \"History.\"\n4. Click \"Delete files.\"\n\nFor Microsoft Edge:\n1. Click on the three dots in the upper right corner of the browser window and select \"Settings.\"\n2. Scroll down to \"Services\" and", "response_time": 50.249, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": -0.153, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Unknown", "relevance_score": -0.297, "content_preview": "Upon successful logging into CPMS, user will land on the home screen. For consistency, the panel on ..."}], "status": "success"}, {"question": "CPMS mobile app not working", "category": "Technical", "response": "To access CPMS, please follow the steps below using a web browser:\n1. Enter URL www.dotpension.gov.in in your web browser.\n2. Enter your User Name.\n3. Enter your Password.\n4. Enter the Captcha code.\n5. Click on Login button.\n\nFor the best experience, use Google Chrome as the preferred browser for CPMS and Internet Explorer Version 9 or 11 for DSC. If you encounter any issues, kindly contact the helpdesk at [helpdesk email] or call [helpdesk phone number].", "response_time": 42.367, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.072, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": -0.193, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "status": "success"}, {"question": "Browser compatibility for CPMS", "category": "Technical", "response": "The preferred web browsers for accessing the CPMS application are Google Chrome and Internet Explorer Version 9 or 11. Users should enter www.dotpension.gov.in in their browser to reach the login screen, where they will then input their User Name, Password, and Captcha before clicking on the Login button.", "response_time": 39.111, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.042, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}, {"source": "Web - FAQ", "relevance_score": -0.201, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in FAQ Index Scraped: 2025..."}], "status": "success"}, {"question": "How to create AO login in CPMS?", "category": "Procedures", "response": "To create an A<PERSON> (Accounts Officer) login in CPMS:\n\n**Prerequisites:**\n• Only CCA can create AO login\n• CCA must be logged into CPMS\n\n**Step-by-Step Process:**\n1. **Login as CCA**\n   • Access CPMS portal with CCA credentials\n\n2. **Navigate to User Registration**\n   • Go to Users → User Registration\n   • Select the Role Type as \"AO\"\n\n3. **Fill User Details**\n   • Enter AO's personal information\n   • Fill all mandatory fields\n   • Save the user details\n\n4. **Assign Authorization**\n   • Click on Lock icon in the authorization column\n   • Assign module authorization to AO\n   • Set appropriate rights and permissions\n\n5. **Complete Setup**\n   • Multiple AO users can be created\n   • AO will be subordinate to CCA in Pension Section\n\n**Important Notes:**\n• AO is second level login under CCA\n• AO can create AAO logins under their supervision\n• Proper authorization is essential for system access\n\n**For Assistance:**\n• Helpline: 1800-113-5800\n• Email: <EMAIL>", "response_time": 0.002, "cached": true, "cache_type": "FAQ", "sources": [{"source": "CPMS User Manual - Section 1.8.1", "relevance_score": 0.96}], "status": "success"}, {"question": "How to create DH pension in CPMS?", "category": "Procedures", "response": "To create a DH pension in CPMS, follow these steps:\n1. Log into the CPMS portal at www.dotpension.gov.in using your user ID and password.\n2. Navigate to the \"Pension Sanction\" tab and click on \"New Pension Sanction.\"\n3. Fill out all required fields, including pensioner details, nominee information, and retirement details.\n4. Attach all necessary documents as per the checklist.\n5. Review and submit the form to the Pension Section (CCA Office) by clicking on \"Send to PAO.\" Ensure that you send all required documents with the form. The CCA may then dispatch the papers by post.", "response_time": 33.253, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.437, "content_preview": " After all the aforementioned steps, DH will submit the form to Pension Section (CCA Office) by cli..."}, {"source": "Unknown", "relevance_score": 0.285, "content_preview": " After all the aforementioned steps, DH will submit the form to Pension Section by clicking on “Sen..."}], "status": "success"}, {"question": "What is the pension approval process?", "category": "Procedures", "response": "The Comprehensive Pension Management System (CPMS) is designed to streamline the pension process with a single window system. It enables direct disbursement of pensions on a timely basis without intermediaries, online grievance management for pensioners, and tracking of pension status from home. The system covers 3.5 lakh current pensioners, family pensioners, and 1.5 lakh future retirees. For technical help, contact the support email or call 1800-113-5800, while for administrative and training queries, email <EMAIL>. The faster processing of arrears and pension revisions also ensures efficient pension approval.", "response_time": 22.015, "cached": false, "cache_type": "None", "sources": [{"source": "Web - About Us", "relevance_score": 0.265, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home AboutUs Scraped: 2..."}, {"source": "Web - About Us", "relevance_score": 0.265, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home AboutUs Scraped: 2..."}], "status": "success"}, {"question": "How to process pension papers?", "category": "Procedures", "response": "To process pension papers, the DH (Disbursing Officer) should first ensure all required documents are submitted by the pensioner. After filling out the forms correctly and checking for errors, the DH will submit them to the Pension Section by clicking \"Send to PAO\". The papers may then be dispatched by post. It's important to note that all necessary documents must be included when sending the form to avoid delays in processing. If errors are detected at the form received stage, the case may be returned for refilling. The pensioner will not be required to fill the forms; this activity is completed by the DH before submission. In some cases, where final papers have not been received, the case shall not be processed beyond the AE (Application Entry) in Pension Section Module.", "response_time": 47.582, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.315, "content_preview": " After all the aforementioned steps, DH will submit the form to Pension Section by clicking on “Sen..."}, {"source": "Unknown", "relevance_score": 0.266, "content_preview": "Processing a Case) This chapter deals with flow of pension papers in the CCA office when the case is..."}], "status": "success"}, {"question": "What is the role of CCA in CPMS?", "category": "Procedures", "response": "The CCA, or Centralized Pension Processing Authority, is a user role in the CPMS system. The System Administrator creates and assigns this role to an officer in charge of a pension processing office (e.g., BSNL, DOT units). The CCA has the highest authority for login and managing pension-related tasks within their office using the CPMS platform. To create a new CCA user account, the Administrator logs into the system, navigates to the Users tab, and registers a new user with the appropriate details. After registration, the Administrator assigns the CCA role and rights to this user through the Role Master section.", "response_time": 44.166, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.337, "content_preview": "The CCA roles will be created by the System Administrator beforehand. It may be noted that the entir..."}, {"source": "Unknown", "relevance_score": 0.23, "content_preview": "a) Administrator User will create CCA user name/password for login. b) CCA or any officer nominated ..."}], "status": "success"}, {"question": "How to submit pension documents?", "category": "Procedures", "response": "To submit pension documents, the Designated Official (DH) should fill out the forms accurately based on the submitted pensioner's documents. After completing the form, DH clicks \"Send to PAO\" to submit it to the Pension Section for further processing. The documents must include all those mentioned in the checklist before sending. If any errors are detected during the 'Form Received' stage, the case might be returned for refilling. DH is responsible for filling out the forms and should do so carefully on the \"Form Received\" page under \"Cases pending at the Pensioner level\". Select the \"Edit\" button to fill in the pensioner's details. After completing the form, choose the option \"Cases submitted without Physical Copy\" from the Status dropdown list and click 'Submit'.", "response_time": 42.595, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.399, "content_preview": " After all the aforementioned steps, DH will submit the form to Pension Section by clicking on “Sen..."}, {"source": "Unknown", "relevance_score": 0.375, "content_preview": " Prior to Form received stage, DH shall be required to fill up the forms as per documents submitted..."}], "status": "success"}, {"question": "How to check application status?", "category": "Status", "response": "Retirees can check the status of their application on the CPMS dashboard. Milestones completed will be indicated by green color dots, and pending ones will be marked with red dots. Administrators can also view the list of all users and their application status from the User Registration section. Retirees can access their pension details by clicking on the pension ledger. For any grievances or queries, they can use the dashboard as well.", "response_time": 18.494, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": -0.366, "content_preview": " Retiring officer/official can see the progress of his/her application at the top of the dashboard ..."}, {"source": "Unknown", "relevance_score": -0.388, "content_preview": " Administrator User will click on Submit button. 16 1.1.5 1.1.5 (a) User Registration: It shows the..."}], "status": "success"}, {"question": "Where to see pension payment history?", "category": "Status", "response": "To view the pension payment history, retirees can log in to their account on the Comprehensive Pension Management System (CPMS) dashboard and check the pension statements available there.", "response_time": 11.962, "cached": false, "cache_type": "None", "sources": [{"source": "Web - Retiree Corner", "relevance_score": 0.198, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home RetireeCorner Scra..."}, {"source": "Web - Retiree Corner", "relevance_score": 0.198, "content_preview": "Title: Comprehensive Pension Management System URL: https: dotpension.gov.in Home RetireeCorner Scra..."}], "status": "success"}, {"question": "How to track document verification?", "category": "Status", "response": "To track document verification in the CPMS system, follow these steps:\n\n1. The Data Handling (DH) team checks the received form and attached documents for accuracy and quality. Once verified, they send it to the Head of Office (HoO) for approval.\n2. If HoO approves the form, it will appear in the 'Form Verification' tab of the DH User interface. However, if HoO returns the form due to any discrepancies, it goes back to the DH for re-verification.\n3. For service verification, DH selects the 'Service Verification' tab from the Menu options and sends the form for approval to HoO after physically verifying the service book. Any unverified portions should be noted and attached in the file being sent to the Pension Section (CCA Office).\n4. HoO logs in, approves the service book verification, and also physically verifies it. If they find any discrepancies, they can return the form to DH for re-verification. The process continues until all discrepancies are resolved.\n5. Once all verifications are completed successfully, the retiree's profile is updated with the", "response_time": 29.996, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": -0.023, "content_preview": " DH to check that the form received is correctly filled and that all scanned documents uploaded are..."}, {"source": "Unknown", "relevance_score": -0.158, "content_preview": " DH to select ‘Service Verification’ tab from the Menu options.  DH to check the form and send it ..."}], "status": "success"}, {"question": "What are the different status types in CPMS?", "category": "Status", "response": "In Comprehensive Pension Management System (CPMS), the pensioner can track the status of their pension under various stages such as:\n1. Pension Sanctioned\n2. Pension Disbursed\n3. Pending for Disbursement\n4. Rejected\n5. Closed\n6. Under Process\n7. Partially Disbursed\nEach status indicates the current stage of pension processing and disbursement in CPMS.", "response_time": 15.041, "cached": false, "cache_type": "None", "sources": [{"source": "Web - FAQ", "relevance_score": 0.174, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}, {"source": "Web - FAQ", "relevance_score": 0.174, "content_preview": "\n\nTable Data:\n1) | What is CPMS?\n\n\nAns : \nComprehensive Pension Management System (CPMS) is an end-t..."}], "status": "success"}, {"question": "How to get status updates?", "category": "Status", "response": "Retirees can view the status of their pension application, including completed milestones in green and pending ones in red, on the top of their dashboard in the CPMS Retiree Corner. They can also check the status of any lodged grievances online. For further assistance, they may call the national helpline at 1800-113-5800 <NAME_EMAIL>. Additionally, pensioners can access their pension sanctions, pension payments made till now, and update their contact information on the dashboard.", "response_time": 18.745, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": -0.375, "content_preview": " Retiring officer/official can see the progress of his/her application at the top of the dashboard ..."}, {"source": "Web - Retiree Corner", "relevance_score": -0.386, "content_preview": "\n\nList:\n1. Direct disbursement of pension on timely basis without intermediaries\n2. Single window sy..."}], "status": "success"}, {"question": "How to update personal details?", "category": "Updates", "response": "To update personal details such as email address or mobile number on the CPMS platform, first log in using your PAN number. After logging in, click on your profile picture and select \"Edit Profile.\" In the pop-up window that appears, choose the detail you'd like to modify (email address or mobile number). For email address, enter your new address, generate an OTP, and input it to confirm the change. For mobile number, enter your new number and follow the same process by generating and entering the OTP received on your registered number.", "response_time": 17.98, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.21, "content_preview": "For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefit..."}, {"source": "Unknown", "relevance_score": -0.045, "content_preview": "Upon selecting ‘Email ID’, the following screen will be displayed (5.14)) (5.14) Reti<PERSON> can enter h..."}], "status": "success"}, {"question": "How to change address in CPMS?", "category": "Updates", "response": "To change your address in CPMS, you need to register a grievance related to the updation of your address on the pensioner grievance page. Once the grievance is registered, it will be assigned to the respective Disbursing House (DH) who will then update the address. Remember to upload proof of the new address during this process. To log in to CPMS, follow these steps: enter the URL www.dotpension.gov.in in your web browser and use your PAN number as the username. After logging in, click on the profile picture and select \"Edit Profile.\" A pop-up window will appear with options to edit various details, including your address.", "response_time": 19.902, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": -0.035, "content_preview": "To update address, pensioner would be taken to the pensioner grievance page where a grievance relate..."}, {"source": "Unknown", "relevance_score": -0.054, "content_preview": "Users have to take following steps in order to login in to CPMS application. 1. Enter URL www.dotpen..."}], "status": "success"}, {"question": "How to update mobile number?", "category": "Updates", "response": "To update your mobile number, log in to the pensioner portal using your PAN number as the username. Once logged in, click on your profile picture and select \"Edit Profile.\" In the pop-up window, choose the \"Mobile Number\" option and enter your new number. Then, select either your registered email address or the entered mobile number to receive an OTP for verification. After receiving the OTP, enter it on the screen and save the changes to update your mobile number.", "response_time": 16.526, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": 0.402, "content_preview": "Upon selecting ‘Mobile Number’, the following screen will be displayed (14.3)) 206 Fig(14.3) Retiree..."}, {"source": "Unknown", "relevance_score": 0.384, "content_preview": "Upon selecting ‘Mobile Number’, the following screen will be displayed (5.12)) Fig(5.12) Retiree can..."}], "status": "success"}, {"question": "How to change email address?", "category": "Updates", "response": "To change your email address, log in to your account using your PAN number as the username. Once logged in, click on your profile picture and select \"Edit Profile\". In the pop-up window that appears, choose the option to update your email ID. Enter your new email address, generate an OTP which will be sent to your registered mobile number, enter the received OTP, and then click on \"save\" to complete the process.", "response_time": 16.01, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": -0.137, "content_preview": "Upon selecting ‘Email ID’, the following screen will be displayed (14.5)) 207 (14.5) Retiree can ent..."}, {"source": "Unknown", "relevance_score": -0.138, "content_preview": "Upon selecting ‘Email ID’, the following screen will be displayed (5.14)) (5.14) Reti<PERSON> can enter h..."}], "status": "success"}, {"question": "How to update nominee details?", "category": "Updates", "response": "To update nominee details before retirement, the pensioner should contact their respective DH (Disbursing House) and provide them with the updated information. The DH will then fill out and submit the necessary forms accordingly. After retirement, the pensioner can log in using their PAN number, click on their profile picture, and select \"Edit Profile\" to update their nominee details by following the steps outlined for updating other personal information (selecting the nominee option and making the desired changes). Note: If the pensioner is unable to login or needs assistance with updating their details after retirement, they should contact their DH directly.", "response_time": 18.962, "cached": false, "cache_type": "None", "sources": [{"source": "Unknown", "relevance_score": -0.174, "content_preview": "For updating mobile number, Email or Address of a pensioner after finalisation of retirement benefit..."}, {"source": "Unknown", "relevance_score": -0.25, "content_preview": "To update address, pensioner would be taken to the pensioner grievance page where a grievance relate..."}], "status": "success"}]}