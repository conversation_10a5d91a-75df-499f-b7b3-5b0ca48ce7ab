{"instruction": "How is pension sanctioned in CPMS?", "input": "", "output": "For pension sanctioning in CPMS:\n\n   The CCA roles will be created by the System Administrator beforehand. It may be noted that the entire work under CPMS has been segmented into four broad parts as follows: 1. System Administrator. 2. Collection and submission of Pension papers. The HoO and DH terms are used for CCA offices/BSNL offices/Other DOT units for Head of that particular office and Dealing hand. It is recommended <PERSON><PERSON> should be any officer of the rank of AAO or AO and <PERSON><PERSON> should be officer of the rank below AAO (Sr. Acct./Jr. Acct.). 3. Processing and sanction of Pension. 4. Disbursement of pensionary benefits and monthly pension.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What is the pension sanction process?", "input": "", "output": "For pension sanctioning in CPMS:\n\n   The CCA roles will be created by the System Administrator beforehand. It may be noted that the entire work under CPMS has been segmented into four broad parts as follows: 1. System Administrator. 2. Collection and submission of Pension papers. The HoO and DH terms are used for CCA offices/BSNL offices/Other DOT units for Head of that particular office and Dealing hand. It is recommended HoO should be any officer of the rank of AAO or AO and DH should be officer of the rank below AAO (Sr. Acct./Jr. Acct.). 3. Processing and sanction of Pension. 4. Disbursement of pensionary benefits and monthly pension.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to approve pension calculation?", "input": "", "output": "For pension sanctioning in CPMS:\n\n   The CCA roles will be created by the System Administrator beforehand. It may be noted that the entire work under CPMS has been segmented into four broad parts as follows: 1. System Administrator. 2. Collection and submission of Pension papers. The HoO and DH terms are used for CCA offices/BSNL offices/Other DOT units for Head of that particular office and Dealing hand. It is recommended HoO should be any officer of the rank of AAO or AO and DH should be officer of the rank below AAO (Sr. Acct./Jr. Acct.). 3. Processing and sanction of Pension. 4. Disbursement of pensionary benefits and monthly pension.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What are the steps in pension sanctioning?", "input": "", "output": "For pension sanctioning in CPMS:\n\n   The CCA roles will be created by the System Administrator beforehand. It may be noted that the entire work under CPMS has been segmented into four broad parts as follows: 1. System Administrator. 2. Collection and submission of Pension papers. The HoO and DH terms are used for CCA offices/BSNL offices/Other DOT units for Head of that particular office and Dealing hand. It is recommended HoO should be any officer of the rank of AAO or AO and DH should be officer of the rank below AAO (Sr. Acct./Jr. Acct.). 3. Processing and sanction of Pension. 4. Disbursement of pensionary benefits and monthly pension.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to digitally sign pension orders?", "input": "", "output": "For pension sanctioning in CPMS:\n\n   The CCA roles will be created by the System Administrator beforehand. It may be noted that the entire work under CPMS has been segmented into four broad parts as follows: 1. System Administrator. 2. Collection and submission of Pension papers. The HoO and DH terms are used for CCA offices/BSNL offices/Other DOT units for Head of that particular office and Dealing hand. It is recommended HoO should be any officer of the rank of AAO or AO and DH should be officer of the rank below AAO (Sr. Acct./Jr. Acct.). 3. Processing and sanction of Pension. 4. Disbursement of pensionary benefits and monthly pension.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How is pension sanctioned in CPMS?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The pension case coming from HoO will appear in ‘Allotment’ tab of the Accounts Officer (AO) of the Pension Sanctioning Section. AO pension sanctioning section will allot the case to the Dealing Hand of the section. (3.1)  The current practice based on which cases are allotted to DH by AOs may be continued. This will be done by the AO who has been authorized to allot the case (It is to be noted that only 1 AO is authorised for allotment). 3.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Pension Case to DH", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What is the pension sanction process?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The pension case coming from HoO will appear in ‘Allotment’ tab of the Accounts Officer (AO) of the Pension Sanctioning Section. AO pension sanctioning section will allot the case to the Dealing Hand of the section. (3.1)  The current practice based on which cases are allotted to DH by AOs may be continued. This will be done by the AO who has been authorized to allot the case (It is to be noted that only 1 AO is authorised for allotment). 3.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Pension Case to DH", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to approve pension calculation?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The pension case coming from HoO will appear in ‘Allotment’ tab of the Accounts Officer (AO) of the Pension Sanctioning Section. AO pension sanctioning section will allot the case to the Dealing Hand of the section. (3.1)  The current practice based on which cases are allotted to DH by AOs may be continued. This will be done by the AO who has been authorized to allot the case (It is to be noted that only 1 AO is authorised for allotment). 3.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Pension Case to DH", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What are the steps in pension sanctioning?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The pension case coming from HoO will appear in ‘Allotment’ tab of the Accounts Officer (AO) of the Pension Sanctioning Section. AO pension sanctioning section will allot the case to the Dealing Hand of the section. (3.1)  The current practice based on which cases are allotted to DH by AOs may be continued. This will be done by the AO who has been authorized to allot the case (It is to be noted that only 1 AO is authorised for allotment). 3.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Pension Case to DH", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to digitally sign pension orders?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The pension case coming from HoO will appear in ‘Allotment’ tab of the Accounts Officer (AO) of the Pension Sanctioning Section. AO pension sanctioning section will allot the case to the Dealing Hand of the section. (3.1)  The current practice based on which cases are allotted to DH by AOs may be continued. This will be done by the AO who has been authorized to allot the case (It is to be noted that only 1 AO is authorised for allotment). 3.1\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Pension Case to DH", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How is pension sanctioned in CPMS?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The case will now flow into the ‘Pay Regulation’ tab of DH (Pension Sanctioning Section) level (Action->Pension Section->Pay Regulation).  DH (Pension Sanctioning Section) will select the case and click on Pay Regulation Sheet (3.4).  DH (Pension Sanctioning Section) will then add Pay Scale Data for the particular pension case and save the same. 3.4 62 3.5 (a) 3.5 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pay Regulation", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What is the pension sanction process?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The case will now flow into the ‘Pay Regulation’ tab of DH (Pension Sanctioning Section) level (Action->Pension Section->Pay Regulation).  DH (Pension Sanctioning Section) will select the case and click on Pay Regulation Sheet (3.4).  DH (Pension Sanctioning Section) will then add Pay Scale Data for the particular pension case and save the same. 3.4 62 3.5 (a) 3.5 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pay Regulation", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What is the pension sanction process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    The case will now flow into the ‘Pay Regulation’ tab of DH (Pension Sanctioning Section) level (Action->Pension Section->Pay Regulation).  DH (Pension Sanctioning Section) will select the case and click on Pay Regulation Sheet (3.4).  DH (Pension Sanctioning Section) will then add Pay Scale Data for the particular pension case and save the same. 3.4 62 3.5 (a) 3.5 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Pay Regulation", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How to approve pension calculation?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The case will now flow into the ‘Pay Regulation’ tab of DH (Pension Sanctioning Section) level (Action->Pension Section->Pay Regulation).  DH (Pension Sanctioning Section) will select the case and click on Pay Regulation Sheet (3.4).  DH (Pension Sanctioning Section) will then add Pay Scale Data for the particular pension case and save the same. 3.4 62 3.5 (a) 3.5 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pay Regulation", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to approve pension calculation?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    The case will now flow into the ‘Pay Regulation’ tab of DH (Pension Sanctioning Section) level (Action->Pension Section->Pay Regulation).  DH (Pension Sanctioning Section) will select the case and click on Pay Regulation Sheet (3.4).  DH (Pension Sanctioning Section) will then add Pay Scale Data for the particular pension case and save the same. 3.4 62 3.5 (a) 3.5 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Pay Regulation", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "What are the steps in pension sanctioning?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The case will now flow into the ‘Pay Regulation’ tab of DH (Pension Sanctioning Section) level (Action->Pension Section->Pay Regulation).  DH (Pension Sanctioning Section) will select the case and click on Pay Regulation Sheet (3.4).  DH (Pension Sanctioning Section) will then add Pay Scale Data for the particular pension case and save the same. 3.4 62 3.5 (a) 3.5 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pay Regulation", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What are the steps in pension sanctioning?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    The case will now flow into the ‘Pay Regulation’ tab of DH (Pension Sanctioning Section) level (Action->Pension Section->Pay Regulation).  DH (Pension Sanctioning Section) will select the case and click on Pay Regulation Sheet (3.4).  DH (Pension Sanctioning Section) will then add Pay Scale Data for the particular pension case and save the same. 3.4 62 3.5 (a) 3.5 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Pay Regulation", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How to digitally sign pension orders?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The case will now flow into the ‘Pay Regulation’ tab of DH (Pension Sanctioning Section) level (Action->Pension Section->Pay Regulation).  DH (Pension Sanctioning Section) will select the case and click on Pay Regulation Sheet (3.4).  DH (Pension Sanctioning Section) will then add Pay Scale Data for the particular pension case and save the same. 3.4 62 3.5 (a) 3.5 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Pay Regulation", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to digitally sign pension orders?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    The case will now flow into the ‘Pay Regulation’ tab of DH (Pension Sanctioning Section) level (Action->Pension Section->Pay Regulation).  DH (Pension Sanctioning Section) will select the case and click on Pay Regulation Sheet (3.4).  DH (Pension Sanctioning Section) will then add Pay Scale Data for the particular pension case and save the same. 3.4 62 3.5 (a) 3.5 (b)\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Pay Regulation", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How is pension sanctioned in CPMS?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The pension case coming from Pension Sanctioning Section will appear in the ‘Allotment’ tab of the AO PDA Section. (AO PDA Login Allotment Allocate to PDA- DH)  AO PDA will allot the case to the Dealing Hand of the PDA section. .4.1) Fig.4.1  Now Case will flow to DH PDA Section. (DH PDA Login Action PDA Section Sanction Order Received).  DH should take the printouts of digitally signed EPPO(2 copies) Commutation Sanction, Gratuity Sanction, PPO Authority Model in 5 sets from the links available on the page, as shown in the screenshot below. After AO PDA approves and sends the case to PFMS for payment, the above hardcopies (1 set each) needs to be physically sent to HoO section, Pension section, Cash section and Pensioner. One copy will remain with PDA section.  Now the DH (PDA Section) will select the case and click on Receive Sanction & Send to Bill button. At this stage the name of the employee will appear in the name column of the table as shown below. 75 Fig.4.2\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Pension Case", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What is the pension sanction process?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The pension case coming from Pension Sanctioning Section will appear in the ‘Allotment’ tab of the AO PDA Section. (AO PDA Login Allotment Allocate to PDA- DH)  AO PDA will allot the case to the Dealing Hand of the PDA section. .4.1) Fig.4.1  Now Case will flow to DH PDA Section. (DH PDA Login Action PDA Section Sanction Order Received).  DH should take the printouts of digitally signed EPPO(2 copies) Commutation Sanction, Gratuity Sanction, PPO Authority Model in 5 sets from the links available on the page, as shown in the screenshot below. After AO PDA approves and sends the case to PFMS for payment, the above hardcopies (1 set each) needs to be physically sent to HoO section, Pension section, Cash section and Pensioner. One copy will remain with PDA section.  Now the DH (PDA Section) will select the case and click on Receive Sanction & Send to Bill button. At this stage the name of the employee will appear in the name column of the table as shown below. 75 Fig.4.2\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Pension Case", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What is the pension sanction process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    The pension case coming from Pension Sanctioning Section will appear in the ‘Allotment’ tab of the AO PDA Section. (AO PDA Login Allotment Allocate to PDA- DH)  AO PDA will allot the case to the Dealing Hand of the PDA section. .4.1) Fig.4.1  Now Case will flow to DH PDA Section. (DH PDA Login Action PDA Section Sanction Order Received).  DH should take the printouts of digitally signed EPPO(2 copies) Commutation Sanction, Gratuity Sanction, PPO Authority Model in 5 sets from the links available on the page, as shown in the screenshot below. After AO PDA approves and sends the case to PFMS for payment, the above hardcopies (1 set each) needs to be physically sent to HoO section, Pension section, Cash section and Pensioner. One copy will remain with PDA section.  Now the DH (PDA Section) will select the case and click on Receive Sanction & Send to Bill button. At this stage the name of the employee will appear in the name column of the table as shown below. 75 Fig.4.2\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Allotment of Pension Case", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How to approve pension calculation?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The pension case coming from Pension Sanctioning Section will appear in the ‘Allotment’ tab of the AO PDA Section. (AO PDA Login Allotment Allocate to PDA- DH)  AO PDA will allot the case to the Dealing Hand of the PDA section. .4.1) Fig.4.1  Now Case will flow to DH PDA Section. (DH PDA Login Action PDA Section Sanction Order Received).  DH should take the printouts of digitally signed EPPO(2 copies) Commutation Sanction, Gratuity Sanction, PPO Authority Model in 5 sets from the links available on the page, as shown in the screenshot below. After AO PDA approves and sends the case to PFMS for payment, the above hardcopies (1 set each) needs to be physically sent to HoO section, Pension section, Cash section and Pensioner. One copy will remain with PDA section.  Now the DH (PDA Section) will select the case and click on Receive Sanction & Send to Bill button. At this stage the name of the employee will appear in the name column of the table as shown below. 75 Fig.4.2\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Pension Case", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to approve pension calculation?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    The pension case coming from Pension Sanctioning Section will appear in the ‘Allotment’ tab of the AO PDA Section. (AO PDA Login Allotment Allocate to PDA- DH)  AO PDA will allot the case to the Dealing Hand of the PDA section. .4.1) Fig.4.1  Now Case will flow to DH PDA Section. (DH PDA Login Action PDA Section Sanction Order Received).  DH should take the printouts of digitally signed EPPO(2 copies) Commutation Sanction, Gratuity Sanction, PPO Authority Model in 5 sets from the links available on the page, as shown in the screenshot below. After AO PDA approves and sends the case to PFMS for payment, the above hardcopies (1 set each) needs to be physically sent to HoO section, Pension section, Cash section and Pensioner. One copy will remain with PDA section.  Now the DH (PDA Section) will select the case and click on Receive Sanction & Send to Bill button. At this stage the name of the employee will appear in the name column of the table as shown below. 75 Fig.4.2\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Allotment of Pension Case", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "What are the steps in pension sanctioning?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The pension case coming from Pension Sanctioning Section will appear in the ‘Allotment’ tab of the AO PDA Section. (AO PDA Login Allotment Allocate to PDA- DH)  AO PDA will allot the case to the Dealing Hand of the PDA section. .4.1) Fig.4.1  Now Case will flow to DH PDA Section. (DH PDA Login Action PDA Section Sanction Order Received).  DH should take the printouts of digitally signed EPPO(2 copies) Commutation Sanction, Gratuity Sanction, PPO Authority Model in 5 sets from the links available on the page, as shown in the screenshot below. After AO PDA approves and sends the case to PFMS for payment, the above hardcopies (1 set each) needs to be physically sent to HoO section, Pension section, Cash section and Pensioner. One copy will remain with PDA section.  Now the DH (PDA Section) will select the case and click on Receive Sanction & Send to Bill button. At this stage the name of the employee will appear in the name column of the table as shown below. 75 Fig.4.2\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Pension Case", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What are the steps in pension sanctioning?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    The pension case coming from Pension Sanctioning Section will appear in the ‘Allotment’ tab of the AO PDA Section. (AO PDA Login Allotment Allocate to PDA- DH)  AO PDA will allot the case to the Dealing Hand of the PDA section. .4.1) Fig.4.1  Now Case will flow to DH PDA Section. (DH PDA Login Action PDA Section Sanction Order Received).  DH should take the printouts of digitally signed EPPO(2 copies) Commutation Sanction, Gratuity Sanction, PPO Authority Model in 5 sets from the links available on the page, as shown in the screenshot below. After AO PDA approves and sends the case to PFMS for payment, the above hardcopies (1 set each) needs to be physically sent to HoO section, Pension section, Cash section and Pensioner. One copy will remain with PDA section.  Now the DH (PDA Section) will select the case and click on Receive Sanction & Send to Bill button. At this stage the name of the employee will appear in the name column of the table as shown below. 75 Fig.4.2\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Allotment of Pension Case", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How to digitally sign pension orders?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    The pension case coming from Pension Sanctioning Section will appear in the ‘Allotment’ tab of the AO PDA Section. (AO PDA Login Allotment Allocate to PDA- DH)  AO PDA will allot the case to the Dealing Hand of the PDA section. .4.1) Fig.4.1  Now Case will flow to DH PDA Section. (DH PDA Login Action PDA Section Sanction Order Received).  DH should take the printouts of digitally signed EPPO(2 copies) Commutation Sanction, Gratuity Sanction, PPO Authority Model in 5 sets from the links available on the page, as shown in the screenshot below. After AO PDA approves and sends the case to PFMS for payment, the above hardcopies (1 set each) needs to be physically sent to HoO section, Pension section, Cash section and Pensioner. One copy will remain with PDA section.  Now the DH (PDA Section) will select the case and click on Receive Sanction & Send to Bill button. At this stage the name of the employee will appear in the name column of the table as shown below. 75 Fig.4.2\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Allotment of Pension Case", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to digitally sign pension orders?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    The pension case coming from Pension Sanctioning Section will appear in the ‘Allotment’ tab of the AO PDA Section. (AO PDA Login Allotment Allocate to PDA- DH)  AO PDA will allot the case to the Dealing Hand of the PDA section. .4.1) Fig.4.1  Now Case will flow to DH PDA Section. (DH PDA Login Action PDA Section Sanction Order Received).  DH should take the printouts of digitally signed EPPO(2 copies) Commutation Sanction, Gratuity Sanction, PPO Authority Model in 5 sets from the links available on the page, as shown in the screenshot below. After AO PDA approves and sends the case to PFMS for payment, the above hardcopies (1 set each) needs to be physically sent to HoO section, Pension section, Cash section and Pensioner. One copy will remain with PDA section.  Now the DH (PDA Section) will select the case and click on Receive Sanction & Send to Bill button. At this stage the name of the employee will appear in the name column of the table as shown below. 75 Fig.4.2\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Allotment of Pension Case", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How is pension sanctioned in CPMS?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    From the DH log-in of PDA Section go to Others Bill Generate (DH PDA LoginBill GenerationOthers Bill Generation). Bill other than monthly bills like Commutation, DCRG, etc. will appear at this Tab. Here the bills are appearing in the name of claimants who will get the payment through PFMS. Now select the case from the check box and click Generate Bill Button. The bill will be generated and it will go to AAO of PDA Section. 78 Fig.4.5  AAO of PDA Section will log-in and select the case by clicking the check box corresponding to the case and click on Approve button (AAO PDA LoginApprovalOthers BillsApprove). The case will go to AO of PDA Section for approval. Fig.4.6  Now, AO of PDA Section will log-in and go to approval then other bills (AO PDA Login Approval Others Bills). The AO will then select the appropriate Account Head Code from the drop down list on the top. After entering the “Not Payable Before Date” click on Approve button. Now the Bill will automatically go to PFMS for payment.  After this, the 5 copies of digitally signed Sanctions (taken earlier by DH) should be sent to the HoO section, Pension Section, Cash Section, Pensioner and 1 copy retained in PDA section (as mentioned on 71, sub heading 4.1 of this manual). 79 Fig.4.7\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What is the pension sanction process?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    From the DH log-in of PDA Section go to Others Bill Generate (DH PDA LoginBill GenerationOthers Bill Generation). Bill other than monthly bills like Commutation, DCRG, etc. will appear at this Tab. Here the bills are appearing in the name of claimants who will get the payment through PFMS. Now select the case from the check box and click Generate Bill Button. The bill will be generated and it will go to AAO of PDA Section. 78 Fig.4.5  AAO of PDA Section will log-in and select the case by clicking the check box corresponding to the case and click on Approve button (AAO PDA LoginApprovalOthers BillsApprove). The case will go to AO of PDA Section for approval. Fig.4.6  Now, AO of PDA Section will log-in and go to approval then other bills (AO PDA Login Approval Others Bills). The AO will then select the appropriate Account Head Code from the drop down list on the top. After entering the “Not Payable Before Date” click on Approve button. Now the Bill will automatically go to PFMS for payment.  After this, the 5 copies of digitally signed Sanctions (taken earlier by DH) should be sent to the HoO section, Pension Section, Cash Section, Pensioner and 1 copy retained in PDA section (as mentioned on 71, sub heading 4.1 of this manual). 79 Fig.4.7\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What is the pension sanction process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    From the DH log-in of PDA Section go to Others Bill Generate (DH PDA LoginBill GenerationOthers Bill Generation). Bill other than monthly bills like Commutation, DCRG, etc. will appear at this Tab. Here the bills are appearing in the name of claimants who will get the payment through PFMS. Now select the case from the check box and click Generate Bill Button. The bill will be generated and it will go to AAO of PDA Section. 78 Fig.4.5  AAO of PDA Section will log-in and select the case by clicking the check box corresponding to the case and click on Approve button (AAO PDA LoginApprovalOthers BillsApprove). The case will go to AO of PDA Section for approval. Fig.4.6  Now, AO of PDA Section will log-in and go to approval then other bills (AO PDA Login Approval Others Bills). The AO will then select the appropriate Account Head Code from the drop down list on the top. After entering the “Not Payable Before Date” click on Approve button. Now the Bill will automatically go to PFMS for payment.  After this, the 5 copies of digitally signed Sanctions (taken earlier by DH) should be sent to the HoO section, Pension Section, Cash Section, Pensioner and 1 copy retained in PDA section (as mentioned on 71, sub heading 4.1 of this manual). 79 Fig.4.7\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How to approve pension calculation?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    From the DH log-in of PDA Section go to Others Bill Generate (DH PDA LoginBill GenerationOthers Bill Generation). Bill other than monthly bills like Commutation, DCRG, etc. will appear at this Tab. Here the bills are appearing in the name of claimants who will get the payment through PFMS. Now select the case from the check box and click Generate Bill Button. The bill will be generated and it will go to AAO of PDA Section. 78 Fig.4.5  AAO of PDA Section will log-in and select the case by clicking the check box corresponding to the case and click on Approve button (AAO PDA LoginApprovalOthers BillsApprove). The case will go to AO of PDA Section for approval. Fig.4.6  Now, AO of PDA Section will log-in and go to approval then other bills (AO PDA Login Approval Others Bills). The AO will then select the appropriate Account Head Code from the drop down list on the top. After entering the “Not Payable Before Date” click on Approve button. Now the Bill will automatically go to PFMS for payment.  After this, the 5 copies of digitally signed Sanctions (taken earlier by DH) should be sent to the HoO section, Pension Section, Cash Section, Pensioner and 1 copy retained in PDA section (as mentioned on 71, sub heading 4.1 of this manual). 79 Fig.4.7\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to approve pension calculation?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    From the DH log-in of PDA Section go to Others Bill Generate (DH PDA LoginBill GenerationOthers Bill Generation). Bill other than monthly bills like Commutation, DCRG, etc. will appear at this Tab. Here the bills are appearing in the name of claimants who will get the payment through PFMS. Now select the case from the check box and click Generate Bill Button. The bill will be generated and it will go to AAO of PDA Section. 78 Fig.4.5  AAO of PDA Section will log-in and select the case by clicking the check box corresponding to the case and click on Approve button (AAO PDA LoginApprovalOthers BillsApprove). The case will go to AO of PDA Section for approval. Fig.4.6  Now, AO of PDA Section will log-in and go to approval then other bills (AO PDA Login Approval Others Bills). The AO will then select the appropriate Account Head Code from the drop down list on the top. After entering the “Not Payable Before Date” click on Approve button. Now the Bill will automatically go to PFMS for payment.  After this, the 5 copies of digitally signed Sanctions (taken earlier by DH) should be sent to the HoO section, Pension Section, Cash Section, Pensioner and 1 copy retained in PDA section (as mentioned on 71, sub heading 4.1 of this manual). 79 Fig.4.7\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "What are the steps in pension sanctioning?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    From the DH log-in of PDA Section go to Others Bill Generate (DH PDA LoginBill GenerationOthers Bill Generation). Bill other than monthly bills like Commutation, DCRG, etc. will appear at this Tab. Here the bills are appearing in the name of claimants who will get the payment through PFMS. Now select the case from the check box and click Generate Bill Button. The bill will be generated and it will go to AAO of PDA Section. 78 Fig.4.5  AAO of PDA Section will log-in and select the case by clicking the check box corresponding to the case and click on Approve button (AAO PDA LoginApprovalOthers BillsApprove). The case will go to AO of PDA Section for approval. Fig.4.6  Now, AO of PDA Section will log-in and go to approval then other bills (AO PDA Login Approval Others Bills). The AO will then select the appropriate Account Head Code from the drop down list on the top. After entering the “Not Payable Before Date” click on Approve button. Now the Bill will automatically go to PFMS for payment.  After this, the 5 copies of digitally signed Sanctions (taken earlier by DH) should be sent to the HoO section, Pension Section, Cash Section, Pensioner and 1 copy retained in PDA section (as mentioned on 71, sub heading 4.1 of this manual). 79 Fig.4.7\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What are the steps in pension sanctioning?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    From the DH log-in of PDA Section go to Others Bill Generate (DH PDA LoginBill GenerationOthers Bill Generation). Bill other than monthly bills like Commutation, DCRG, etc. will appear at this Tab. Here the bills are appearing in the name of claimants who will get the payment through PFMS. Now select the case from the check box and click Generate Bill Button. The bill will be generated and it will go to AAO of PDA Section. 78 Fig.4.5  AAO of PDA Section will log-in and select the case by clicking the check box corresponding to the case and click on Approve button (AAO PDA LoginApprovalOthers BillsApprove). The case will go to AO of PDA Section for approval. Fig.4.6  Now, AO of PDA Section will log-in and go to approval then other bills (AO PDA Login Approval Others Bills). The AO will then select the appropriate Account Head Code from the drop down list on the top. After entering the “Not Payable Before Date” click on Approve button. Now the Bill will automatically go to PFMS for payment.  After this, the 5 copies of digitally signed Sanctions (taken earlier by DH) should be sent to the HoO section, Pension Section, Cash Section, Pensioner and 1 copy retained in PDA section (as mentioned on 71, sub heading 4.1 of this manual). 79 Fig.4.7\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How to digitally sign pension orders?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    From the DH log-in of PDA Section go to Others Bill Generate (DH PDA LoginBill GenerationOthers Bill Generation). Bill other than monthly bills like Commutation, DCRG, etc. will appear at this Tab. Here the bills are appearing in the name of claimants who will get the payment through PFMS. Now select the case from the check box and click Generate Bill Button. The bill will be generated and it will go to AAO of PDA Section. 78 Fig.4.5  AAO of PDA Section will log-in and select the case by clicking the check box corresponding to the case and click on Approve button (AAO PDA LoginApprovalOthers BillsApprove). The case will go to AO of PDA Section for approval. Fig.4.6  Now, AO of PDA Section will log-in and go to approval then other bills (AO PDA Login Approval Others Bills). The AO will then select the appropriate Account Head Code from the drop down list on the top. After entering the “Not Payable Before Date” click on Approve button. Now the Bill will automatically go to PFMS for payment.  After this, the 5 copies of digitally signed Sanctions (taken earlier by DH) should be sent to the HoO section, Pension Section, Cash Section, Pensioner and 1 copy retained in PDA section (as mentioned on 71, sub heading 4.1 of this manual). 79 Fig.4.7\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - None", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to digitally sign pension orders?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    From the DH log-in of PDA Section go to Others Bill Generate (DH PDA LoginBill GenerationOthers Bill Generation). Bill other than monthly bills like Commutation, DCRG, etc. will appear at this Tab. Here the bills are appearing in the name of claimants who will get the payment through PFMS. Now select the case from the check box and click Generate Bill Button. The bill will be generated and it will go to AAO of PDA Section. 78 Fig.4.5  AAO of PDA Section will log-in and select the case by clicking the check box corresponding to the case and click on Approve button (AAO PDA LoginApprovalOthers BillsApprove). The case will go to AO of PDA Section for approval. Fig.4.6  Now, AO of PDA Section will log-in and go to approval then other bills (AO PDA Login Approval Others Bills). The AO will then select the appropriate Account Head Code from the drop down list on the top. After entering the “Not Payable Before Date” click on Approve button. Now the Bill will automatically go to PFMS for payment.  After this, the 5 copies of digitally signed Sanctions (taken earlier by DH) should be sent to the HoO section, Pension Section, Cash Section, Pensioner and 1 copy retained in PDA section (as mentioned on 71, sub heading 4.1 of this manual). 79 Fig.4.7\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - None", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How is pension sanctioned in CPMS?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    While processing the case for monthly bill the income tax deducted shall be filled and accordingly the bill shall be processed. The income tax deducted shall be used for feeding income tax on monthly and quarterly basis.  A printout of the monthly bill shall be taken and handed over to AO Cash conveying the sanction.  Thereafter the cases in DH can be passed to AAO and further to AO for approval. Then AO will send the case for monthly bill/pension payment, through PFMS. This cycle will be repeated for every month. 85 Fig.4.14 Fig.4.15 86  PDA AO can view the PFMS arrear transaction report of the pensioner/claimant, in the Reports TabOther Bill Report. (4.16) Fig.4.16\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Monthly Bill", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What is the pension sanction process?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    While processing the case for monthly bill the income tax deducted shall be filled and accordingly the bill shall be processed. The income tax deducted shall be used for feeding income tax on monthly and quarterly basis.  A printout of the monthly bill shall be taken and handed over to AO Cash conveying the sanction.  Thereafter the cases in DH can be passed to AAO and further to AO for approval. Then AO will send the case for monthly bill/pension payment, through PFMS. This cycle will be repeated for every month. 85 Fig.4.14 Fig.4.15 86  PDA AO can view the PFMS arrear transaction report of the pensioner/claimant, in the Reports TabOther Bill Report. (4.16) Fig.4.16\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Monthly Bill", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What is the pension sanction process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    While processing the case for monthly bill the income tax deducted shall be filled and accordingly the bill shall be processed. The income tax deducted shall be used for feeding income tax on monthly and quarterly basis.  A printout of the monthly bill shall be taken and handed over to AO Cash conveying the sanction.  Thereafter the cases in DH can be passed to AAO and further to AO for approval. Then AO will send the case for monthly bill/pension payment, through PFMS. This cycle will be repeated for every month. 85 Fig.4.14 Fig.4.15 86  PDA AO can view the PFMS arrear transaction report of the pensioner/claimant, in the Reports TabOther Bill Report. (4.16) Fig.4.16\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Monthly Bill", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How to approve pension calculation?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    While processing the case for monthly bill the income tax deducted shall be filled and accordingly the bill shall be processed. The income tax deducted shall be used for feeding income tax on monthly and quarterly basis.  A printout of the monthly bill shall be taken and handed over to AO Cash conveying the sanction.  Thereafter the cases in DH can be passed to AAO and further to AO for approval. Then AO will send the case for monthly bill/pension payment, through PFMS. This cycle will be repeated for every month. 85 Fig.4.14 Fig.4.15 86  PDA AO can view the PFMS arrear transaction report of the pensioner/claimant, in the Reports TabOther Bill Report. (4.16) Fig.4.16\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Monthly Bill", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to approve pension calculation?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    While processing the case for monthly bill the income tax deducted shall be filled and accordingly the bill shall be processed. The income tax deducted shall be used for feeding income tax on monthly and quarterly basis.  A printout of the monthly bill shall be taken and handed over to AO Cash conveying the sanction.  Thereafter the cases in DH can be passed to AAO and further to AO for approval. Then AO will send the case for monthly bill/pension payment, through PFMS. This cycle will be repeated for every month. 85 Fig.4.14 Fig.4.15 86  PDA AO can view the PFMS arrear transaction report of the pensioner/claimant, in the Reports TabOther Bill Report. (4.16) Fig.4.16\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Monthly Bill", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "What are the steps in pension sanctioning?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    While processing the case for monthly bill the income tax deducted shall be filled and accordingly the bill shall be processed. The income tax deducted shall be used for feeding income tax on monthly and quarterly basis.  A printout of the monthly bill shall be taken and handed over to AO Cash conveying the sanction.  Thereafter the cases in DH can be passed to AAO and further to AO for approval. Then AO will send the case for monthly bill/pension payment, through PFMS. This cycle will be repeated for every month. 85 Fig.4.14 Fig.4.15 86  PDA AO can view the PFMS arrear transaction report of the pensioner/claimant, in the Reports TabOther Bill Report. (4.16) Fig.4.16\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Monthly Bill", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What are the steps in pension sanctioning?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    While processing the case for monthly bill the income tax deducted shall be filled and accordingly the bill shall be processed. The income tax deducted shall be used for feeding income tax on monthly and quarterly basis.  A printout of the monthly bill shall be taken and handed over to AO Cash conveying the sanction.  Thereafter the cases in DH can be passed to AAO and further to AO for approval. Then AO will send the case for monthly bill/pension payment, through PFMS. This cycle will be repeated for every month. 85 Fig.4.14 Fig.4.15 86  PDA AO can view the PFMS arrear transaction report of the pensioner/claimant, in the Reports TabOther Bill Report. (4.16) Fig.4.16\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Monthly Bill", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How to digitally sign pension orders?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    While processing the case for monthly bill the income tax deducted shall be filled and accordingly the bill shall be processed. The income tax deducted shall be used for feeding income tax on monthly and quarterly basis.  A printout of the monthly bill shall be taken and handed over to AO Cash conveying the sanction.  Thereafter the cases in DH can be passed to AAO and further to AO for approval. Then AO will send the case for monthly bill/pension payment, through PFMS. This cycle will be repeated for every month. 85 Fig.4.14 Fig.4.15 86  PDA AO can view the PFMS arrear transaction report of the pensioner/claimant, in the Reports TabOther Bill Report. (4.16) Fig.4.16\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Monthly Bill", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to digitally sign pension orders?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    While processing the case for monthly bill the income tax deducted shall be filled and accordingly the bill shall be processed. The income tax deducted shall be used for feeding income tax on monthly and quarterly basis.  A printout of the monthly bill shall be taken and handed over to AO Cash conveying the sanction.  Thereafter the cases in DH can be passed to AAO and further to AO for approval. Then AO will send the case for monthly bill/pension payment, through PFMS. This cycle will be repeated for every month. 85 Fig.4.14 Fig.4.15 86  PDA AO can view the PFMS arrear transaction report of the pensioner/claimant, in the Reports TabOther Bill Report. (4.16) Fig.4.16\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Monthly Bill", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How is pension sanctioned in CPMS?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    Again login with AO Pension go to the Masters Generate ID Card.  Here Fill the PPO number of pensioner and click on the Search button. (9.4) 160 (9.4)  Now you can take the print of ID Card by clicking on the “Print Front side” and “Print Back Side” button, as shown in below figure. (9.5) (9.5) 161 10 10. Revision Under revision module, there are 5 categories of revision (shown in 10) 1. Revision in the rate of DA: Due to change of DA as on Date of retirement, the DCRG shall get revised and the differential amount of DCRG shall be due to be paid. For this sanction for payment shall be issued by Pension section and PDA shall make payment. Any impact on pension payable has to be separately calculated and paid in the next monthly bill. 2. Revision on account of withheld amount: On receipt of sanction for release of full/part payment of Withheld amount, this revision shall be processed. 3. Revision of pension to family pension (f.p) on account of death (When No eligible f.p. is mentioned in the PPO): In case when claimant name is not mentioned in the PPO, then revision shall be done. Under this, PPO shall be issued by Pension section. 4. Revision of pension to f.p on account of death (when eligible f.p. is mentioned in PPO) In case when claimant name is mentioned in the PPO, then revision shall be done however as previous PPO will suffice, no fresh PPO shall be issued in such case. 5. Revision of pension due to pay change/Court matter: Due to change in Pay details or service details arising out of Court order then, the Revision shall be processed. (10) It is , currently ,mandatory that the Revision should only be done by the same DH Pension user who make entry of the case earlier in the CPMS.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Generate ID Card", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What is the pension sanction process?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    Again login with AO Pension go to the Masters Generate ID Card.  Here Fill the PPO number of pensioner and click on the Search button. (9.4) 160 (9.4)  Now you can take the print of ID Card by clicking on the “Print Front side” and “Print Back Side” button, as shown in below figure. (9.5) (9.5) 161 10 10. Revision Under revision module, there are 5 categories of revision (shown in 10) 1. Revision in the rate of DA: Due to change of DA as on Date of retirement, the DCRG shall get revised and the differential amount of DCRG shall be due to be paid. For this sanction for payment shall be issued by Pension section and PDA shall make payment. Any impact on pension payable has to be separately calculated and paid in the next monthly bill. 2. Revision on account of withheld amount: On receipt of sanction for release of full/part payment of Withheld amount, this revision shall be processed. 3. Revision of pension to family pension (f.p) on account of death (When No eligible f.p. is mentioned in the PPO): In case when claimant name is not mentioned in the PPO, then revision shall be done. Under this, PPO shall be issued by Pension section. 4. Revision of pension to f.p on account of death (when eligible f.p. is mentioned in PPO) In case when claimant name is mentioned in the PPO, then revision shall be done however as previous PPO will suffice, no fresh PPO shall be issued in such case. 5. Revision of pension due to pay change/Court matter: Due to change in Pay details or service details arising out of Court order then, the Revision shall be processed. (10) It is , currently ,mandatory that the Revision should only be done by the same DH Pension user who make entry of the case earlier in the CPMS.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Generate ID Card", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What is the pension sanction process?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    Again login with AO Pension go to the Masters Generate ID Card.  Here Fill the PPO number of pensioner and click on the Search button. (9.4) 160 (9.4)  Now you can take the print of ID Card by clicking on the “Print Front side” and “Print Back Side” button, as shown in below figure. (9.5) (9.5) 161 10 10. Revision Under revision module, there are 5 categories of revision (shown in 10) 1. Revision in the rate of DA: Due to change of DA as on Date of retirement, the DCRG shall get revised and the differential amount of DCRG shall be due to be paid. For this sanction for payment shall be issued by Pension section and PDA shall make payment. Any impact on pension payable has to be separately calculated and paid in the next monthly bill. 2. Revision on account of withheld amount: On receipt of sanction for release of full/part payment of Withheld amount, this revision shall be processed. 3. Revision of pension to family pension (f.p) on account of death (When No eligible f.p. is mentioned in the PPO): In case when claimant name is not mentioned in the PPO, then revision shall be done. Under this, PPO shall be issued by Pension section. 4. Revision of pension to f.p on account of death (when eligible f.p. is mentioned in PPO) In case when claimant name is mentioned in the PPO, then revision shall be done however as previous PPO will suffice, no fresh PPO shall be issued in such case. 5. Revision of pension due to pay change/Court matter: Due to change in Pay details or service details arising out of Court order then, the Revision shall be processed. (10) It is , currently ,mandatory that the Revision should only be done by the same DH Pension user who make entry of the case earlier in the CPMS.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Generate ID Card", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How to approve pension calculation?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    Again login with AO Pension go to the Masters Generate ID Card.  Here Fill the PPO number of pensioner and click on the Search button. (9.4) 160 (9.4)  Now you can take the print of ID Card by clicking on the “Print Front side” and “Print Back Side” button, as shown in below figure. (9.5) (9.5) 161 10 10. Revision Under revision module, there are 5 categories of revision (shown in 10) 1. Revision in the rate of DA: Due to change of DA as on Date of retirement, the DCRG shall get revised and the differential amount of DCRG shall be due to be paid. For this sanction for payment shall be issued by Pension section and PDA shall make payment. Any impact on pension payable has to be separately calculated and paid in the next monthly bill. 2. Revision on account of withheld amount: On receipt of sanction for release of full/part payment of Withheld amount, this revision shall be processed. 3. Revision of pension to family pension (f.p) on account of death (When No eligible f.p. is mentioned in the PPO): In case when claimant name is not mentioned in the PPO, then revision shall be done. Under this, PPO shall be issued by Pension section. 4. Revision of pension to f.p on account of death (when eligible f.p. is mentioned in PPO) In case when claimant name is mentioned in the PPO, then revision shall be done however as previous PPO will suffice, no fresh PPO shall be issued in such case. 5. Revision of pension due to pay change/Court matter: Due to change in Pay details or service details arising out of Court order then, the Revision shall be processed. (10) It is , currently ,mandatory that the Revision should only be done by the same DH Pension user who make entry of the case earlier in the CPMS.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Generate ID Card", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to approve pension calculation?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    Again login with AO Pension go to the Masters Generate ID Card.  Here Fill the PPO number of pensioner and click on the Search button. (9.4) 160 (9.4)  Now you can take the print of ID Card by clicking on the “Print Front side” and “Print Back Side” button, as shown in below figure. (9.5) (9.5) 161 10 10. Revision Under revision module, there are 5 categories of revision (shown in 10) 1. Revision in the rate of DA: Due to change of DA as on Date of retirement, the DCRG shall get revised and the differential amount of DCRG shall be due to be paid. For this sanction for payment shall be issued by Pension section and PDA shall make payment. Any impact on pension payable has to be separately calculated and paid in the next monthly bill. 2. Revision on account of withheld amount: On receipt of sanction for release of full/part payment of Withheld amount, this revision shall be processed. 3. Revision of pension to family pension (f.p) on account of death (When No eligible f.p. is mentioned in the PPO): In case when claimant name is not mentioned in the PPO, then revision shall be done. Under this, PPO shall be issued by Pension section. 4. Revision of pension to f.p on account of death (when eligible f.p. is mentioned in PPO) In case when claimant name is mentioned in the PPO, then revision shall be done however as previous PPO will suffice, no fresh PPO shall be issued in such case. 5. Revision of pension due to pay change/Court matter: Due to change in Pay details or service details arising out of Court order then, the Revision shall be processed. (10) It is , currently ,mandatory that the Revision should only be done by the same DH Pension user who make entry of the case earlier in the CPMS.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Generate ID Card", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "What are the steps in pension sanctioning?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    Again login with AO Pension go to the Masters Generate ID Card.  Here Fill the PPO number of pensioner and click on the Search button. (9.4) 160 (9.4)  Now you can take the print of ID Card by clicking on the “Print Front side” and “Print Back Side” button, as shown in below figure. (9.5) (9.5) 161 10 10. Revision Under revision module, there are 5 categories of revision (shown in 10) 1. Revision in the rate of DA: Due to change of DA as on Date of retirement, the DCRG shall get revised and the differential amount of DCRG shall be due to be paid. For this sanction for payment shall be issued by Pension section and PDA shall make payment. Any impact on pension payable has to be separately calculated and paid in the next monthly bill. 2. Revision on account of withheld amount: On receipt of sanction for release of full/part payment of Withheld amount, this revision shall be processed. 3. Revision of pension to family pension (f.p) on account of death (When No eligible f.p. is mentioned in the PPO): In case when claimant name is not mentioned in the PPO, then revision shall be done. Under this, PPO shall be issued by Pension section. 4. Revision of pension to f.p on account of death (when eligible f.p. is mentioned in PPO) In case when claimant name is mentioned in the PPO, then revision shall be done however as previous PPO will suffice, no fresh PPO shall be issued in such case. 5. Revision of pension due to pay change/Court matter: Due to change in Pay details or service details arising out of Court order then, the Revision shall be processed. (10) It is , currently ,mandatory that the Revision should only be done by the same DH Pension user who make entry of the case earlier in the CPMS.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Generate ID Card", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What are the steps in pension sanctioning?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    Again login with AO Pension go to the Masters Generate ID Card.  Here Fill the PPO number of pensioner and click on the Search button. (9.4) 160 (9.4)  Now you can take the print of ID Card by clicking on the “Print Front side” and “Print Back Side” button, as shown in below figure. (9.5) (9.5) 161 10 10. Revision Under revision module, there are 5 categories of revision (shown in 10) 1. Revision in the rate of DA: Due to change of DA as on Date of retirement, the DCRG shall get revised and the differential amount of DCRG shall be due to be paid. For this sanction for payment shall be issued by Pension section and PDA shall make payment. Any impact on pension payable has to be separately calculated and paid in the next monthly bill. 2. Revision on account of withheld amount: On receipt of sanction for release of full/part payment of Withheld amount, this revision shall be processed. 3. Revision of pension to family pension (f.p) on account of death (When No eligible f.p. is mentioned in the PPO): In case when claimant name is not mentioned in the PPO, then revision shall be done. Under this, PPO shall be issued by Pension section. 4. Revision of pension to f.p on account of death (when eligible f.p. is mentioned in PPO) In case when claimant name is mentioned in the PPO, then revision shall be done however as previous PPO will suffice, no fresh PPO shall be issued in such case. 5. Revision of pension due to pay change/Court matter: Due to change in Pay details or service details arising out of Court order then, the Revision shall be processed. (10) It is , currently ,mandatory that the Revision should only be done by the same DH Pension user who make entry of the case earlier in the CPMS.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Generate ID Card", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How to digitally sign pension orders?", "input": "", "output": "For pension sanctioning in CPMS:\n\n    Again login with AO Pension go to the Masters Generate ID Card.  Here Fill the PPO number of pensioner and click on the Search button. (9.4) 160 (9.4)  Now you can take the print of ID Card by clicking on the “Print Front side” and “Print Back Side” button, as shown in below figure. (9.5) (9.5) 161 10 10. Revision Under revision module, there are 5 categories of revision (shown in 10) 1. Revision in the rate of DA: Due to change of DA as on Date of retirement, the DCRG shall get revised and the differential amount of DCRG shall be due to be paid. For this sanction for payment shall be issued by Pension section and PDA shall make payment. Any impact on pension payable has to be separately calculated and paid in the next monthly bill. 2. Revision on account of withheld amount: On receipt of sanction for release of full/part payment of Withheld amount, this revision shall be processed. 3. Revision of pension to family pension (f.p) on account of death (When No eligible f.p. is mentioned in the PPO): In case when claimant name is not mentioned in the PPO, then revision shall be done. Under this, PPO shall be issued by Pension section. 4. Revision of pension to f.p on account of death (when eligible f.p. is mentioned in PPO) In case when claimant name is mentioned in the PPO, then revision shall be done however as previous PPO will suffice, no fresh PPO shall be issued in such case. 5. Revision of pension due to pay change/Court matter: Due to change in Pay details or service details arising out of Court order then, the Revision shall be processed. (10) It is , currently ,mandatory that the Revision should only be done by the same DH Pension user who make entry of the case earlier in the CPMS.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Generate ID Card", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to digitally sign pension orders?", "input": "", "output": "Here are the step-by-step instructions:\n\nFor pension sanctioning in CPMS:\n\n    Again login with AO Pension go to the Masters Generate ID Card.  Here Fill the PPO number of pensioner and click on the Search button. (9.4) 160 (9.4)  Now you can take the print of ID Card by clicking on the “Print Front side” and “Print Back Side” button, as shown in below figure. (9.5) (9.5) 161 10 10. Revision Under revision module, there are 5 categories of revision (shown in 10) 1. Revision in the rate of DA: Due to change of DA as on Date of retirement, the DCRG shall get revised and the differential amount of DCRG shall be due to be paid. For this sanction for payment shall be issued by Pension section and PDA shall make payment. Any impact on pension payable has to be separately calculated and paid in the next monthly bill. 2. Revision on account of withheld amount: On receipt of sanction for release of full/part payment of Withheld amount, this revision shall be processed. 3. Revision of pension to family pension (f.p) on account of death (When No eligible f.p. is mentioned in the PPO): In case when claimant name is not mentioned in the PPO, then revision shall be done. Under this, PPO shall be issued by Pension section. 4. Revision of pension to f.p on account of death (when eligible f.p. is mentioned in PPO) In case when claimant name is mentioned in the PPO, then revision shall be done however as previous PPO will suffice, no fresh PPO shall be issued in such case. 5. Revision of pension due to pay change/Court matter: Due to change in Pay details or service details arising out of Court order then, the Revision shall be processed. (10) It is , currently ,mandatory that the Revision should only be done by the same DH Pension user who make entry of the case earlier in the CPMS.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.\n\n💡 For additional help:\n• Helpline: 1800-113-5800\n• Email: <EMAIL>\n• Technical Support: <EMAIL>", "source": "CPMS User Manual - Generate ID Card", "procedure_type": "PENSION_SANCTIONING", "enhanced": true}
{"instruction": "How is pension sanctioned in CPMS?", "input": "", "output": "For pension sanctioning in CPMS:\n\n   This type of revision is used when there is a change in the DA Rate. No fresh sanction for HoO for this case shall be required. Steps for this revision are as follows: -  First of all, login with the DH Pension  Go to the Revision -> Revision of Pension tab (10.1.b). 162 (10.1.b)  Here fill in PPO No and select the “Reason for Revision” (10.1.c). (10.1.c)  Now click on the edit button (pencil icon) in last Action column.  Then next screen opens, here check the details and then click on the “Send to Revision Sanction order” button and enter w.e.f. date (10.1.d). 163 (10.1.d)  Now Go to-> Revision->Revision Sanction Order tab shown in (10.1.e) (10.1.e)  From this screen you can view the Sanction order, by clicking on the View link (10.1.e). Also you can take the printout of this Sanction Order (10.1.f). 164 (10.1.f)  Now click on the Last Column, verify link to send record for AAO approval (10.1.e).  Now login with the AAO Pension. Go to ->Approval->Revision->Revision Sanction Order Shown in (10.1.g) (10.1.g)  Now Click on the View link to see the Sanction order and then Approve/Return the record by click on the Approve/Return link and send record for AO approval (10.1.g).  Now login with the AO Pension (only on IE Browser).  Go to Approval>Revision>Revision Sanction Order (10.1.h) 165 (10.1.h)  Click on View link to see or take the print of the Sanction Order, attach the DSc in the system for digital signature and click on the Approve link.At this stage, Digitally signed authority shall get generated.  Now login with AO PDA, Go to->Allotment->Allotment To PDA DH (10.1.i) (10.1.i)  Now select the case, Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on “Send To DH” button.  After this login with PDA DH, Go to ->Bill Generation->Revision, (10.1.j) 166 (10.1.j)  Select the Record and Click on the “Approve and Send to AAO” button, (10.1.j).  Now login with AAO PDA, Go to->Approval->PDA->Revision, (10.1.k) (10.1.k)  Now select the record and click on the “Approve Bill and Send to AO”.  After this login from PDA AO, Go to->Approval->PDA->Revision (10.1.l) 167 (10.1.l)  Select the Account Head from the dropdown, then it shows the respective records, then fill the “Enter Not Payable Before Date” and click on the Approve Bill button.  After this record will show in PFMS for payment. Monthly Bill shall remain unchanged.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Revision in the rate of DA", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What is the pension sanction process?", "input": "", "output": "For pension sanctioning in CPMS:\n\n   This type of revision is used when there is a change in the DA Rate. No fresh sanction for HoO for this case shall be required. Steps for this revision are as follows: -  First of all, login with the DH Pension  Go to the Revision -> Revision of Pension tab (10.1.b). 162 (10.1.b)  Here fill in PPO No and select the “Reason for Revision” (10.1.c). (10.1.c)  Now click on the edit button (pencil icon) in last Action column.  Then next screen opens, here check the details and then click on the “Send to Revision Sanction order” button and enter w.e.f. date (10.1.d). 163 (10.1.d)  Now Go to-> Revision->Revision Sanction Order tab shown in (10.1.e) (10.1.e)  From this screen you can view the Sanction order, by clicking on the View link (10.1.e). Also you can take the printout of this Sanction Order (10.1.f). 164 (10.1.f)  Now click on the Last Column, verify link to send record for AAO approval (10.1.e).  Now login with the AAO Pension. Go to ->Approval->Revision->Revision Sanction Order Shown in (10.1.g) (10.1.g)  Now Click on the View link to see the Sanction order and then Approve/Return the record by click on the Approve/Return link and send record for AO approval (10.1.g).  Now login with the AO Pension (only on IE Browser).  Go to Approval>Revision>Revision Sanction Order (10.1.h) 165 (10.1.h)  Click on View link to see or take the print of the Sanction Order, attach the DSc in the system for digital signature and click on the Approve link.At this stage, Digitally signed authority shall get generated.  Now login with AO PDA, Go to->Allotment->Allotment To PDA DH (10.1.i) (10.1.i)  Now select the case, Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on “Send To DH” button.  After this login with PDA DH, Go to ->Bill Generation->Revision, (10.1.j) 166 (10.1.j)  Select the Record and Click on the “Approve and Send to AAO” button, (10.1.j).  Now login with AAO PDA, Go to->Approval->PDA->Revision, (10.1.k) (10.1.k)  Now select the record and click on the “Approve Bill and Send to AO”.  After this login from PDA AO, Go to->Approval->PDA->Revision (10.1.l) 167 (10.1.l)  Select the Account Head from the dropdown, then it shows the respective records, then fill the “Enter Not Payable Before Date” and click on the Approve Bill button.  After this record will show in PFMS for payment. Monthly Bill shall remain unchanged.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Revision in the rate of DA", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to approve pension calculation?", "input": "", "output": "For pension sanctioning in CPMS:\n\n   This type of revision is used when there is a change in the DA Rate. No fresh sanction for HoO for this case shall be required. Steps for this revision are as follows: -  First of all, login with the DH Pension  Go to the Revision -> Revision of Pension tab (10.1.b). 162 (10.1.b)  Here fill in PPO No and select the “Reason for Revision” (10.1.c). (10.1.c)  Now click on the edit button (pencil icon) in last Action column.  Then next screen opens, here check the details and then click on the “Send to Revision Sanction order” button and enter w.e.f. date (10.1.d). 163 (10.1.d)  Now Go to-> Revision->Revision Sanction Order tab shown in (10.1.e) (10.1.e)  From this screen you can view the Sanction order, by clicking on the View link (10.1.e). Also you can take the printout of this Sanction Order (10.1.f). 164 (10.1.f)  Now click on the Last Column, verify link to send record for AAO approval (10.1.e).  Now login with the AAO Pension. Go to ->Approval->Revision->Revision Sanction Order Shown in (10.1.g) (10.1.g)  Now Click on the View link to see the Sanction order and then Approve/Return the record by click on the Approve/Return link and send record for AO approval (10.1.g).  Now login with the AO Pension (only on IE Browser).  Go to Approval>Revision>Revision Sanction Order (10.1.h) 165 (10.1.h)  Click on View link to see or take the print of the Sanction Order, attach the DSc in the system for digital signature and click on the Approve link.At this stage, Digitally signed authority shall get generated.  Now login with AO PDA, Go to->Allotment->Allotment To PDA DH (10.1.i) (10.1.i)  Now select the case, Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on “Send To DH” button.  After this login with PDA DH, Go to ->Bill Generation->Revision, (10.1.j) 166 (10.1.j)  Select the Record and Click on the “Approve and Send to AAO” button, (10.1.j).  Now login with AAO PDA, Go to->Approval->PDA->Revision, (10.1.k) (10.1.k)  Now select the record and click on the “Approve Bill and Send to AO”.  After this login from PDA AO, Go to->Approval->PDA->Revision (10.1.l) 167 (10.1.l)  Select the Account Head from the dropdown, then it shows the respective records, then fill the “Enter Not Payable Before Date” and click on the Approve Bill button.  After this record will show in PFMS for payment. Monthly Bill shall remain unchanged.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Revision in the rate of DA", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "What are the steps in pension sanctioning?", "input": "", "output": "For pension sanctioning in CPMS:\n\n   This type of revision is used when there is a change in the DA Rate. No fresh sanction for HoO for this case shall be required. Steps for this revision are as follows: -  First of all, login with the DH Pension  Go to the Revision -> Revision of Pension tab (10.1.b). 162 (10.1.b)  Here fill in PPO No and select the “Reason for Revision” (10.1.c). (10.1.c)  Now click on the edit button (pencil icon) in last Action column.  Then next screen opens, here check the details and then click on the “Send to Revision Sanction order” button and enter w.e.f. date (10.1.d). 163 (10.1.d)  Now Go to-> Revision->Revision Sanction Order tab shown in (10.1.e) (10.1.e)  From this screen you can view the Sanction order, by clicking on the View link (10.1.e). Also you can take the printout of this Sanction Order (10.1.f). 164 (10.1.f)  Now click on the Last Column, verify link to send record for AAO approval (10.1.e).  Now login with the AAO Pension. Go to ->Approval->Revision->Revision Sanction Order Shown in (10.1.g) (10.1.g)  Now Click on the View link to see the Sanction order and then Approve/Return the record by click on the Approve/Return link and send record for AO approval (10.1.g).  Now login with the AO Pension (only on IE Browser).  Go to Approval>Revision>Revision Sanction Order (10.1.h) 165 (10.1.h)  Click on View link to see or take the print of the Sanction Order, attach the DSc in the system for digital signature and click on the Approve link.At this stage, Digitally signed authority shall get generated.  Now login with AO PDA, Go to->Allotment->Allotment To PDA DH (10.1.i) (10.1.i)  Now select the case, Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on “Send To DH” button.  After this login with PDA DH, Go to ->Bill Generation->Revision, (10.1.j) 166 (10.1.j)  Select the Record and Click on the “Approve and Send to AAO” button, (10.1.j).  Now login with AAO PDA, Go to->Approval->PDA->Revision, (10.1.k) (10.1.k)  Now select the record and click on the “Approve Bill and Send to AO”.  After this login from PDA AO, Go to->Approval->PDA->Revision (10.1.l) 167 (10.1.l)  Select the Account Head from the dropdown, then it shows the respective records, then fill the “Enter Not Payable Before Date” and click on the Approve Bill button.  After this record will show in PFMS for payment. Monthly Bill shall remain unchanged.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Revision in the rate of DA", "procedure_type": "PENSION_SANCTIONING"}
{"instruction": "How to digitally sign pension orders?", "input": "", "output": "For pension sanctioning in CPMS:\n\n   This type of revision is used when there is a change in the DA Rate. No fresh sanction for HoO for this case shall be required. Steps for this revision are as follows: -  First of all, login with the DH Pension  Go to the Revision -> Revision of Pension tab (10.1.b). 162 (10.1.b)  Here fill in PPO No and select the “Reason for Revision” (10.1.c). (10.1.c)  Now click on the edit button (pencil icon) in last Action column.  Then next screen opens, here check the details and then click on the “Send to Revision Sanction order” button and enter w.e.f. date (10.1.d). 163 (10.1.d)  Now Go to-> Revision->Revision Sanction Order tab shown in (10.1.e) (10.1.e)  From this screen you can view the Sanction order, by clicking on the View link (10.1.e). Also you can take the printout of this Sanction Order (10.1.f). 164 (10.1.f)  Now click on the Last Column, verify link to send record for AAO approval (10.1.e).  Now login with the AAO Pension. Go to ->Approval->Revision->Revision Sanction Order Shown in (10.1.g) (10.1.g)  Now Click on the View link to see the Sanction order and then Approve/Return the record by click on the Approve/Return link and send record for AO approval (10.1.g).  Now login with the AO Pension (only on IE Browser).  Go to Approval>Revision>Revision Sanction Order (10.1.h) 165 (10.1.h)  Click on View link to see or take the print of the Sanction Order, attach the DSc in the system for digital signature and click on the Approve link.At this stage, Digitally signed authority shall get generated.  Now login with AO PDA, Go to->Allotment->Allotment To PDA DH (10.1.i) (10.1.i)  Now select the case, Allot the case to the PDA DH by selecting the respective DH from the dropdown list and Click on “Send To DH” button.  After this login with PDA DH, Go to ->Bill Generation->Revision, (10.1.j) 166 (10.1.j)  Select the Record and Click on the “Approve and Send to AAO” button, (10.1.j).  Now login with AAO PDA, Go to->Approval->PDA->Revision, (10.1.k) (10.1.k)  Now select the record and click on the “Approve Bill and Send to AO”.  After this login from PDA AO, Go to->Approval->PDA->Revision (10.1.l) 167 (10.1.l)  Select the Account Head from the dropdown, then it shows the respective records, then fill the “Enter Not Payable Before Date” and click on the Approve Bill button.  After this record will show in PFMS for payment. Monthly Bill shall remain unchanged.\n\nFor any technical issues, contact CPMS helpline at 1800-113-5800 <NAME_EMAIL>.", "source": "CPMS User Manual - Revision in the rate of DA", "procedure_type": "PENSION_SANCTIONING"}
