"""
Authentication service for pensioner login with EPPO and OTP
"""
import os
import random
import string
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
try:
    from jose import JWTError, jwt
except ImportError:
    # Fallback to PyJWT if python-jose fails
    import jwt
    from jwt.exceptions import InvalidTokenError as JWTError
from passlib.context import CryptContext
import logging

# Import database models
from backend.models.pensioner_models import Pensioner, OTPSession
from backend.database.database import get_db, get_system_config

# JWT Configuration
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_HOURS = 24

# Password context for hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class AuthService:
    """Authentication service for pensioner login"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def generate_otp(self) -> str:
        """Generate a 6-digit OTP"""
        return ''.join(random.choices(string.digits, k=6))
    
    def hash_otp(self, otp: str) -> str:
        """Hash OTP for secure storage"""
        return hashlib.sha256(otp.encode()).hexdigest()
    
    def verify_otp_hash(self, otp: str, hashed_otp: str) -> bool:
        """Verify OTP against hash"""
        return hashlib.sha256(otp.encode()).hexdigest() == hashed_otp
    
    def send_otp_sms(self, mobile_number: str, otp: str) -> bool:
        """
        Send OTP via SMS
        In production, integrate with SMS gateway like Twilio or government SMS service
        """
        try:
            # For development/testing, just log the OTP
            self.logger.info(f"📱 OTP for {mobile_number}: {otp}")
            
            # TODO: Integrate with actual SMS service
            # Example with Twilio:
            # from twilio.rest import Client
            # client = Client(account_sid, auth_token)
            # message = client.messages.create(
            #     body=f"Your CPMS OTP is: {otp}. Valid for 10 minutes.",
            #     from_='+**********',
            #     to=mobile_number
            # )
            
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to send OTP to {mobile_number}: {e}")
            return False
    
    def authenticate_pensioner(self, eppo_number: str, mobile_number: str, db: Session) -> Dict[str, Any]:
        """
        Authenticate pensioner with EPPO number and mobile number
        """
        try:
            # Find pensioner by EPPO number
            pensioner = db.query(Pensioner).filter(
                Pensioner.eppo_number == eppo_number,
                Pensioner.is_active == True
            ).first()
            
            if not pensioner:
                return {
                    "success": False,
                    "message": "Invalid EPPO number. Please check and try again.",
                    "error_code": "INVALID_EPPO"
                }
            
            # Verify mobile number
            if pensioner.mobile_number != mobile_number:
                return {
                    "success": False,
                    "message": "Mobile number does not match our records.",
                    "error_code": "MOBILE_MISMATCH"
                }
            
            # Generate and send OTP
            otp = self.generate_otp()
            otp_hash = self.hash_otp(otp)
            
            # Get OTP expiry time from config
            expiry_minutes = int(get_system_config("otp_expiry_minutes", "10"))
            expires_at = datetime.utcnow() + timedelta(minutes=expiry_minutes)
            
            # Save OTP session
            otp_session = OTPSession(
                pensioner_id=pensioner.id,
                mobile_number=mobile_number,
                otp_code=otp_hash,
                expires_at=expires_at
            )
            db.add(otp_session)
            db.commit()
            
            # Send OTP
            if self.send_otp_sms(mobile_number, otp):
                return {
                    "success": True,
                    "message": f"OTP sent to {mobile_number}. Please enter the 6-digit code.",
                    "session_id": otp_session.id,
                    "expires_in": expiry_minutes * 60  # seconds
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to send OTP. Please try again.",
                    "error_code": "SMS_FAILED"
                }
                
        except Exception as e:
            self.logger.error(f"❌ Authentication error: {e}")
            return {
                "success": False,
                "message": "Authentication service temporarily unavailable.",
                "error_code": "SERVICE_ERROR"
            }
    
    def verify_otp(self, session_id: int, otp: str, db: Session) -> Dict[str, Any]:
        """
        Verify OTP and create JWT token
        """
        try:
            # Find OTP session
            otp_session = db.query(OTPSession).filter(
                OTPSession.id == session_id,
                OTPSession.is_verified == False
            ).first()
            
            if not otp_session:
                return {
                    "success": False,
                    "message": "Invalid or expired OTP session.",
                    "error_code": "INVALID_SESSION"
                }
            
            # Check if OTP is expired
            if datetime.utcnow() > otp_session.expires_at:
                return {
                    "success": False,
                    "message": "OTP has expired. Please request a new one.",
                    "error_code": "OTP_EXPIRED"
                }
            
            # Verify OTP
            if not self.verify_otp_hash(otp, otp_session.otp_code):
                return {
                    "success": False,
                    "message": "Invalid OTP. Please check and try again.",
                    "error_code": "INVALID_OTP"
                }
            
            # Mark OTP as verified
            otp_session.is_verified = True
            
            # Get pensioner details
            pensioner = db.query(Pensioner).filter(
                Pensioner.id == otp_session.pensioner_id
            ).first()
            
            # Update last login
            pensioner.last_login = datetime.utcnow()
            
            db.commit()
            
            # Create JWT token
            token_data = {
                "sub": str(pensioner.id),
                "eppo": pensioner.eppo_number,
                "name": pensioner.name,
                "exp": datetime.utcnow() + timedelta(hours=ACCESS_TOKEN_EXPIRE_HOURS)
            }
            
            access_token = jwt.encode(token_data, SECRET_KEY, algorithm=ALGORITHM)
            
            return {
                "success": True,
                "message": "Login successful",
                "access_token": access_token,
                "token_type": "bearer",
                "expires_in": ACCESS_TOKEN_EXPIRE_HOURS * 3600,  # seconds
                "pensioner": {
                    "id": pensioner.id,
                    "eppo_number": pensioner.eppo_number,
                    "name": pensioner.name,
                    "mobile_number": pensioner.mobile_number
                }
            }
            
        except Exception as e:
            self.logger.error(f"❌ OTP verification error: {e}")
            return {
                "success": False,
                "message": "Verification service temporarily unavailable.",
                "error_code": "SERVICE_ERROR"
            }
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Verify JWT token and return pensioner data
        """
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            pensioner_id = payload.get("sub")
            if pensioner_id is None:
                return None
            return payload
        except JWTError:
            return None
    
    def get_current_pensioner(self, token: str, db: Session) -> Optional[Pensioner]:
        """
        Get current pensioner from token
        """
        try:
            payload = self.verify_token(token)
            if not payload:
                return None
            
            pensioner_id = int(payload.get("sub"))
            pensioner = db.query(Pensioner).filter(
                Pensioner.id == pensioner_id,
                Pensioner.is_active == True
            ).first()
            
            return pensioner
        except Exception as e:
            self.logger.error(f"❌ Error getting current pensioner: {e}")
            return None

# Global auth service instance
auth_service = AuthService()
