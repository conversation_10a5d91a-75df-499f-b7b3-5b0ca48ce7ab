#!/usr/bin/env python3
"""
Force the system to use local GGUF model and verify setup
Run this on your Ubuntu server
"""
import os
import sys
from pathlib import Path

def check_model_files():
    """Check what model files exist"""
    model_dir = "/home/<USER>/CPMS_CHATBOT/models/mistral-7b-instruct-v0.2.Q4_K_S"
    
    print(f"🔍 Checking model directory: {model_dir}")
    
    if not os.path.exists(model_dir):
        print("❌ Model directory does not exist!")
        return None
    
    # List all files
    files = []
    for item in os.listdir(model_dir):
        item_path = os.path.join(model_dir, item)
        if os.path.isfile(item_path):
            size_mb = os.path.getsize(item_path) / (1024 * 1024)
            files.append({
                'name': item,
                'path': item_path,
                'size_mb': size_mb
            })
    
    print(f"📁 Found {len(files)} files:")
    for file in files:
        print(f"  - {file['name']} ({file['size_mb']:.1f} MB)")
    
    # Find GGUF files
    gguf_files = [f for f in files if f['name'].endswith('.gguf')]
    
    if not gguf_files:
        print("❌ No .gguf files found!")
        return None
    
    # Return the largest GGUF file (most likely the main model)
    largest_gguf = max(gguf_files, key=lambda x: x['size_mb'])
    print(f"🎯 Using: {largest_gguf['name']} ({largest_gguf['size_mb']:.1f} MB)")
    
    return largest_gguf['path']

def update_config(model_path):
    """Update the configuration file"""
    config_file = "backend/config/config.py"
    
    if not os.path.exists(config_file):
        print(f"❌ Config file not found: {config_file}")
        return False
    
    # Read current config
    with open(config_file, 'r') as f:
        content = f.read()
    
    # Replace the model path
    old_line = 'MISTRAL_MODEL_PATH = "/home/<USER>/CPMS_CHATBOT/models/mistral-7b-instruct-v0.2.Q4_K_S/mistral-7b-instruct-v0.2.Q4_K_S.gguf"'
    new_line = f'MISTRAL_MODEL_PATH = "{model_path}"'
    
    if old_line in content:
        content = content.replace(old_line, new_line)
        print(f"✅ Updated config to use: {model_path}")
    else:
        print("⚠️ Could not find exact line to replace")
        print(f"💡 Manually set: MISTRAL_MODEL_PATH = \"{model_path}\"")
    
    # Write back
    with open(config_file, 'w') as f:
        f.write(content)
    
    return True

def set_environment_variable(model_path):
    """Set environment variable as backup"""
    print(f"🔧 Setting environment variable...")
    os.environ['MISTRAL_MODEL_PATH'] = model_path
    
    # Also create a script to set it permanently
    script_content = f"""#!/bin/bash
# Add this to your ~/.bashrc or run before starting the backend
export MISTRAL_MODEL_PATH="{model_path}"
echo "✅ Model path set to: $MISTRAL_MODEL_PATH"
"""
    
    with open("set_model_path.sh", "w") as f:
        f.write(script_content)
    
    os.chmod("set_model_path.sh", 0o755)
    print("✅ Created set_model_path.sh script")

def test_import():
    """Test if the configuration works"""
    print("🧪 Testing configuration...")
    
    try:
        sys.path.insert(0, 'backend')
        from config import config
        
        print(f"✅ Config loaded successfully")
        print(f"   Model path: {config.MISTRAL_MODEL_PATH}")
        
        if os.path.exists(config.MISTRAL_MODEL_PATH):
            print("✅ Model file exists at configured path")
            return True
        else:
            print("❌ Model file NOT found at configured path")
            return False
            
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Force Local Model Setup")
    print("=" * 30)
    
    # Step 1: Find the actual model file
    model_path = check_model_files()
    if not model_path:
        print("\n❌ No usable model found!")
        print("💡 Make sure the .gguf file is in the directory")
        return False
    
    # Step 2: Update configuration
    print(f"\n📝 Updating configuration...")
    update_config(model_path)
    
    # Step 3: Set environment variable as backup
    set_environment_variable(model_path)
    
    # Step 4: Test the configuration
    print(f"\n🧪 Testing setup...")
    if test_import():
        print("\n✅ Setup complete!")
        print("🚀 Your backend should now use the local model")
        print("\n📋 Next steps:")
        print("1. cd backend")
        print("2. python3 run_backend.py")
        print("\n⚠️ If it still downloads HuggingFace model:")
        print("   Run: source set_model_path.sh")
        print("   Then restart the backend")
    else:
        print("\n❌ Setup failed!")
        print("💡 Check the model file and try again")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Setup cancelled")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)
