"""
Database models for pensioner data and authentication
"""
from sqlalchemy import Column, Integer, String, DateTime, Float, Boolean, Text, Date, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime, date
from typing import Optional

Base = declarative_base()

class Pensioner(Base):
    """Main pensioner table with authentication and basic info"""
    __tablename__ = "pensioners"
    
    id = Column(Integer, primary_key=True, index=True)
    eppo_number = Column(String(20), unique=True, index=True, nullable=False)
    name = Column(String(100), nullable=False)
    mobile_number = Column(String(15), nullable=False)
    email = Column(String(100), nullable=True)
    date_of_birth = Column(Date, nullable=False)
    date_of_retirement = Column(Date, nullable=False)
    
    # Authentication fields
    is_active = Column(<PERSON>olean, default=True)
    last_login = Column(DateTime, nullable=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationships
    pension_details = relationship("PensionDetails", back_populates="pensioner", uselist=False)
    otp_sessions = relationship("OTPSession", back_populates="pensioner")
    query_logs = relationship("QueryLog", back_populates="pensioner")

class PensionDetails(Base):
    """Detailed pension information for each pensioner"""
    __tablename__ = "pension_details"
    
    id = Column(Integer, primary_key=True, index=True)
    pensioner_id = Column(Integer, ForeignKey("pensioners.id"), nullable=False)
    
    # LC (Life Certificate) Information
    lc_expiry_date = Column(Date, nullable=True)
    lc_last_submitted = Column(Date, nullable=True)
    lc_status = Column(String(20), default="pending")  # pending, submitted, verified
    
    # Pension Breakup Information
    basic_pension = Column(Float, nullable=False, default=0.0)
    da_amount = Column(Float, nullable=False, default=0.0)
    medical_allowance = Column(Float, nullable=False, default=0.0)
    additional_pension = Column(Float, nullable=False, default=0.0)
    total_pension = Column(Float, nullable=False, default=0.0)
    last_pension_month = Column(String(7), nullable=True)  # YYYY-MM format
    
    # Commutation Information
    commutation_amount = Column(Float, nullable=True)
    commutation_date = Column(Date, nullable=True)
    commutation_restoration_date = Column(Date, nullable=True)
    commutation_restored = Column(Boolean, default=False)
    
    # DA (Dearness Allowance) Information
    current_da_rate = Column(Float, nullable=False, default=0.0)
    da_effective_date = Column(Date, nullable=True)
    
    # Additional Pension Information
    additional_pension_rate = Column(Float, nullable=False, default=0.0)
    additional_pension_effective_date = Column(Date, nullable=True)
    
    # Family Pension Information
    normal_family_pension = Column(Float, nullable=False, default=0.0)
    enhanced_family_pension = Column(Float, nullable=False, default=0.0)
    family_pension_effective_date = Column(Date, nullable=True)
    
    # FMA (Fixed Medical Allowance) Information
    fma_amount = Column(Float, nullable=False, default=0.0)
    fma_effective_date = Column(Date, nullable=True)
    
    # Sampann Migration Information
    sampann_migration_date = Column(Date, nullable=True)
    sampann_migration_status = Column(String(20), default="pending")  # pending, completed, failed
    
    # Bank Details
    bank_name = Column(String(100), nullable=True)
    account_number = Column(String(20), nullable=True)
    ifsc_code = Column(String(11), nullable=True)
    
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationships
    pensioner = relationship("Pensioner", back_populates="pension_details")

class OTPSession(Base):
    """OTP sessions for authentication"""
    __tablename__ = "otp_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    pensioner_id = Column(Integer, ForeignKey("pensioners.id"), nullable=False)
    mobile_number = Column(String(15), nullable=False)
    otp_code = Column(String(6), nullable=False)
    is_verified = Column(Boolean, default=False)
    expires_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    
    # Relationships
    pensioner = relationship("Pensioner", back_populates="otp_sessions")

class QueryLog(Base):
    """Log of pensioner queries for audit and analytics"""
    __tablename__ = "query_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    pensioner_id = Column(Integer, ForeignKey("pensioners.id"), nullable=False)
    query_text = Column(Text, nullable=False)
    query_type = Column(String(50), nullable=False)  # lc_expiry, pension_breakup, etc.
    response_text = Column(Text, nullable=True)
    processing_time = Column(Float, nullable=True)
    session_id = Column(String(50), nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    created_at = Column(DateTime, server_default=func.now())
    
    # Relationships
    pensioner = relationship("Pensioner", back_populates="query_logs")

class SystemConfiguration(Base):
    """System-wide configuration for pension parameters"""
    __tablename__ = "system_configuration"
    
    id = Column(Integer, primary_key=True, index=True)
    config_key = Column(String(100), unique=True, nullable=False)
    config_value = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
