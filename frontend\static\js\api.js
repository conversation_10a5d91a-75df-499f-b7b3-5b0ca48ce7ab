/**
 * CPMS API Client
 * Handles all communication with the backend API
 */

class CPMSApiClient {
    constructor() {
        // Get API URL from environment or use default
        this.baseUrl = this.getApiBaseUrl();
        this.timeout = 150000; // 2.5 minutes timeout
        this.retryAttempts = 3;
        this.retryDelay = 1000; // 1 second
        
        // Connection status
        this.isConnected = false;
        this.connectionListeners = [];
        
        // Request queue for handling concurrent requests
        this.requestQueue = new Map();
        
        console.log(`🔌 CPMS API Client initialized with base URL: ${this.baseUrl}`);
    }
    
    /**
     * Get API base URL from configuration or environment
     */
    getApiBaseUrl() {
        // Try to get from window config first (can be set by server)
        if (window.CPMS_CONFIG && window.CPMS_CONFIG.apiUrl) {
            return window.CPMS_CONFIG.apiUrl;
        }
        
        // Default to localhost:8001
        return 'http://localhost:8001';
    }
    
    /**
     * Add connection status listener
     */
    onConnectionChange(callback) {
        this.connectionListeners.push(callback);
    }
    
    /**
     * Notify connection status change
     */
    notifyConnectionChange(status, message = '') {
        this.isConnected = status;
        this.connectionListeners.forEach(callback => {
            try {
                callback(status, message);
            } catch (error) {
                console.error('Error in connection listener:', error);
            }
        });
    }
    
    /**
     * Make HTTP request with retry logic
     */
    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const requestId = `${endpoint}-${Date.now()}`;
        
        // Default options
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            timeout: this.timeout,
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        // Add request to queue
        this.requestQueue.set(requestId, { url, options: finalOptions });
        
        let lastError;
        
        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                console.log(`🌐 API Request [${attempt}/${this.retryAttempts}]: ${finalOptions.method} ${url}`);
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), finalOptions.timeout);
                
                const response = await fetch(url, {
                    ...finalOptions,
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                // Remove from queue on success
                this.requestQueue.delete(requestId);
                
                // Update connection status
                if (!this.isConnected) {
                    this.notifyConnectionChange(true, 'Connected to backend API');
                }
                
                console.log(`✅ API Response: ${finalOptions.method} ${url}`, data);
                return data;
                
            } catch (error) {
                lastError = error;
                console.warn(`⚠️ API Request failed [${attempt}/${this.retryAttempts}]:`, error.message);
                
                // Update connection status on error
                if (this.isConnected) {
                    this.notifyConnectionChange(false, `Connection error: ${error.message}`);
                }
                
                // Don't retry on certain errors
                if (error.name === 'AbortError' || error.message.includes('404')) {
                    break;
                }
                
                // Wait before retry (except on last attempt)
                if (attempt < this.retryAttempts) {
                    await this.delay(this.retryDelay * attempt);
                }
            }
        }
        
        // Remove from queue on failure
        this.requestQueue.delete(requestId);
        
        throw new Error(`API request failed after ${this.retryAttempts} attempts: ${lastError.message}`);
    }
    
    /**
     * Utility function for delays
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * Health check endpoint
     */
    async healthCheck() {
        try {
            const response = await this.makeRequest('/health');
            return {
                success: true,
                data: response
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * Send chat query
     */
    async sendQuery(message, includeSources = true, modelType = null) {
        if (!message || !message.trim()) {
            throw new Error('Message cannot be empty');
        }
        
        const payload = {
            message: message.trim(),
            include_sources: includeSources
        };
        
        if (modelType) {
            payload.model_type = modelType;
        }
        
        try {
            const response = await this.makeRequest('/query', {
                method: 'POST',
                body: JSON.stringify(payload)
            });
            
            return {
                success: true,
                data: response
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * Get suggested questions
     */
    async getSuggestions() {
        try {
            const response = await this.makeRequest('/suggestions');
            return {
                success: true,
                data: Array.isArray(response) ? response : []
            };
        } catch (error) {
            console.warn('Failed to get suggestions:', error.message);
            return {
                success: false,
                error: error.message,
                data: this.getFallbackSuggestions()
            };
        }
    }
    
    /**
     * Get system statistics
     */
    async getSystemStats() {
        try {
            const response = await this.makeRequest('/stats');
            return {
                success: true,
                data: response
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * Clear system cache
     */
    async clearCache() {
        try {
            const response = await this.makeRequest('/clear-cache', {
                method: 'POST'
            });
            return {
                success: true,
                data: response
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * Get API root information
     */
    async getApiInfo() {
        try {
            const response = await this.makeRequest('/');
            return {
                success: true,
                data: response
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * Fallback suggestions when API is unavailable
     */
    getFallbackSuggestions() {
        return [
            "What is CPMS?",
            "How can I login to CPMS portal?",
            "When is pension credited?",
            "What is the helpline number?",
            "How to lodge a grievance?",
            "What documents are required for pension?",
            "How to check pension status?",
            "What are the pension schemes available?"
        ];
    }
    
    /**
     * Cancel all pending requests
     */
    cancelAllRequests() {
        console.log(`🚫 Cancelling ${this.requestQueue.size} pending requests`);
        this.requestQueue.clear();
    }
    
    /**
     * Get connection status
     */
    getConnectionStatus() {
        return {
            connected: this.isConnected,
            baseUrl: this.baseUrl,
            pendingRequests: this.requestQueue.size
        };
    }
}

// Create global API client instance
window.cpmsApi = new CPMSApiClient();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CPMSApiClient;
}
