{"title": "Comprehensive Pension Management System User Manual (Version 6.2)", "version": "6.2", "date": "Wednesday, August 7, 2019", "document_versions": [], "table_of_contents": [{"entry": "1.1 SAMPANN", "page": 11}, {"entry": "1.2 Users", "page": 11}, {"entry": "1.3 Launching& Logging into CPMS", "page": 11}, {"entry": "1.4 CPMS Dashboard and Administrator Role", "page": 12}, {"entry": "1.4.1 Administrator", "page": 13}, {"entry": "1.5 User Creation &Management", "page": 18}, {"entry": "1.5.1 Unit wise Login Verticals creation in CPMS", "page": 18}, {"entry": "1.6 System Administrator", "page": 18}, {"entry": "1.6.1 Administrator and CCA login", "page": 18}, {"entry": "1.7 Collection &Submission of Pension Papers", "page": 20}, {"entry": "1.7.1 For BSNL and DoT offices", "page": 20}, {"entry": "1.7.1.1 HoO Creation", "page": 20}, {"entry": "1.7.1.2 DH Creation", "page": 21}, {"entry": "1.8 Processing and Sanction of Pension", "page": 23}, {"entry": "1.8.1 AO Creation", "page": 23}, {"entry": "1.8.2 AAO Creation", "page": 25}, {"entry": "1.8.3 DH Creation", "page": 26}, {"entry": "1.9 Disbursement of Pensionary Benefits and Pension", "page": 28}, {"entry": "1.9.1 AO Creation", "page": 28}, {"entry": "1.9.2 AAO Creation", "page": 29}, {"entry": "1.9.3 DH Creation", "page": 31}, {"entry": "2. HoO Unit", "page": 34}, {"entry": "2.1 Normal Pension Case", "page": 34}, {"entry": "2.1.1 Creation of Retiree Profile", "page": 34}, {"entry": "2.1.2 Service Book Verification (12M BDR)", "page": 36}, {"entry": "2.1.3 Send Form to Retiree (8M BDR)", "page": 36}, {"entry": "2.1.4 Form Received (6M BDR)", "page": 38}, {"entry": "2.1.5 Form Verification (4M BDR)", "page": 39}, {"entry": "2.1.6 Form 7", "page": 40}, {"entry": "2.1.7 Form 8", "page": 44}, {"entry": "2.1.9 View Forms", "page": 45}, {"entry": "2.2 Family Pension Case", "page": 46}, {"entry": "2.2.1 Personal Detail", "page": 46}, {"entry": "2.2.2 Service Verification (Family Pension case)", "page": 50}, {"entry": "2.2.3 Form 14", "page": 51}, {"entry": "2.2.4 Form 12", "page": 54}, {"entry": "2.2.5 Form 18", "page": 55}, {"entry": "2.2.7 View Forms", "page": 58}, {"entry": "3. Pension Sanctioning Section.", "page": 59}, {"entry": "3.2 Form Received", "page": 59}, {"entry": "3.3 Pay Regulation", "page": 61}, {"entry": "3.4 Account Enfacement", "page": 62}, {"entry": "3.5 Revise Form List", "page": 64}, {"entry": "3.6 Calculation Sheet", "page": 68}, {"entry": "3.7 Pension Sanction", "page": 71}, {"entry": "4. PDA Section", "page": 74}, {"entry": "4.1 Allotment of Pension Case", "page": 74}, {"entry": "4.6 Monthly Bill", "page": 84}, {"entry": "5. <PERSON><PERSON><PERSON>", "page": 86}, {"entry": "5.2 Fill & Submit Forms", "page": 87}, {"entry": "5.3 Updation of Mobile, Email and address", "page": 94}, {"entry": "5.3.1 Mobile Number Update", "page": 95}, {"entry": "5.3.3 Address Update", "page": 97}, {"entry": "5.5 View documents and Ledger", "page": 99}, {"entry": "5.6. Income Tax Module", "page": 100}, {"entry": "5.6.1 Proposed Investment Declaration", "page": 100}, {"entry": "5.6.2 Actual Investment Declaration", "page": 105}, {"entry": "6.1 Pensioner Grievance", "page": 110}, {"entry": "6.3 Grievance Resolution", "page": 112}, {"entry": "8.1 Annexure 1 (Side Channel User Creation)", "page": 133}, {"entry": "8.2 HoO Unit- Side channel (Steps for Processing a Case)", "page": 136}, {"entry": "******* Creation of Retiree Profile", "page": 137}, {"entry": "******* Service Book Verification", "page": 138}, {"entry": "******* Send Form to Retiree", "page": 139}, {"entry": "******* Form Received", "page": 140}, {"entry": "******* Form Verification", "page": 141}, {"entry": "******* Form 7", "page": 141}, {"entry": "******* Form 8", "page": 144}, {"entry": "******* View Forms", "page": 145}, {"entry": "8.2.2 Family Pension Case", "page": 145}, {"entry": "8.2.2.1 Personal Detail", "page": 145}, {"entry": "8.2.2.2 Service Verification (Family Pension case)", "page": 149}, {"entry": "8.2.2.3 Form 14", "page": 150}, {"entry": "8.2.2.4 Form 12", "page": 153}, {"entry": "8.2.2.5 Form 18", "page": 154}, {"entry": "8.2.2.7 View Forms", "page": 157}, {"entry": "9. ID Card Generation", "page": 158}, {"entry": "9.1 Upload AO Pension signature", "page": 158}, {"entry": "9.2 Generate ID Card", "page": 159}, {"entry": "10. Revision", "page": 161}, {"entry": "10.1 Revision in the rate of DA", "page": 161}, {"entry": "10.2 Revision Due to Withheld Amount", "page": 167}, {"entry": "10.3 Revision on account of Pay revision/Court Order", "page": 173}, {"entry": "10.4 Revision of Pension to FP (No eligible family member mentioned in PPO)", "page": 180}, {"entry": "10.5 Revision of Pension to FP (Eligible family member mentioned in PPO)", "page": 188}, {"entry": "11.1 Retiree Profile Authentication", "page": 195}, {"entry": "13. Bill / Payment slip generation", "page": 202}, {"entry": "14. Updation of Mobile, Email and address", "page": 204}, {"entry": "14.1 Mobile Number Update", "page": 205}, {"entry": "14.2 Email ID Update", "page": 206}, {"entry": "15. Other pension Types", "page": 208}, {"entry": "15.2 Compassionate Allowance", "page": 212}, {"entry": "15.3 Miscellaneous issues", "page": 214}, {"entry": "16. Income Tax Processing", "page": 215}], "chapters": [{"number": 1, "title": null, "sections": [{"number": "1.1", "title": "SAMPANN .......................................................................................................................... 11", "content": []}, {"number": "1.2", "title": "Users .................................................................................................................................. 11", "content": []}, {"number": "1.3", "title": "Launching& Logging into CPMS ......................................................................................... 11", "content": []}, {"number": "1.4", "title": "CPMS Dashboard and Administrator Role ......................................................................... 12", "content": []}, {"number": "1.4", "title": null, "content": []}, {"number": "1.5", "title": "User Creation &Management ............................................................................................ 18", "content": []}, {"number": "1.5", "title": null, "content": []}, {"number": "1.6", "title": "System Administrator ........................................................................................................ 18", "content": []}, {"number": "1.6", "title": null, "content": []}, {"number": "1.7", "title": "Collection &Submission of Pension Papers ....................................................................... 20", "content": []}, {"number": "1.7", "title": null, "content": []}, {"number": "1.7", "title": null, "content": []}, {"number": "1.7", "title": null, "content": []}, {"number": "1.8", "title": "Processing and Sanction of Pension .................................................................................. 23", "content": []}, {"number": "1.8", "title": null, "content": []}, {"number": "1.8", "title": null, "content": []}, {"number": "1.8", "title": null, "content": []}, {"number": "1.9", "title": "Disbursement of Pensionary Benefits and Pension ........................................................... 28", "content": []}, {"number": "1.9", "title": null, "content": []}, {"number": "1.9", "title": null, "content": []}, {"number": "1.9", "title": null, "content": []}]}, {"number": 2, "title": null, "sections": [{"number": "2.1", "title": "Normal Pension Case ......................................................................................................... 34", "content": []}, {"number": "2.1", "title": null, "content": []}, {"number": "2.1", "title": null, "content": []}, {"number": "2.1", "title": null, "content": []}, {"number": "2.1", "title": null, "content": []}, {"number": "2.1", "title": null, "content": []}, {"number": "2.1", "title": null, "content": []}, {"number": "2.1", "title": null, "content": []}, {"number": "2.1", "title": null, "content": []}, {"number": "2.1", "title": null, "content": []}, {"number": "2.2", "title": "Family Pension Case ........................................................................................................... 46", "content": []}, {"number": "2.2", "title": null, "content": []}, {"number": "2.2", "title": null, "content": []}, {"number": "2.2", "title": null, "content": []}, {"number": "2.2", "title": null, "content": []}, {"number": "2.2", "title": null, "content": []}, {"number": "2.2", "title": null, "content": []}, {"number": "2.2", "title": null, "content": []}]}, {"number": 3, "title": null, "sections": [{"number": "3.1", "title": "Allotment of Pension Case to DH....................................................................................... 59", "content": []}, {"number": "3.2", "title": "Form Received ................................................................................................................... 59", "content": []}, {"number": "3.3", "title": "Pay Regulation ................................................................................................................... 61", "content": []}, {"number": "3.4", "title": "Account Enfacement .......................................................................................................... 62", "content": []}, {"number": "3.5", "title": "Revise Form List ................................................................................................................. 64", "content": []}, {"number": "3.6", "title": "Calculation Sheet ............................................................................................................... 68", "content": []}, {"number": "3.7", "title": "Pension Sanction ................................................................................................................ 71", "content": []}]}, {"number": 4, "title": null, "sections": [{"number": "4.1", "title": "Allotment of Pension Case ................................................................................................. 74", "content": []}, {"number": "4.2", "title": null, "content": []}, {"number": "4.3", "title": null, "content": []}, {"number": "4.4", "title": null, "content": ["Comprehensive Pension Management System User Manual (Version 6.2)", "5"]}, {"number": "4.5", "title": "Arrears................................................................................................................................ 81", "content": []}, {"number": "4.6", "title": "Monthly Bill ........................................................................................................................ 84", "content": []}]}, {"number": 5, "title": null, "sections": [{"number": "5.1", "title": null, "content": []}, {"number": "5.2", "title": "Fill & Submit Forms ............................................................................................................ 87", "content": []}, {"number": "5.2", "title": null, "content": []}, {"number": "5.3", "title": "Updation of Mobile, Email and address ............................................................................ 94", "content": []}, {"number": "5.3", "title": null, "content": []}, {"number": "5.3", "title": null, "content": []}, {"number": "5.3", "title": null, "content": []}, {"number": "5.4", "title": "Lodge Grievance................................................................................................................ 98", "content": []}, {"number": "5.5", "title": "View documents and Ledger ............................................................................................. 99", "content": []}, {"number": "5.6", "title": null, "content": []}, {"number": "5.6", "title": null, "content": []}, {"number": "5.6", "title": null, "content": []}]}, {"number": 6, "title": null, "sections": [{"number": "6.1", "title": "Pensioner Grievance ........................................................................................................ 110", "content": []}, {"number": "6.2", "title": "Allotment of Grievance to DH.......................................................................................... 111", "content": []}, {"number": "6.3", "title": "Grievance Resolution ....................................................................................................... 112", "content": []}]}, {"number": 7, "title": null, "sections": []}, {"number": 8, "title": null, "sections": [{"number": "8.1", "title": "Annexure 1 (Side Channel User Creation) ....................................................................... 133", "content": []}, {"number": "8.2", "title": "HoO Unit- Side channel (Steps for Processing a Case) .................................................... 136", "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": ["Comprehensive Pension Management System User Manual (Version 6.2)", "6"]}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": ["Chapter 9................................................................................................................................ 158", "9. ID Card Generation ............................................................................................................ 158"]}, {"number": "9.1", "title": "Upload AO Pension signature .......................................................................................... 158", "content": []}, {"number": "9.2", "title": "Generate ID Card ............................................................................................................. 159", "content": ["Chapter 10.............................................................................................................................. 161", "10. Revision ............................................................................................................................ 161"]}, {"number": "10.1", "title": "Revision in the rate of DA .............................................................................................. 161", "content": []}, {"number": "10.2", "title": "Revision Due to Withheld Amount ................................................................................ 167", "content": []}, {"number": "10.3", "title": "Revision on account of Pay revision/Court Order ......................................................... 173", "content": []}, {"number": "10.4", "title": "Revision of Pension to FP (No eligible family member mentioned in PPO) .................. 180", "content": []}, {"number": "10.5", "title": "Revision of Pension to FP (Eligible family member mentioned in PPO) ....................... 188", "content": ["Chapter 11.............................................................................................................................. 195", "11.Profile Authentication ....................................................................................................... 195"]}, {"number": "11.1", "title": "Retiree Profile Authentication ....................................................................................... 195", "content": ["Chapter 12.............................................................................................................................. 199", "12. Upload utility.................................................................................................................... 199", "Chapter 13.............................................................................................................................. 202", "13. Bill / Payment slip generation .......................................................................................... 202", "Chapter 14.............................................................................................................................. 204", "14. Updation of Mobile, Email and address .......................................................................... 204"]}, {"number": "14.1", "title": "Mobile Number Update ................................................................................................. 205", "content": []}, {"number": "14.2", "title": "Email ID Update ............................................................................................................. 206", "content": []}, {"number": "14.3", "title": "Address Update.............................................................................................................. 207", "content": ["Comprehensive Pension Management System User Manual (Version 6.2)", "7", "Chapter 15.............................................................................................................................. 208", "15. Other pension Types ........................................................................................................ 208"]}, {"number": "15.1", "title": "Compulsory retirement.................................................................................................. 209", "content": []}, {"number": "15.2", "title": "Compassionate Allowance ............................................................................................. 212", "content": []}, {"number": "15.3", "title": "Miscellaneous issues ...................................................................................................... 214", "content": ["Chapter 16.............................................................................................................................. 215", "16. Income Tax Processing ..................................................................................................... 215", "Comprehensive Pension Management System User Manual (Version 6.2)", "8", "Definitions and Acronyms", "Abbreviation / Term Description", "AAO Assistant Account officer", "ACCA Assistant Controller of Communication Accounts", "AE Account Enfacement", "AO Accounts Officer", "CCA Controller of Communication Accounts", "CGCA Controller General of Communication Accounts", "DH Dealing Hand", "DSC Digital Signature Certificate", "HoO Head of Office", "LPD Last Pay Drawn", "M / BDR Month / Before Date of Retirement", "PAO Pay & Accounts Office", "PDA Pension Disbursement Authority", "PFMS Public Financial Management System", "Comprehensive Pension Management System User Manual (Version 6.2)", "9", "What’s New", "What’s New in Version 3.0", " Instruction for DH PDA amended in PDA module, relating to sending EPPO and", "Sanction hard copies to different sections (Page 73, sub heading 4.1).", " Instructions for AO Cash/PFMS amended in PDA module, relating to payment from", "PFMS (Page 80, sub heading 4.4).", " Instructions for creation of HoO and DH amended, relating to role assignment to", "officers (Page 15, sub heading 1.5.1).", " Instructions for filling Form 7 (Page 43, sub heading 2.1.6), Form 18 (Page 56, sub", "heading 2.2.5) and editing calculation sheet on (Page 67, sub heading 3.5).", " Instructions for filling bank details in Form 14 amended (Page 52, sub heading 2.2.3).", " Instructions for filling bank details in Form 12 amended (Page 53, sub heading 2.2.4).", " Instructions for filling bank details in Pensioner profile amended (Page 91, sub heading"]}, {"number": "5.2", "title": null, "content": [" Instructions for AO pension section (after DSC) amended, relating to enclosures to be", "forwarded to AO PDA (<PERSON> 72, sub heading 3.7).", " Instructions in Vendor verification of PDA section amended, relating to PDA Utility tab", "(Page 74, sub heading 4.2).", " Arrear Section (Page 82, sub heading 4.5)", " Manual for DSC installation (Page 97 onwards).", "What’s New in Version 4.0", " Reference for Side Channel creation in Annexure 1 (Page 17, sub heading 1.7.1.1).", " Instructions for filling Retiree Profile (Page 32, sub heading 2.1.1).", " Instructions for correction needed in Form 7 and Form 8(Page 42, sub heading 2.1.7).", " Instructions for filling Form 12 (Page 51,52, sub heading 2.2.4).", " Instructions while allot case to DH by AO Pension (Page 56, sub heading 3.1).", " Instructions for AE Generated in Pension Section (Page 61, sub heading 3.4).", " Instructions for filling Pension Sanction (Page 68, sub heading 3.7).", " Instructions for filling Monthly Bill (Page 82, sub heading 4.5).", " Annexure 1 for Side Channel User creation (Page 117 onwards).", "What’s New in Version 5.0", " Reference for ID Card Generation for CDA Pensioners (Page 142, sub heading 9.1)", " Instructions for taking print of ID Card (Page 144, sub heading 9.2)", "What’s New in Version 6.0", " Addition of 10. Revision of Pension Module (Page 146, sub heading 10)", " Addition of 11. Profile Authentication (Page 34 , 124 and 176 )", "Comprehensive Pension Management System User Manual (Version 6.2)", "10", " Addition of chapter 12. Upload utility to chapter 15. Other Pension Types", " Addition of 5.3 to 5.5 in Retiree Module", "What’s New in Version 6.2", " Addition in chapter 5- 5.6 Income Tax Module", " Addition in Chapter 16. Income Tax Processing", "Comprehensive Pension Management System User Manual (Version 6.2)", "11"]}]}, {"number": 1, "title": null, "sections": [{"number": "1.1", "title": "SAMPANN", "content": ["SAMPANN(System for Accounting and Management of Pension) is brand Name for", "Comprehensive Pension Management System( CPMS) ,a web portal for Pension Processing,", "Direct Disbursement, Accounting and Auditing of Pension and Pensionary Benefits to", "Pensioners of Department of Telecommunication. It has been designed with the following", "objectives:", " Direct Credit of pensionary benefits and pension to Pensioners’ Bank Account.", " Bringing greater transparency in processing of pensions.", " Reducing delays in disbursement of pension and arrears of pension.", " Digitization of forms and streamlining in HoO and CCA offices to reduce time and", "effort.", " Optimum utilization of resources in processing and disbursement of pension.", " Providing timely updates and alerts to the Pensioners.", " Creating a platform for direct interaction with pensioners for serving them better.", " Improving the quality of service through consistent and standardized work programs.", " Creating a faster and more responsive online grievance registration and redressal", "system.", " Providing real time MIS to CCA and senior officers of DoT and Controller General of", "Communication Accounts (CGCA)."]}, {"number": "1.2", "title": "Users", "content": ["This User Manual has been designed for the needs of different users of the application. The", "target users are listed below –", " CCA offices", " Other DoT field units", " BSNL field units", " Retiring officers/officials", " Pensioner (including Family Pensioners)", " MIS User"]}, {"number": "1.3", "title": "Launching& Logging into CPMS", "content": ["Users have to take following steps in order to login in to CPMS application.", "1. Enter URL www.dotpension.gov.in in web browser* to go to login screen.", "(*the preferred web browser for CPMS use is Google Chrome and for DSC, it is", "Internet Explorer Version 9 or 11).", "2. Enter User Name.", "Comprehensive Pension Management System User Manual (Version 6.2)", "12", "3. <PERSON><PERSON> Password.", "4. <PERSON><PERSON>.", "5. Click on Login button."]}, {"number": "1.4", "title": "CPMS Dashboard and Administrator Role", "content": ["Upon successful logging into CPMS, user will land on the home screen. For consistency, the", "panel on the left shows the <PERSON><PERSON> options for selection depending upon the work involved.", "Menu is arranged as per the sequence of operation and the frequency of usage depending", "upon the type of user logged in. (Fig 1.0, 1.1)", "Once the selection is made, the information is displayed in tabular form. The top right corner", "of the screen will show the User (logged in) profile and photograph, if available. The logout", "option is next to the user detail at the top right.", "Comprehensive Pension Management System User Manual (Version 6.2)", "13", "Fig 1.0", "Fig 1.1"]}, {"number": "1.4", "title": null, "content": ["Administrator logins screen will show the following options to work with. (Fig 1.1.1)", "Comprehensive Pension Management System User Manual (Version 6.2)", "14", "Fig 1.1.1", "Masters: This allows the Administrator User to manage the master data of different modules", "of CPMS. (Fig 1.1.2)", "Fig 1.1.2", "Users: User section consists of user roles, access rights, user registration and DSC approval. It", "allows the Administrator User to assign and modify roles, access rights and grant DSC approval", "to different users. (Fig 1.1.3)", "Comprehensive Pension Management System User Manual (Version 6.2)", "15", "Fig 1.1.3", "Role Master: Administrator User can see the different roles available in the Role Master and", "can also add a new role (Fig 1.1.4)", " Administrator Users Role Master", "Fig 1.1.4", "Role Rights: Administrator User can assign the access rights to the available roles (created by", "admin) (Fig 1.1.5)", " Administrator Users Role Rights Click on Action icon of the respective roles", "to give the access rights.", " Administrator User can select the respective role from the dropdown and assign the", "access of modules to it by clicking on check boxes next to the module name. Fig"]}, {"number": "1.1", "title": null, "content": [" Administrator User will click on Submit button.", "Comprehensive Pension Management System User Manual (Version 6.2)", "16", "Fig 1.1.5", "Fig 1.1.5 (a)", "User Registration: It shows the list of all the existing users of the application along with the", "details. Administrator User can also add new user from this screen. (Fig 1.1.6)", "Comprehensive Pension Management System User Manual (Version 6.2)", "17", "Fig 1.1.6", "Reports: This allows the Administrator User to generate the reports as per requirements. (Fig"]}, {"number": "1.1", "title": null, "content": ["Fig 1.1.7", "Audit Trail: This allows the Administrator User to see the workflows and audit trail as per the", "activities performed. (Fig 1.1.8)", "Comprehensive Pension Management System User Manual (Version 6.2)", "18", "Fig 1.1.8"]}, {"number": "1.5", "title": "User Creation &Management", "content": []}, {"number": "1.5", "title": null, "content": ["The CCA roles will be created by the System Administrator beforehand. It may be noted that", "the entire work under CPMS has been segmented into four broad parts as follows:", "1. System Administrator.", "2. Collection and submission of Pension papers. The HoO and DH terms are used for CCA", "offices/BSNL offices/Other DOT units for Head of that particular office and Dealing", "hand. It is recommended <PERSON><PERSON> should be any officer of the rank of AAO or AO and DH", "should be officer of the rank below AAO (Sr. Acct./Jr. Acct.).", "3. Processing and sanction of Pension.", "4. Disbursement of pensionary benefits and monthly pension."]}, {"number": "1.6", "title": "System Administrator", "content": []}, {"number": "1.6", "title": null, "content": ["a) Administrator User will create CCA user name/password for login.", "b) CCA or any officer nominated by CCA shall be the highest authority in CCA office for", "login in CPMS.", "c) Path to create the CCA user: Login as Administrator  UsersUser Registration", "Click on “ADD NEW” (fill the user detail and save it).(Fig 1.3)", "Comprehensive Pension Management System User Manual (Version 6.2)", "19", "Fig 1.3", "d) In next step Administrator User will assign roles to CCA.", "e) Path to assign the CCA Role and Rights: Login as Administrator  Users Role", "Master Click on “ADD NEW” (fill the user detail and save it).(Fig. 1.4&1.5)", "Fig 1.4", "Comprehensive Pension Management System User Manual (Version 6.2)", "20", "Fig 1.5"]}, {"number": "1.7", "title": "Collection &Submission of Pension Papers", "content": []}, {"number": "1.7", "title": null, "content": []}, {"number": "1.7", "title": null, "content": ["a) CCA will create the login of HoO.", "b) Ho<PERSON> is the second level login after CCA.", "c) Path to create the HoO User Login: Login as CCA Users User Registration (Select", "the Role Type as HoO, fill the user detail and save it).(Fig 1.6 (A))", "d) For side channel (Telecom Circle) HoO creation, please refer Annexure 1.", "e) After creating the login, CCA will assign the authorization of modules to HoO by", "clicking on the Lock icon in the authorization column. (Fig 1.6 (i) (A))", "f) CCA will subsequently assign rights. (Fig. 1.6 (i)(A))", "g) CCA will enter/edit details of the HoO. (Fig 1.7 (A))", "Fig 1.6 (A)", "Comprehensive Pension Management System User Manual (Version 6.2)", "21", "Assign Access Rights Fig 1.6 (i) (A)", "Edit user details Fig 1.7 (A)"]}, {"number": "1.7", "title": null, "content": ["a) DH is the third level login and subordinate to HoO.", "b) HoO will create the login for DH.", "Comprehensive Pension Management System User Manual (Version 6.2)", "22", "c) Path to create the DH User Login: Login as HoO Users User Registration (Select", "the Role Type as DH, fill the user detail and save it). (Fig 1.8 (A) and 1.9 (A))", "d) After creating the login, HoO will assign the authorization of modules to DH by clicking", "on the Lock icon in the authorization column. Fig 1.9 (i) (A)", "Fig 1.8 (A)", "Edit user details Fig 1.9 (A)", "Comprehensive Pension Management System User Manual (Version 6.2)", "23", "Assign Access Rights Fig 1.9 (i) (A)"]}, {"number": "1.8", "title": "Processing and Sanction of Pension", "content": []}, {"number": "1.8", "title": null, "content": ["a) AO is the second level login and subordinate to CCA in Pension Section.", "b) CCA will create the login for AO to work in Pension Section.", "c) Path to create the AO User Login: Login as CCA Users User Registration (Select", "the Role Type as AO, fill the user detail and save it). (Fig 1.10 and 1.11)", "d) After creating the login, CCA will assign the authorization of modules to AO by clicking", "on Lock icon in the authorization column. Fig 1.11 (a)", "e) CCA will subsequently assign rights. (Fig 1.11 (a))", "f) Multiple AO Users can be created.", "Comprehensive Pension Management System User Manual (Version 6.2)", "24", "Fig 1.10", "Edit user details Fig 1.11", "Comprehensive Pension Management System User Manual (Version 6.2)", "25", "Fig 1.11 (a)"]}, {"number": "1.8", "title": null, "content": ["a) AAO is the third level login and subordinate to AO in Pension Section.", "b) AO will create the login for AAO to work in Pension Section. (Fig 1.12 and 1.13)", "c) Path to create the AAO User Login: Login as AO Users User Registration (Select", "the Role Type as AAO, fill the user detail and save it). (Fig 1.12 and 1.13)", "d) After creating the login, AO will assign the authorization of modules to AAO by clicking", "on Lock icon in the authorization column. Fig 1.13 (a)", "e) AO will subsequently assign rights. (Fig 1.13 (a))", "f) Multiple AAO Users can be created.", "Fig 1.12", "Comprehensive Pension Management System User Manual (Version 6.2)", "26", "Edit user details (Fig 1.13)", "Assign Access Rights Fig 1.13 (a)"]}, {"number": "1.8", "title": null, "content": ["a) DH is the third level login and subordinate to AAO.", "b) AAO will create the login for DH.", "c) Path to create the DH User Login: Login as AAO Users User Registration (Select", "the Role Type as DH, fill the user detail and save it). (Fig 1.14 and 1.15)", "d) After creating the login, AAO will assign the authorization of modules to DH by clicking", "on Lock icon in the authorization column. Fig 1.15 (i)", "e) Multiple DH Users can be created.", "Comprehensive Pension Management System User Manual (Version 6.2)", "27", "Fig 1.14", "Edit user details Fig 1.15", "Comprehensive Pension Management System User Manual (Version 6.2)", "28", "Assign Access Rights Fig 1.15 (i)"]}, {"number": "1.9", "title": "Disbursement of Pensionary Benefits and", "content": ["Pension"]}, {"number": "1.9", "title": null, "content": ["a) AO is the second level login and subordinate to CCA in Pension Disbursement Section.", "b) CCA will create the login for AO to work in Pension Disbursement Section.", "c) Path to create the AO User Login: Login as CCA Users User Registration (Select", "the Role Type as AO, fill the user detail and save it). (Fig 1.16 and 1.17)", "d) After creating the login, CCA will assign the authorization of modules to AO by clicking", "on Lock icon in the authorization column. Fig 1.17 (i)", "e) CCA will subsequently assign rights. (Fig 1.17 (i))", "f) Multiple AO Users can be created.", "Fig 1.16", "Comprehensive Pension Management System User Manual (Version 6.2)", "29", "Edit user details Fig (1.17)", "Assign Access Rights Fig 1.17 (i)"]}, {"number": "1.9", "title": null, "content": ["a) AAO is the third level login and subordinate to AO in Pension Disbursement Section.", "b) AO will create the login for AAO to work in Pension Disbursement Section. (Fig 1.18", "and 1.19)", "c) Path to create the AAO User Login: Login as AO Users User Registration (Select", "the Role Type as AAO, fill the user detail and save it). (Fig 1.18 and 1.19)", "d) After creating the login, AO will assign the authorization of modules to AAO by clicking", "on Lock icon in the authorization column. Fig 1.19 (i)", "e) AO will subsequently assign rights. (Fig 1.19 (i))", "Comprehensive Pension Management System User Manual (Version 6.2)", "30", "f) Multiple AAO Users can be created.", "(Fig 1.18)", "Edit user details (Fig 1.19)", "Comprehensive Pension Management System User Manual (Version 6.2)", "31", "Assign Access Rights Fig 1.19 (i)"]}, {"number": "1.9", "title": null, "content": ["a) DH is the third level login and subordinate to AAO in Pension Disbursement Section.", "b) AAO will create the login for DH.", "c) Path to create the DH User Login: Login as AAO Users User Registration (Select", "the Role Type as DH, fill the user detail and save it). (Fig 1.20 and 1.21)", "d) After creating the login, AAO will assign the authorization of modules to DH by clicking", "on lock icon in authorization column. Fig 1.21 (i)", "e) Multiple DH Users can be created.", "(Fig 1.20)", "Comprehensive Pension Management System User Manual (Version 6.2)", "32", "Edit user details (Fig 1.21)", "Assign Access Rights Fig (1.21) (i)", "Important Points to Remember", " Administrator cannot create the login for any other User except CCA.", " CCA cannot create login for any other user except HoO, AO(Pen) and AO(PDA)", " HoO cannot create login for any other user except DH.", " AO cannot create login for any other user except AAO.", " AAO cannot create login for any other user except DH.", " In CPMS, each user is uniquely identified with a username, password, role and rights", "alongside other settings. The role and rights determine which tasks a user can perform,", "what data user can see, and how to perform the required activities using the available", "data.", "Comprehensive Pension Management System User Manual (Version 6.2)", "33", "Additional Key Points", "1. On first login, all Users shall be prompted to update their profiles and change", "passwords.", "2. In case of transfer of personnel or for any updating of profile information, the new", "details can be incorporated by writing to the Authority who created the user.", "3. It is important that user creation is immediately followed by grant of authorisation", "rights, otherwise the user will find an empty screen.", "4. CCA user must check the list of HoO available for creation. If he finds any error in", "name, it should be immediately brought to the notice of Administrator by writing to", "<NAME_EMAIL>.", "Comprehensive Pension Management System User Manual (Version 6.2)", "34"]}]}, {"number": 2, "title": null, "sections": [{"number": "2.1", "title": "Normal Pension Case", "content": []}, {"number": "2.1", "title": null, "content": [" DH to login to create the retiree profile.", " Select Retiree details on the Menu options available on the left pane.", " Click on “Add New” button at the right top corner of Retiree Profile page to get the", "form to fill.", " Enter the following information on retiree detail form (underlined parameters are the", "mandatory fields):", "a. Title", "b. First Name", "c. Middle Name", "d. Last Name", "e. Type of Retirement", "f. Height", "g. Father’s/ Husband’s Name.", "h. <PERSON>’s Name", "i. Date of Birth", "j. Gender", "k. <PERSON><PERSON><PERSON><PERSON>", "l. PAN Number", "m. Mobile Number (This should be an Active Number)", "n. Email ID", "o. Identification Mark 1", "p. Identification Mark 2", " Following details to be filled in other details section (All are mandatory fields):", "a. Designation", "b. Group", "c. Date of Joining", "d. Date of Retirement", "e. Date of Superannuation", "f. Govt. Accommodation provided by Directorate of Estates/BSNL at any time", "of service: YES/ NO (to select from the dropdown)", "Comprehensive Pension Management System User Manual (Version 6.2)", "35", "g. Pay Commission (this should be the Pay Commission corresponding to the last", "pay drawn)", "*After filling the aforementioned information, DH to click on Save button. By using the Clear", "button, User can also refresh the form and fill in fresh information. (Fig 2.1)", "IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore,", "due diligence should be exercised while creating the Retiree Profile.", "In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk.", "Fig 2.1", "Creation of the Retiree Profile is the most important step in the process, so it should be", "ensured entries made therein are correct in all respects. After saving the information, it is", "recommended that a printout of the page be taken from the screen and Vetted by HoO level", "User before proceeding ahead in Service Verification. If any error is detected, then retiree", "profile be edited before initiating 2.1.2(Further updates in chapter 11)", "Comprehensive Pension Management System User Manual (Version 6.2)", "36"]}, {"number": "2.1", "title": null, "content": [" DH to re-check the form and send it for approval to HoO for service book verification.", " HoO to login and approve the service book verification. After HoO approves the form,", "the form will appear in ‘Send Form to Retiree’ tab.", " HoO can also return the form to DH in case any discrepancy is found. Then DH will", "have to again verify and send the form to HoO for approval. (Fig 2.2)", " Before feeding Service Book information and updating, it has to be", "ensured that the Service Book is actually verified for the period", "mentioned. If any period remains unverified, please mention it in", "Remarks and keep a printout in the pension file. Service Book", "verification should be completed at the earliest.", " This activity has to be completed 12 months before date of retirement.", "Fig 2.2"]}, {"number": "2.1", "title": null, "content": [" DH to send the form for approval to HoO. (Fig 2.3)", " HoO to login and approve the form. HoO can also return the form to DH by clicking", "on Return button in case any discrepancy has been found. (Fig 2.4)", " After the approval form HoO, form will be sent to Retiree to fill the required", "information.", "Comprehensive Pension Management System User Manual (Version 6.2)", "37", " This activity has to be completed by 8 months before date of", "retirement.", "Fig 2.3", "Fig 2.4", "Comprehensive Pension Management System User Manual (Version 6.2)", "38"]}, {"number": "2.1", "title": null, "content": [" DH to receive the form after retiree fills and submits the form from his/her end (Fig"]}, {"number": "2.5", "title": null, "content": [" DH to check that the form received is correctly filled and that all scanned documents", "uploaded are of good quality. Then on receipt of hard copy (duly signed by the", "Retiree), DH may cross verify the details and also check whether all enclosures (as", "per checklist) have been duly attached. Then DH shall send it to HoO.", " HoO to Approve/ Return the form as applicable. (Fig 2.6)", " If HoO approves the form, it will appear in ‘Form Verification’ tab of DH User. In case", "HoO returns the form, it goes back to DH for re-verification. If now, any error is", "detected by DH, then file has to be returned to retiree with remarks. <PERSON><PERSON><PERSON> shall", "correct the error and resubmit the papers.", "Fig 2.5", "Comprehensive Pension Management System User Manual (Version 6.2)", "39", "Fig 2.6"]}, {"number": "2.1", "title": null, "content": [" This process has been especially incorporated to put another check on correctness", "of the forms submitted by Re<PERSON>ree.", " DH to check and verify the form and send it for the approval to HoO by clicking on", "“Verify<PERSON> (Fig 2.7)", " HoO to Approve/ Return the form as applicable. (Fig 2.8)", " If HoO approves the form, it will appear in ‘Form 7’ tab of DH. In case the HoO returns", "the form, it goes back to DH for re-verification.", " Simultaneously, the forms/papers will be countersigned by the competent authority", "in the physical file and process for preparation of Form 7 initiated.", "Comprehensive Pension Management System User Manual (Version 6.2)", "40", "Fig2.7", "Fig 2.8"]}, {"number": "2.1", "title": null, "content": [" DH to verify and fill the required information in Form 7.", " Some information in Form 7 is auto populated. Others have to be entered. (Fig 2.9)", "a) Name of the retiring Government Employee", "Comprehensive Pension Management System User Manual (Version 6.2)", "41", "b) Father's/Husband's Name", "c) PAN NO.", "d) Height & Marks of identification", "e) Date of Birth", "f) Service to which he/she belongs (indicate name of Organised service, if any,", "otherwise say General Central Service)", "g) Particulars of post held at the time of retirement", "h) Name of the office", "i) Post held", "j) Scale of Pay/Pay Band & Grade pay of the post", "k) Basic Pay/Pay in the pay band & Grade pay.", "l) Basic Pay/Pay in the pay band & Grade pay", "m) Whether the appointment mentioned above was under Government or", "outside the Government on foreign service terms?", "n) If on foreign service, scale of pay/pay band, pay in the pay band and grade pay", "of the post in the parent department.", "o) Whether declared substantive in any Post under the Central Govt.?", "p) Date of beginning of service", "q) Date of ending service", "r) Cause of ending service", "s) In case of compulsory retirement, the orders of the competent authority", "whether pension may be allowed at full rates or at reduced rates and in case", "of reduced rates, the percentage at which it is to be allowed (Please See Rule", "41)", "t) In case of removal/dismissal from service whether orders of the competent", "authority have been obtained for grant of compassionate allowance and if so,", "at what rate (Please see Rule 41)", "u) Particulars relating to military service, if any.", "v) Particulars relating to the service in autonomous body, if any.", "w) Whether any Departmental or judicial proceedings in terms of rule 9 of the", "CCS(Pension) Rules, 1972 are pending against the retiring employee. (lf yes, in", "terms of Rule 69, provisional pension will be admissible and gratuity will be", "withheld till the conclusion of departmental or judicial proceedings and issue", "of final orders)", "x) Length of service", "i. Details of omission, imperfection or deficiencies in the Service Book", "which have been ignored [under rules 59(1) (b) (ii)]", "ii. Period not counting as qualifying service.", "iii. Additions to qualifying Service.", "iv. Whether any leave without pay.", "v. Net Qualifying service.", "Comprehensive Pension Management System User Manual (Version 6.2)", "42", "vi. Qualifying service expressed in terms of complete six monthly periods", "(Period of three months and above is to be treated as completed six", "monthly periods (Rule 49)", "y) Emoluments", "a. Emoluments in terms of Rule33.", "b. Emoluments drawn during ten months pending retirement.", "z) Others: Details of Govt. dues recoverable out of gratuity", "aa) Licence fee for Govt. accommodation see sub-rules (2), (3) and (4) of rule", "2)", "bb) Dues referred to in Rule 73", "cc) Amount indicated by Directorate of Estates to be withheld under", "Sub rule(S) of Rule 72", "dd) Post-retirement address of the retiree", "Comprehensive Pension Management System User Manual (Version 6.2)", "43", "Fig 2.9", " After the verification, DH will click on tab “Submit and Calculate” to calculate", "the pensionary benefits.", " DH will then send form 7 to HoO by clicking on save calculation and submit", " Ho<PERSON> will Approve/ Return the form.", " If HoO approves the form it will be processed further. In case of Return, form", "will be sent back to DH for reverification and the same process followed for", "approval.", "IMPORTANT: -Form 7 calculates the pensionary benefits and pension", "as applicable to the pensioner. Once this form is filled and next stage", "initiated it cannot be edited in HoO section. Therefore, due diligence", "Comprehensive Pension Management System User Manual (Version 6.2)", "44", "should be exercised while filling in all important fields like Pay Band,", "Pay level, Qualifying/Non Qualifying service etc.", "In case Wrong or incorrect information is saved in the system, please", "immediately inform the Helpdesk."]}, {"number": "2.1", "title": null, "content": [" DH to verify and fill the minimum required information in Form 8. Some part", "of information is auto populated in Form 8. The details of recovery under", "various heads has to be filled up in this Form. It may be ensured that the total", "matches with the details filled in Form 7 (Fig 2.10).", " If Nil recovery is due, then just click “Save and Send for Approval”.", " HoO will approve/ Return the form.", " If HoO approves the form it will be processed further. In case of Return, Form", "will be sent back to DH for reverification and the same process followed for", "approval.", " The printouts of Form 7 and 8 can be taken from ‘View Forms’ tab. Approval", "of competent authority may be taken on the physical Form 7 and 8.", "Comprehensive Pension Management System User Manual (Version 6.2)", "45", "Fig 2.10", "Form 7 and Form 8 will be processed roughly four months before the date of retirement", "(Superannuation). HOO will again send the final papers like retirement order, LPC, No dues", "and Vigilance clearance. If there is any change in the pay etc. before retirement, then based", "on final papers received Form 7 shall be digitally revised in Pension section.", "After processing the form 7 from DH level, if HoO user is satisfied then a printout may be", "taken and along with details of recovery which will be fed in form 8, approval and signature", "of competent authority may be taken. If any error is detected in the process, then HoO may", "return to DH the form 7 for necessary correction. DH shall correct it and send it to HoO user", "again. The approval of competent authority shall be taken. Afterwards, form 8 shall also be", "filled as already approved and approval taken."]}, {"number": "2.1", "title": null, "content": [" After all the aforementioned steps, DH will submit the form to Pension Section", "by clicking on “Send to PAO”. While sending this please ensure that all the", "documents mentioned in checklist are being sent. The papers may then be", "dispatched by post."]}, {"number": "2.1", "title": null, "content": [" All users’ can view the list of all the retirees and their generated forms.", " Printout of Form7 and Form 8 can be taken by clicking on ’Print’ and sent to", "Pension Section of concerned CCA office. (Fig 2.11)", "Comprehensive Pension Management System User Manual (Version 6.2)", "46", "Fig 2.11"]}, {"number": "2.2", "title": "Family Pension Case", "content": []}, {"number": "2.2", "title": null, "content": [" DH to login and click on Action, then on Family Pension, then on Personal Details", "option in left pane(DH LoginActionFamily PensionPersonal Details)(Fig"]}, {"number": "2.13", "title": null, "content": [" Following information need to be entered on personal detail page:", "a. Title", "b. First Name", "c. Middle Name", "d. Last Name", "e. Type of retirement.", "f. Height", "g. Father’s/ Husband’s Name.", "h. Mothers Name", "i. Date of Birth", "j. Gender", "k. <PERSON><PERSON><PERSON><PERSON>", "l. PAN Number", "m. FP Mobile Number", "n. FP Email ID", "o. FP Identification Mark 1", "p. FP Identification Mark 2", "q. Employee Code", "Comprehensive Pension Management System User Manual (Version 6.2)", "47", "r. <PERSON>", " Following details to be filled in Other Details section (All are mandatory fields):", "a. Designation at the time of death", "b. Group", "c. Date of Joining", "d. Date of Death", "e. Date of Superannuation", "f. Govt. Accommodation provided by Directorate of Estates/BSNL at any time", "of service: YES/ NO (to select from the dropdown)", "g. Pay Commission", "*After filling the aforementioned information, D<PERSON> to click on Save button. Once filled the", "details cannot be changed so due diligence should be exercised before saving. (Fig 2.13)", "To expedite the disbursement of pensionary benefits, Family Pensioner should immediately be", "advised to submit the required information along with relevant documents to the HoO. Family", "Pensioners’ may also be advised to provide mobile phone number (mandatory) so that they", "can get updates on the pension process and pension disbursement.", "Fig 2.13", "Comprehensive Pension Management System User Manual (Version 6.2)", "48", " After saving the details, an instance will be created in the Personal Details tab, as", "shown in the Fig 2.14.", "Fig 2.14", " Then DH has to click on Govt. Emp Details edit tab, and proceeds to the subsequent", "screen.", " The next screen will show three tabs viz. Personal details, Family Details and", "Nomination Form1 (Fig 2.15)", "Fig 2.15", "Comprehensive Pension Management System User Manual (Version 6.2)", "49", " Now the DH has to fill in details of Family and Nominations (Fig 2.16, Fig 2.17, Fig 2.18)", "Fig 2.16", "Fig 2.17", "Comprehensive Pension Management System User Manual (Version 6.2)", "50", "Fig 2.18", " After filling all the details, the DH will submit the form by clicking submit button."]}, {"number": "2.2", "title": null, "content": [" DH to select ‘Service Verification’ tab from the Menu options.", " DH to check the form and send it for approval to HoO for service verification. Before", "this is done, physical service book verification needs to be done as well. Any", "unverified portion of the service book should be noted and attached in the file being", "sent to Pension Section (CCA Office). (Fig 2.14)", " HoO to login and approve the service book verification. HoO also to verify the service", "book physically.", " HoO can also return the form to DH in case any discrepancy is found. Then DH will", "again have to verify and send the form to HoO for approval.", "Fig 2.14", "Comprehensive Pension Management System User Manual (Version 6.2)", "51"]}, {"number": "2.2", "title": null, "content": [" DH to verify and fill the required information in Form 14. DH may select the", "claimant who is eligible for pension. This is to be done as per CCS Pension", "Rules,1972. The Form shows the claimant details, the bank details, the", "documents to be uploaded like photograph, signature and death certificate", "etc. The Bank Undertaking Form also needs to be uploaded duly signed by bank", "authorities as well the Family Pensioner. FMA option has to be chosen (if", "applicable and desired by the Family Pensioner) (Fig 2.15 (a), Fig 2.15 (b) and", "Fig 2.15 (c)).", " It should be seen that the photograph and signature are clear and visible.", " DH clicks on ‘Form 12’ tab.", "Comprehensive Pension Management System User Manual (Version 6.2)", "52", "Fig 2.15 (a)", "Comprehensive Pension Management System User Manual (Version 6.2)", "53", "Fig 2.15 (b)", "Fig 2.15 (c)", "IMPORTANT: -Form 14 prompts the user to enter bank details. The", "pension of the family pensioner will be credited into this account.", "Therefore, due diligence should be exercised while filling in all important", "fields like Bank Account no., IFSC code etc. After required, correct", "information is filled, Bank Undertaking has to be printed and physically", "signed by Bank authorities and Family Pensioner, and then uploaded on", "CPMS.", "Comprehensive Pension Management System User Manual (Version 6.2)", "54"]}, {"number": "2.2", "title": null, "content": [" DH to verify and fill the required information in Form 12. For each claimant", "who has been nominated for gratuity in the nomination form filled earlier.", "Here DH has to click on the Edit button against the name of the nominee, and", "his/her details will be populated in the Form 12 (Fig 2.16).", " Again Bank Account details and claimant’s details, signature needs to be", "uploaded.", " After updation of Form 12 and 14, the case will appear in ‘Form 18’ tab of DH.", "Fig 2.16", "IMPORTANT: - Form 12 prompts the user to enter bank details. The DCRG", "of the family pensioner will be credited into this account. Therefore, due", "diligence should be exercised while filling in all important fields like Bank", "Account no., IFSC code etc. After required, correct information is filled,", "Bank Undertaking has to be printed and physically signed by Bank", "authorities and Family Pensioner, and then uploaded on CPMS. In case", "of claimant who is claimant for only Gratuity and not pension, only", "Mandate form, as generated, needs to be filled by the pensioner and", "uploaded in portal.", "In case where there is no nomination, DCRG has to be divided in equal", "shares among the family members. Accordingly, the nomination form", "and Form 12s will be filled.", "Comprehensive Pension Management System User Manual (Version 6.2)", "55"]}, {"number": "2.2", "title": null, "content": [" DH to verify and fill the required information in Form 18. Some information is", "auto populated in Form 18. DH should be careful while filling the important", "details like qualifying service, non-qualifying service period, last month’s pay", "details etc. Pensionary benefits will be calculated on basis of these figures and", "hence figures must be cross-verified from service book. (Fig 2.17)", " DH will send Form 18 for approval of HoO.", " HoO will approve/ Return the form. HoO should verify the Pensionary benefit", "amounts. If the amounts are correct, he/she can approve it. If incorrect, <PERSON><PERSON>", "can return the case back to DH for rectification.", " If HoO approves the form it will be processed further. In case of Return, form", "will be sent back to DH for reverification and thereafter process followed for", "approval.", "Comprehensive Pension Management System User Manual (Version 6.2)", "56", "Comprehensive Pension Management System User Manual (Version 6.2)", "57", "Fig 2.17", " Form 18 and 19 to be put up in physical file for approval of competent authority.", "IMPORTANT: -Form 18 calculates the pensionary benefits and pension", "as applicable to the Family pensioner. Once this form is filled and next", "stage initiated it cannot be edited in HoO section. Therefore, due", "diligence should be exercised while filling in all important fields like Pay", "Band, pay level, Qualifying/Non Qualifying service etc.", "In case Wrong or incorrect information is saved in the system, please", "immediately inform the Helpdesk.", "After processing the form 18 from DH level, if HoO user is satisfied then a printout may be", "taken and approval and signature of competent authority may be taken. If any error is", "detected in the process, then HOO may return to DH the form 18 for necessary correction.", "DH shall correct it and send it to HoO user. The approval of competent authority shall be", "taken.", "Comprehensive Pension Management System User Manual (Version 6.2)", "58"]}, {"number": "2.2", "title": null, "content": [" After all the aforementioned steps, DH will submit the form to Pension Section", "(CCA Office) by clicking on “Send to PAO”."]}, {"number": "2.2", "title": null, "content": ["All users’ can view the list of all the retirees and their generated forms. (Fig 2.18)", "Fig 2.18", "Comprehensive Pension Management System User Manual (Version 6.2)", "59"]}]}, {"number": 3, "title": null, "sections": [{"number": "3.1", "title": "Allotment of Pension Case to DH", "content": [" The pension case coming from HoO will appear in ‘Allotment’ tab of the Accounts", "Officer (AO) of the Pension Sanctioning Section. AO pension sanctioning section will", "allot the case to the Dealing Hand of the section. (Fig 3.1)", " The current practice based on which cases are allotted to DH by AOs may", "be continued. This will be done by the AO who has been authorized to", "allot the case (It is to be noted that only 1 AO is authorised for", "allotment).", "Fig 3.1"]}, {"number": "3.2", "title": "Form Received", "content": [" DH (Pension Sanctioning Section) will receive the case allotted by AO (Action->Pension", "Section->Form Received).", " DH (Pension Sanctioning Section) will select on the particular pension case, and click on", "Receive Form tab (Fig 3.2).", "Comprehensive Pension Management System User Manual (Version 6.2)", "60", " DH (Pension Sanctioning Section) will then select the forms submitted by the", "HoO/ACCA. DH will then enter the actual date of receipt of physical forms and other", "documents. DH will then save the case (Fig 3.3).", " A physical file has to be opened by the DH upon receipt of forms and documents by", "the DH.", "Fig 3.2", "Fig 3.3", "Comprehensive Pension Management System User Manual (Version 6.2)", "61"]}, {"number": "3.3", "title": "Pay Regulation", "content": [" The case will now flow into the ‘Pay Regulation’ tab of DH (Pension Sanctioning", "Section) level (Action->Pension Section->Pay Regulation).", " DH (Pension Sanctioning Section) will select the case and click on Pay Regulation", "Sheet (Fig 3.4).", " DH (Pension Sanctioning Section) will then add Pay Scale Data for the particular", "pension case and save the same.", "Fig 3.4", "Comprehensive Pension Management System User Manual (Version 6.2)", "62", "Fig 3.5 (a)", "Fig 3.5 (b)"]}, {"number": "3.4", "title": "Account Enfacement", "content": [" The above case will now flow into Account Enfacement (AE) at DH (Pension Sanctioning", "Section) level (Action->Pension Section->Account Enfacement).", "Comprehensive Pension Management System User Manual (Version 6.2)", "63", " The DH (Pension Sanctioning Section) will then select the particular pension case. The", "DH (Pension Sanctioning Section) can edit the Pay Details by clicking on ‘Pay Regulation’", "tab, if the same was not correctly done in 3.3 above.", " DH (Pension Sanctioning Section) shall analyse if the case is fit for finalisation of pension", "or not. If some documents have not been received, then he/she will generate AE by", "clicking on ‘Generate AE’ tab. The same shall be put up for approval of competent", "authority along with pay regulation sheet.", " The Approved AE shall be uploaded by in the software.", " The physical copy of the AE will be sent by mail/dak to concerned HoO. Reminders can", "be sent using facility for regenerating AE. However, when necessary papers have been", "received, the ‘Resolve AE’ tab may be clicked.", "Fig 3.6 (a)", "Comprehensive Pension Management System User Manual (Version 6.2)", "64", "Fig 3.6 (b)", "A Pension case in which all the papers have not been received or some matter remains", "unaddressed shall remain pending at AE stage itself. Only when all issues relating to pensioner", "etc are finalized then case should move beyond AE tab. Before going to Revise Form Stage, it", "may be checked that Pay regulation has been correctly filled."]}, {"number": "3.5", "title": "Revise Form List", "content": [" The case will now flow from Account Enfacement at DH (Pension Sanctioning Section)", "level to ‘Revise Form List’ Tab(Action->Pension Section->Revise Form List).", " The DH (Pension Sanctioning Section) will then select the particular pension case. The", "DH (Pension Sanctioning Section) can view Pay Regulation Form. The calculation sheet", "can be reviewed and edited, if required. It should be noted that if there is any", "discrepancy in calculation of pension or pensionary benefits, the same can be rectified", "here. After this the figures cannot be rectified. Hence due diligence need to be", "exercise here. Fig (3.7)", " DH (Pension Sanctioning Section) will, if required, edit FORM 7 (Family Pension) or", "FORM 18 (Normal Pension) and calculate the Retiree’s pensionary benefits. (Fig 3.7)", " DH (Pension Sanctioning Section) will then enter the following information in FORM 7/", "FORM18 (some information is auto populated):", "Comprehensive Pension Management System User Manual (Version 6.2)", "65", "a) Service to which he/she belongs.", "b) Scale of Pay/Pay Band & Grade pay of the post.", "c) Whether the appointment mentioned above was under Government or outside the", "Government on foreign service terms", "d) Particulars relating to military service, if any", "e) Particulars relating to the service in autonomous body, if any", "f) Period not counting as qualifying service?", "g) Additions to qualifying Service?", "h) Whether any leave without pay?", "*Points d, e, f, g and h to be filled in carefully, as they will impact the calculation of net", "qualifying service.", "i) Emoluments drawn during 1/ 10 month before retirement.", "*Point “i” will calculate Average Emolument, which will be compared against LPD to", "arrive at pensionary benefits.", "j) Govt. dues recoverable out of gratuity.", " After filling the details, the DH will click on submit and calculate button. The amount", "of gratuity, commutation and monthly pension will be calculated and displayed.", " ‘Revise Form ‘action can be performed if there is a need to carry out correction in", "Form 7 received from HoO.", "Fig 3.7 (a)", "Comprehensive Pension Management System User Manual (Version 6.2)", "66", "Fig 3.7 (b)", "Comprehensive Pension Management System User Manual (Version 6.2)", "67", "Fig 3.7 (c)", "Comprehensive Pension Management System User Manual (Version 6.2)", "68", "IMPORTANT: -Calculation Sheet shows the pensionary benefits and", "pension as applicable to the pensioner. Once this form is reviewed", "and next stage initiated, it cannot be edited. Therefore, due diligence", "should be exercised while reviewing and correcting, if required, in all", "important fields like Pay Band, pay level, Qualifying/Non Qualifying", "service etc.", "In case Wrong or incorrect information is saved in the system, please", "immediately inform the Helpdesk."]}, {"number": "3.6", "title": "Calculation Sheet", "content": [" The case will now flow into Calculation Sheet at DH (Pension Sanctioning Section) level", "(Action->Pension Section-> Calculation Sheet).", " DH (Pension Sanctioning Section) will select the case and view the calculation sheet and", "submit it to AAO for further approval. (Fig 3.8)", "Comprehensive Pension Management System User Manual (Version 6.2)", "69", "Fig 3.8", "Comprehensive Pension Management System User Manual (Version 6.2)", "70", " Now the case will flow to AAO (Pension Sanctioning Section) (AAO Login", "Approval Pension Section Calculation Sheet)", " The AAO (Pension Sanctioning Section) can either Approve/ Return the case. (Fig 3.9)", " If AAO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension", "Sanctioning Section) for editing in “Revise Form” tab. In case of Approval, it will go to", "AO (Pension Sanctioning Section).", " Now the case will flow to AO (Pension Sanctioning Section) (AO Login Approval", "Pension Section Calculation Sheet)", " The AO (Pension Sanctioning Section) can Approve/ Return the case.", " If AO (Pension Sanctioning Section) returns the case, it will again go to DH (Pension", "Sanctioning Section) for editing in “Revise Form” tab.", "Fig 3.9 (a)", "Comprehensive Pension Management System User Manual (Version 6.2)", "71", "Fig 3.9 (b)"]}, {"number": "3.7", "title": "Pension Sanction", "content": [" After the Approval of Calculation Sheet by the AO, the case will come to Pension Sanction", "tab of DH Login.(DH Login Action Pension Section Pension Sanction)", " DH can view Sanctions and ePPO. Fig 3.10) (a)", " Now DH will send the case to AAO for approval.", " AAO can approve the case and send it to AO for further approval. (AAO Login Action", "Pension Section Pension Sanction)", " AO can approve the case (AO Login Action Pension Section Pension Sanction)(Fig"]}, {"number": "3.10", "title": null, "content": [" This approval can be done only on Internet Explorer version 9 or 11 and by using the Digital", "Signature Certificate (DSC) of AO Pension Section.", " Post approval by AO, case will move to the AO- PDA section.", " DSC installation and signature process is dealt separately in the Chapter on DSC", "Installation.", "Comprehensive Pension Management System User Manual (Version 6.2)", "72", "Fig 3.10 (a)", "Fig 3.10 (b)", "Comprehensive Pension Management System User Manual (Version 6.2)", "73", "Fig 3.10 (b)", "Important: - AO Pension section will send the physical copy of following", "documents to AO PDA, after Digital signing of EPPO and Sanctions are", "done (the same is also mentioned in the enclosures section of PPO", "Authority Model).", "1. Physically signed copy of Pension authority", "2. Photo Copy of CDA/IDA/DR List.", "3. Photocopy of PAN no and <PERSON><PERSON><PERSON><PERSON> no.", "4. Bank undertaking and Mandate form of pensioner, indicating name", "and full address of the Authorised Public Sector Bank.", "Comprehensive Pension Management System User Manual (Version 6.2)", "74"]}]}, {"number": 4, "title": null, "sections": [{"number": "4.1", "title": "Allotment of Pension Case", "content": [" The pension case coming from Pension Sanctioning Section will appear in the ‘Allotment’", "tab of the AO PDA Section. (AO PDA Login Allotment Allocate to PDA- DH)", " AO PDA will allot the case to the Dealing Hand of the PDA section. (Fig.4.1)", "Fig.4.1", " Now Case will flow to DH PDA Section. (DH PDA Login Action PDA Section", "Sanction Order Received).", " DH should take the printouts of digitally signed EPPO(2 copies) Commutation Sanction,", "Gratuity Sanction, PPO Authority Model in 5 sets from the links available on the page, as", "shown in the screenshot below. After AO PDA approves and sends the case to PFMS for", "payment, the above hardcopies (1 set each) needs to be physically sent to HoO section,", "Pension section, Cash section and Pensioner. One copy will remain with PDA section.", " Now the DH (PDA Section) will select the case and click on Receive Sanction & Send to", "Bill button. At this stage the name of the employee will appear in the name column of", "the table as shown below.", "Comprehensive Pension Management System User Manual (Version 6.2)", "75", "Fig.4.2"]}, {"number": "4.2", "title": null, "content": [" Now, from the DH login of PDA Section go to Action then Vendor Verification (DH", "PDA Login Action Vendor Verification). The case will appear here in the name", "of pensioners/ claimants. From PFMS the pensioner’s/ claimant’s (Vendor) bank", "credentials will be verified and the status with error description in the table will be", "shown as in Fig. 4.3 below. This process takes approximately 5-10 minutes and when", "the verification is complete, the case will automatically go to Bill Generation Tab.", "Otherwise failed cases will remain at this stage which may be Resend.", " In case the failure is due to wrong Bank account details, the same will be flashed in", "the Error Description column. The wrong Bank account details can be rectified from", "the PDA Utility tab in the AO PDA (Go to AO PDA login, open PDA Utility tab, enter", "PPO no. of the particular case and edit the Bank details as shown in Fig. 4.4(a) and", "Fig. 4.4(b)).", "Comprehensive Pension Management System User Manual (Version 6.2)", "76", "Fig.4.3", " If the case has failed because of any reason other than wrong bank details, the case", "may be Resend.", "Fig.4.4", "Comprehensive Pension Management System User Manual (Version 6.2)", "77", "Fig.4.4(a)", "Fig.4.4(b)", "After completion of Vendor Verification in PFMS the bill will move to the next section of DH", "log-in of PDA Section i.e. Bill Generation."]}, {"number": "4.3", "title": null, "content": [" From the DH log-in of PDA Section go to Others Bill Generate (DH PDA LoginBill", "GenerationOthers Bill Generation). Bill other than monthly bills like Commutation,", "DCRG, etc. will appear at this Tab. Here the bills are appearing in the name of claimants", "who will get the payment through PFMS. Now select the case from the check box and", "click Generate <PERSON>. The bill will be generated and it will go to AAO of PDA", "Section.", "Comprehensive Pension Management System User Manual (Version 6.2)", "78", "Fig.4.5", " AAO of PDA Section will log-in and select the case by clicking the check box", "corresponding to the case and click on Approve button (AAO PDA", "LoginApprovalOthers BillsApprove). The case will go to AO of PDA Section", "for approval.", "Fig.4.6", " Now, AO of PDA Section will log-in and go to approval then other bills (AO PDA", "Login Approval Others Bills). The AO will then select the appropriate Account", "Head Code from the drop down list on the top. After entering the “Not Payable", "Before Date” click on Approve button. Now the Bill will automatically go to PFMS for", "payment.", " After this, the 5 copies of digitally signed Sanctions (taken earlier by DH) should be", "sent to the HoO section, Pension Section, Cash Section, Pensioner and 1 copy", "retained in PDA section (as mentioned on Page 71, sub heading 4.1 of this manual).", "Comprehensive Pension Management System User Manual (Version 6.2)", "79", "Fig.4.7"]}, {"number": "4.4", "title": null, "content": [" Now go to PFMS and log-in with PAO credential. Select the Bill(s), Generate Payment", "Batch File and sign Digitally with the DSC. If the amount of the payment is more than", "10 lakhs, signatory 2 is required for making the payment. The Payment screens are", "given below.", "Fig.4.8", "Comprehensive Pension Management System User Manual (Version 6.2)", "80", "Fig.4.9", "Fig.4.10", "Comprehensive Pension Management System User Manual (Version 6.2)", "81", "Fig.4.11", " It is recommended that above payment from PFMS should be done on the same day,", "when AO PDA sends the case to PFMS. After successful payment from PFMS the", "cases will appear LC & DLC Verification tab of PDA DH.", " PDA AO can view, if required, the PFMS transaction report of pensionary benefits of", "the pensioner/claimant, in the Reports TabOther Bill Report.", " Thereafter the cases will be processed as per the arrear section, below."]}, {"number": "4.5", "title": "Arrears", "content": [" After successful payment, login from DH PDA and click the tab LC & DLC Verification", "(DH PDA Login LC & DLC Verification  LC Verification).", "Comprehensive Pension Management System User Manual (Version 6.2)", "82", "Fig.4.12", " Now click on Verify link shown in the Action column, a popup window will appear as", "shown in the figure below. Here we have to select the Life Certificate and Non", "Employment (FMA also, in case of CDA pensioners) Validity, From and to dates (From", "date will be next date, from the date of retirement). After filling the details click on", "Confirm button. (Note-The above certificate date is for non-employment of", "pensioner)", "Fig.4.13", " After LC & DLC the case will go in Arrears tab (PDA DH login Action Bill", "Generation  Arrears)", "Comprehensive Pension Management System User Manual (Version 6.2)", "83", " Here select the Reason for Arrear and fill the PPO Number of pensioner, then click", "on the Search button. Now it will show the Arrear calculation. After checking the", "calculation, printout of the arrear details should be taken and the soft copy of the", "same saved (by clicking ctrl +P). Now click on the Save & Send for Approval button.", "Then case will go to AAO of PDA Section for approval. (The above printout will be", "used as Arrear Sanction for PFMS payment).", " AAO PDA will select the case (by clicking the check box corresponding to the case)", "and click on Approve button (PDA AAO login Action ApprovalPDA", "Arrears). The case will go to AO of PDA Section for approval.", "Comprehensive Pension Management System User Manual (Version 6.2)", "84", " Now, AO PDA (PDA AO login Action ApprovalPDA Arrears) will select the", "appropriate Account Head Code from the drop down list. Now select the case (by", "clicking the check box corresponding to the case) and enter Not Payable Before Date", "and click on Approve button. Now the Bill will automatically go to PFMS for arrear", "payment.", " After Arrears payment, the case will go to monthly bill section (PDA DH login", "Action Bill Generation Monthly bill (Normal/Family, IDA/CDA))."]}, {"number": "4.6", "title": "Monthly Bill", "content": [" While processing the case for monthly bill the income tax deducted shall be filled", "and accordingly the bill shall be processed. The income tax deducted shall be used", "for feeding income tax on monthly and quarterly basis.", " A printout of the monthly bill shall be taken and handed over to AO Cash conveying", "the sanction.", " Thereafter the cases in DH can be passed to AAO and further to AO for approval.", "Then AO will send the case for monthly bill/pension payment, through PFMS. This", "cycle will be repeated for every month.", "Comprehensive Pension Management System User Manual (Version 6.2)", "85", "Fig.4.14", "Fig.4.15", "Comprehensive Pension Management System User Manual (Version 6.2)", "86", " PDA AO can view the PFMS arrear transaction report of the pensioner/claimant, in", "the Reports TabOther Bill Report. (Fig 4.16)", "Fig.4.16"]}]}, {"number": 5, "title": null, "sections": [{"number": "5.1", "title": null, "content": [" Retiring officer/official can see the progress of his/her application at the top of the", "dashboard (Fig 5.1)", " Milestones completed will be shown in GREEN colour dots and the pending ones with", "RED colour dots.", " Re<PERSON><PERSON> can see the pensionary benefits and lodge their grievance, if any, using the", "dashboard.", " <PERSON><PERSON><PERSON> can see his/her pension details by clicking on pension ledger.", "Comprehensive Pension Management System User Manual (Version 6.2)", "87", "Fig 5.1"]}, {"number": "5.2", "title": "Fill & Submit Forms", "content": ["Retiree can fill the forms by following the following steps:"]}, {"number": "5.2", "title": null, "content": [" Pensioner/Retiree should login into CPMS using PAN and Password received on SMS (NOTE:", "In case of Family Pension the Retiree Profile will be created by the SSA office itself). Click on", "Pensioners Details >> Profile. Fig 5.2", "Comprehensive Pension Management System User Manual (Version 6.2)", "88", "Fig 5.2", " Few details will be pre-populated in the profile of the retiree and rest of the details will be", "filled by the retiree (In case there is any discrepancy in the pre-populated details of the", "retiree, he/she may write to SSA unit and get the same rectified).", "Personal Details Tab:", " Click on Personal Details Tab and fill the same. After filling the same click on Save button", "and move on to next tab ‘Commutation and FMA’. Fig 5.3", "Comprehensive Pension Management System User Manual (Version 6.2)", "89", "Fig 5.3", "Commutation and FMA Tab:", "Comprehensive Pension Management System User Manual (Version 6.2)", "90", "In this tab user will fill the FMA and Commutation details. The commutation percentage can", "be maximum 40%. Fig 5.4", "In case FMA is applicable, retiree needs to select his/her area of residence as CGHS or non-", "CGHS. Other requirements may also be filled in.", "Fig 5.4", "Family Details Tab:", " <PERSON><PERSON><PERSON> will fill information about his/her family members in this tab.", " Then the <PERSON><PERSON><PERSON> should fill the nominations, alternate nominees etc. and keep clicking", "save button to move on to the next tabs. Fig 5.5", " The <PERSON><PERSON><PERSON> should NOT fill his/her own details in this tab.", "Comprehensive Pension Management System User Manual (Version 6.2)", "91", "Fig 5.5", "Nomination Form 1: Fig 5.6", "Fig 5.6", "Comprehensive Pension Management System User Manual (Version 6.2)", "92", "Nomination Form A Fig 5.7", "Fig 5.7", "Bank Details Tab:", "<PERSON><PERSON><PERSON> will fill the bank details in this tab. He/she should be careful while entering these", "details as this is important information for the pension disbursement. Fig 5.8", " After filling the bank details, bank undertaking will be printed (button provided on screen", "as Print Bank Undertaking). Pensioner will himself/herself sign the same and also get", "signed from bank authorities. Re<PERSON>ree will then upload the signed Bank Undertaking on", "CPMS (upload a file).", "Comprehensive Pension Management System User Manual (Version 6.2)", "93", "Fig 5.8", "IMPORTANT: -Pensioner profile prompts the pensioner to enter bank", "details. The pension and pensionary benefits of the pensioner will be", "credited into this account. Therefore, due diligence should be exercised", "while filling in all important fields like Bank Account no., IFSC code etc.", "After required, correct information is filled, Bank Undertaking has to be", "printed and physically signed by Bank authorities and Pensioner, and", "then uploaded on CPMS.", "Check List Tab:", "The Retiree will fill the Check List as per the criteria and scenarios. SL. No. 2, 7, 9 cannot be", "NO. Retiree need to fill them carefully. Fig 5.9", "Fig 5.9", "*Click on ‘Final Submission ‘tab whereupon the case will be finally saved. Once finally", "submitted, the retiree will not be able to change the data. Hence the details should be", "thoroughly checked before submission. Fig 5.9", "Comprehensive Pension Management System User Manual (Version 6.2)", "94", "The Retiree shall take print out of the forms-form 5, form 3, Nomination forms and Form", "1A-and after signing duly submit them with enclosures as mentioned in the checklist of form", "5 to <PERSON><PERSON>."]}, {"number": "5.3", "title": "Updation of Mobile, Email and address", "content": ["For updating mobile number, Email or Address of a pensioner after finalisation of retirement", "benefits, following process will be followed.", "The user/pensioner will have to first login using the PAN no. as the Username. After login,", "click on the profile picture and select the option “Edit Profile” (Fig(5.10)).", "Fig (5.10)", "A Pop-up window (Fig(5.11)) will be displayed with the option to choose the following details", "which the user wants to edit or change: -", "Comprehensive Pension Management System User Manual (Version 6.2)", "95", "Fig (5.11)", "The user has to select the option which he/she wants to change or modify."]}, {"number": "5.3", "title": null, "content": ["Upon selecting ‘Mobile Number’, the following screen will be displayed (Fig(5.12))", "Fig(5.12)", "<PERSON><PERSON><PERSON> can enter his/her mobile number and then select either his/her registered email ID", "or the entered mobile number to receive an OTP to verify the number (Fig (5.12)). Upon", "receiving the OTP, retiree should enter the OTP (Fig (5.13)) and save which will then update", "the mobile number.", "Comprehensive Pension Management System User Manual (Version 6.2)", "96", "Fig (5.13)"]}, {"number": "5.3", "title": null, "content": ["Upon selecting ‘Email ID’, the following screen will be displayed (Fig(5.14))", "Fig (5.14)", "<PERSON><PERSON><PERSON> can enter his/her new email ID and the click on generate OTP which will then send", "an OTP to the registered mobile number (Fig (5.14)). <PERSON><PERSON><PERSON> should then enter the OTP", "received and click on save (Fig (5.15)) which would then update the email ID.", "Comprehensive Pension Management System User Manual (Version 6.2)", "97", "Fig (5.15)"]}, {"number": "5.3", "title": null, "content": ["To update address, pensioner would be taken to the pensioner grievance page where a", "grievance related to updation of address can be registered (Fig (5.16)).", "Fig (5.16)", "Once such a grievance has been registered, it will be assigned to the respective DH who will", "then update the address. Uploading of proof of address is mandatory in such case.", "Comprehensive Pension Management System User Manual (Version 6.2)", "98"]}, {"number": "5.4", "title": "Lodge Grievance", "content": [" <PERSON><PERSON><PERSON> can login and raise his/her Grievance related to pension, if any. <PERSON><PERSON><PERSON>", "Login Pensioners detailGrievance.", " Re<PERSON><PERSON> can select the Grievance Type from the dropdown and add the description", "about it. (Fig 5.17)", "Fig 5.17", " Retiree can also upload the attachment related to the Grievance, if any. (Fig 5.18)", " After filling all the details, <PERSON><PERSON><PERSON> will click on Submit button.(Fig 5.19)", "Comprehensive Pension Management System User Manual (Version 6.2)", "99", "Fig 5.18", "Fig 5.19", "Re<PERSON><PERSON> shall be able track the status of his grievance from grievance history.(Fig 5.19)"]}, {"number": "5.5", "title": "View documents and Ledger", "content": ["<PERSON><PERSON><PERSON> can access ePPO and DCRG sanction along with ledger at any point of time. Also, he", "can access any corrigendum/revision authority on his dashboard as well. (Fig 5.20)", "Comprehensive Pension Management System User Manual (Version 6.2)", "100", "Fig 5.20"]}, {"number": "5.6", "title": null, "content": []}, {"number": "5.6", "title": null, "content": ["Proposed Declaration: All the pensioners drawing pension via SAMPANN are to submit the", "proposed declaration forms of investment and other information for availing income tax", "rebate to the respective pension paying branch by 15th April each year. They can submit by", "filling the online form from their Dashboard or fill a physical copy and send to Concerned", "CCA office. In case of online filling of proposed form, no physical copy needs to be sent to the", "CCA office.", "The pensioners who fail to submit the proposed declaration in time schedule mentioned", "above but submit subsequently on a later date, the tax deduction shall get impacted after", "receipt of the declaration. No proposed declaration would be entertained however after 9th", "October. After that Actual declaration shall submitted by the pensioner.", "Pensioner has to take following steps in order to fill the income tax declaration in SAMPANN", "application:", "Comprehensive Pension Management System User Manual (Version 6.2)", "101", "1. Pensioner shall open SAMPANN website- www.dotpension.gov.in and login using", "his/her credentials as screen shown in the below Fig(5.21).", "Fig (5.21)", "Now Pensioner has to click on the Pensioner Details-> Investment Declaration link shown in", "below Fig (5.22)", "Pensioner will have the option to submit proposed declaration or actual declaration.", "A", "B", "C", "D", "Comprehensive Pension Management System User Manual (Version 6.2)", "102", "Fig(5.22)", "User has to click on the 1st “Fill Proposed Investment” button shown in Fig (5.23) and fill his", "proposed investment declarations details.", "Fig (5.23)", "Comprehensive Pension Management System User Manual (Version 6.2)", "103", "Document upload is not mandatory in case of Proposed Investment Declaration.", "Also proposed declaration can be submitted by the pensioner before 15 April. In case multiple", "declarations are submitted, latest one shall be taken into account. Fig (5.25)", "Comprehensive Pension Management System User Manual (Version 6.2)", "104", "Fig (5.25)", "After entering the Proposed Investment Declarations, the below screen will be shown. Here", "<PERSON><PERSON><PERSON> can see his filled information by clicking on the View link shown in grid under", "“Submitted Declaration” title shown in Fig (5.26).", "Fig (5.26)", "Comprehensive Pension Management System User Manual (Version 6.2)", "105", "After click on View link below screen will shown Fig (5.27). From this screen Re<PERSON><PERSON> can", "take the print of this page.", "Fig (5.27)"]}, {"number": "5.6", "title": null, "content": ["Actual declaration: All the pensioners drawing pension via SAMPANN are to submit the actual", "declaration forms of investment and other information for availing income tax rebate to the", "respective pension paying branch by 10th October each year. They can submit by filling the", "online form from their Dashboard. However, they can also fill a physical copy available on", "Pensioners’ Dashboard and send to Concerned CCA office.", "If no actual declaration is received by 10th October then the proposed declaration, if received,", "shall stand nullified. Also, Actual declaration submitted later will be taken into cognizance in", "the monthly bills remaining to be processed. Hence, no actual declaration shall be accepted", "beyond 15th Feb . Fig (5.28)", "Pensioners should fill Actual declaration of investment as indicated below: - Fig (5.28)", "Comprehensive Pension Management System User Manual (Version 6.2)", "106", "Fig (5.28)", "After selecting the Actual Investment Declaration option the following form will appear: -", "Fig (5.29)", "Comprehensive Pension Management System User Manual (Version 6.2)", "107", "Fig (5.29)", "Comprehensive Pension Management System User Manual (Version 6.2)", "108", "Fig (5.30)", "In this Actual investment declaration form, Pensioner may upload proof of", "investment/savings. All documents required as proof of saving (Investment) should be", "uploaded. They can be uploaded against each entry or as one file against any one of the fields.", "After entering the Actual Investment Declarations, the below screen will be shown. Here", "<PERSON><PERSON><PERSON> can see his filled information by clicking on the View link shown in grid under", "“Submitted Declaration” title shown in Fig (5.30)", ".", "Comprehensive Pension Management System User Manual (Version 6.2)", "109", "Fig (5.30)", "The proposed /Actual declaration shall be received by CCA Office and there after pensioner", "may view the admitted declaration. In case of any issue, fresh declaration may be submitted.", "The reason for change in submitted & Admitted document shall be available in remarks. It", "may be noted that fresh declaration can be submitted only when pensioner’s last declaration", "has been admitted.", "Proposed/Actual declaration submitted by the pensioner shall be available for view &", "accounting to PDA section.", "Now <PERSON><PERSON><PERSON> can see the Approved Calculation sheet on Dashboardby click on the View link", "shown in the grid under “Admitted Declaration” title shown in Fig 5.30.", "Comprehensive Pension Management System User Manual (Version 6.2)", "110"]}]}, {"number": 6, "title": null, "sections": [{"number": "6.1", "title": "Pensioner Grievance", "content": [" <PERSON><PERSON><PERSON> can login and raise his/her Grievance related to pension, if any. <PERSON><PERSON><PERSON>", "Login Pensioners detailGrievance.", " Re<PERSON><PERSON> can select the Grievance Type from the dropdown and add the description", "about it. (Fig 6.1)", " Retiree can also upload the attachment related to the Grievance, if any. (Fig 6.2)", " After filling all the details, <PERSON><PERSON><PERSON> will click on Submit button.", " The concerned section in CCA office/HoO will be displayed as shown at point no. 3.", "(Fig 6.1)", "*After submission, the Grievance will be received by Account Officer (AO) of concerned", "section/HoO as applicable and the history will be maintained in Grievance History section. Fig", "(6.3) point 6.", "Fig 6.1", "Comprehensive Pension Management System User Manual (Version 6.2)", "111", "Fig 6.2", "Fig 6.3"]}, {"number": "6.2", "title": "Allotment of Grievance to DH", "content": [" Once the Grievance is received at AO (Concerned Section) level, he/she will assign that", "Grievance to the respective DH (Concerned Section). (Fig 6.4)", "Comprehensive Pension Management System User Manual (Version 6.2)", "112", " AO Login Grievance Management Assign Grievance.", " AO (Concerned Section) to select the respective DH (Concerned Section) has to click", "on Grievance check box and then click on Assign button. (Fig 6.4)", " After all the aforementioned steps, Grievance will move to respective selected DH", "(Concerned Section).", "Fig 6.4"]}, {"number": "6.3", "title": "Grievance Resolution", "content": [" Once the Grievances received at DH (Concerned Section) DH will take the required", "action on the grievance.", " DH Login Action Grievance Management Resolve Grievance. (Fig 6.5)", " However, before settling the grievance, the DH (Concerned Section) should get", "approval of competent authority in physical file also.", " History of Grievance will be shown in Grievance history with status “Recent” or", "‘Settled”.", " AO (concerned section) will be able to see it in Settled Grievance.", "*The Pensioner will get Notification on Pensioner Dashboard once the Grievance is settled or", "any other action taken on the Grievance.", "Comprehensive Pension Management System User Manual (Version 6.2)", "113", "Fig 6.5", "Comprehensive Pension Management System User Manual (Version 6.2)", "114"]}]}, {"number": 7, "title": null, "sections": []}, {"number": 8, "title": null, "sections": [{"number": "8.1", "title": "Annexure 1 (Side Channel User Creation)", "content": ["Comprehensive Pension Management System User Manual (Version 6.2)", "134", " Login from CCA User-id and go to “Users-> User Registration” tab.", " Click on the Add New button shown there. Then a new window will open shown in", "Fig 8.0. From the drop down menu, Select the Head of Office (SSA Unit) for side", "channel user.", "Fig 8.0", "Comprehensive Pension Management System User Manual (Version 6.2)", "135", " Fig 8.1", " After selection you see the list as shown in above screen. From that list, select the", "option which ends with “Telecom Circle” and having the SSA Unit Code starting with", "”9” which is the unique identifier for the side channel user shown in Fig 8.1.", " Now enter the user-id of HoO as per the format given (<EMAIL>) in", "Email Id box (this is a user name only and no need to fill valid email ID) ,Mobile No.", "and select the “Menu List” Options give below and save the record.", " Now, once the user is created, login from that particular user id, fill the mandatory", "information. Also you can change the default password from there and save the", "record again.", " Once the HoO id for Side channel is created, then login from the same user-id, go to", "“Users -> User Registration”, click on the “ADD NEW” button and create the next", "hierarchy i.e. DH for the Side channel of SSA unit.", " Once the DH user is created, click on the “Authorization” lock icon shown in the grid", "and assign the necessary menu rights to the user shown in Fig 8.3.", "Comprehensive Pension Management System User Manual (Version 6.2)", "136", "Fig 8.2a", "Fig 8.2b", " After giving the rights, login from the DH user-id and default password and fill", "the necessary info and save the record. Now user will be able to process case", "from side channel."]}, {"number": "8.2", "title": "HoO Unit- Side channel (Steps for", "content": ["Processing a Case)", "This chapter deals with flow of pension papers in the CCA office when the case is processed", "through Side channel on behalf of the HOO unit and the pensioner to enable creation of digital", "profile of pensioner.", "Comprehensive Pension Management System User Manual (Version 6.2)", "137"]}, {"number": "8.2", "title": null, "content": []}, {"number": "8.2", "title": null, "content": [" DH has to login to create the retiree profile.", " Select Retiree details on the Menu options available on the left panel.", " Click on “Add New” button at the right top corner of Retiree Profile page to get the", "form to fill.", " Enter the following information on retiree detail form (underlined parameters are the", "mandatory fields):", "q. Title", "r. First Name", "s. Middle Name", "t. Last Name", "u. Type of Retirement", "v. <PERSON>", "w. <PERSON>’s/ Husband’s Name.", "x. <PERSON>’s Name", "y. Date of Birth", "z. Gender", "aa. <PERSON><PERSON><PERSON><PERSON>", "bb. PAN Number", "cc. Mobile Number (This should be an Active Number)", "dd. <PERSON><PERSON> ID", "ee. Identification Mark 1", "ff. Identification Mark 2", " Following details to be filled in other details section (All are mandatory fields):", "h. Designation", "i. Group", "j. Date of Joining", "k. Date of Retirement", "l. Date of Superannuation", "m. Govt. Accommodation provided by Directorate of Estates/BSNL at any time", "of service: YES/ NO (to select from the dropdown)", "n. Pay Commission (this should be the Pay Commission corresponding to the last", "pay drawn)", "*After filling the aforementioned information, DH to click on Save button. By using the Clear", "button, User can also refresh the form and fill in fresh information. (Fig 8.3)", "Comprehensive Pension Management System User Manual (Version 6.2)", "138", "IMPORTANT:-Once this form is filled and next stage initiated it cannot be edited. Therefore,", "due diligence should be exercised while creating the Retiree Profile.", "In case Wrong or incorrect profile is detected, please immediately inform the Helpdesk.", "Fig 8.3", "Creation of the Retiree Profile is the most important step in the process, so it should be", "ensured entries made therein are correct in all respects. After saving the information, it is", "recommended a printout of the page be taken from the screen and Vetted by HoO level User", "before proceeding ahead in Service Verification. If any error is detected, then retiree profile", "be edited before initiating *******. (further updates in chapter 11)"]}, {"number": "8.2", "title": null, "content": [" It may be noted while the case is processed via side channel, the timelines related", "to lose their relevance.", " Based on the S/B received in office, same may be filled in the module by DH. If some", "portion remains unverified it may be filled in remarks column.", " There shall be only one level passing.", "Comprehensive Pension Management System User Manual (Version 6.2)", "139"]}, {"number": "8.2", "title": null, "content": [" At this stage after DH sends form to retiree, the digital profile of pensioner will be", "created.", " There shall be only one level passing.", "Comprehensive Pension Management System User Manual (Version 6.2)", "140"]}, {"number": "8.2", "title": null, "content": [" Prior to Form received stage, DH shall be required to fill up the forms as per", "documents submitted by the pensioner. Before submission the final submit button,", "it may be checked and vetted that the details are correctly filled.", " If any error is detected however at the form received stage, then the case may be", "returned for refilling.", " It may be noted that retiree will not be required to fill the forms. This activity shall", "be completed by DH.", " For filling the form go to “Form Received”, where you find the record under status", "“Cases pending at the Pensioner level” which is default selected. Now click on the", "Edit button (Pencil Icon) shown in the last Action column and fill all the detail of", "Pensioner very carefully. Fig 8.4", "Fig 8.4", " After filling up the profile, on the same “Form Received” page select the option", "“Cases submitted without Physical Copy” from Status dropdown list shown in Fig"]}, {"number": "8.5", "title": null, "content": ["“Form 7” for processing by skipping the step for “Form Verification”.", " There shall be only one level passing.", "Comprehensive Pension Management System User Manual (Version 6.2)", "141", "Fig 8.5"]}, {"number": "8.2", "title": null, "content": ["THIS STEP SHALL NOT BE AVAILABLE IN SIDECHANNEL i.e. SKIPPED"]}, {"number": "8.2", "title": null, "content": [" DH to verify and fill the required information in Form 7.", " Some information in Form 7 is auto populated. Others have to be entered. (Fig 8.6)", "ee) Name of the retiring Government Employee", "ff) Father's/Husband's Name", "gg) PAN NO.", "hh) Height & Marks of identification", "ii) Date of Birth", "jj) Service to which he/she belongs (indicate name of Organised service, if any,", "otherwise say General Central Service)", "kk) Particulars of post held at the time of retirement", "ll) Name of the office", "mm) Post held", "nn) Scale of Pay/Pay Band & Grade pay of the post", "oo) Basic Pay/Pay in the pay band & Grade pay.", "pp) Basic Pay/Pay in the pay band & Grade pay", "qq) Whether the appointment mentioned above was under Government", "or outside the Government on foreign service terms?", "rr) If on foreign service, scale of pay/pay band, pay in the pay band and grade pay", "of the post in the parent department.", "Comprehensive Pension Management System User Manual (Version 6.2)", "142", "ss) Whether declared substantive in any Post under the Central Govt.?", "tt) Date of beginning of service", "uu) Date of ending service", "vv) Cause of ending service", "ww) In case of compulsory retirement, the orders of the competent", "authority whether pension may be allowed at full rates or at reduced rates and", "in case of reduced rates, the percentage at which it is to be allowed (Please See", "Rule 41)", "xx)In case of removal/dismissal from service whether orders of the competent", "authority have been obtained for grant of compassionate allowance and if so,", "at what rate (Please see Rule 41)", "yy) Particulars relating to military service, if any.", "zz) Particulars relating to the service in autonomous body, if any.", "aaa) Whether any Departmental or judicial proceedings in terms of rule 9 of", "the CCS(Pension) Rules, 1972 are pending against the retiring employee. (lf yes,", "in terms of Rule 69, provisional pension will be admissible and gratuity will be", "withheld till the conclusion of departmental or judicial proceedings and issue", "of final orders)", "bbb) Length of service", "vii. Details of omission, imperfection or deficiencies in the Service Book", "which have been ignored [under rules 59(1) (b) (ii)]", "viii. Period not counting as qualifying service.", "ix. Additions to qualifying Service.", "x. Whether any leave without pay.", "xi. Net Qualifying service.", "xii. Qualifying service expressed in terms of complete six monthly periods", "(Period of three months and above is to be treated as completed six", "monthly periods (Rule 49)", "ccc) Emoluments", "c. Emoluments in terms of Rule33.", "d. Emoluments drawn during ten months pending retirement.", "ddd) Others: Details of Govt. dues recoverable out of gratuity", "eee) Licence fee for Govt. accommodation see sub-rules (2), (3) and (4) of rule", "2)", "fff) Dues referred to in Rule 73", "ggg) Amount indicated by Directorate of Estates to be withheld under", "Sub rule(S) of Rule 72", "hhh) Post-retirement address of the retiree", "Comprehensive Pension Management System User Manual (Version 6.2)", "143", "Comprehensive Pension Management System User Manual (Version 6.2)", "144", "Fig 8.6", " DH shall fill up the form 7 as per form 7 received from the HoO. It may be noted", "if there is any change from the form 7 received from HOO based on final papers", "received, Same may be fed.", " There shall be only one level passing."]}, {"number": "8.2", "title": null, "content": ["THIS STEP SHALL NOT BE AVAILABLE IN SIDECHANNEL i.e. SKIPPED"]}, {"number": "8.2", "title": null, "content": [" After all the aforementioned steps, DH will submit the form to Pension Section", "by clicking on “Send to PAO”. While sending this please ensure that all the", "documents mentioned in checklist are being sent. The papers may then be", "dispatched by post. This will be the only step in which two level passing will be", "there.", " For cases in which the final papers have not been received, the case shall not be", "processed beyond AE in Pension Section Module.", "Comprehensive Pension Management System User Manual (Version 6.2)", "145"]}, {"number": "8.2", "title": null, "content": [" All users’ can view the list of all the retirees and their generated forms.", " Printout of Form7 and Form 8 can be taken by clicking on ’Print’ and sent to", "Pension Section of concerned CCA office. (Fig 8.7)", "Fig 8.7"]}, {"number": "8.2", "title": null, "content": ["There shall be no change in the treatment of family pension case while processing through", "Side Channel. While feeding the forms, all the details – in S/B module, form 12/14 and 18", "- shall be filled based on the forms submitted by the retiree."]}, {"number": "8.2", "title": null, "content": [" DH to login and click on Action, then on Family Pension, then on Personal Details", "option in left pane(DH LoginActionFamily PensionPersonal Details)(Fig"]}, {"number": "8.8", "title": null, "content": [" Following information need to be entered on personal detail page:", "s. Title", "t. First Name", "u. Middle Name", "v. Last Name", "w. Type of retirement.", "x. Height", "y. Father’s/ Husband’s Name.", "z. Mothers Name", "aa. Date of Birth", "Comprehensive Pension Management System User Manual (Version 6.2)", "146", "bb. Gender", "cc. A<PERSON>haar Number", "dd. <PERSON><PERSON> Number", "ee. FP Mobile Number", "ff. FP Email ID", "gg. FP Identification Mark 1", "hh. FP Identification Mark 2", "ii. Employee Code", "jj. Office", " Following details to be filled in Other Details section (All are mandatory fields):", "h. Designation at the time of death", "i. Group", "j. Date of Joining", "k. Date of Death", "l. Date of Superannuation", "m. Govt. Accommodation provided by Directorate of Estates/BSNL at any time", "of service: YES/ NO (to select from the dropdown)", "n. Pay Commission", "*After filling the aforementioned information, D<PERSON> to click on Save button. Once filled the", "details cannot be changed so due diligence should be exercised before saving. (Fig 8.8)", "To expedite the disbursement of pensionary benefits, Family Pensioner should immediately be", "advised to submit the required information along with relevant documents to the HoO. Family", "Pensioners’ may also be advised to provide mobile phone number (mandatory) so that they", "can get updates on the pension process and pension disbursement.", "Comprehensive Pension Management System User Manual (Version 6.2)", "147", "Fig 8.8", " After saving the details, an instance will be created in the Personal Details tab, as", "shown in the Fig 8.9", "Fig 8.9", " Then DH has to click on Govt. Emp Details edit tab, and proceeds to the subsequent", "screen.", " The next screen will show three tabs viz. Personal details, Family Details and", "Nomination Form1 (Fig 8.10)", "Comprehensive Pension Management System User Manual (Version 6.2)", "148", "Fig 8.10", " Now the DH has to fill in details of Family and Nominations (Fig 8.11, Fig 8.12, Fig 8.13)", "Fig 8.11", "Comprehensive Pension Management System User Manual (Version 6.2)", "149", "Fig 8.12", "Fig 8.13", " After filling all the details, the DH will submit the form by clicking submit button."]}, {"number": "8.2", "title": null, "content": [" DH to select ‘Service Verification’ tab from the Menu options.", " DH to check the form and send it for approval to HoO for service verification. Before", "this is done, physical service book verification needs to be done as well. Any", "Comprehensive Pension Management System User Manual (Version 6.2)", "150", "unverified portion of the service book should be noted and attached in the file being", "sent to Pension Section (CCA Office). (Fig 8.14)", " HoO to login and approve the service book verification. HoO also to verify the service", "book physically.", " HoO can also return the form to DH in case any discrepancy is found. Then DH will", "again have to verify and send the form to HoO for approval.", "Fig 8.14"]}, {"number": "8.2", "title": null, "content": [" DH to verify and fill the required information in Form 14. DH may select the", "claimant who is eligible for pension. This is to be done as per CCS Pension", "Rules,1972. 14. The Form shows the claimant details, the bank details, the", "documents to be uploaded like photograph, signature and death certificate", "etc. The Bank Undertaking Form also needs to be uploaded duly signed by bank", "authorities as well the Family Pensioner. FMA option has to be chosen (if", "applicable and desired by the Family Pensioner) (Fig 8.11 (a), Fig 8.11 (b) and", "Fig 8.11 (c)).", " It should be seen that the photograph and signature are clear and visible.", " DH clicks on ‘Form 12’ tab.", "Comprehensive Pension Management System User Manual (Version 6.2)", "151", "Fig 8.11 (a)", "Comprehensive Pension Management System User Manual (Version 6.2)", "152", "Fig 8.11 (b)", "Fig 8.11 (c)", "IMPORTANT: -Form 14 prompts the user to enter bank details. The", "pension of the family pensioner will be credited into this account.", "Therefore, due diligence should be exercised while filling in all important", "fields like Bank Account no., IFSC code etc. After required, correct", "information is filled, Bank Undertaking has to be printed and physically", "signed by Bank authorities and Family Pensioner, and then uploaded on", "CPMS.", "Comprehensive Pension Management System User Manual (Version 6.2)", "153"]}, {"number": "8.2", "title": null, "content": [" DH to verify and fill the required information in Form 12 for each claimant who", "has been nominated for gratuity in the nomination form filled earlier. Here DH", "has to click on the Edit button against the name of the nominee, and his/her", "details will be populated in the Form 12 (Fig 8.15).", " Again Bank Account details and claimant’s details, picture and signature needs", "to be uploaded.", " After updation of Form 12 and 14, the case will appear in ‘Form 18’ tab of DH.", "Fig 8.15", "IMPORTANT: - Form 12 prompts the user to enter bank details. The DCRG", "of the family pensioner will be credited into this account. Therefore, due", "diligence should be exercised while filling in all important fields like Bank", "Account no., IFSC code etc. After required, correct information is filled,", "Bank Undertaking has to be printed and physically signed by Bank", "Comprehensive Pension Management System User Manual (Version 6.2)", "154", "authorities and Family Pensioner, and then uploaded on CPMS. In case", "of claimant who is claimant for only Gratuity and not pension, only", "Mandate form, as generated, needs to be filled by the pensioner and", "uploaded in portal.", "In case where there is no nomination, DCRG has to be divided in equal", "shares among the family members. Accordingly, the nomination form", "and Form 12s will be filled."]}, {"number": "8.2", "title": null, "content": [" DH to verify and fill the required information in Form 18. Some information is", "auto populated in Form 18. DH should be careful while filling the important", "details like qualifying service, non-qualifying service period, last month’s pay", "details etc. Pensionary benefits will be calculated on basis of these figures and", "hence figures must be cross-verified from service book. (Fig 8.16)", " DH will send Form 18 for approval of HoO.", " HoO will approve/ Return the form. HoO should verify the Pensionary benefit", "amounts. If the amounts are correct, he/she can approve it. If incorrect, <PERSON><PERSON>", "can return the case back to DH for rectification.", " If HoO approves the form it will be processed further. In case of Return, form", "will be sent back to DH for reverification and thereafter process followed for", "approval.", "Comprehensive Pension Management System User Manual (Version 6.2)", "155", "Comprehensive Pension Management System User Manual (Version 6.2)", "156", "Fig 8.16", " Form 18 and 19 to be put up in physical file for approval of competent authority.", "IMPORTANT: -Form 18 calculates the pensionary benefits and pension", "as applicable to the Family pensioner. Once this form is filled and next", "stage initiated it cannot be edited in HoO section. Therefore, due", "diligence should be exercised while filling in all important fields like Pay", "Band, pay level, Qualifying/Non Qualifying service etc.", "In case Wrong or incorrect information is saved in the system, please", "immediately inform the Helpdesk.", "After processing the form 18 from DH level, if HoO user is satisfied then a printout may be", "taken and approval and signature of competent authority may be taken. If any error is", "detected in the process, then HOO may return to DH the form 18 for necessary correction.", "DH shall correct it and send it to HoO user. The approval of competent authority shall be", "taken.", "Comprehensive Pension Management System User Manual (Version 6.2)", "157"]}, {"number": "8.2", "title": null, "content": [" After all the aforementioned steps, DH will submit the form to Pension Section", "(CCA Office) by clicking on “Send to PAO”.", " For cases in which the final papers have not been received, the case shall not be", "processed beyond AE in Pension Section Module."]}, {"number": "8.2", "title": null, "content": ["All users’ can view the list of all the retirees and their generated forms. (Fig 8.17)", "Fig 8.17", "Comprehensive Pension Management System User Manual (Version 6.2)", "158", "Chapter 9", "9. ID Card Generation", "This chapter deals with the ID Card generation module, where the Retiree ID card can be", "generated.", "**Note-ID Card generation will be applicable only for CDA Pensioners"]}, {"number": "9.1", "title": "Upload AO Pension signature", "content": [" Login with AO Pension go to the Masters Issuing Authority Signature.", " Click on the “upload a file” button and upload the signature of the Issuing Authority.", "(Fig 9.1) (Fig 9.2)", " After uploading the image, click on the Update button (Fig 9.3).", "(Fig 9.1)", "Comprehensive Pension Management System User Manual (Version 6.2)", "159", "(Fig 9.2)", "(Fig 9.3)"]}, {"number": "9.2", "title": "Generate ID Card", "content": [" Again login with AO Pension go to the Masters Generate ID Card.", " Here Fill the PPO number of pensioner and click on the Search button. (Fig 9.4)", "Comprehensive Pension Management System User Manual (Version 6.2)", "160", "(Fig 9.4)", " Now you can take the print of ID Card by clicking on the “Print Front side” and “Print", "Back Side” button, as shown in below figure. (Fig 9.5)", "(Fig 9.5)", "Comprehensive Pension Management System User Manual (Version 6.2)", "161", "Chapter 10", "10. Revision", "Under revision module, there are 5 categories of revision (shown in Fig 10)", "1. Revision in the rate of DA: Due to change of DA as on Date of retirement, the DCRG", "shall get revised and the differential amount of DCRG shall be due to be paid. For this", "sanction for payment shall be issued by Pension section and PDA shall make payment. Any", "impact on pension payable has to be separately calculated and paid in the next monthly", "bill.", "2. Revision on account of withheld amount: On receipt of sanction for release of full/part", "payment of Withheld amount, this revision shall be processed.", "3. Revision of pension to family pension (f.p) on account of death (When No eligible f.p. is", "mentioned in the PPO): In case when claimant name is not mentioned in the PPO, then", "revision shall be done. Under this, PPO shall be issued by Pension section.", "4. Revision of pension to f.p on account of death (when eligible f.p. is mentioned in PPO)", "In case when claimant name is mentioned in the PPO, then revision shall be done however", "as previous PPO will suffice, no fresh PPO shall be issued in such case.", "5. Revision of pension due to pay change/Court matter: Due to change in Pay details or", "service details arising out of Court order then, the Revision shall be processed.", "Fig (10)", "It is , currently ,mandatory that the Revision should only be done by the same DH", "Pension user who make entry of the case earlier in the CPMS."]}, {"number": "10.1", "title": "Revision in the rate of DA", "content": ["This type of revision is used when there is a change in the DA Rate. No fresh sanction for", "HoO for this case shall be required.", "Steps for this revision are as follows: -", " First of all, login with the DH Pension", " Go to the Revision -> Revision of Pension tab Fig (10.1.b).", "Comprehensive Pension Management System User Manual (Version 6.2)", "162", "Fig (10.1.b)", " Here fill in PPO No and select the “Reason for Revision” Fig (10.1.c).", "Fig (10.1.c)", " Now click on the edit button (pencil icon) in last Action column.", " Then next screen opens, here check the details and then click on the “Send to", "Revision Sanction order” button and enter w.e.f. date Fig (10.1.d).", "Comprehensive Pension Management System User Manual (Version 6.2)", "163", "Fig (10.1.d)", " Now Go to-> Revision->Revision Sanction Order tab shown in Fig (10.1.e)", "Fig (10.1.e)", " From this screen you can view the Sanction order, by clicking on the View link Fig", "(10.1.e). Also you can take the printout of this Sanction Order Fig (10.1.f).", "Comprehensive Pension Management System User Manual (Version 6.2)", "164", "Fig (10.1.f)", " Now click on the Last Column, verify link to send record for AAO approval Fig", "(10.1.e).", " Now login with the AAO Pension. Go to ->Approval->Revision->Revision Sanction", "Order Shown in Fig (10.1.g)", "Fig (10.1.g)", " Now Click on the View link to see the Sanction order and then Approve/Return the", "record by click on the Approve/Return link and send record for AO approval Fig", "(10.1.g).", " Now login with the AO Pension (only on IE Browser).", " Go to Approval>Revision>Revision Sanction Order Fig (10.1.h)", "Comprehensive Pension Management System User Manual (Version 6.2)", "165", "Fig (10.1.h)", " Click on View link to see or take the print of the Sanction Order, attach the DSc in the", "system for digital signature and click on the Approve link.At this stage, Digitally signed", "authority shall get generated.", " Now login with AO PDA, Go to->Allotment->Allotment To PDA DH Fig (10.1.i)", "Fig (10.1.i)", " Now select the case, Allot the case to the PDA DH by selecting the respective DH", "from the dropdown list and Click on “Send To DH” button.", " After this login with PDA DH, Go to -><PERSON>->Revision, Fig (10.1.j)", "Comprehensive Pension Management System User Manual (Version 6.2)", "166", "Fig (10.1.j)", " Select the Record and Click on the “Approve and Send to AAO” button, Fig (10.1.j).", " Now login with AAO PDA, Go to->Approval->PDA->Revision, Fig (10.1.k)", "Fig (10.1.k)", " Now select the record and click on the “Approve Bill and Send to AO”.", " After this login from PDA AO, Go to->Approval->PDA->Revision Fig (10.1.l)", "Comprehensive Pension Management System User Manual (Version 6.2)", "167", "Fig (10.1.l)", " Select the Account Head from the dropdown, then it shows the respective records,", "then fill the “Enter Not Payable Before Date” and click on the Approve Bill button.", " After this record will show in PFMS for payment. Monthly Bill shall remain", "unchanged."]}, {"number": "10.2", "title": "Revision Due to Withheld Amount", "content": ["This type of revision is used when there is a withheld amount of DCRG in r/o pensioner and", "sanction for release of full amount/part amount has been received from HoO. Steps for this", "revision are as follows: -", " First of all, login with the DH Pension", " Go to the Revision -> Revision of Pension tab Fig (10.2.b).", "Comprehensive Pension Management System User Manual (Version 6.2)", "168", "Fig (10.2.b)", " Here fill all required information like PPO No and select the “Reason for Revision”", "Fig (10.2.c).", "Fig (10.2.c)", " Now click on the edit button (pencil icon) in last Action column.", " Then next screen opens, here Fill the ‘Recoveries From Withheld amount’ (11.(3))", "and then click on the “Send to Revision Sanction order” button Fig (10.2.d) and enter", "reason for withheld.", "Comprehensive Pension Management System User Manual (Version 6.2)", "169", "Fig (10.2.d)", " Now Go to-> Revision->Revision Sanction Order tab shown in Fig (10.2.e)", "Fig (10.2.e)", " From this screen you can view the Sanction order, by clicking on the View Link Fig", "(10.2.e). Also you can take the printout of this Sanction Order Fig (10.2.f).", "Comprehensive Pension Management System User Manual (Version 6.2)", "170", "Fig (10.2.f)", " Now click on the Last column Verify link to send record for AAO approval Fig (10.2.e).", " Now login with the AAO Pension.", "Go to ->Approval->Revision->Revision Sanction Order Shown in Fig (10.2.g)", "Fig (10.2.g)", " Now Click on the View link to see the Sanction order and then Approve/Return the", "record by click on the Approve/Return link and send record for AO approval Fig", "(10.2.g).", " Now login with the AO Pension (only on IE Browser).", " Go to Approval>Revision>Revision Sanction Order Fig (10.2.h)", "Comprehensive Pension Management System User Manual (Version 6.2)", "171", "Fig (10.2.h)", " Click on View link to see or take the print of the Sanction Order, attach the DSC in", "the system for digital signature and click on the Approve link.", " Now login with AO PDA, Go to->Allotment->Allotment To PDA DH Fig (10.2.i)", "Fig (10.2.i)", " Now select the record and Allot the case to the PDA DH by selecting the respective", "DH from the dropdown list and Click on “Send To DH” button.", " After this login with PDA DH, Go to -><PERSON>->Revision, Fig (10.2.j)", "Comprehensive Pension Management System User Manual (Version 6.2)", "172", "Fig (10.2.j)", " Select the Record and Click on the “Approve and Send to AAO” button, Fig (10.2.j).", " Now login with AAO PDA, Go to->Approval->PDA->Revision, Fig (10.2.k)", "Fig (10.2.k)", " Now select the record and click on the “Approve Bill and Send to AO”.", " After this login from PDA AO, Go to->Approval->PDA->Revision Fig(10.2.l)", "Comprehensive Pension Management System User Manual (Version 6.2)", "173", "Fig (10.2.l)", " Now select the Account Head from the dropdown, then it shows the respective", "records, then fill the “Enter Not Payable Before Date” and click on the Approve Bill", "button.", " After this record will show in PFMS for payment. Monthly Bill shall remain", "unchanged."]}, {"number": "10.3", "title": "Revision on account of Pay", "content": ["revision/Court Order", "This type of revision is used when there is a revision in the pay /court order. For this sanction", "shall be issued by HoO and sent to Pension section.", "Steps for this revision are as follows: -", " First of all, login with the DH Pension", " Go to the Revision -> Revision of Pension tab Fig (10.3.b).", "Comprehensive Pension Management System User Manual (Version 6.2)", "174", "Fig (10.3.b)", " Here fill all required information like PPO No and select the “Reason for Revision”", "Fig (10.3.c)", "Fig (10.3.c)", " Now click on the edit button (pencil icon) in last Action column.", " Then next screen opens, here fill the all required details. DH should enter the following", "requisite parameters:", "a. <PERSON>", "b. New Last Pay Drawn", "c. New Qualifying Service", "In case any figure remains unchanged then the original value has to be fed.", " System will then calculate the revised pension, commutation (if any) and gratuity (if", "any) and the additional amount. Also, if some data entry has been wrongly", "entered, Press cancel. Once satisfied, Press SAVE.", "Comprehensive Pension Management System User Manual (Version 6.2)", "175", " Sanction Order will then be generated and it will be available in revision sanction", "order Tab. DH may view the sanction order. Once a case is at this stage, it cannot be", "edited.", "Fig (10.3.d)", "Fig (10.3.d)", "Comprehensive Pension Management System User Manual (Version 6.2)", "176", "Fig (10.3.d)", " Now Go to-> Revision->Revision Sanction Order tab shown in Fig (10.3.e)", "Fig (10.3.e)", " From this screen you can view the Sanction order, by clicking on the View link Fig", "(10.3.e). Also you can take the printout of this Sanction Order Fig (10.3.f).", "Comprehensive Pension Management System User Manual (Version 6.2)", "177", "Fig (10.3.f)", " Now click on the Last column Verify link to send record for AAO approval Fig (10.3.e).", " Now login with the AAO Pension. Go to ->Approval->Revision->Revision Sanction", "Order Shown in Fig (10.3.g)", "Fig (10.3.g)", " Now Click on the View link to see the Sanction order and then Approve/Return the", "record by click on the Approve/Return link and send record for AO approval Fig", "(10.3.g).", " Now login with the AO Pension (only on IE Browser).", " Go to Approval>Revision>Revision Sanction Order", " Click on View link to see or take the print of the Sanction Order, attach the DSc in the", "system for digital signature and click on the Approve link.", " Now login with AO PDA, Go to->Allotment->Allotment To PDA DH Fig (10.3.i)", "Comprehensive Pension Management System User Manual (Version 6.2)", "178", "Fig (10.3.i)", " Now Allot the case to the PDA DH by selecting the respective DH from the dropdown", "list and Click on “Send To DH” button.", " After this login with PDA DH, Go to ->Action->PDA Section->Revision Sanction Order,", "Fig (10.3.j)", " DH PDA must view and save a digital copy of the Sanction Order and take requisite", "printouts, so that copy can be sent to Ho<PERSON> , pensioner, Pension section apart one", "office copy(after updation of date of restoration of 2nd commutation after completion", "of steps till point 10 ). The copy will not be available at any other screen, so saving a", "digital copy is mandatory.", " PDA section shall manually calculate the amount of arrears to be paid on account of", "the revision of pension from date of retirement till the current month. Also, For the", "month, pro rate calculation has to be done manually.", " The arrear amount may be fed in the monthly bill of the current month, if not paid", "already, and pushed from DH to AAO. Once the file moves from sanction received", "stage, the pension of current month will not be available at DH level any more. So", "the monthly bill for current month should be pushed with the arrear from DH to", "AAO. The monthly bill can be paid as per rules. After completion of above, DH PDA", "will now generate the bill for the above sanction order and send the generated bills", "namely commutation and gratuity to cash after three level passing.", "Comprehensive Pension Management System User Manual (Version 6.2)", "179", "Fig (10.3.j)", " Click on the Send button in front of the record.", " Now Go to->Action-><PERSON>->Revision.", " Select the Record and Click on the “Approve and Send to AAO” button.", " Now login with AAO PDA, Go to->Approval->PDA->Revision, Fig (10.3.k)", "Fig (10.3.k)", " Now select the record and click on the “Approve Bill and Send to AO”.", " After this login from PDA AO, Go to->Approval->PDA->Revision Fig (10.3.l)", "Comprehensive Pension Management System User Manual (Version 6.2)", "180", "Fig (10.3.l)", " Now select the Account Head from the dropdown, then it shows the respective", "records, then fill the “Enter Not Payable Before Date” and click on the Approve Bill", "button.", " After this record will show in PFMS for payment. The Commutation/ DCRG amount", "shall be paid by Cash the same day when AO PDA send it and same shall be used for", "arriving at the date of restoration of 2nd commutation and shall be updated in revision", "copy mentioned in point 6.", " After successful payment, LC and DLC will get refreshed and respective dates will be", "entered for the pensioner.", " Thereafter, the monthly bill in for current month lying at AAO shall be pushed with", "NPB date to AO and then to Cash for payment.", " The monthly pension of following months(including current month", ") will come as per revised pension."]}, {"number": "10.4", "title": "Revision of Pension to FP (No eligible", "content": ["family member mentioned in PPO)", "This type of revision will be done when after the death of pensioner, the claimant’s name is", "not mentioned in the PPO. In such case, revision shall be initiated after form 14 with", "enclosure is duly forwarded by HOO along with sanction of payment manually.", "Documents that will be required to be submitted in this case shall be", "1. Death certificate of Pensioner(s)", "2. Life certificate/DLC of the claimant", "3. Mandate form/cancelled cheque and undertaking", "Comprehensive Pension Management System User Manual (Version 6.2)", "181", "4. <PERSON>ly filled form 14 with enclosures.", "If there is a case where though the claimant name is not mentioned in the PPO, but same has", "been added via corrigendum and uploaded via Upload utility e.g. permanently disabled", "children/siblings and disabled parent, then they will be processed though this type. In such", "cases form 14 shall be required to be submitted to the Pension/PDA directly and no fresh", "sanction shall be called for.", "Documents that shall be required to be submitted in this case shall be", "1. Death certificate of Pensioner(s)", "2. Life certificate/DLC of the claimant", "3. Mandate form/cancelled cheque and undertaking", "4. Form 14 duly filled with enclosures.", "Steps for this revision are as follows: -", " First of all, login with the DH Pension", " Go to the Revision -> Revision of Pension tab Fig (10.4.b).", "Fig (10.4.b)", " Here fill all required information like PPO No and select the “Reason for Revision”", "Fig (10.4.c).", "Comprehensive Pension Management System User Manual (Version 6.2)", "182", "Fig (10.4.c)", " Now click on the edit button (pencil icon) in last Action column.", " Then next screen opens, here Fill all the details and then click on the “Save” button", "Fig (10.4.d).", "Comprehensive Pension Management System User Manual (Version 6.2)", "183", "Fig (10.4.d)", " Now Go to-> Revision->Revision Sanction Order tab shown in Fig (10.4.e)", "Fig (10.4.e)", " From this screen you can view the Sanction order, by clicking on the View link Fig", "(10.4.e). Also you can take the printout of this Sanction Order Fig (10.4.f).", "Comprehensive Pension Management System User Manual (Version 6.2)", "184", "Fig (10.4.f)", " Now click on the Last Column Verify link to send record for AAO approval Fig", "(10.4.e).", " Now login with the AAO Pension. Go to ->Approval->Revision->Revision Sanction", "Order Shown in Fig (10.4.g)", "Fig (10.4.g)", " Now Click on the View link to see the Sanction order and then Approve/Return the", "record by click on the Approve/Return link and send record for AO approval Fig", "(10.4.g).", " Now login with the AO Pension (only on IE Browser).", "Comprehensive Pension Management System User Manual (Version 6.2)", "185", " Go to Approval>Revision>Revision Sanction Order", " Click on View link to see or take the print of the Sanction Order, attach the DSc in the", "system for digital signature and click on the Approve link.", " Now login with AO PDA, Go to->Allotment->Allotment To PDA DH Fig (10.4.i)", "Fig (10.4.i)", " Now Allot the case to the PDA DH by selecting the respective DH from the dropdown", "list and Click on “Send To DH” button.", " After this login with PDA DH, Go to ->Action->PDA Section->Revision Sanction Order,", "Fig (10.4.j)", "Comprehensive Pension Management System User Manual (Version 6.2)", "186", "Fig (10.4.j)", " After this the record will show in Vendor Verification, GO to->Action->Vendor", "Verification Fig (10.4.k).", "Fig (10.4.k)", " After this record will Go to->LC & DLC Verification->LC Verification, Fig (10.4.l).", "Comprehensive Pension Management System User Manual (Version 6.2)", "187", "Fig (10.4.l)", " According to the dates filled in the revision module, case shall get processed.", " After this the case will show in Next month Monthly Bill ( from the month you", "process the case), Fig(10.4.m)", "Fig(10.4.m)", " Now Process the monthly bill and do payment. An assessment shall be done on", "account of amount payable , if any, due to delay of intimation of death , from the day", "following date of death till the the disbursement of 1st revised pension. Such amount", "shall be paid as arrear/recovery along with 1st revised pension.", "Comprehensive Pension Management System User Manual (Version 6.2)", "188"]}, {"number": "10.5", "title": "Revision of Pension to FP (Eligible family", "content": ["member mentioned in PPO)", "This type of revision will be done when after the death of pensioner, the claimant’- Spouse-", "name is mentioned in the PPO. In such case, documents that will be required to be submitted", "in this case shall be", "1. Death certificate of Pensioner(s)", "2. Life certificate/DLC of the claimant", "3. Revised Mandate Form/Cancelled cheque and Undertaking, if not available.", "Steps for this revision are as follows: -", " First of all, login with the DH Pension", " Go to the Revision -> Revision of Pension tab Fig (10.5.b).", "Fig (10.5.b)", " Here fill all required information like PPO No and select the “Reason for Revision”", "Fig (10.5.c).", "Comprehensive Pension Management System User Manual (Version 6.2)", "189", "Fig (10.5.c)", " Now click on the edit button (pencil icon) in last Action column.", " Then next screen opens, here fill the required details and then click on the “Save”", "button Fig (10.5.d).", "Comprehensive Pension Management System User Manual (Version 6.2)", "190", "Fig (10.5.d)", " Now Go to-> Revision->Revision Sanction Order tab shown in Fig (10.5.e)", "Fig (10.5.e)", " From this screen you can view the Sanction order, by clicking on the View link Fig", "(10.5.e). Also you can take the printout of this Sanction Order Fig (10.5.f).", "Comprehensive Pension Management System User Manual (Version 6.2)", "191", "Fig (10.5.f)", " Now click on the Last Column Verify link to send record for AAO approval Fig", "(10.5.e).", " Now login with the AAO Pension. Go to ->Approval->Revision->Revision Sanction", "Order Shown in Fig (10.5.g)", "Fig (10.5.g)", " Now Click on the View link to see the Sanction order and then Approve/Return the", "record by click on the Approve/Return link and send record for AO approval Fig", "(10.5.g).", " Now login with the AO Pension (only on IE Browser).", " Go to Approval>Revision>Revision Sanction Order", " Click on View link to see or take the print of the Sanction Order, attach the DSc in the", "system for digital signature and click on the Approve link.", " Now login with AO PDA, Go to->Allotment->Allotment To PDA DH Fig (10.5.i)", "Comprehensive Pension Management System User Manual (Version 6.2)", "192", "Fig (10.5.i)", " Now Allot the case to the PDA DH by selecting the respective DH from the dropdown", "list and Click on “Send To DH” button.", " After this login with PDA DH, Go to ->Action->PDA Section->Revision Sanction Order,", "Fig (10.5.j)", "Fig (10.5.j)", "After this the record will for Vendor Verification, GO to->Action->Vendor Verification", "Fig (10.5.k)", "Comprehensive Pension Management System User Manual (Version 6.2)", "193", "Fig (10.5.k)", " After this record will Go to->LC & DLC Verification->LC Verification, Fig (10.5.l).", "Comprehensive Pension Management System User Manual (Version 6.2)", "194", "Fig (10.5.l)", " Now Do the LC & DLC and according to the date filled in the revision module, pension", "shall be payable.", " After this the case will show in Next month ( from the month you process the case)", "Monthly Bill. Fig (10.5.m). An assessment shall be done on account of amount payable", ", if any, due to delay of intimation of death , from the day following date of death till", "the the disbursement of 1st revised pension. Such amount shall be paid as", "arrear/recovery along with 1st revised pension.", "Comprehensive Pension Management System User Manual (Version 6.2)", "195", "Chapter 11", "11.Profile Authentication"]}, {"number": "11.1", "title": "Retiree Profile Authentication", "content": ["In order to ensure that the data filled on retiree profile is correct and has been authenticated", "by AO Pension (in case of side channel) / HOO user (in case of BSNL), retiree profile upload", "utility has been developed. On service book verification page (Fig(11.1)) DH user shall view", "the retiree profile and if satisfied then a printout of retiree profile shall be taken (Fig(11.2)).", "Fig(11.1)", "Comprehensive Pension Management System User Manual (Version 6.2)", "196", "Fig (11.2)", "AO Pension (in case of side channel) / HOO user (in case of BSNL) will match the retiree details", "against physical records and the scanned copy is to be uploaded.", "Before signing, the retiree profile details shall be matched against physical records and in case", "of error necessary corrections be made. The scanned copy shall be uploaded (Fig(11.3)) only", "after it has been checked as mentioned above.", "Comprehensive Pension Management System User Manual (Version 6.2)", "197", "Fig (11.3)", "The uploaded verification form can be viewed by clicking eye button (Fig(11.3), Fig(11.4)).", "Fig (11.4)", "The form may then be sent to HOO(SSA Unit) for approval (Fig(11.5)).", "Comprehensive Pension Management System User Manual (Version 6.2)", "198", "Fig (11.5)", "Comprehensive Pension Management System User Manual (Version 6.2)", "199", "Chapter 12", "12. Upload utility", "There are scenarios wherein either orders have been issued in continuation of PPO or", "corrigendum have been issued. It is important that these orders – being critical in nature- are", "not just sent to the pensioner but are also uploaded to the pensioners’ dashboard. For this", "purpose, upload utility has been created.", "Some of the scenarios in which upload utility shall be used:", "1. Issue of corrigendum of PPO for inclusion of permanently disabled children by PPO.", "2. Sanction and payment of arrears of pension in cases where manual calculation has been", "done", "In such cases AO(pension)/AO(PDA) shall upload the documents which shall be visible on the", "pensioners’ dashboard.", "Fig (12.1)", "After entering the PPO number and clicking on the “Search” button (Fig(12.1)), the below", "stated screen will get displayed (Fig(12.2)). The file to be sent to Pensioner’s Dashboard shall", "be selected and then uploaded. The description of file shall be added (Fig(12.3)). It may be", "noted that multiple files may be sent to pensioner’s Dashboard. Files can be sent by clicking", "the Submit button (Fig(12.3)).", "Comprehensive Pension Management System User Manual (Version 6.2)", "200", "Fig (12.2)", "Fig (12.3)", "The AO(pension)/AO(PDA) can view the history or date wise records for the information", "being shared with the pensioner on its dashboard Fig(12.4).", "Comprehensive Pension Management System User Manual (Version 6.2)", "201", "Fig (12.4)", "Similarly, the pensioner can also view the information shared by the Department on his/her", "dashboard by clicking on the tab “Shared Documents” (Fig(12.5), Fig(12.6)).", "Fig (12.5)", "Comprehensive Pension Management System User Manual (Version 6.2)", "202", "Fig (12.6)", "Chapter 13", "13. Bill / Payment slip generation", "For reconciliation sanction sent from PDA section with the bills appearing on PFMS and to", "ensure smooth processing, PDF for the bills sent to PFMS for payment can be generated. For", "bills sent on a day, a pdf can be generated. The date selected for generation of PDF is the date", "on which DH created the bills.", "The option of Bill Type and Status Type is to be selected as per the below stated criteria", "(Fig(13.1), Fig(13.2), Fig(13.3))according to user needs:-", "Comprehensive Pension Management System User Manual (Version 6.2)", "203", "Fig (13.1)", "Fig (13.2)", "Fig (13.3)", "A PDF of the bills generated in the particular time frame will be displayed/downloaded on", "your desired location which can be sent to cash as sanction. The PDF (Fig(13.4)) can be", "generated for the files sent on a day and handed over to Cash for DSC.", "Comprehensive Pension Management System User Manual (Version 6.2)", "204", "Fig (13.4)", "Chapter 14", "14. Updation of Mobile, Email and address", "For updating mobile number, Email or Address of a pensioner after finalisation of retirement", "benefits, following process will be followed.", "The user/pensioner will have to first login using the PAN no. as the Username. After login,", "click on the profile picture and select the option “Edit Profile” (Fig(14.1).", "Comprehensive Pension Management System User Manual (Version 6.2)", "205", "Fig (14.1)", "A Pop-up window (Fig(14.2)) will be displayed with the option to choose the following details", "which the user wants to edit or change: -", "Fig (14.2)", "The user has to select the option which he/she wants to change or modify."]}, {"number": "14.1", "title": "Mobile Number Update", "content": ["Upon selecting ‘Mobile Number’, the following screen will be displayed (Fig(14.3))", "Comprehensive Pension Management System User Manual (Version 6.2)", "206", "Fig(14.3)", "<PERSON><PERSON><PERSON> can enter his/her mobile number and then select either his/her registered email ID", "or the entered mobile number to receive an OTP to verify the number (Fig (14.3)). Upon", "receiving the OTP, retiree should enter the OTP (Fig (14.4)) and save which will then update", "the mobile number.", "Fig (14.4)"]}, {"number": "14.2", "title": "Email ID Update", "content": ["Upon selecting ‘Email ID’, the following screen will be displayed (Fig(14.5))", "Comprehensive Pension Management System User Manual (Version 6.2)", "207", "Fig (14.5)", "<PERSON><PERSON><PERSON> can enter his/her new email ID and the click on generate OTP which will then send", "an OTP to the registered mobile number (Fig (14.5)). <PERSON><PERSON><PERSON> should then enter the OTP", "received and click on save (Fig (14.6)) which would then update the email ID.", "Fig (14.6)"]}, {"number": "14.3", "title": "Address Update", "content": ["To update address, pensioner would be taken to the pensioner grievance page where a", "grievance related to updation of address can be registered (Fig (14.7)).", "Comprehensive Pension Management System User Manual (Version 6.2)", "208", "Fig (14.7)", "Once such a grievance has been registered, it will be assigned to the respective DH who will", "then update the address. Uploading of proof of address is mandatory in such case. For", "more details on Grievance Management please refer Chapter 6.", "Chapter 15", "15. Other pension Types", "The pension for VRS/superannuation pension/Pro-rata/compensation pension shall be as per", "above user manual. However, following changes have to be kept in mind while processing the", "pension under Compulsory retirement/Invalid pension/Compassionate allowance.", "For these three categories of retirement, the application of applicant is eligible for", "commutation only after medical examination. So, the date on which the medical authority", "signs the medical report in Part III of Form 4 shall be required to be entered in retiree profile", "as in Fig 15.1.", "Comprehensive Pension Management System User Manual (Version 6.2)", "209", "Fig (15.1)", "Based on the date entered, the commutation will get calculated as per CCS (Commutation of", "Pension) Rules, 1981 and get displayed in pension papers. This shall be the only change in", "case of Invalid pension. Additionally, form 7 shall be provided with additional input fields in", "case of other two retirement types detailed as under."]}, {"number": "15.1", "title": "Compulsory retirement", "content": ["In form 7 in case of compulsory retirement, at the time of creating the retiree profile, the", "Date of Medical Report is to be provided Fig(15.1.1).", "Comprehensive Pension Management System User Manual (Version 6.2)", "210", "Fig (15.1.1)", "After processing the case just like for the Normal Pension case, when the user reached at the", "Form 7 level, careful assessment is required at the time of entering the Percentage (%) at", "which the pension and DCRG is to be reduced Fig(15.1.2).", "Fig (15.1.2)", "The impact of which can be seen at the Form 7 which the system calculates automatically", "(Fig(15.1.3), Fig(15.1.4), Fig(15.1.5)).", "Comprehensive Pension Management System User Manual (Version 6.2)", "211", "Fig (15.1.3)", "Fig (15.1.4)", "Comprehensive Pension Management System User Manual (Version 6.2)", "212", "Fig (15.1.5)"]}, {"number": "15.2", "title": "Compassionate Allowance", "content": ["The DH at the time of creating the retiree profile will exercise the option of “Removal/Dismissal", "from service (Rule 24 and 41)” (Fig(15.2.1)) and the rest of the process flow will continue just", "like the same we process for the Normal Pension cases. However, at the time of the filling of", "Form 7, the DH must ensure to enter the correct and verified amount for the field wherein case", "of removal/dismissal from service whether orders of the competent authority have been", "obtained for grant of compassionate allowance and if so, at what rate (Please see Rule 41).", "Fig (15.2.1)", "Comprehensive Pension Management System User Manual (Version 6.2)", "213", "The impact of which reflects in the Form 7 (Fig(15.2.2), Fig(15.2.3)).", "Fig (15.2.2)", "Fig (15.2.3)", "Comprehensive Pension Management System User Manual (Version 6.2)", "214"]}, {"number": "15.3", "title": "Miscellaneous issues", "content": ["In order to account for delayed submission of commutation, on Form 7 has been provided. As", "per rule 6 of CCS (Commutation of Pension ) Rules , 1981, the user will enter the date of", "receipt of Form 1/Medical Report signing date (In case where form is submitted after the date", "of retirement) (Fig(15.3.1)). In case where Form for commutation is submitted before date", "of retirement then commutation shall become absolute on the day following date of", "retirement.", "Fig (15.3.1)", "The impact will reflect at the time of processing of Form 7, the factor of commutation will", "take effect from the Date of submission of Form 1/Medical Report, i.e., In the case where", "the form is submitted after retirement.", "**********************", "Comprehensive Pension Management System User Manual (Version 6.2)", "215", "Chapter 16", "16. Income Tax Processing", "AO PDA will allot the Case to DH PDA. After this, DH PDA will login and process the case", "further.", "Now DH PDA will go to the “Investment Declaration”. DH PDA can see the detail of particular", "record by click on the “View” link shown in the last column of the grid.", "Proposed Declaration: Uploading of Supporting documents against savings/investment is not", "mandatory so the figures will only be checked in r/o ceiling as per IT Act.", "Actual Declaration: Uploading of Supporting documents against savings/investment is", "mandatory so the figures as well as Proof of saving will also be checked.", "In case of documents not clear/Documents not as per amount fed, pensioner may be", "requested to re upload. In such case, the declaration may be admitted as per available", "document with remarks mentioning that pensioner may upload proper document in respect", "of rejected column. After receiving of correct supporting document in fresh declaration,", "based on revised declaration income tax may be processed. should be processed. Fig(16.1)", "Fig(16.1)", "Comprehensive Pension Management System User Manual (Version 6.2)", "216", "Once the DHPDA click on the View link, he can see the below screen .Now DHPDA will check", "all the details filled by <PERSON><PERSON><PERSON> and send it to AAOPDA ,by selecting AAOPDA from the given", "dropdownlist and click on the Submit button as shown in Fig(16.2)", "Fig(16.2)", "Now AAOPDA will login and Process the case further.", "After login, AAOPDA will go to the “Investment Declaration Approval”. Now AAOPDA can", "check the detail by click the View link shown in the grid. After crosschecking the detail he can", "Approve or Return the record by click on the “Approve” or “Return” button shown in the grid", "shown in Fig(16.3)", ".", "Fig(16.3)", "Comprehensive Pension Management System User Manual (Version 6.2)", "217", "Now AAOPDA will go to the IT Calculation Sheet. From hereon, AAO can see the calculation", "Sheet by click on the View click shown in the grid Fig(16.4).", "Fig(16.4)", "Once AAOPDA click on the View link of “View” the below shown page will open Fig (16.5).", "Fig(16.5)", "Comprehensive Pension Management System User Manual (Version 6.2)", "218", "Also AAOPDA can see the “Salary Breakup” from here. (Fig 16.6)", "Fig(16.6)", "If no proposed/actual declaration is sent by Pensioner, AAO PDA will run- Auto Calculation.", "Fig(16.7)", "Fig(16.7)", "And can see the IT calculation for any pensioner by feeding PPO No. Fig(16.8)", "Comprehensive Pension Management System User Manual (Version 6.2)", "219", "Fig(16.8)", "********End*******"]}]}]}