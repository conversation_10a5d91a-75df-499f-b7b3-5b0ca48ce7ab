"""
Ultra-Optimized RAG System for CPMS Chatbot
Combines FAQ cache, FAISS search, and aggressive caching for sub-10s responses
"""
import time
import uuid
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from backend.config import config
from backend.cache.faq_cache import faq_cache
from backend.models.optimized_model_handler import OptimizedCPMSModelHandler

# Try FAISS first, fallback to ChromaDB
try:
    from backend.data.faiss_vector_store import FAISSVectorStore
    FAISS_AVAILABLE = True
    print("✅ Using FAISS for ultra-fast vector search")
except ImportError:
    from backend.data.optimized_vector_store import OptimizedCPMSVectorStore as FAISSVectorStore
    FAISS_AVAILABLE = False
    print("⚠️ FAISS not available, using ChromaDB")

class UltraOptimizedRAGSystem:
    def __init__(self, model_type: str = "gguf"):
        """Initialize ultra-optimized RAG system"""
        self.model_type = model_type
        self.session_id = str(uuid.uuid4())

        # Initialize components with error handling
        self.vector_store = None
        self.model_handler = None
        self.executor = ThreadPoolExecutor(max_workers=4)

        print(f"🚀 Initializing Ultra-Optimized RAG System...")
        print(f"Model Type: {model_type}")
        print(f"Session ID: {self.session_id[:8]}...")

        # Initialize vector store
        try:
            self.vector_store = FAISSVectorStore() if FAISS_AVAILABLE else FAISSVectorStore()
            print("✅ Vector store initialized")
        except Exception as e:
            print(f"❌ Vector store initialization failed: {e}")
            raise

        # Initialize model handler
        try:
            self.model_handler = OptimizedCPMSModelHandler(model_type=model_type)
            print("✅ Model handler initialized")
        except Exception as e:
            print(f"❌ Model handler initialization failed: {e}")
            raise

        print("🎯 Ultra-Optimized RAG System ready!")

    def query(self, user_query: str, include_sources: bool = True) -> Dict[str, Any]:
        """
        Process user query with ultra-fast optimizations

        Priority order:
        0. Query Relevance Check (filter out off-topic questions)
        1. Persistent Cache (exact match, instant response)
        2. FAQ Cache (instant response)
        3. Session Cache (near-instant)
        4. Dynamic Cache (fast)
        5. Full RAG Pipeline (optimized)
        """
        start_time = time.time()
        print(f"🔍 Processing query: {user_query}")

        # Step 0: Check if query is relevant to CPMS/pension topics
        if config.ENABLE_QUERY_FILTERING and not self._is_query_relevant(user_query):
            processing_time = time.time() - start_time
            print(f"🚫 Query rejected as off-topic: {user_query}")
            return self._create_off_topic_response(user_query, processing_time)

        # Step 1: Check persistent cache for exact question match (highest priority)
        persistent_response = faq_cache.get_persistent_answer(user_query)
        if persistent_response:
            processing_time = time.time() - start_time
            print(f"💾 Persistent cache hit! Response in {processing_time:.3f}s")
            return persistent_response

        # Step 2: Check FAQ cache for instant response
        faq_response = faq_cache.get_faq_response(user_query)
        if faq_response:
            processing_time = time.time() - start_time
            print(f"⚡ FAQ cache hit! Response in {processing_time:.3f}s")
            return faq_response

        # Step 3: Check session cache
        session_response = faq_cache.get_session_cache(self.session_id, user_query)
        if session_response:
            processing_time = time.time() - start_time
            print(f"🔥 Session cache hit! Response in {processing_time:.3f}s")
            return session_response

        # Step 4: Full RAG pipeline with parallel processing
        return self._process_with_rag(user_query, include_sources, start_time)

    def _process_with_rag(self, user_query: str, include_sources: bool, start_time: float) -> Dict[str, Any]:
        """Process query through optimized RAG pipeline"""

        if config.PARALLEL_RETRIEVAL:
            return self._parallel_rag_processing(user_query, include_sources, start_time)
        else:
            return self._sequential_rag_processing(user_query, include_sources, start_time)

    def _parallel_rag_processing(self, user_query: str, include_sources: bool, start_time: float) -> Dict[str, Any]:
        """Process RAG with parallel retrieval and generation"""

        # Submit parallel tasks
        context_future = self.executor.submit(
            self.vector_store.get_relevant_context,
            user_query,
            config.MAX_CONTEXT_LENGTH
        )

        # Get context (should be fast with FAISS)
        try:
            context = context_future.result(timeout=config.VECTOR_SEARCH_TIMEOUT)
        except Exception as e:
            print(f"⚠️ Context retrieval failed or timed out: {e}")
            context = "Unable to retrieve relevant context."

        # Generate response
        print("🤖 Generating response...")
        response_start = time.time()

        # Create optimized prompt
        prompt = self._create_optimized_prompt(user_query, context)
        response = self.model_handler.generate_response(
            prompt,
            max_tokens=config.MAX_NEW_TOKENS,
            temperature=config.TEMPERATURE
        )

        response_time = time.time() - response_start
        print(f"📝 Response generated in {response_time:.2f}s")

        # Get sources if requested
        sources = []
        if include_sources:
            try:
                source_docs = self.vector_store.similarity_search(user_query, k=3)
                sources = [
                    {
                        'source': doc['metadata'].get('source', 'Unknown'),
                        'relevance_score': round(doc['score'], 3),
                        'content_preview': doc['content'][:100] + "..." if len(doc['content']) > 100 else doc['content']
                    }
                    for doc in source_docs[:2]  # Limit to top 2 sources
                ]
            except Exception as e:
                print(f"⚠️ Source retrieval failed: {e}")

        # Calculate total processing time
        processing_time = time.time() - start_time

        # Prepare result
        result = {
            'query': user_query,
            'response': response,
            'processing_time': round(processing_time, 2),
            'model_type': self.model_type,
            'sources': sources,
            'optimized': True,
            'cache_type': 'None',
            'cached': False
        }

        # Cache the response for future use
        faq_cache.cache_response(user_query, result)
        faq_cache.cache_session_response(self.session_id, user_query, result)
        faq_cache.cache_persistent_answer(user_query, result)  # Cache persistently

        print(f"✅ Query processed in {processing_time:.2f}s")
        return result

    def _sequential_rag_processing(self, user_query: str, include_sources: bool, start_time: float) -> Dict[str, Any]:
        """Sequential RAG processing (fallback)"""

        # Step 1: Retrieve context
        print("🔍 Retrieving context...")
        context = self.vector_store.get_relevant_context(user_query, config.MAX_CONTEXT_LENGTH)

        # Step 2: Generate response
        print("🤖 Generating response...")
        prompt = self._create_optimized_prompt(user_query, context)
        response = self.model_handler.generate_response(prompt)

        # Step 3: Get sources
        sources = []
        if include_sources:
            source_docs = self.vector_store.similarity_search(user_query, k=2)
            sources = [
                {
                    'source': doc['metadata'].get('source', 'Unknown'),
                    'relevance_score': round(doc['score'], 3)
                }
                for doc in source_docs
            ]

        processing_time = time.time() - start_time

        result = {
            'query': user_query,
            'response': response,
            'processing_time': round(processing_time, 2),
            'model_type': self.model_type,
            'sources': sources,
            'optimized': True,
            'cached': False
        }

        # Cache the response
        faq_cache.cache_response(user_query, result)
        faq_cache.cache_session_response(self.session_id, user_query, result)
        faq_cache.cache_persistent_answer(user_query, result)  # Cache persistently

        print(f"✅ Query processed in {processing_time:.2f}s")
        return result

    def _create_optimized_prompt(self, user_query: str, context: str) -> str:
        """Create optimized prompt for faster generation"""
        # Shorter, more direct prompt for speed
        prompt = f"""Based on the CPMS documentation below, provide a helpful and accurate answer.

Context:
{context}

Question: {user_query}

Answer (be concise and helpful):"""

        return prompt

    def _is_query_relevant(self, user_query: str) -> bool:
        """Check if the user query is relevant to CPMS/pension topics"""
        query_lower = user_query.lower().strip()
        import re

        # First check for CPMS-related keywords (prioritize positive matches)
        for keyword in config.CPMS_KEYWORDS:
            if keyword.lower() in query_lower:
                print(f"✅ Query contains CPMS keyword: '{keyword}'")
                return True

        # Then check for irrelevant topics using word boundaries to avoid false matches
        # This prevents "certificate" from being flagged because it contains "cat"
        for topic in config.IRRELEVANT_TOPICS:
            # Use word boundaries for single words, substring for phrases
            if ' ' in topic:
                # Multi-word phrases - use substring matching
                if topic.lower() in query_lower:
                    print(f"❌ Query contains irrelevant topic: '{topic}'")
                    return False
            else:
                # Single words - use word boundaries to avoid false matches
                pattern = r'\b' + re.escape(topic.lower()) + r'\b'
                if re.search(pattern, query_lower):
                    print(f"❌ Query contains irrelevant topic: '{topic}'")
                    return False

        # Check if it's a general greeting or help request
        general_patterns = [
            r'\bhello\b', r'\bhi\b', r'\bhelp\b', r'\bwhat can you do\b',
            r'\bwhat do you know\b', r'\bwho are you\b', r'\bwhat is this\b',
            r'\bhow does this work\b', r'\bwhat can you help with\b'
        ]

        for pattern in general_patterns:
            if re.search(pattern, query_lower):
                print(f"✅ Query is general help/greeting: '{pattern}'")
                return True

        # If query is very short (< 3 words) and no keywords, likely off-topic
        if len(query_lower.split()) < 3:
            print(f"❌ Query too short and no CPMS keywords found")
            return False

        # Default: allow if uncertain (to avoid false negatives)
        print(f"⚠️ Query relevance uncertain, allowing through")
        return True

    def _create_off_topic_response(self, user_query: str, processing_time: float) -> Dict[str, Any]:
        """Create a polite response for off-topic questions"""
        response = f"""I'm sorry, but I can only help with questions related to the Comprehensive Pension Management System (CPMS) and pension-related matters.

Your question appears to be about a different topic. I'm specifically designed to assist with:

🏛️ **CPMS Portal & Login**
📋 **Pension Forms & Procedures**
💰 **Pension Status & Payments**
📞 **Contact Information & Support**
👨‍👩‍👧‍👦 **Family Pension & Benefits**
📄 **Document Requirements**

**Some example questions I can help with:**
• What is CPMS?
• How can I login to CPMS portal?
• When is my pension credited?
• What documents are needed for family pension?
• How to check pension status?

**For CPMS assistance:**
📞 Helpline: {config.HELPLINE_NUMBER}
📧 Email: {config.TECHNICAL_EMAIL}
🌐 Portal: www.dotpension.gov.in

Please ask me a pension-related question, and I'll be happy to help!"""

        return {
            'query': user_query,
            'response': response,
            'processing_time': round(processing_time, 3),
            'model_type': 'filter',
            'sources': [],
            'optimized': True,
            'cache_type': 'Filtered',
            'cached': False,
            'filtered': True,
            'reason': 'Off-topic query'
        }

    def suggest_questions(self, count: int = 5) -> List[str]:
        """Get suggested questions from FAQ cache"""
        suggestions = [
            "What is CPMS?",
            "How can I login to CPMS portal?",
            "When is pension credited?",
            "What is the helpline number?",
            "How to change my bank account details?",
            "How to submit tax declarations?",
            "What documents are needed for family pension?",
            "How to lodge a grievance?",
            "What is the life certificate procedure?",
            "How to check pension status?"
        ]
        return suggestions[:count]

    def get_system_stats(self) -> Dict[str, Any]:
        """Get comprehensive system statistics"""
        stats = {
            'model_type': self.model_type,
            'session_id': self.session_id[:8],
            'optimizations_enabled': {
                'faq_cache': config.ENABLE_FAQ_CACHE,
                'session_cache': config.ENABLE_SESSION_CACHE,
                'persistent_cache': config.ENABLE_PERSISTENT_CACHE,
                'query_filtering': config.ENABLE_QUERY_FILTERING,
                'vector_cache': config.ENABLE_VECTOR_CACHE,
                'response_cache': config.ENABLE_RESPONSE_CACHING,
                'parallel_retrieval': config.PARALLEL_RETRIEVAL,
                'faiss_index': FAISS_AVAILABLE and config.ENABLE_FAISS_INDEX,
                'aggressive_caching': config.AGGRESSIVE_CACHING
            }
        }

        # Add vector store stats
        if self.vector_store:
            stats['vector_store'] = self.vector_store.get_collection_stats()

        # Add cache stats
        stats['cache_stats'] = faq_cache.get_cache_stats()

        # Add model info
        if self.model_handler:
            stats['model_info'] = self.model_handler.get_model_info()

        return stats

    def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check"""
        issues = []

        # Check vector store
        if not self.vector_store:
            issues.append("Vector store not initialized")
        elif not hasattr(self.vector_store, 'get_collection_stats'):
            issues.append("Vector store missing required methods")

        # Check model handler
        if not self.model_handler:
            issues.append("Model handler not initialized")

        # Check FAISS availability
        if config.ENABLE_FAISS_INDEX and not FAISS_AVAILABLE:
            issues.append("FAISS not available but enabled in config")

        # Check cache configuration
        if not config.ENABLE_FAQ_CACHE:
            issues.append("FAQ cache disabled - responses will be slower")

        if not config.ENABLE_PERSISTENT_CACHE:
            issues.append("Persistent cache disabled - no cross-restart caching")

        if not config.ENABLE_QUERY_FILTERING:
            issues.append("Query filtering disabled - may answer off-topic questions")

        return {
            'status': 'healthy' if not issues else 'issues',
            'issues': issues,
            'optimizations_active': len([k for k, v in self.get_system_stats()['optimizations_enabled'].items() if v])
        }

    def clear_session_cache(self):
        """Clear current session cache"""
        # This would be implemented to clear only current session
        print(f"🧹 Session cache cleared for {self.session_id[:8]}")

    def __del__(self):
        """Cleanup resources"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)
