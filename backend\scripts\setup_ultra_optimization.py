"""
Setup script for Ultra-Optimized CPMS Chatbot
Installs dependencies and runs optimization pipeline
"""
import subprocess
import sys
import os
from pathlib import Path

def fix_huggingface_hub_compatibility():
    """Fix huggingface_hub compatibility issue with sentence-transformers"""
    print("🔄 Fixing huggingface_hub compatibility...")

    try:
        # Directly downgrade huggingface_hub to a known compatible version
        print("🔄 Downgrading huggingface_hub to compatible version...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install",
            "huggingface_hub==0.16.4", "--force-reinstall", "--quiet"
        ])
        print("✅ huggingface_hub downgraded to compatible version")

        # Test that sentence-transformers can be imported
        try:
            import sentence_transformers
            print("✅ sentence-transformers compatibility verified")
            return True
        except ImportError as e:
            print(f"⚠️ sentence-transformers still has issues: {e}")
            return False

    except Exception as e:
        print(f"❌ Failed to fix huggingface_hub: {e}")
        return False

def install_faiss():
    """Install FAISS for ultra-fast vector search"""
    print("🔄 Installing FAISS for ultra-fast vector search...")

    try:
        # Try CPU version first (more compatible)
        subprocess.check_call([sys.executable, "-m", "pip", "install", "faiss-cpu"])
        print("✅ FAISS-CPU installed successfully")
        return True
    except subprocess.CalledProcessError:
        try:
            # Try GPU version if available
            subprocess.check_call([sys.executable, "-m", "pip", "install", "faiss-gpu"])
            print("✅ FAISS-GPU installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install FAISS")
            return False

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("🔄 Checking dependencies...")
    
    required_packages = [
        "sentence-transformers",
        "chromadb", 
        "gradio",
        "numpy",
        "torch",
        "transformers"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - Missing")
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Installing missing packages...")
        
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ Installed {package}")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {package}")
                return False
    
    return True

def run_optimization_pipeline():
    """Run the complete optimization pipeline"""
    print("\n🚀 Running Ultra-Optimization Pipeline...")
    print("=" * 60)
    
    try:
        # Import and run precomputation
        from precompute_optimizations import main as run_precompute
        success = run_precompute()
        
        if success:
            print("\n✅ Ultra-optimization pipeline completed successfully!")
            print("🎯 Your CPMS chatbot is now optimized for ultra-fast responses!")
            return True
        else:
            print("\n❌ Optimization pipeline failed")
            return False
            
    except Exception as e:
        print(f"\n❌ Optimization pipeline error: {e}")
        return False

def test_optimized_system():
    """Test the optimized system"""
    print("\n🧪 Testing Ultra-Optimized System...")
    print("-" * 40)
    
    try:
        from ultra_optimized_rag_system import UltraOptimizedRAGSystem
        
        # Initialize system
        rag_system = UltraOptimizedRAGSystem(model_type="gguf")
        
        # Test query
        test_query = "What is CPMS?"
        print(f"Test Query: {test_query}")
        
        result = rag_system.query(test_query)
        
        print(f"✅ Response Time: {result['processing_time']}s")
        print(f"✅ Cache Status: {'Hit' if result.get('cached') else 'Miss'}")
        print(f"✅ Response Length: {len(result['response'])} characters")
        
        # Get system stats
        stats = rag_system.get_system_stats()
        print(f"\n📊 System Statistics:")
        print(f"   Documents: {stats['vector_store']['total_documents']}")
        print(f"   FAQ Cache: {stats['cache_stats']['precomputed_faqs']} precomputed")
        print(f"   Optimizations: {sum(stats['optimizations_enabled'].values())}/7 enabled")
        
        return True
        
    except Exception as e:
        print(f"❌ System test failed: {e}")
        return False

def create_launch_script():
    """Create a convenient launch script"""
    print("\n📝 Creating launch script...")
    
    launch_script = """#!/usr/bin/env python3
\"\"\"
Ultra-Optimized CPMS Chatbot Launcher
\"\"\"
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🚀 Launching Ultra-Optimized CPMS Chatbot...")
    
    try:
        from web_interface import main as launch_web
        launch_web()
    except KeyboardInterrupt:
        print("\\n👋 Chatbot stopped by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
"""
    
    with open("launch_optimized_chatbot.py", "w", encoding="utf-8") as f:
        f.write(launch_script)
    
    print("✅ Launch script created: launch_optimized_chatbot.py")

def display_performance_tips():
    """Display performance optimization tips"""
    print("\n💡 Performance Optimization Tips:")
    print("=" * 50)
    print("""
🔥 ULTRA-FAST RESPONSE STRATEGIES IMPLEMENTED:

1. ⚡ FAQ Cache (Sub-millisecond responses)
   • Top 50+ questions pre-answered
   • Instant responses for common queries
   • 24-hour cache duration

2. 🚀 FAISS Vector Search (Millisecond search)
   • HNSW index for approximate nearest neighbor
   • 10-100x faster than traditional search
   • Optimized for speed vs accuracy

3. 🧠 Aggressive Caching
   • Session-level query cache
   • Response caching with TTL
   • Pre-computed embeddings

4. ⚙️ Model Optimizations
   • Model loading cache
   • Optimized inference parameters
   • Parallel processing where possible

5. 📊 Context Preservation
   • Full context length maintained (1500 chars)
   • 4 documents for better accuracy
   • No compromise on answer quality

EXPECTED PERFORMANCE:
• FAQ Questions: < 0.1 seconds
• Cached Queries: < 1 second  
• New Queries: < 10 seconds
• Cache Hit Rate: 60-80%

USAGE TIPS:
• First query may be slower (model loading)
• Repeated questions are instant
• Similar questions benefit from cache
• System learns and improves over time
""")

def main():
    """Main setup function"""
    print("🚀 CPMS Chatbot Ultra-Optimization Setup")
    print("=" * 50)
    print("This script will optimize your chatbot for ultra-fast responses")
    print("Expected improvements: 60+ seconds → < 10 seconds")
    print()

    # Step 1: Fix compatibility issues
    if not fix_huggingface_hub_compatibility():
        print("⚠️ Could not fix huggingface_hub compatibility, continuing anyway...")

    # Step 2: Check dependencies
    if not check_dependencies():
        print("❌ Dependency check failed")
        return False

    # Step 3: Install FAISS
    if not install_faiss():
        print("⚠️ FAISS installation failed, will use ChromaDB (slower)")

    # Step 4: Run optimization pipeline
    if not run_optimization_pipeline():
        print("❌ Optimization failed")
        return False
    
    # Step 5: Test system
    if not test_optimized_system():
        print("⚠️ System test failed, but optimization may still work")

    # Step 6: Create launch script
    create_launch_script()

    # Step 7: Display tips
    display_performance_tips()
    
    print("\n🎉 Setup Complete!")
    print("=" * 30)
    print("To launch your ultra-optimized chatbot:")
    print("  python launch_optimized_chatbot.py")
    print("  OR")
    print("  python web_interface.py --optimized")
    print()
    print("🎯 Your chatbot is now ready for ultra-fast responses!")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Setup cancelled by user")
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)
