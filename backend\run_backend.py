#!/usr/bin/env python3
"""
Backend API Server Launcher
Run this first to start the backend API server
"""
import sys
import os

# Add workspace root to Python path (go up one level from backend folder)
workspace_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, workspace_root)

def main():
    """Launch the backend API server"""
    print("CPMS Backend API Server")
    print("=" * 50)
    print("Starting backend API server...")
    print("This will provide REST API endpoints for the frontend")
    print("Server will be available at: http://localhost:8001")
    print("API docs will be at: http://localhost:8001/docs")
    print("Frontend can connect to this backend")
    print("=" * 50)

    try:
        # Import and run the backend server
        from backend.api.server import main as run_server
        run_server()

    except KeyboardInterrupt:
        print("\nBackend API server stopped by user")
    except Exception as e:
        print(f"Error starting backend server: {e}")
        print("\nTroubleshooting:")
        print("1. Ensure dependencies are installed: pip install -r requirements.txt")
        print("2. Check that model files are accessible")
        print("3. Verify port 8001 is not in use")
        sys.exit(1)

if __name__ == "__main__":
    main()
