"""
Compatibility patch for huggingface_hub cached_download function
This fixes the compatibility issue between newer huggingface_hub and sentence-transformers
"""
import sys
import warnings

def apply_huggingface_compatibility_patch():
    """Apply compatibility patch for huggingface_hub"""
    try:
        import huggingface_hub

        # Check if list_repo_tree is missing and add it
        if not hasattr(huggingface_hub, 'list_repo_tree'):
            print("🔄 Adding missing list_repo_tree function...")

            def list_repo_tree(repo_id, **kwargs):
                """Compatibility wrapper for list_repo_tree"""
                try:
                    from huggingface_hub import list_repo_files
                    return list_repo_files(repo_id, **kwargs)
                except ImportError:
                    # Fallback: return empty list
                    return []

            huggingface_hub.list_repo_tree = list_repo_tree
            print("✅ Added list_repo_tree compatibility function")

        # Check if OfflineModeIsEnabled is missing and add it
        try:
            from huggingface_hub.utils import OfflineModeIsEnabled
        except ImportError:
            print("🔄 Adding missing OfflineModeIsEnabled...")

            class OfflineModeIsEnabled(Exception):
                """Compatibility class for OfflineModeIsEnabled"""
                pass

            # Add to utils module
            import huggingface_hub.utils
            huggingface_hub.utils.OfflineModeIsEnabled = OfflineModeIsEnabled
            print("✅ Added OfflineModeIsEnabled compatibility class")

        # Check if get_token is missing and add it
        try:
            from huggingface_hub.utils import get_token
        except ImportError:
            print("🔄 Adding missing get_token...")

            def get_token():
                """Compatibility function for get_token"""
                return None  # Return None for no token

            # Add to utils module
            import huggingface_hub.utils
            huggingface_hub.utils.get_token = get_token
            print("✅ Added get_token compatibility function")

        # Check if HF_HUB_CACHE is missing and add it
        try:
            from huggingface_hub.constants import HF_HUB_CACHE
        except (ImportError, AttributeError):
            print("🔄 Adding missing HF_HUB_CACHE...")

            from pathlib import Path

            # Add to constants module
            import huggingface_hub.constants
            default_cache = Path.home() / ".cache" / "huggingface" / "hub"
            huggingface_hub.constants.HF_HUB_CACHE = str(default_cache)
            print("✅ Added HF_HUB_CACHE compatibility constant")

        # Check if HF_HOME is missing and add it
        try:
            from huggingface_hub.constants import HF_HOME
        except (ImportError, AttributeError):
            print("🔄 Adding missing HF_HOME...")

            from pathlib import Path

            # Add to constants module
            import huggingface_hub.constants
            default_home = Path.home() / ".cache" / "huggingface"
            huggingface_hub.constants.HF_HOME = str(default_home)
            print("✅ Added HF_HOME compatibility constant")

        # Check if file_exists is missing and add it
        if not hasattr(huggingface_hub, 'file_exists'):
            print("🔄 Adding missing file_exists...")

            def file_exists(repo_id, filename, **kwargs):
                """Compatibility function for file_exists"""
                try:
                    from huggingface_hub import repo_exists
                    return repo_exists(repo_id)  # Simple fallback
                except ImportError:
                    return True  # Assume file exists

            huggingface_hub.file_exists = file_exists
            print("✅ Added file_exists compatibility function")

        # Check if split_torch_state_dict_into_shards is missing and add it
        if not hasattr(huggingface_hub, 'split_torch_state_dict_into_shards'):
            print("🔄 Adding missing split_torch_state_dict_into_shards...")

            def split_torch_state_dict_into_shards(*args, **kwargs):
                """Compatibility function for split_torch_state_dict_into_shards"""
                # Return a simple structure that won't break anything
                return {}, {}

            huggingface_hub.split_torch_state_dict_into_shards = split_torch_state_dict_into_shards
            print("✅ Added split_torch_state_dict_into_shards compatibility function")

        # Check if LastCommitInfo is missing and add it
        try:
            from huggingface_hub.hf_api import LastCommitInfo
        except ImportError:
            print("🔄 Adding missing LastCommitInfo...")

            class LastCommitInfo:
                """Compatibility class for LastCommitInfo"""
                def __init__(self, commit_id="unknown", commit_message="", commit_date=None):
                    self.commit_id = commit_id
                    self.commit_message = commit_message
                    self.commit_date = commit_date

            # Add to hf_api module
            import huggingface_hub.hf_api
            huggingface_hub.hf_api.LastCommitInfo = LastCommitInfo
            print("✅ Added LastCommitInfo compatibility class")

        # Check if cached_download is already available
        if hasattr(huggingface_hub, 'cached_download'):
            print("✅ cached_download already available")
            return True

        # Try to add cached_download function
        try:
            from huggingface_hub import hf_hub_download

            def cached_download(url, cache_dir=None, force_download=False, **kwargs):
                """Compatibility wrapper for cached_download using hf_hub_download"""
                # Remove unsupported parameters
                kwargs.pop('legacy_cache_layout', None)
                kwargs.pop('user_agent', None)

                # Extract repo_id and filename from URL if it's a HF URL
                if "huggingface.co" in url and "/resolve/" in url:
                    # Format: https://huggingface.co/repo_id/resolve/main/filename
                    parts = url.split("/resolve/")
                    if len(parts) == 2:
                        repo_part = parts[0].replace("https://huggingface.co/", "")
                        file_part = parts[1].split("/", 1)
                        if len(file_part) == 2:
                            revision = file_part[0]
                            filename = file_part[1]
                            return hf_hub_download(
                                repo_id=repo_part,
                                filename=filename,
                                revision=revision,
                                cache_dir=cache_dir,
                                force_download=force_download,
                                **kwargs
                            )

                # Fallback: try to download directly
                import requests
                from pathlib import Path

                if cache_dir is None:
                    cache_dir = Path.home() / ".cache" / "huggingface"

                cache_dir = Path(cache_dir)
                cache_dir.mkdir(parents=True, exist_ok=True)

                filename = url.split("/")[-1]
                cache_file = cache_dir / filename

                if not cache_file.exists() or force_download:
                    response = requests.get(url)
                    response.raise_for_status()
                    with open(cache_file, 'wb') as f:
                        f.write(response.content)

                return str(cache_file)

            # Monkey patch the function
            huggingface_hub.cached_download = cached_download
            print("✅ Applied cached_download compatibility patch")
            return True

        except ImportError as e:
            print(f"⚠️ Could not create compatibility patch: {e}")
            return False

    except ImportError:
        print("⚠️ huggingface_hub not available")
        return False

def ensure_sentence_transformers_compatibility():
    """Ensure sentence-transformers can import without cached_download errors"""
    try:
        # Apply the patch first
        apply_huggingface_compatibility_patch()
        
        # Try importing sentence-transformers
        import sentence_transformers
        print("✅ sentence-transformers imported successfully")
        return True
        
    except ImportError as e:
        if "cached_download" in str(e):
            print(f"❌ sentence-transformers still has cached_download issue: {e}")
            print("🔄 Trying alternative approach...")
            
            # Try downgrading huggingface_hub
            import subprocess
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", 
                    "huggingface_hub==0.16.4", "--force-reinstall", "--quiet"
                ])
                print("✅ Downgraded huggingface_hub to compatible version")
                
                # Try importing again
                import sentence_transformers
                print("✅ sentence-transformers imported successfully after downgrade")
                return True
                
            except Exception as e2:
                print(f"❌ Failed to fix compatibility: {e2}")
                return False
        else:
            print(f"❌ Other sentence-transformers import error: {e}")
            return False

if __name__ == "__main__":
    print("Testing huggingface_hub compatibility patch...")
    success = ensure_sentence_transformers_compatibility()
    if success:
        print("🎉 Compatibility patch successful!")
    else:
        print("❌ Compatibility patch failed")
