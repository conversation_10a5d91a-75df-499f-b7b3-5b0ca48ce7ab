"""
CPMS User Manual Data Processor
Processes structured user manual JSON to extract step-by-step procedures
"""
import json
import os
import sys
from typing import Dict, List, Any, Tuple
from pathlib import Path

# Add parent directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from backend.config import config

class CPMSUserManualProcessor:
    def __init__(self):
        self.manual_path = Path(config.BASE_DIR) / "structured_cpms_USER_MANUAL.json"
        self.processed_data = []
        self.qa_pairs = []

    def load_manual_data(self) -> Dict[str, Any]:
        """Load the structured user manual JSON"""
        try:
            with open(self.manual_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading user manual: {e}")
            return {}

    def extract_step_by_step_procedures(self, manual_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract step-by-step procedures from manual data"""
        procedures = []

        if 'chapters' not in manual_data:
            return procedures

        for chapter in manual_data['chapters']:
            chapter_num = chapter.get('number', 'Unknown')

            for section in chapter.get('sections', []):
                section_num = section.get('number', '')
                section_title = section.get('title', '')
                content = section.get('content', [])

                if content and isinstance(content, list):
                    # Process content for step-by-step procedures
                    procedure_text = self._extract_procedure_text(content)

                    if procedure_text and self._is_procedural_content(procedure_text):
                        procedure = {
                            'chapter': chapter_num,
                            'section': section_num,
                            'title': section_title,
                            'procedure': procedure_text,
                            'type': self._classify_procedure_type(section_title, procedure_text)
                        }
                        procedures.append(procedure)

        return procedures

    def _extract_procedure_text(self, content: List[str]) -> str:
        """Extract and clean procedure text from content"""
        if not content:
            return ""

        # Join content and clean up
        text = ' '.join(str(item) for item in content if item)

        # Remove figure references and page numbers
        text = self._clean_procedure_text(text)

        return text

    def _clean_procedure_text(self, text: str) -> str:
        """Clean and format procedure text"""
        # Remove common noise
        noise_patterns = [
            'Comprehensive Pension Management System User Manual (Version 6.2)',
            'Fig ', 'Figure ', '(Fig', 'Page ', 'Chapter '
        ]

        for pattern in noise_patterns:
            text = text.replace(pattern, '')

        # Clean up extra whitespace
        text = ' '.join(text.split())

        return text.strip()

    def _is_procedural_content(self, text: str) -> bool:
        """Check if content contains step-by-step procedures"""
        procedure_indicators = [
            'step', 'click', 'login', 'select', 'enter', 'fill', 'submit',
            'path to create', 'following steps', 'procedure', 'process',
            '→', 'then', 'after', 'next', 'button', 'tab', 'menu'
        ]

        text_lower = text.lower()
        return any(indicator in text_lower for indicator in procedure_indicators) and len(text) > 100

    def _classify_procedure_type(self, title: str, content: str) -> str:
        """Classify the type of procedure"""
        title_lower = title.lower() if title else ""
        content_lower = content.lower()

        if any(term in title_lower or term in content_lower for term in ['ao creation', 'ao login', 'create ao']):
            return 'AO_LOGIN_CREATION'
        elif any(term in title_lower or term in content_lower for term in ['dh creation', 'dh login', 'create dh']):
            return 'DH_CREATION'
        elif any(term in title_lower or term in content_lower for term in ['retiree profile', 'profile creation']):
            return 'RETIREE_PROFILE'
        elif any(term in title_lower or term in content_lower for term in ['form', 'fill form', 'submit form']):
            return 'FORM_PROCESSING'
        elif any(term in title_lower or term in content_lower for term in ['pension sanction', 'sanction']):
            return 'PENSION_SANCTIONING'
        elif any(term in title_lower or term in content_lower for term in ['grievance', 'complaint']):
            return 'GRIEVANCE_MANAGEMENT'
        elif any(term in title_lower or term in content_lower for term in ['bank', 'payment', 'pfms']):
            return 'PAYMENT_PROCESSING'
        elif any(term in title_lower or term in content_lower for term in ['mobile', 'email', 'address', 'update']):
            return 'PROFILE_UPDATE'
        else:
            return 'GENERAL_PROCEDURE'

    def generate_qa_pairs_from_procedures(self, procedures: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """Generate Q&A pairs from step-by-step procedures"""
        qa_pairs = []

        for procedure in procedures:
            proc_type = procedure['type']
            title = procedure['title']
            content = procedure['procedure']

            # Generate multiple Q&A pairs per procedure
            questions = self._generate_questions_for_procedure(proc_type, title, content)

            for question in questions:
                qa_pair = {
                    'question': question,
                    'answer': self._format_answer(content, proc_type),
                    'source': f"CPMS User Manual - {title}",
                    'procedure_type': proc_type,
                    'chapter': procedure['chapter'],
                    'section': procedure['section']
                }
                qa_pairs.append(qa_pair)

        return qa_pairs

    def _generate_questions_for_procedure(self, proc_type: str, title: str, content: str) -> List[str]:
        """Generate relevant questions for each procedure type"""
        questions = []

        question_templates = {
            'AO_LOGIN_CREATION': [
                "How to create AO login in CPMS?",
                "What is the path to create AO user login?",
                "How does CCA create AO login?",
                "What are the steps to create Accounts Officer login?",
                "How to assign authorization to AO in CPMS?"
            ],
            'AAO_LOGIN_CREATION': [
                "How to create AAO login in CPMS?",
                "What is the path to create AAO user login?",
                "How does AO create AAO login?",
                "What are the steps to create Assistant Administrative Officer login?",
                "How to assign authorization to AAO in CPMS?",
                "Path to create AAO login?",
                "How to create AAO user?",
                "AAO login creation process"
            ],
            'DH_CREATION': [
                "How to create DH login in CPMS?",
                "What is the procedure to create Dealing Hand login?",
                "How to create DH user in pension section?",
                "What are the steps for DH creation in CPMS?",
                "How to assign rights to DH user?"
            ],
            'RETIREE_PROFILE': [
                "How to create retiree profile in CPMS?",
                "What are the steps to create pensioner profile?",
                "How to fill retiree details in CPMS?",
                "What information is required for retiree profile?",
                "How to add new retiree in CPMS?"
            ],
            'FORM_PROCESSING': [
                "How to fill pension forms in CPMS?",
                "What is the procedure to submit forms?",
                "How to process pension forms?",
                "What are the steps to fill Form 7/8/12/14/18?",
                "How to verify and approve forms?"
            ],
            'PENSION_SANCTIONING': [
                "How is pension sanctioned in CPMS?",
                "What is the pension sanction process?",
                "How to approve pension calculation?",
                "What are the steps in pension sanctioning?",
                "How to digitally sign pension orders?"
            ],
            'GRIEVANCE_MANAGEMENT': [
                "How to lodge grievance in CPMS?",
                "What is the procedure to raise complaint?",
                "How to track grievance status?",
                "How to resolve pensioner grievances?",
                "What are the steps to submit grievance?"
            ],
            'PAYMENT_PROCESSING': [
                "How to process pension payments?",
                "What is the PFMS payment procedure?",
                "How to update bank details?",
                "How to generate pension bills?",
                "What are the steps for payment processing?"
            ],
            'PROFILE_UPDATE': [
                "How to update mobile number in CPMS?",
                "How to change email address?",
                "What is the procedure to update address?",
                "How to modify pensioner details?",
                "How to update contact information?"
            ],
            'GENERAL_PROCEDURE': [
                f"How to {title.lower()}?" if title else "What is this procedure?",
                f"What are the steps for {title.lower()}?" if title else "What are the steps?",
                f"How to complete {title.lower()}?" if title else "How to complete this process?"
            ]
        }

        return question_templates.get(proc_type, [f"How to {title.lower()}?" if title else "What is this procedure?"])

    def _format_answer(self, content: str, proc_type: str) -> str:
        """Format the answer with proper structure"""
        if not content:
            return "Please refer to the CPMS User Manual for detailed instructions."

        # Add procedure type context
        type_context = {
            'AO_LOGIN_CREATION': "To create an AO (Accounts Officer) login in CPMS:",
            'DH_CREATION': "To create a DH (Dealing Hand) login in CPMS:",
            'RETIREE_PROFILE': "To create a retiree profile in CPMS:",
            'FORM_PROCESSING': "To process pension forms in CPMS:",
            'PENSION_SANCTIONING': "For pension sanctioning in CPMS:",
            'GRIEVANCE_MANAGEMENT': "To manage grievances in CPMS:",
            'PAYMENT_PROCESSING': "For payment processing in CPMS:",
            'PROFILE_UPDATE': "To update profile information in CPMS:",
            'GENERAL_PROCEDURE': "For this CPMS procedure:"
        }

        context = type_context.get(proc_type, "For this CPMS procedure:")

        # Format the content with better structure
        formatted_content = self._structure_procedure_steps(content)

        answer = f"{context}\n\n{formatted_content}\n\nFor any technical issues, contact CPMS helpline at {config.HELPLINE_NUMBER} or email {config.HELPDESK_EMAIL}."

        return answer

    def _structure_procedure_steps(self, content: str) -> str:
        """Structure the procedure content into clear steps"""
        # Split content into bullet points or steps
        lines = content.split('•')
        if len(lines) == 1:
            lines = content.split('◦')
        if len(lines) == 1:
            lines = content.split('\n')

        structured_steps = []
        step_num = 1

        for line in lines:
            line = line.strip()
            if line and len(line) > 10:  # Filter out very short lines
                # Clean up the line
                if line.startswith(('a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)')):
                    structured_steps.append(f"   {line}")
                elif any(line.lower().startswith(word) for word in ['login', 'click', 'select', 'enter', 'fill', 'go to', 'after']):
                    structured_steps.append(f"{step_num}. {line}")
                    step_num += 1
                else:
                    structured_steps.append(f"   {line}")

        return '\n'.join(structured_steps) if structured_steps else content

    def process_all_manual_data(self) -> Tuple[List[Dict[str, Any]], List[Dict[str, str]]]:
        """Process all user manual data and return procedures and Q&A pairs"""
        print("🔄 Loading CPMS User Manual data...")
        manual_data = self.load_manual_data()

        if not manual_data:
            print("❌ Failed to load user manual data")
            return [], []

        print("📋 Extracting step-by-step procedures...")
        procedures = self.extract_step_by_step_procedures(manual_data)
        print(f"✅ Extracted {len(procedures)} procedures")

        print("❓ Generating Q&A pairs from procedures...")
        qa_pairs = self.generate_qa_pairs_from_procedures(procedures)
        print(f"✅ Generated {len(qa_pairs)} Q&A pairs")

        # Save processed data
        self._save_processed_data(procedures, qa_pairs)

        return procedures, qa_pairs

    def _save_processed_data(self, procedures: List[Dict[str, Any]], qa_pairs: List[Dict[str, str]]):
        """Save processed data to files"""
        try:
            # Save procedures
            procedures_path = config.PROCESSED_DATA_PATH / "user_manual_procedures.json"
            with open(procedures_path, 'w', encoding='utf-8') as f:
                json.dump(procedures, f, indent=2, ensure_ascii=False)

            # Save Q&A pairs
            qa_path = config.PROCESSED_DATA_PATH / "user_manual_qa_pairs.json"
            with open(qa_path, 'w', encoding='utf-8') as f:
                json.dump(qa_pairs, f, indent=2, ensure_ascii=False)

            print(f"💾 Saved processed data to {config.PROCESSED_DATA_PATH}")

        except Exception as e:
            print(f"❌ Error saving processed data: {e}")

def main():
    """Main function to process user manual data"""
    processor = CPMSUserManualProcessor()
    procedures, qa_pairs = processor.process_all_manual_data()

    print("\n📊 Processing Summary:")
    print(f"   • Total Procedures: {len(procedures)}")
    print(f"   • Total Q&A Pairs: {len(qa_pairs)}")

    # Show sample procedures by type
    procedure_types = {}
    for proc in procedures:
        proc_type = proc['type']
        if proc_type not in procedure_types:
            procedure_types[proc_type] = 0
        procedure_types[proc_type] += 1

    print("\n📋 Procedure Types:")
    for proc_type, count in procedure_types.items():
        print(f"   • {proc_type}: {count} procedures")

if __name__ == "__main__":
    main()
