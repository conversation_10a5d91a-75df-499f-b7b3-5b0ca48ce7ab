<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPMS Professional Redesign Demo</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .demo-section {
            padding: var(--card-padding);
            margin: var(--spacing-xl) 0;
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-light);
            box-shadow: var(--shadow-card);
        }

        .demo-title {
            font-size: var(--text-2xl);
            font-weight: var(--font-weight-bold);
            margin-bottom: var(--spacing-lg);
            color: var(--text-primary);
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }

        .demo-item {
            padding: var(--spacing-lg);
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-light);
            text-align: center;
        }

        .component-showcase {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="app" style="height: auto; min-height: 100vh;">
        <!-- Enhanced Header Demo -->
        <header class="header">
            <div class="header-content">
                <div class="header-left">
                    <div class="logo">
                        <span class="logo-icon hover-lift">🏛️</span>
                        <div class="logo-text">
                            <h1>CPMS Professional Demo</h1>
                            <p>Refined, Modern & Accessible Interface</p>
                        </div>
                    </div>
                </div>
                <div class="header-right">
                    <div class="connection-status">
                        <div class="status-indicator connected"></div>
                        <span>Connected</span>
                    </div>
                    <button class="header-btn" title="Settings">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="header-btn" title="Info">
                        <i class="fas fa-info-circle"></i>
                    </button>
                </div>
            </div>
        </header>

        <main style="padding: var(--spacing-xl); background: var(--bg-secondary);">
            <!-- Welcome Section -->
            <div class="demo-section">
                <h2 class="demo-title">🏛️ Professional CPMS Interface</h2>
                <p>Experience the refined, trustworthy, and accessible CPMS interface with navy-teal color scheme, clean typography, and professional card-style components.</p>

                <div class="welcome-features" style="margin-top: var(--spacing-xl);">
                    <div class="feature">
                        <i class="fas fa-palette"></i>
                        <span>Refined Design</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-shield-check"></i>
                        <span>Trustworthy</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-universal-access"></i>
                        <span>WCAG Compliant</span>
                    </div>
                </div>
            </div>

            <!-- Buttons Demo -->
            <div class="demo-section">
                <h3 class="demo-title">Interactive Buttons</h3>
                <div class="component-showcase">
                    <button class="header-btn glass hover-lift">
                        <i class="fas fa-home"></i>
                    </button>
                    <button class="send-btn hover-lift glow">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                    <button class="suggestion-btn" style="width: auto; margin: 0;">
                        Try this suggestion
                    </button>
                    <button class="clear-chat-btn" style="width: auto;">
                        <i class="fas fa-trash"></i>
                        Clear Chat
                    </button>
                </div>
            </div>

            <!-- Badges Demo -->
            <div class="demo-section">
                <h3 class="demo-title">Status Badges</h3>
                <div class="component-showcase">
                    <span class="badge primary">Primary</span>
                    <span class="badge secondary">Secondary</span>
                    <span class="badge success">Success</span>
                    <span class="badge warning">Warning</span>
                    <span class="badge error">Error</span>
                    <span class="status-badge healthy">Healthy</span>
                    <span class="status-badge warning">Warning</span>
                    <span class="status-badge error">Error</span>
                </div>
            </div>

            <!-- Glass Effects Demo -->
            <div class="demo-section">
                <h3 class="demo-title">Glassmorphism Effects</h3>
                <div class="demo-grid">
                    <div class="demo-item glass">
                        <h4>Glass Card</h4>
                        <p>Backdrop blur with transparency</p>
                    </div>
                    <div class="demo-item glass glow">
                        <h4>Glass + Glow</h4>
                        <p>Enhanced with glow effect</p>
                    </div>
                    <div class="demo-item glass hover-lift">
                        <h4>Interactive Glass</h4>
                        <p>Hover for lift effect</p>
                    </div>
                </div>
            </div>

            <!-- Animation Demo -->
            <div class="demo-section">
                <h3 class="demo-title">Smooth Animations</h3>
                <div class="component-showcase">
                    <div class="animate-pulse" style="padding: var(--spacing-lg); background: var(--primary-color); color: white; border-radius: var(--radius-lg);">
                        Pulse Animation
                    </div>
                    <div class="animate-bounce" style="padding: var(--spacing-lg); background: var(--secondary-color); color: white; border-radius: var(--radius-lg);">
                        Bounce Animation
                    </div>
                    <div class="animate-float" style="padding: var(--spacing-lg); background: var(--accent-color); color: white; border-radius: var(--radius-lg);">
                        Float Animation
                    </div>
                </div>
            </div>

            <!-- Progress Bar Demo -->
            <div class="demo-section">
                <h3 class="demo-title">Progress Indicators</h3>
                <div style="max-width: 400px; margin: 0 auto;">
                    <div class="progress-bar" style="--progress: 75%; margin-bottom: var(--spacing-md);"></div>
                    <div class="progress-bar" style="--progress: 45%; margin-bottom: var(--spacing-md);"></div>
                    <div class="progress-bar" style="--progress: 90%;"></div>
                </div>
            </div>

            <!-- Typography Demo -->
            <div class="demo-section">
                <h3 class="demo-title">Enhanced Typography</h3>
                <div style="text-align: center;">
                    <h1 class="gradient-text" style="font-size: var(--text-4xl); margin-bottom: var(--spacing-md);">
                        Gradient Text Effect
                    </h1>
                    <p style="font-size: var(--text-lg); color: var(--text-secondary);">
                        Beautiful typography with modern font stacks and enhanced readability
                    </p>
                </div>
            </div>
        </main>

        <!-- Floating Action Button -->
        <button class="fab tooltip" data-tooltip="Quick Action">
            <i class="fas fa-plus"></i>
        </button>
    </div>

    <script>
        // Simple demo interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handlers for demo buttons
            document.querySelectorAll('button').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Button clicked:', this.textContent || 'Icon button');
                });
            });

            // Animate progress bars on load
            setTimeout(() => {
                document.querySelectorAll('.progress-bar').forEach((bar, index) => {
                    bar.style.setProperty('--progress', `${[75, 45, 90][index]}%`);
                });
            }, 500);
        });
    </script>
</body>
</html>
