{"timeout_fixes_applied": ["Increased frontend timeout to 150 seconds", "Added backend API timeout handling (120 seconds)", "Added model generation timeout (90 seconds)", "Added vector search timeout (10 seconds)", "Reduced MAX_NEW_TOKENS from 512 to 256 for faster generation", "Added async processing with ThreadPoolExecutor", "Enhanced error handling with user-friendly timeout messages"], "performance_optimizations": ["FAISS vector search for faster retrieval", "Multi-level caching (FAQ, Session, Response)", "Parallel processing for context retrieval", "Model response caching with LRU cache", "Optimized GGUF model settings"], "expected_improvements": {"cached_responses": "< 0.1 seconds", "simple_queries": "5-15 seconds", "complex_queries": "15-60 seconds", "timeout_threshold": "150 seconds (frontend) / 120 seconds (backend)"}, "troubleshooting_tips": ["If timeouts persist, try simpler questions", "Break complex queries into smaller parts", "Check system resources (RAM, CPU)", "Consider using GPU acceleration", "Monitor backend logs for bottlenecks"]}