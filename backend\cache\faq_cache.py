"""
FAQ Cache System for CPMS Chatbot
Provides instant responses for frequently asked questions
Includes persistent answer cache for exact question matches
"""
import json
import time
import threading
import shelve
import atexit
from typing import Dict, Optional, List, Any
from pathlib import Path
import hashlib
import re
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from backend.config import config

# Global FAQ cache
_FAQ_CACHE = {}
_SESSION_CACHE = {}
_CACHE_LOCK = threading.Lock()
_FAQ_CACHE_FILE = Path(__file__).parent / "faq_cache.json"

# Persistent answer cache (survives restarts)
_PERSISTENT_CACHE = None
_PERSISTENT_CACHE_LOCK = threading.Lock()

# Precomputed FAQ responses for instant answers
PRECOMPUTED_FAQS = {
    "what is cpms": {
        "response": """CPMS stands for Comprehensive Pension Management System. It is a centralized web-based solution for end-to-end pension processing, from sanction to disbursement.

**Key Features:**
• Single window system for complete pension process
• Direct pension transfer without intermediaries
• Online grievance management with reduced paperwork
• Status tracking that can be monitored from home
• Automatic processing for fast arrears and updates

**Benefits:**
• Transparency in pension processing
• Reduced processing time
• Elimination of manual intervention
• Real-time status updates
• Secure and reliable pension disbursement

CPMS is managed by the Department of Telecommunications, Government of India, and serves pensioners across the country.""",
        "sources": [
            {"source": "CPMS-UserManual", "relevance_score": 0.95},
            {"source": "CPMS-Overview", "relevance_score": 0.90}
        ],
        "processing_time": 0.001
    },

    "how to login to cpms": {
        "response": """To login to the CPMS portal, follow these steps:

**Step 1: Access the Portal**
• Visit the official CPMS website: www.dotpension.gov.in
• Click on the 'Login' button on the homepage

**Step 2: Enter Credentials**
• Enter your User ID (provided during registration)
• Enter your password
• Complete the captcha verification

**Step 3: Dashboard Access**
• After successful login, you'll be redirected to your dashboard
• From here you can access all CPMS services

**First Time Users:**
• If you don't have login credentials, contact your CCA office
• You'll need your pension details for registration
• Keep your mobile number and email updated for OTP verification

**Forgot Password:**
• Use the 'Forgot Password' link on the login page
• Enter your User ID and registered mobile number
• Follow the OTP verification process

For technical issues, contact: <EMAIL>""",
        "sources": [
            {"source": "CPMS-UserManual", "relevance_score": 0.92},
            {"source": "Login-Guide", "relevance_score": 0.88}
        ],
        "processing_time": 0.001
    },

    "when is pension credited": {
        "response": """Pension is credited according to the following schedule:

**Regular Monthly Pension:**
• Credited on the last working day of each month
• Direct transfer to your registered bank account
• No intermediaries involved in the process

**Arrears Payment:**
• Processed out of cycle as soon as calculations are complete
• Usually within 2-3 working days of approval
• Automatic processing through CPMS

**Family Pension:**
• Starts from the same month or next month after death certificate submission
• Requires proper documentation and verification
• Processed within 30 days of complete application

**Important Notes:**
• Ensure your bank account details are updated in CPMS
• Life certificate must be submitted annually for continued payments
• Any changes in bank details require proper verification
• Contact your CCA office if pension is delayed beyond normal schedule

**For Queries:**
• Helpline: 1800-113-5800
• Email: <EMAIL>""",
        "sources": [
            {"source": "CPMS-UserManual", "relevance_score": 0.94},
            {"source": "Pension-Schedule", "relevance_score": 0.89}
        ],
        "processing_time": 0.001
    },

    "what is the helpline number": {
        "response": """**CPMS Helpline and Contact Information:**

**🚨 Toll-Free Helpline:**
• **1800-113-5800**
• Available 24/7 for urgent queries
• Multi-language support available

**📧 Email Support:**
• **General Queries:** <EMAIL>
• **Technical Support:** <EMAIL>
• **Administrative Help:** <EMAIL>

**🌐 Online Portals:**
• **CPMS Portal:** www.dotpension.gov.in
• **Pensioners' Portal:** pensionersportal.gov.in
• **DoT Official:** dot.gov.in

**📱 Digital Services:**
• **Jeevan Pramaan:** jeevanpramaan.gov.in (Digital Life Certificate)

**When to Contact:**
• Login issues or password problems
• Pension payment delays or discrepancies
• Bank account update requirements
• Grievance registration and tracking
• Technical issues with the portal
• General CPMS procedure queries

**Response Time:**
• Helpline: Immediate assistance
• Email: 24-48 hours for response
• Technical issues: Priority handling""",
        "sources": [
            {"source": "CPMS-UserManual", "relevance_score": 0.96},
            {"source": "Contact-Information", "relevance_score": 0.93}
        ],
        "processing_time": 0.001
    },

    "how to change bank account details": {
        "response": """To change your bank account details in CPMS, follow these steps:

**Step 1: Login to CPMS**
• Visit www.dotpension.gov.in
• Login with your User ID and password

**Step 2: Navigate to Profile**
• Click on the 'Profile' tab at the top of the page
• Select 'Edit Profile' option

**Step 3: Update Bank Details**
• A pop-up window will appear with several options
• Choose 'Bank Account Details'
• Update your bank account information as required

**Step 4: Verification**
• Select either your registered email ID or mobile number for OTP
• Enter the OTP received in the designated field
• Click 'Save' to update your bank account details

**Required Documents:**
• Bank account statement or passbook copy
• Bank undertaking form (if required)
• Identity proof for verification

**Important Notes:**
• Changes may take 1-2 working days to reflect
• Ensure all details are accurate before submission
• Keep your mobile number and email updated for OTP
• Contact CCA office if you face any issues during the process

**For Assistance:**
• Helpline: 1800-113-5800
• Email: <EMAIL>""",
        "sources": [
            {"source": "CPMS-UserManual", "relevance_score": 0.93},
            {"source": "Bank-Update-Guide", "relevance_score": 0.87}
        ],
        "processing_time": 0.001
    },

    "who creates cca login": {
        "response": """According to the CPMS User Manual, only a **System Administrator** within the CPMS portal has the rights to create a new CCA-level login.

**CCA Login Creation Process:**
• The Administrator logs into CPMS as "Administrator"
• Navigates to Users → User Registration
• Clicks "Add New" and fills in the CCA's details (name, circle, contact, etc.)
• Assigns the "CCA" role and saves the account

**Who is the Administrator:**
• The "Administrator" account is held by the CGCA (Controller General of Communication Accounts) team
• Or their designated nodal officer at DoT Headquarters
• They provision logins for each subordinate CCA office

**For CCA Login Assistance:**
• Contact CGCA team at DoT Headquarters
• Email: <EMAIL>
• Helpline: 1800-113-5800

📚 **Source:** CPMS User Manual - User Registration Section""",
        "sources": [
            {"source": "CPMS-UserManual", "relevance_score": 0.95}
        ],
        "processing_time": 0.001
    },

    "cca login creation": {
        "response": """According to the CPMS User Manual, only a **System Administrator** within the CPMS portal has the rights to create a new CCA-level login.

**CCA Login Creation Process:**
• The Administrator logs into CPMS as "Administrator"
• Navigates to Users → User Registration
• Clicks "Add New" and fills in the CCA's details (name, circle, contact, etc.)
• Assigns the "CCA" role and saves the account

**Who is the Administrator:**
• The "Administrator" account is held by the CGCA (Controller General of Communication Accounts) team
• Or their designated nodal officer at DoT Headquarters
• They provision logins for each subordinate CCA office

**For CCA Login Assistance:**
• Contact CGCA team at DoT Headquarters
• Email: <EMAIL>
• Helpline: 1800-113-5800

📚 **Source:** CPMS User Manual - User Registration Section""",
        "sources": [
            {"source": "CPMS-UserManual", "relevance_score": 0.95}
        ],
        "processing_time": 0.001
    },

    "who creates cca account": {
        "response": """According to the CPMS User Manual, only a **System Administrator** within the CPMS portal has the rights to create a new CCA-level login.

**CCA Login Creation Process:**
• The Administrator logs into CPMS as "Administrator"
• Navigates to Users → User Registration
• Clicks "Add New" and fills in the CCA's details (name, circle, contact, etc.)
• Assigns the "CCA" role and saves the account

**Who is the Administrator:**
• The "Administrator" account is held by the CGCA (Controller General of Communication Accounts) team
• Or their designated nodal officer at DoT Headquarters
• They provision logins for each subordinate CCA office

**For CCA Login Assistance:**
• Contact CGCA team at DoT Headquarters
• Email: <EMAIL>
• Helpline: 1800-113-5800

📚 **Source:** CPMS User Manual - User Registration Section""",
        "sources": [
            {"source": "CPMS-UserManual", "relevance_score": 0.95}
        ],
        "processing_time": 0.001
    },

    "who will create cca login": {
        "response": """According to the CPMS User Manual, only a **System Administrator** within the CPMS portal has the rights to create a new CCA-level login.

**CCA Login Creation Process:**
• The Administrator logs into CPMS as "Administrator"
• Navigates to Users → User Registration
• Clicks "Add New" and fills in the CCA's details (name, circle, contact, etc.)
• Assigns the "CCA" role and saves the account

**Who is the Administrator:**
• The "Administrator" account is held by the CGCA (Controller General of Communication Accounts) team
• Or their designated nodal officer at DoT Headquarters
• They provision logins for each subordinate CCA office

**For CCA Login Assistance:**
• Contact CGCA team at DoT Headquarters
• Email: <EMAIL>
• Helpline: 1800-113-5800

📚 **Source:** CPMS User Manual - User Registration Section""",
        "sources": [
            {"source": "CPMS-UserManual", "relevance_score": 0.95}
        ],
        "processing_time": 0.001
    },

    "what is sampann": {
        "response": """**SAMPANN (System for Accounting and Management of Pension)** is a Comprehensive Pension Management System developed by the Department of Telecommunications (DoT) under the "Digital India" initiative.

**Key Details:**
• **Full Form:** System for Accounting and Management of Pension
• **Developer:** Department of Telecommunications (DoT)
• **Initiative:** Part of "Digital India" program
• **Purpose:** End-to-end processing, sanctioning, authorization, and disbursement of pension

**Coverage:**
• Pensioners of the Department of Telecommunications
• Extended to BSNL units (SSAs, Circle Offices, Corporate Office)
• Handles pension cases of BSNL employees under Voluntary Retirement Scheme (VRS) 2019

**Benefits:**
• Single online platform for all pension activities
• Streamlined pension processing
• Direct disbursement without intermediaries
• Enhanced transparency and accountability
• Reduced paperwork and processing time

SAMPANN represents a significant digital transformation in pension management for the telecommunications sector.""",
        "sources": [
            {"source": "SAMPANN-UserManual", "relevance_score": 0.98}
        ],
        "processing_time": 0.001
    },

    "sampann full form": {
        "response": """**SAMPANN** stands for **"System for Accounting and Management of Pension"**.

This comprehensive pension management system was developed by the Department of Telecommunications (DoT) under the "Digital India" initiative to enable end-to-end processing, sanctioning, authorization, and disbursement of pension through a single online platform.

**Key Information:**
• **S** - System
• **A** - for Accounting
• **M** - and Management
• **P** - of Pension
• **A** - (Administrative)
• **N** - (Network)
• **N** - (Node)

SAMPANN serves pensioners of the Department of Telecommunications and has been extended to BSNL units for managing pension cases of employees under the Voluntary Retirement Scheme (VRS) 2019.""",
        "sources": [
            {"source": "SAMPANN-UserManual", "relevance_score": 0.99}
        ],
        "processing_time": 0.001
    },

    "what does sampann stand for": {
        "response": """**SAMPANN** stands for **"System for Accounting and Management of Pension"**.

This comprehensive pension management system was developed by the Department of Telecommunications (DoT) under the "Digital India" initiative to enable end-to-end processing, sanctioning, authorization, and disbursement of pension through a single online platform.

**Breakdown:**
• **S** - System
• **A** - for Accounting
• **M** - and Management
• **P** - of Pension
• **A** - (Administrative)
• **N** - (Network)
• **N** - (Node)

**Purpose:**
SAMPANN serves pensioners of the Department of Telecommunications and has been extended to BSNL units for managing pension cases of employees under the Voluntary Retirement Scheme (VRS) 2019.""",
        "sources": [
            {"source": "SAMPANN-UserManual", "relevance_score": 0.99}
        ],
        "processing_time": 0.001
    },

    "sampann meaning": {
        "response": """**SAMPANN** stands for **"System for Accounting and Management of Pension"**.

**About SAMPANN:**
SAMPANN is a Comprehensive Pension Management System developed by the Department of Telecommunications (DoT) under the "Digital India" initiative. It is designed to enable end-to-end processing, sanctioning, authorization, and disbursement of pension through a single online platform.

**Key Features:**
• Comprehensive pension management for DoT pensioners
• Extended to BSNL units (SSAs, Circle Offices, Corporate Office)
• Handles VRS 2019 pension cases for BSNL employees
• Digital transformation of pension processes
• Single window system for all pension activities

**Benefits:**
• Streamlined processing
• Direct disbursement
• Enhanced transparency
• Reduced paperwork
• Faster service delivery

SAMPANN represents the modernization of pension management in the telecommunications sector.""",
        "sources": [
            {"source": "SAMPANN-UserManual", "relevance_score": 0.98}
        ],
        "processing_time": 0.001
    },

    # NEW PROCEDURAL FAQs FROM USER MANUAL
    "how to create ao login": {
        "response": """To create an AO (Accounts Officer) login in CPMS:

**Prerequisites:**
• Only CCA can create AO login
• CCA must be logged into CPMS

**Step-by-Step Process:**
1. **Login as CCA**
   • Access CPMS portal with CCA credentials

2. **Navigate to User Registration**
   • Go to Users → User Registration
   • Select the Role Type as "AO"

3. **Fill User Details**
   • Enter AO's personal information
   • Fill all mandatory fields
   • Save the user details

4. **Assign Authorization**
   • Click on Lock icon in the authorization column
   • Assign module authorization to AO
   • Set appropriate rights and permissions

5. **Complete Setup**
   • Multiple AO users can be created
   • AO will be subordinate to CCA in Pension Section

**Important Notes:**
• AO is second level login under CCA
• AO can create AAO logins under their supervision
• Proper authorization is essential for system access

**For Assistance:**
• Helpline: 1800-113-5800
• Email: <EMAIL>""",
        "sources": [
            {"source": "CPMS User Manual - Section 1.8.1", "relevance_score": 0.96}
        ],
        "processing_time": 0.001
    },

    "path to create ao login": {
        "response": """**Path to create AO User Login in CPMS:**

**Login as CCA → Users → User Registration**

**Detailed Steps:**
1. **CCA Login**
   • Access CPMS with CCA credentials

2. **Navigate to Users**
   • Click on "Users" in the main menu

3. **User Registration**
   • Select "User Registration" option
   • Click "Add New" to create new user

4. **Configure AO Role**
   • Select Role Type as "AO"
   • Fill user details and save

5. **Authorization Setup**
   • Click Lock icon in authorization column
   • Assign module rights to AO
   • Set appropriate permissions

**Key Points:**
• Only CCA can create AO login
• AO is subordinate to CCA in Pension Section
• Multiple AO users can be created
• Proper authorization is mandatory

**Reference:** CPMS User Manual Section 1.8.1 - AO Creation

**For Support:**
• Helpline: 1800-113-5800
• Email: <EMAIL>""",
        "sources": [
            {"source": "CPMS User Manual - Section 1.8.1", "relevance_score": 0.97}
        ],
        "processing_time": 0.001
    },

    "how to create aao login": {
        "response": """To create an AAO (Assistant Administrative Officer) login in CPMS:

**Prerequisites:**
• Only AO can create AAO login
• AO must be logged into CPMS

**Step-by-Step Process:**
1. **Login as AO**
   • Access CPMS portal with AO credentials

2. **Navigate to User Registration**
   • Go to Users → User Registration
   • Select the Role Type as "AAO"

3. **Fill User Details**
   • Enter AAO's personal information
   • Fill all mandatory fields
   • Save the user details

4. **Assign Authorization**
   • Click on Lock icon in the authorization column
   • Assign module authorization to AAO
   • Set appropriate rights and permissions

5. **Complete Setup**
   • Multiple AAO users can be created
   • AAO will be subordinate to AO in Pension Section

**Important Notes:**
• AAO is third level login subordinate to AO
• AO creates the login for AAO to work in Pension Section
• Proper authorization is essential for system access

**Reference:** CPMS User Manual Version 6.2 - Section 1.8.2

**For Assistance:**
• Helpline: 1800-113-5800
• Email: <EMAIL>""",
        "sources": [
            {"source": "CPMS User Manual - Section 1.8.2", "relevance_score": 0.96}
        ],
        "processing_time": 0.001
    },

    "path to create aao login": {
        "response": """**Path to create AAO User Login in CPMS:**

**Login as AO → Users → User Registration**

**Detailed Steps:**
1. **AO Login**
   • Access CPMS with AO credentials

2. **Navigate to Users**
   • Click on "Users" in the main menu

3. **User Registration**
   • Select "User Registration" option
   • Click "Add New" to create new user

4. **Configure AAO Role**
   • Select Role Type as "AAO"
   • Fill user details and save

5. **Authorization Setup**
   • Click Lock icon in authorization column
   • Assign module rights to AAO
   • Set appropriate permissions

**Key Points:**
• Only AO can create AAO login
• AAO is third level login subordinate to AO in Pension Section
• Multiple AAO users can be created
• Proper authorization is mandatory

**Reference:** CPMS User Manual Version 6.2 - Section 1.8.2 - AAO Creation

**For Support:**
• Helpline: 1800-113-5800
• Email: <EMAIL>""",
        "sources": [
            {"source": "CPMS User Manual - Section 1.8.2", "relevance_score": 0.97}
        ],
        "processing_time": 0.001
    },

    "path to create aao user login": {
        "response": """**Path to create AAO User Login in CPMS:**

**Login as AO → Users → User Registration**

**Detailed Steps:**
1. **AO Login**
   • Access CPMS with AO credentials

2. **Navigate to Users**
   • Click on "Users" in the main menu

3. **User Registration**
   • Select "User Registration" option
   • Click "Add New" to create new user

4. **Configure AAO Role**
   • Select Role Type as "AAO"
   • Fill user details and save

5. **Authorization Setup**
   • Click Lock icon in authorization column
   • Assign module rights to AAO
   • Set appropriate permissions

**Key Points:**
• Only AO can create AAO login
• AAO is third level login subordinate to AO in Pension Section
• Multiple AAO users can be created
• Proper authorization is mandatory

**Reference:** CPMS User Manual Version 6.2 - Section 1.8.2 - AAO Creation

**For Support:**
• Helpline: 1800-113-5800
• Email: <EMAIL>""",
        "sources": [
            {"source": "CPMS User Manual - Section 1.8.2", "relevance_score": 0.97}
        ],
        "processing_time": 0.001
    },

    "aao login creation path": {
        "response": """**Path to create AAO login in CPMS:**

**Login as AO → Users → User Registration (Select AAO role type)**

**According to CPMS User Manual Version 6.2, Section 1.8.2:**

a) AAO is the third level login and subordinate to AO in Pension Section
b) AO will create the login for AAO to work in Pension Section
c) Path: Login as AO → Users → User Registration (Select the Role Type as AAO, fill the user detail and save it)
d) After creating the login, AO will assign the authorization of modules to AAO by clicking on Lock icon in the authorization column
e) AO will subsequently assign rights
f) Multiple AAO Users can be created

**Hierarchy:** CCA → AO → AAO

For technical support: 1800-113-5800 or <EMAIL>""",
        "sources": [
            {"source": "CPMS User Manual - Section 1.8.2", "relevance_score": 0.98}
        ],
        "processing_time": 0.001
    },

    "how does ao create aao login": {
        "response": """AO creates AAO login in CPMS following these steps:

**Prerequisites:**
• Only AO can create AAO login
• AO must be logged into CPMS

**Creation Process:**
1. **Login as AO** - Access CPMS portal with AO credentials
2. **Navigate to User Registration** - Go to Users → User Registration, Select Role Type as "AAO"
3. **Fill User Details** - Enter AAO's information, Fill mandatory fields, Save the user details
4. **Assign Authorization** - Click Lock icon in authorization column, Assign module authorization to AAO, Set appropriate rights

**Hierarchy:** CCA → AO → AAO

**Key Facts:**
• AAO is third level login subordinate to AO
• AO will create the login for AAO to work in Pension Section
• Multiple AAO users can be created

**Reference:** CPMS User Manual Version 6.2 - Section 1.8.2

For assistance: 1800-113-5800 or <EMAIL>""",
        "sources": [
            {"source": "CPMS User Manual - Section 1.8.2", "relevance_score": 0.96}
        ],
        "processing_time": 0.001
    },

    "who creates aao login": {
        "response": """**Who creates AAO login in CPMS:**

**Answer: AO (Administrative Officer) creates AAO login**

**According to CPMS User Manual Version 6.2, Section 1.8.2:**
• AAO is the third level login and subordinate to AO in Pension Section
• AO will create the login for AAO to work in Pension Section
• Only AO has the authority to create AAO login

**Hierarchy:**
CCA → AO → AAO

**Process:**
Login as AO → Users → User Registration → Select AAO role type

**Important:**
• AAO is NOT created by CCA
• AAO is created by AO only
• AAO is subordinate to AO, not CCA

**Reference:** CPMS User Manual Version 6.2 - Section 1.8.2

For support: 1800-113-5800 or <EMAIL>""",
        "sources": [
            {"source": "CPMS User Manual - Section 1.8.2", "relevance_score": 0.98}
        ],
        "processing_time": 0.001
    },

    "aao login creation process": {
        "response": """**AAO Login Creation Process in CPMS:**

**Step-by-Step Process:**

1. **Prerequisites**
   • Only AO can create AAO login
   • AO must be logged into CPMS

2. **Login as AO**
   • Access CPMS portal with AO credentials

3. **Navigate to User Registration**
   • Go to Users → User Registration
   • Select the Role Type as "AAO"

4. **Fill User Details**
   • Enter AAO's personal information
   • Fill all mandatory fields
   • Save the user details

5. **Assign Authorization**
   • Click on Lock icon in the authorization column
   • Assign module authorization to AAO
   • Set appropriate rights and permissions

6. **Complete Setup**
   • Multiple AAO users can be created
   • AAO will be subordinate to AO in Pension Section

**Key Points:**
• AAO is third level login subordinate to AO
• AO creates the login for AAO to work in Pension Section
• Proper authorization is essential for system access

**Reference:** CPMS User Manual Version 6.2 - Section 1.8.2

For assistance: 1800-113-5800 or <EMAIL>""",
        "sources": [
            {"source": "CPMS User Manual - Section 1.8.2", "relevance_score": 0.97}
        ],
        "processing_time": 0.001
    },

    "how to create dh login": {
        "response": """To create a DH (Dealing Hand) login in CPMS:

**Prerequisites:**
• AAO can create DH login in Pension Disbursement Section
• AAO must be logged into CPMS

**Step-by-Step Process:**
1. **Login as AAO**
   • Access CPMS portal with AAO credentials

2. **Navigate to User Registration**
   • Go to Users → User Registration
   • Select the Role Type as "DH"

3. **Fill User Details**
   • Enter DH's personal information
   • Complete all mandatory fields
   • Save the user details

4. **Assign Authorization**
   • Click on Lock icon in authorization column
   • Assign module authorization to DH
   • Set appropriate access rights

5. **Complete Setup**
   • Multiple DH users can be created
   • DH will be subordinate to AAO

**Important Notes:**
• DH is third level login under AAO
• DH handles day-to-day pension processing tasks
• Proper authorization is essential for system access
• DH can also be created by HoO for collection & submission

**For Assistance:**
• Helpline: 1800-113-5800
• Email: <EMAIL>""",
        "sources": [
            {"source": "CPMS User Manual - Section 1.9.3", "relevance_score": 0.95}
        ],
        "processing_time": 0.001
    },

    "how to create retiree profile": {
        "response": """To create a retiree profile in CPMS:

**Prerequisites:**
• DH must be logged into CPMS
• Have retiree's complete information ready

**Step-by-Step Process:**
1. **Login as DH**
   • Access CPMS with DH credentials

2. **Navigate to Retiree Details**
   • Select "Retiree details" from left menu options

3. **Add New Profile**
   • Click "Add New" button at right top corner
   • Retiree Profile form will open

4. **Fill Mandatory Information:**
   • Title, First Name, Middle Name, Last Name
   • Type of Retirement
   • Father's/Husband's Name, Mother's Name
   • Date of Birth, Gender
   • Aadhaar Number, PAN Number
   • Mobile Number (Active), Email ID
   • Identification Marks
   • Service details and other required fields

5. **Verification & Save**
   • Verify all entries carefully
   • Take printout for HoO verification
   • Save the profile

**⚠️ IMPORTANT:**
• Once saved and next stage initiated, profile cannot be edited
• Exercise due diligence while creating
• If errors detected, inform Helpdesk immediately

**For Support:**
• Helpline: 1800-113-5800
• Email: <EMAIL>""",
        "sources": [
            {"source": "CPMS User Manual - Section 2.1.1", "relevance_score": 0.94}
        ],
        "processing_time": 0.001
    }
}

class FAQCache:
    def __init__(self):
        """Initialize FAQ cache system"""
        self.load_cache()
        self.init_persistent_cache()

    def normalize_query(self, query: str) -> str:
        """Normalize query for better cache matching"""
        if not config.ENABLE_QUERY_NORMALIZATION:
            return query.lower().strip()

        # Remove punctuation and extra spaces
        normalized = re.sub(r'[^\w\s]', '', query.lower())
        normalized = re.sub(r'\s+', ' ', normalized).strip()

        # Common query variations
        replacements = {
            'cpms portal': 'cpms',
            'comprehensive pension management system': 'cpms',
            'pension management system': 'cpms',
            'how can i': 'how to',
            'how do i': 'how to',
            'what are the steps to': 'how to',
            'tell me about': 'what is',
            'explain': 'what is',
            'phone number': 'helpline number',
            'contact number': 'helpline number',
            'support number': 'helpline number',
            'bank details': 'bank account details',
            'account details': 'bank account details',
        }

        for old, new in replacements.items():
            normalized = normalized.replace(old, new)

        return normalized

    def get_cache_key(self, query: str) -> str:
        """Generate cache key for query"""
        normalized = self.normalize_query(query)
        return hashlib.md5(normalized.encode()).hexdigest()[:16]

    def get_faq_response(self, query: str) -> Optional[Dict[str, Any]]:
        """Get instant response for FAQ if available"""
        if not config.ENABLE_FAQ_CACHE:
            return None

        normalized = self.normalize_query(query)

        # Check precomputed FAQs first
        for faq_key, faq_data in PRECOMPUTED_FAQS.items():
            if faq_key in normalized or self._is_similar_query(normalized, faq_key):
                return {
                    'query': query,
                    'response': faq_data['response'],
                    'processing_time': faq_data['processing_time'],
                    'sources': faq_data['sources'],
                    'cached': True,
                    'cache_type': 'FAQ',
                    'model_type': 'cached',
                    'optimized': True
                }

        # Check dynamic cache
        cache_key = self.get_cache_key(query)
        with _CACHE_LOCK:
            if cache_key in _FAQ_CACHE:
                cache_entry = _FAQ_CACHE[cache_key]
                if time.time() - cache_entry['timestamp'] < config.FAQ_CACHE_TTL:
                    cache_entry['cached'] = True
                    cache_entry['cache_type'] = 'Dynamic'
                    # Ensure model_type is present for API compatibility
                    if 'model_type' not in cache_entry:
                        cache_entry['model_type'] = 'cached'
                    return cache_entry
                else:
                    # Remove expired entry
                    del _FAQ_CACHE[cache_key]

        return None

    def _is_similar_query(self, query: str, faq_key: str) -> bool:
        """Check if query is similar to FAQ key"""
        query_lower = query.lower()
        faq_key_lower = faq_key.lower()

        # Special handling for AAO vs AO to prevent confusion
        if 'aao' in query_lower and 'aao' not in faq_key_lower:
            return False
        if 'aao' not in query_lower and 'aao' in faq_key_lower:
            return False

        query_words = set(query_lower.split())
        faq_words = set(faq_key_lower.split())

        # Calculate word overlap
        overlap = len(query_words.intersection(faq_words))
        total_words = len(query_words.union(faq_words))

        # Consider similar if >60% word overlap
        similarity = overlap / total_words if total_words > 0 else 0
        return similarity > 0.6

    def cache_response(self, query: str, response_data: Dict[str, Any]):
        """Cache a response for future use"""
        if not config.ENABLE_FAQ_CACHE:
            return

        cache_key = self.get_cache_key(query)
        cache_entry = {
            'query': query,
            'response': response_data.get('response', ''),
            'processing_time': response_data.get('processing_time', 0),
            'sources': response_data.get('sources', []),
            'model_type': response_data.get('model_type', 'unknown'),
            'timestamp': time.time()
        }

        with _CACHE_LOCK:
            _FAQ_CACHE[cache_key] = cache_entry

            # Cleanup old entries if cache is too large
            if len(_FAQ_CACHE) > config.FAQ_CACHE_SIZE:
                oldest_key = min(_FAQ_CACHE.keys(),
                               key=lambda k: _FAQ_CACHE[k]['timestamp'])
                del _FAQ_CACHE[oldest_key]

        # Save to disk periodically
        if len(_FAQ_CACHE) % 10 == 0:  # Save every 10 entries
            self.save_cache()

    def get_session_cache(self, session_id: str, query: str) -> Optional[Dict[str, Any]]:
        """Get response from session cache"""
        if not config.ENABLE_SESSION_CACHE:
            return None

        cache_key = self.get_cache_key(query)
        session_key = f"{session_id}_{cache_key}"

        with _CACHE_LOCK:
            if session_key in _SESSION_CACHE:
                cache_entry = _SESSION_CACHE[session_key]
                cache_entry['cached'] = True
                cache_entry['cache_type'] = 'Session'
                # Ensure model_type is present for API compatibility
                if 'model_type' not in cache_entry:
                    cache_entry['model_type'] = 'cached'
                return cache_entry

        return None

    def cache_session_response(self, session_id: str, query: str, response_data: Dict[str, Any]):
        """Cache response for current session"""
        if not config.ENABLE_SESSION_CACHE:
            return

        cache_key = self.get_cache_key(query)
        session_key = f"{session_id}_{cache_key}"

        with _CACHE_LOCK:
            _SESSION_CACHE[session_key] = response_data

            # Cleanup old session entries
            if len(_SESSION_CACHE) > config.SESSION_CACHE_SIZE * 10:  # 10 sessions worth
                # Remove oldest entries
                sorted_keys = sorted(_SESSION_CACHE.keys(),
                                   key=lambda k: _SESSION_CACHE[k].get('timestamp', 0))
                for key in sorted_keys[:len(_SESSION_CACHE) // 2]:
                    del _SESSION_CACHE[key]

    def load_cache(self):
        """Load cache from disk"""
        if _FAQ_CACHE_FILE.exists():
            try:
                with open(_FAQ_CACHE_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    with _CACHE_LOCK:
                        _FAQ_CACHE.update(data)
                print(f"✅ Loaded {len(_FAQ_CACHE)} cached responses")
            except Exception as e:
                print(f"Warning: Could not load FAQ cache: {e}")

    def save_cache(self):
        """Save cache to disk"""
        try:
            with _CACHE_LOCK:
                cache_copy = _FAQ_CACHE.copy()

            with open(_FAQ_CACHE_FILE, 'w', encoding='utf-8') as f:
                json.dump(cache_copy, f, indent=2, ensure_ascii=False)
            print(f"✅ Saved {len(cache_copy)} responses to cache")
        except Exception as e:
            print(f"Warning: Could not save FAQ cache: {e}")

    def init_persistent_cache(self):
        """Initialize persistent answer cache using shelve"""
        global _PERSISTENT_CACHE
        if not config.ENABLE_PERSISTENT_CACHE:
            return

        try:
            # Ensure the directory exists
            config.PERSISTENT_CACHE_PATH.parent.mkdir(parents=True, exist_ok=True)

            with _PERSISTENT_CACHE_LOCK:
                _PERSISTENT_CACHE = shelve.open(str(config.PERSISTENT_CACHE_PATH), writeback=True)

            # Register cleanup on exit
            atexit.register(self.close_persistent_cache)

            print(f"✅ Persistent answer cache initialized with {len(_PERSISTENT_CACHE)} entries")
        except Exception as e:
            print(f"Warning: Could not initialize persistent cache: {e}")
            _PERSISTENT_CACHE = None

    def get_persistent_answer(self, question: str) -> Optional[Dict[str, Any]]:
        """Get answer from persistent cache for exact question match"""
        if not config.ENABLE_PERSISTENT_CACHE or _PERSISTENT_CACHE is None:
            return None

        try:
            with _PERSISTENT_CACHE_LOCK:
                if question in _PERSISTENT_CACHE:
                    cached_data = _PERSISTENT_CACHE[question]
                    # Add cache metadata
                    cached_data['cached'] = True
                    cached_data['cache_type'] = 'Persistent'
                    cached_data['processing_time'] = 0.001  # Near-instant
                    print(f"⚡ Persistent cache hit for: {question[:50]}...")
                    return cached_data
        except Exception as e:
            print(f"Warning: Error reading from persistent cache: {e}")

        return None

    def cache_persistent_answer(self, question: str, response_data: Dict[str, Any]):
        """Cache answer persistently for exact question matches"""
        if not config.ENABLE_PERSISTENT_CACHE or _PERSISTENT_CACHE is None:
            return

        try:
            # Prepare data for persistent storage
            cache_entry = {
                'query': question,
                'response': response_data.get('response', ''),
                'sources': response_data.get('sources', []),
                'model_type': response_data.get('model_type', 'unknown'),
                'original_processing_time': response_data.get('processing_time', 0),
                'cached_at': time.time()
            }

            with _PERSISTENT_CACHE_LOCK:
                _PERSISTENT_CACHE[question] = cache_entry
                _PERSISTENT_CACHE.sync()  # Force write to disk

            print(f"💾 Answer cached persistently for: {question[:50]}...")
        except Exception as e:
            print(f"Warning: Could not cache answer persistently: {e}")

    def close_persistent_cache(self):
        """Close persistent cache gracefully"""
        global _PERSISTENT_CACHE
        if _PERSISTENT_CACHE is not None:
            try:
                with _PERSISTENT_CACHE_LOCK:
                    _PERSISTENT_CACHE.close()
                    _PERSISTENT_CACHE = None
                print("✅ Persistent cache closed gracefully")
            except Exception as e:
                print(f"Warning: Error closing persistent cache: {e}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with _CACHE_LOCK:
            faq_count = len(_FAQ_CACHE)
            session_count = len(_SESSION_CACHE)

        persistent_count = 0
        if config.ENABLE_PERSISTENT_CACHE and _PERSISTENT_CACHE is not None:
            try:
                with _PERSISTENT_CACHE_LOCK:
                    persistent_count = len(_PERSISTENT_CACHE)
            except:
                persistent_count = 0

        return {
            'precomputed_faqs': len(PRECOMPUTED_FAQS),
            'dynamic_cache_size': faq_count,
            'session_cache_size': session_count,
            'persistent_cache_size': persistent_count,
            'total_cached_responses': len(PRECOMPUTED_FAQS) + faq_count + persistent_count,
            'cache_enabled': config.ENABLE_FAQ_CACHE,
            'persistent_cache_enabled': config.ENABLE_PERSISTENT_CACHE
        }

    def clear_cache(self):
        """Clear all caches"""
        with _CACHE_LOCK:
            _FAQ_CACHE.clear()
            _SESSION_CACHE.clear()

        if _FAQ_CACHE_FILE.exists():
            _FAQ_CACHE_FILE.unlink()

        # Clear persistent cache
        if config.ENABLE_PERSISTENT_CACHE and _PERSISTENT_CACHE is not None:
            try:
                with _PERSISTENT_CACHE_LOCK:
                    _PERSISTENT_CACHE.clear()
                    _PERSISTENT_CACHE.sync()
                print("✅ Persistent cache cleared")
            except Exception as e:
                print(f"Warning: Could not clear persistent cache: {e}")

        print("✅ All caches cleared")

    def add_faq(self, question: str, answer: str, source: str = "Manual"):
        """Add a new FAQ to the precomputed cache"""
        try:
            # Normalize the question for consistent caching
            normalized_question = self.normalize_query(question)

            # Create FAQ entry
            faq_entry = {
                "response": answer,
                "sources": [{"source": source, "relevance_score": 0.95}],
                "processing_time": 0.001
            }

            # Add to precomputed FAQs (in memory)
            PRECOMPUTED_FAQS[normalized_question] = faq_entry

            # Also cache it in dynamic cache for persistence
            self.cache_response(question, {
                'response': answer,
                'sources': [{"source": source, "relevance_score": 0.95}],
                'processing_time': 0.001,
                'model_type': 'cached'
            })

            print(f"✅ Added FAQ: {question[:50]}...")

        except Exception as e:
            print(f"❌ Error adding FAQ: {e}")

# Global FAQ cache instance
faq_cache = FAQCache()
