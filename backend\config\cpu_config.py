"""
CPU-only configuration for Ubuntu server deployment
"""
import os
from pathlib import Path

# Override the main config for CPU-only deployment
def apply_cpu_config():
    """Apply CPU-only optimizations"""
    
    # Disable GPU-specific features
    os.environ["CUDA_VISIBLE_DEVICES"] = ""
    
    # Set CPU-only torch settings
    import torch
    torch.set_num_threads(os.cpu_count() or 4)
    
    print("🔧 Applied CPU-only configuration")
    print(f"   - CPU threads: {torch.get_num_threads()}")
    print(f"   - CUDA disabled")
    
    return {
        "device": "cpu",
        "torch_dtype": "float32",
        "quantization": False,
        "gpu_layers": 0
    }

# CPU-optimized model settings
CPU_MODEL_CONFIG = {
    "max_new_tokens": 128,  # Reduced for CPU
    "temperature": 0.7,
    "top_p": 0.9,
    "top_k": 40,
    "batch_size": 1,  # Single batch for CPU
    "context_length": 512,  # Reduced context
}

# Fallback model if GGUF not available
FALLBACK_MODEL = "microsoft/DialoGPT-medium"  # Smaller, faster model

def get_cpu_model_path():
    """Get the appropriate model path for CPU deployment"""
    base_dir = Path(__file__).parent.parent.parent
    gguf_path = base_dir / "models" / "mistral-7b-instruct-v0.2.Q4_K_S" / "mistral-7b-instruct-v0.2.Q4_K_S.gguf"
    
    if gguf_path.exists():
        return str(gguf_path)
    else:
        print(f"⚠️ GGUF model not found at {gguf_path}")
        print(f"   Will use fallback model: {FALLBACK_MODEL}")
        return FALLBACK_MODEL
