#!/usr/bin/env python3
"""
Main launcher for CPMS Chatbot - Frontend and Backend separated
"""
import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Main launcher function"""
    print("🏛️ CPMS Chatbot Launcher")
    print("=" * 40)
    
    try:
        # Import the web interface from the frontend
        from frontend.interface.web_interface import main as launch_web
        
        print("🚀 Starting CPMS Chatbot Web Interface...")
        print("📁 Frontend: frontend/interface/")
        print("📁 Backend: backend/")
        print("=" * 40)
        
        # Launch the web interface
        launch_web()
        
    except KeyboardInterrupt:
        print("\n👋 Chatbot stopped by user")
    except Exception as e:
        print(f"❌ Error launching chatbot: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure all dependencies are installed: pip install -r backend/requirements.txt")
        print("2. Check that model files are accessible")
        print("3. Verify the directory structure is correct")
        sys.exit(1)

if __name__ == "__main__":
    main()
