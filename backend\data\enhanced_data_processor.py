"""
Enhanced CPMS Data Processor
Integrates user manual data with existing PDF and web-scraped data
"""
import json
import os
import sys
from typing import Dict, List, Any, Tuple
from pathlib import Path

# Add parent directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from backend.config import config
from backend.data.user_manual_processor import CPMSUserManualProcessor

class EnhancedCPMSDataProcessor:
    def __init__(self):
        self.user_manual_processor = CPMSUserManualProcessor()
        self.all_processed_data = []
        self.all_qa_pairs = []
        
    def load_existing_data(self) -> Tuple[List[Dict], List[Dict]]:
        """Load existing processed data"""
        existing_data = []
        existing_qa = []
        
        try:
            # Load existing processed data
            existing_data_path = config.PROCESSED_DATA_PATH / "all_processed_data.json"
            if existing_data_path.exists():
                with open(existing_data_path, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                print(f"✅ Loaded {len(existing_data)} existing data entries")
            
            # Load existing training data
            if config.TRAINING_DATA_PATH.exists():
                with open(config.TRAINING_DATA_PATH, 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.strip():
                            qa_entry = json.loads(line.strip())
                            existing_qa.append(qa_entry)
                print(f"✅ Loaded {len(existing_qa)} existing Q&A pairs")
                
        except Exception as e:
            print(f"⚠️ Error loading existing data: {e}")
            
        return existing_data, existing_qa
    
    def integrate_user_manual_data(self) -> Tuple[List[Dict], List[Dict]]:
        """Integrate user manual data with existing data"""
        print("🔄 Starting enhanced data processing with user manual integration...")
        
        # Load existing data
        existing_data, existing_qa = self.load_existing_data()
        
        # Process user manual data
        manual_procedures, manual_qa = self.user_manual_processor.process_all_manual_data()
        
        # Combine all data
        combined_data = existing_data.copy()
        combined_qa = existing_qa.copy()
        
        # Add user manual procedures as data entries
        for procedure in manual_procedures:
            data_entry = {
                'content': procedure['procedure'],
                'source': f"User Manual - Chapter {procedure['chapter']}, Section {procedure['section']}",
                'title': procedure['title'],
                'type': 'user_manual_procedure',
                'procedure_type': procedure['type'],
                'metadata': {
                    'chapter': procedure['chapter'],
                    'section': procedure['section'],
                    'procedure_type': procedure['type']
                }
            }
            combined_data.append(data_entry)
        
        # Add user manual Q&A pairs
        for qa_pair in manual_qa:
            # Convert to training format
            training_entry = {
                'instruction': qa_pair['question'],
                'input': "",
                'output': qa_pair['answer'],
                'source': qa_pair['source'],
                'procedure_type': qa_pair['procedure_type']
            }
            combined_qa.append(training_entry)
        
        print(f"✅ Combined data: {len(combined_data)} entries")
        print(f"✅ Combined Q&A: {len(combined_qa)} pairs")
        
        return combined_data, combined_qa
    
    def enhance_existing_qa_with_procedures(self, qa_pairs: List[Dict]) -> List[Dict]:
        """Enhance existing Q&A pairs with procedural context"""
        enhanced_qa = []
        
        for qa in qa_pairs:
            enhanced_qa.append(qa)  # Keep original
            
            # Check if we can enhance with procedural information
            question = qa.get('instruction', '').lower()
            
            # Add enhanced versions for common procedural questions
            if any(term in question for term in ['how to', 'steps', 'procedure', 'process']):
                enhanced_answer = self._add_procedural_context(qa.get('output', ''))
                if enhanced_answer != qa.get('output', ''):
                    enhanced_entry = qa.copy()
                    enhanced_entry['output'] = enhanced_answer
                    enhanced_entry['enhanced'] = True
                    enhanced_qa.append(enhanced_entry)
        
        return enhanced_qa
    
    def _add_procedural_context(self, answer: str) -> str:
        """Add procedural context to answers"""
        if not answer:
            return answer
            
        # Add step-by-step formatting if not already present
        if 'step' not in answer.lower() and len(answer) > 100:
            # Try to identify procedural elements
            procedural_keywords = ['login', 'click', 'select', 'enter', 'fill', 'submit', 'go to']
            
            if any(keyword in answer.lower() for keyword in procedural_keywords):
                # Add procedural formatting
                enhanced_answer = f"Here are the step-by-step instructions:\n\n{answer}"
                
                # Add helpful footer
                enhanced_answer += f"\n\n💡 For additional help:\n"
                enhanced_answer += f"• Helpline: {config.HELPLINE_NUMBER}\n"
                enhanced_answer += f"• Email: {config.HELPDESK_EMAIL}\n"
                enhanced_answer += f"• Technical Support: {config.TECHNICAL_EMAIL}"
                
                return enhanced_answer
        
        return answer
    
    def create_specialized_training_sets(self, qa_pairs: List[Dict]) -> Dict[str, List[Dict]]:
        """Create specialized training sets by procedure type"""
        specialized_sets = {
            'login_creation': [],
            'form_processing': [],
            'pension_sanctioning': [],
            'payment_processing': [],
            'grievance_management': [],
            'profile_management': [],
            'general_procedures': []
        }
        
        for qa in qa_pairs:
            proc_type = qa.get('procedure_type', 'GENERAL_PROCEDURE')
            
            if proc_type in ['AO_LOGIN_CREATION', 'DH_CREATION']:
                specialized_sets['login_creation'].append(qa)
            elif proc_type == 'FORM_PROCESSING':
                specialized_sets['form_processing'].append(qa)
            elif proc_type == 'PENSION_SANCTIONING':
                specialized_sets['pension_sanctioning'].append(qa)
            elif proc_type == 'PAYMENT_PROCESSING':
                specialized_sets['payment_processing'].append(qa)
            elif proc_type == 'GRIEVANCE_MANAGEMENT':
                specialized_sets['grievance_management'].append(qa)
            elif proc_type in ['RETIREE_PROFILE', 'PROFILE_UPDATE']:
                specialized_sets['profile_management'].append(qa)
            else:
                specialized_sets['general_procedures'].append(qa)
        
        return specialized_sets
    
    def save_enhanced_data(self, combined_data: List[Dict], combined_qa: List[Dict]):
        """Save enhanced data to files"""
        try:
            # Save combined processed data
            enhanced_data_path = config.PROCESSED_DATA_PATH / "enhanced_processed_data.json"
            with open(enhanced_data_path, 'w', encoding='utf-8') as f:
                json.dump(combined_data, f, indent=2, ensure_ascii=False)
            
            # Save enhanced training data
            enhanced_training_path = config.DATA_DIR / "enhanced_training_data.jsonl"
            with open(enhanced_training_path, 'w', encoding='utf-8') as f:
                for qa in combined_qa:
                    f.write(json.dumps(qa, ensure_ascii=False) + '\n')
            
            # Create specialized training sets
            specialized_sets = self.create_specialized_training_sets(combined_qa)
            
            for set_name, qa_list in specialized_sets.items():
                if qa_list:  # Only save non-empty sets
                    specialized_path = config.DATA_DIR / f"training_{set_name}.jsonl"
                    with open(specialized_path, 'w', encoding='utf-8') as f:
                        for qa in qa_list:
                            f.write(json.dumps(qa, ensure_ascii=False) + '\n')
                    print(f"💾 Saved {len(qa_list)} entries to {set_name} training set")
            
            print(f"✅ Enhanced data saved successfully")
            print(f"   • Combined data: {len(combined_data)} entries")
            print(f"   • Enhanced training: {len(combined_qa)} Q&A pairs")
            
        except Exception as e:
            print(f"❌ Error saving enhanced data: {e}")
    
    def process_all_enhanced_data(self) -> Tuple[List[Dict], List[Dict]]:
        """Process all data with user manual integration"""
        # Integrate user manual data
        combined_data, combined_qa = self.integrate_user_manual_data()
        
        # Enhance Q&A pairs with procedural context
        enhanced_qa = self.enhance_existing_qa_with_procedures(combined_qa)
        
        # Save enhanced data
        self.save_enhanced_data(combined_data, enhanced_qa)
        
        return combined_data, enhanced_qa
    
    def generate_summary_report(self, combined_data: List[Dict], enhanced_qa: List[Dict]):
        """Generate a summary report of the enhanced data"""
        print("\n" + "="*60)
        print("📊 ENHANCED CPMS DATA PROCESSING SUMMARY")
        print("="*60)
        
        # Data summary
        print(f"\n📋 Data Entries: {len(combined_data)}")
        data_types = {}
        for entry in combined_data:
            data_type = entry.get('type', 'unknown')
            data_types[data_type] = data_types.get(data_type, 0) + 1
        
        for data_type, count in data_types.items():
            print(f"   • {data_type}: {count}")
        
        # Q&A summary
        print(f"\n❓ Q&A Pairs: {len(enhanced_qa)}")
        procedure_types = {}
        for qa in enhanced_qa:
            proc_type = qa.get('procedure_type', 'general')
            procedure_types[proc_type] = procedure_types.get(proc_type, 0) + 1
        
        for proc_type, count in procedure_types.items():
            print(f"   • {proc_type}: {count}")
        
        # File locations
        print(f"\n📁 Output Files:")
        print(f"   • Enhanced data: {config.PROCESSED_DATA_PATH}/enhanced_processed_data.json")
        print(f"   • Enhanced training: {config.DATA_DIR}/enhanced_training_data.jsonl")
        print(f"   • Specialized sets: {config.DATA_DIR}/training_*.jsonl")
        
        print("\n✅ Enhanced data processing complete!")
        print("🚀 Ready for improved chatbot training with step-by-step procedures!")

def main():
    """Main function to run enhanced data processing"""
    processor = EnhancedCPMSDataProcessor()
    combined_data, enhanced_qa = processor.process_all_enhanced_data()
    processor.generate_summary_report(combined_data, enhanced_qa)

if __name__ == "__main__":
    main()
