#!/usr/bin/env python3
"""
Show sample pensioners for testing authentication
"""
import sys
import os
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from backend.database.database import SessionLocal
from backend.models.pensioner_models import <PERSON><PERSON><PERSON>

def show_sample_pensioners():
    """Show sample pensioners for testing"""
    db = SessionLocal()
    
    try:
        # Get first 10 pensioners
        pensioners = db.query(Pensioner).limit(10).all()
        
        print("🏛️ CPMS Sample Pensioners for Testing")
        print("=" * 60)
        print(f"Total pensioners in database: {db.query(Pensioner).count()}")
        print("\n📋 Sample pensioners for authentication testing:")
        print("-" * 60)
        
        for i, pensioner in enumerate(pensioners, 1):
            print(f"{i:2d}. EPPO: {pensioner.eppo_number}")
            print(f"    Name: {pensioner.name}")
            print(f"    Mobile: {pensioner.mobile_number}")
            print(f"    DOB: {pensioner.date_of_birth}")
            print(f"    Retirement: {pensioner.date_of_retirement}")
            print()
        
        print("🔐 How to test authentication:")
        print("1. Go to: http://localhost:7861/enhanced_index.html")
        print("2. Click 'Login as Pensioner'")
        print("3. Use any EPPO Number and Mobile Number from above")
        print("4. Check backend console for OTP code")
        print("5. Enter OTP to complete authentication")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    show_sample_pensioners()
