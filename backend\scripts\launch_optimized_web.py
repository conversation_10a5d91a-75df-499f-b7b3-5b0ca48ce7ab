#!/usr/bin/env python3
"""
Simple launcher for optimized CPMS chatbot web interface
"""
import sys
import time
import os
from pathlib import Path

# Add the workspace root to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking Requirements...")
    print("-" * 30)

    # Check if required files exist
    required_files = [
        "frontend/interface/web_interface.py",
        "backend/core/ultra_optimized_rag_system.py",
        "backend/models/optimized_model_handler.py",
        "backend/config/config.py"
    ]

    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)

    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        return False

    print("✅ All required files found")

    # Check if model path exists (for GGUF)
    try:
        from backend.config import config
        if hasattr(config, 'MISTRAL_MODEL_PATH'):
            if not Path(config.MISTRAL_MODEL_PATH).exists():
                print(f"⚠️ GGUF model not found at: {config.MISTRAL_MODEL_PATH}")
                print("   Will fallback to Hugging Face model")
            else:
                print("✅ GGUF model found")
    except Exception as e:
        print(f"⚠️ Could not check model path: {e}")

    return True

def quick_test():
    """Run a quick test of the optimized system"""
    print("\n🧪 Quick System Test...")
    print("-" * 30)

    try:
        from backend.core.ultra_optimized_rag_system import UltraOptimizedRAGSystem

        print("🚀 Initializing optimized system...")
        start_time = time.time()

        # Try GGUF first, fallback to HuggingFace
        try:
            rag = UltraOptimizedRAGSystem(model_type="gguf")
            model_type = "gguf"
        except Exception as e:
            print(f"   GGUF failed: {e}")
            print("   Trying Hugging Face model...")
            rag = UltraOptimizedRAGSystem(model_type="huggingface")
            model_type = "huggingface"

        init_time = time.time() - start_time
        print(f"✅ System initialized in {init_time:.2f}s using {model_type} model")

        # Quick query test
        print("🔍 Testing query response...")
        start_time = time.time()

        result = rag.query("What is CPMS?", include_sources=False)
        response_time = time.time() - start_time

        print(f"✅ Query completed in {response_time:.2f}s")

        if response_time < 10:
            print("🎯 Performance: EXCELLENT (< 10s)")
        elif response_time < 15:
            print("🎯 Performance: GOOD (< 15s)")
        else:
            print("🎯 Performance: NEEDS IMPROVEMENT (> 15s)")

        return True, model_type

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False, None

def launch_web_interface(model_type="gguf"):
    """Launch the web interface"""
    print(f"\n🌐 Launching Optimized Web Interface...")
    print("-" * 30)

    try:
        from frontend.interface.web_interface import CPMSWebInterface
        from backend.config import config

        print(f"🚀 Starting with {model_type} model...")
        print(f"🌍 Server will be available at: http://localhost:{config.GRADIO_PORT}")
        print("💡 Press Ctrl+C to stop the server")
        print("\n" + "🎉 CPMS Chatbot Web Interface Starting..." + "\n")

        # Create and launch interface
        web_interface = CPMSWebInterface(model_type=model_type, use_optimized=True)
        interface = web_interface.create_interface()

        interface.launch(
            server_name="0.0.0.0",
            server_port=config.GRADIO_PORT,
            share=False,
            debug=False,
            show_error=True,
            quiet=False,
            inbrowser=True  # Automatically open browser
        )

    except KeyboardInterrupt:
        print("\n👋 Shutting down web interface...")
    except Exception as e:
        print(f"❌ Error launching web interface: {e}")
        print("\n🔧 Troubleshooting tips:")
        print("   1. Check if all dependencies are installed")
        print("   2. Verify model files are accessible")
        print("   3. Try running: pip install -r requirements.txt")

def main():
    """Main function"""
    print("🏛️ CPMS Optimized Chatbot Web Launcher")
    print("=" * 50)

    # Step 1: Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed. Please fix issues and try again.")
        sys.exit(1)

    # Step 2: Quick test
    print("\n" + "=" * 50)
    test_passed, model_type = quick_test()

    if not test_passed:
        print("\n❌ System test failed. Please check your setup.")
        response = input("\nDo you want to try launching anyway? (y/n): ")
        if response.lower() != 'y':
            sys.exit(1)
        model_type = "gguf"  # Default fallback

    # Step 3: Launch web interface
    print("\n" + "=" * 50)
    print("✅ System test passed! Ready to launch web interface.")

    response = input("\nLaunch web interface now? (y/n): ")
    if response.lower() == 'y':
        launch_web_interface(model_type)
    else:
        print("\n👋 Launch cancelled. You can run this script again anytime.")
        print("\n💡 Quick commands:")
        print("   python launch_optimized_web.py  # Full test and launch")
        print("   python main.py --mode web       # Direct launch")
        print("   python test_optimized_web.py    # Comprehensive testing")

if __name__ == "__main__":
    main()
