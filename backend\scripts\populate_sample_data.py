"""
<PERSON><PERSON><PERSON> to populate sample pensioner data for testing
"""
import sys
import os
from datetime import date, datetime, timedelta

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from backend.database.database import SessionLocal, create_tables, init_database
from backend.models.pensioner_models import Pensioner, PensionDetails, SystemConfiguration

def create_sample_pensioners():
    """Create sample pensioner data for testing"""
    db = SessionLocal()
    
    try:
        # Sample pensioner 1
        pensioner1 = Pensioner(
            eppo_number="DOT12345678",
            name="<PERSON><PERSON>",
            mobile_number="**********",
            email="<EMAIL>",
            date_of_birth=date(1960, 5, 15),
            date_of_retirement=date(2020, 5, 31),
            is_active=True
        )
        
        # Sample pensioner 2
        pensioner2 = Pensioner(
            eppo_number="DOT87654321",
            name="<PERSON><PERSON>",
            mobile_number="**********",
            email="<EMAIL>",
            date_of_birth=date(1962, 8, 20),
            date_of_retirement=date(2022, 8, 31),
            is_active=True
        )
        
        # Sample pensioner 3
        pensioner3 = Pensioner(
            eppo_number="DOT11223344",
            name="Anil <PERSON> Verma",
            mobile_number="**********",
            email="<EMAIL>",
            date_of_birth=date(1958, 12, 10),
            date_of_retirement=date(2018, 12, 31),
            is_active=True
        )
        
        db.add_all([pensioner1, pensioner2, pensioner3])
        db.commit()
        
        # Get the pensioner IDs
        pensioner1 = db.query(Pensioner).filter(Pensioner.eppo_number == "DOT12345678").first()
        pensioner2 = db.query(Pensioner).filter(Pensioner.eppo_number == "DOT87654321").first()
        pensioner3 = db.query(Pensioner).filter(Pensioner.eppo_number == "DOT11223344").first()
        
        # Create pension details for pensioner 1
        pension_details1 = PensionDetails(
            pensioner_id=pensioner1.id,
            lc_expiry_date=date(2024, 12, 31),
            lc_last_submitted=date(2023, 11, 15),
            lc_status="verified",
            basic_pension=25000.00,
            da_amount=12500.00,
            medical_allowance=1000.00,
            additional_pension=2500.00,
            total_pension=41000.00,
            last_pension_month="2024-01",
            commutation_amount=500000.00,
            commutation_date=date(2020, 6, 1),
            commutation_restoration_date=date(2035, 6, 1),
            commutation_restored=False,
            current_da_rate=50.0,
            da_effective_date=date(2024, 1, 1),
            additional_pension_rate=10.0,
            additional_pension_effective_date=date(2023, 1, 1),
            normal_family_pension=15000.00,
            enhanced_family_pension=20000.00,
            family_pension_effective_date=date(2020, 6, 1),
            fma_amount=1000.00,
            fma_effective_date=date(2020, 6, 1),
            sampann_migration_date=date(2021, 4, 1),
            sampann_migration_status="completed",
            bank_name="State Bank of India",
            account_number="***********",
            ifsc_code="SBIN0001234"
        )
        
        # Create pension details for pensioner 2
        pension_details2 = PensionDetails(
            pensioner_id=pensioner2.id,
            lc_expiry_date=date(2024, 8, 31),
            lc_last_submitted=date(2023, 8, 15),
            lc_status="verified",
            basic_pension=22000.00,
            da_amount=11000.00,
            medical_allowance=1000.00,
            additional_pension=2200.00,
            total_pension=36200.00,
            last_pension_month="2024-01",
            commutation_amount=400000.00,
            commutation_date=date(2022, 9, 1),
            commutation_restoration_date=date(2037, 9, 1),
            commutation_restored=False,
            current_da_rate=50.0,
            da_effective_date=date(2024, 1, 1),
            additional_pension_rate=10.0,
            additional_pension_effective_date=date(2023, 1, 1),
            normal_family_pension=13200.00,
            enhanced_family_pension=17600.00,
            family_pension_effective_date=date(2022, 9, 1),
            fma_amount=1000.00,
            fma_effective_date=date(2022, 9, 1),
            sampann_migration_date=date(2022, 10, 1),
            sampann_migration_status="completed",
            bank_name="Punjab National Bank",
            account_number="***********",
            ifsc_code="PUNB0009876"
        )
        
        # Create pension details for pensioner 3
        pension_details3 = PensionDetails(
            pensioner_id=pensioner3.id,
            lc_expiry_date=date(2024, 3, 31),  # Expiring soon
            lc_last_submitted=date(2023, 3, 15),
            lc_status="verified",
            basic_pension=28000.00,
            da_amount=14000.00,
            medical_allowance=1000.00,
            additional_pension=2800.00,
            total_pension=45800.00,
            last_pension_month="2024-01",
            commutation_amount=600000.00,
            commutation_date=date(2019, 1, 1),
            commutation_restoration_date=date(2034, 1, 1),
            commutation_restored=False,
            current_da_rate=50.0,
            da_effective_date=date(2024, 1, 1),
            additional_pension_rate=10.0,
            additional_pension_effective_date=date(2023, 1, 1),
            normal_family_pension=16800.00,
            enhanced_family_pension=22400.00,
            family_pension_effective_date=date(2019, 1, 1),
            fma_amount=1000.00,
            fma_effective_date=date(2019, 1, 1),
            sampann_migration_date=date(2020, 6, 1),
            sampann_migration_status="completed",
            bank_name="Bank of Baroda",
            account_number="***********",
            ifsc_code="BARB0001122"
        )
        
        db.add_all([pension_details1, pension_details2, pension_details3])
        db.commit()
        
        print("✅ Sample pensioner data created successfully!")
        print("\nSample Pensioners:")
        print("1. EPPO: DOT12345678, Mobile: **********, Name: Rajesh Kumar Singh")
        print("2. EPPO: DOT87654321, Mobile: **********, Name: Sunita Sharma")
        print("3. EPPO: DOT11223344, Mobile: **********, Name: Anil Kumar Verma")
        print("\nYou can use these credentials to test the authentication system.")
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        db.rollback()
    finally:
        db.close()

def update_system_config():
    """Update system configuration with current values"""
    db = SessionLocal()
    
    try:
        # Update DA rate
        da_config = db.query(SystemConfiguration).filter(
            SystemConfiguration.config_key == "current_da_rate"
        ).first()
        if da_config:
            da_config.config_value = "50.0"
        
        # Update FMA rate
        fma_config = db.query(SystemConfiguration).filter(
            SystemConfiguration.config_key == "fma_rate"
        ).first()
        if fma_config:
            fma_config.config_value = "1000.0"
        
        db.commit()
        print("✅ System configuration updated!")
        
    except Exception as e:
        print(f"❌ Error updating system config: {e}")
        db.rollback()
    finally:
        db.close()

def main():
    """Main function to set up sample data"""
    print("🗄️ Setting up CPMS Pensioner Database...")
    
    # Create tables
    if create_tables():
        print("✅ Database tables created")
        
        # Initialize with default config
        if init_database():
            print("✅ Database initialized with default configuration")
            
            # Create sample pensioners
            create_sample_pensioners()
            
            # Update system config
            update_system_config()
            
            print("\n🎉 Database setup complete!")
            print("\nNext steps:")
            print("1. Install dependencies: pip install -r requirements.txt")
            print("2. Start the backend: python run_backend.py")
            print("3. Test authentication with sample EPPO numbers")
            
        else:
            print("❌ Database initialization failed")
    else:
        print("❌ Table creation failed")

if __name__ == "__main__":
    main()
