#!/usr/bin/env python3
"""
Frontend Web Client for CPMS Chatbot
Connects to the backend API server via HTTP requests
"""
import gradio as gr
import requests
import time
import json
from typing import List, Tu<PERSON>, Dict, Any
import sys
import os

# Add workspace root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from backend.config import config

class CPMSWebClient:
    def __init__(self, api_url: str = None):
        """Initialize the web client"""
        # Use BACKEND_API_BASE_URL if available, otherwise construct from localhost
        if hasattr(config, 'BACKEND_API_BASE_URL'):
            self.api_url = api_url or config.BACKEND_API_BASE_URL
        else:
            # Fallback to localhost for Windows compatibility
            self.api_url = api_url or f"http://localhost:{config.API_PORT}"
        self.chat_history = []
        self.check_backend_connection()

    def check_backend_connection(self):
        """Check if backend API is accessible"""
        try:
            response = requests.get(f"{self.api_url}/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ Connected to backend API at {self.api_url}")
                health_data = response.json()
                print(f"📊 Backend status: {health_data['status']}")
            else:
                print(f"⚠️ Backend API returned status {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Cannot connect to backend API at {self.api_url}")
            print(f"Error: {e}")
            print("🔧 Make sure the backend server is running with: python backend/api/server.py")

    def chat_response(self, message: str, history: List[List[str]]) -> Tuple[str, List[List[str]]]:
        """Process chat message via API"""
        if not message.strip():
            return "Please enter a question.", history

        try:
            # Send request to backend API
            payload = {
                "message": message,
                "include_sources": True
            }

            response = requests.post(
                f"{self.api_url}/query",
                json=payload,
                timeout=150  # Increased to 2.5 minutes to handle complex queries
            )

            if response.status_code == 200:
                result = response.json()

                # Format response
                bot_response = result['response']

                # Add source information if available
                if result.get('sources'):
                    bot_response += "\n\n📚 **Sources:**"
                    for i, source in enumerate(result['sources'][:2], 1):
                        bot_response += f"\n{i}. {source['source']} (Relevance: {source['relevance_score']})"

                # Add processing time and cache info
                cache_info = ""
                if result.get('cached', False):
                    cache_type = result.get('cache_type', 'Cache')
                    cache_info = f"⚡ {cache_type} Hit"
                else:
                    cache_info = "🚀 Processed"

                bot_response += f"\n\n⏱️ *Response time: {result['processing_time']}s* | {cache_info} | Model: {result['model_type']}"

                # Update history
                history.append([message, bot_response])
                return "", history

            elif response.status_code == 408:
                # Timeout error from backend
                error_msg = "⏰ **Request timed out.** The query is taking longer than expected.\n\n"
                error_msg += "**Suggestions:**\n"
                error_msg += "• Try a simpler or shorter question\n"
                error_msg += "• Break complex questions into parts\n"
                error_msg += "• Try again in a moment\n\n"
                error_msg += "The system is optimized for quick responses, but complex queries may take longer."
                history.append([message, error_msg])
                return "", history
            else:
                error_msg = f"Backend API error (Status {response.status_code})"
                try:
                    error_detail = response.json().get('detail', 'Unknown error')
                    error_msg += f": {error_detail}"
                except:
                    pass

                history.append([message, error_msg])
                return "", history

        except requests.exceptions.Timeout:
            error_response = "⏰ **Request timed out** (after 2.5 minutes).\n\n"
            error_response += "**This usually happens when:**\n"
            error_response += "• The query is very complex or requires extensive processing\n"
            error_response += "• The model is generating a very long response\n"
            error_response += "• The system is under heavy load\n\n"
            error_response += "**Please try:**\n"
            error_response += "• A simpler question\n"
            error_response += "• Breaking your question into smaller parts\n"
            error_response += "• Waiting a moment and trying again"
            history.append([message, error_response])
            return "", history

        except requests.exceptions.RequestException as e:
            error_response = f"🔌 Connection error: {str(e)}\n\nPlease ensure the backend server is running."
            history.append([message, error_response])
            return "", history

        except Exception as e:
            error_response = f"❌ Unexpected error: {str(e)}"
            history.append([message, error_response])
            return "", history

    def get_suggested_questions(self) -> List[str]:
        """Get suggested questions from backend"""
        try:
            response = requests.get(f"{self.api_url}/suggestions", timeout=10)
            if response.status_code == 200:
                return response.json()
        except:
            pass

        # Fallback suggestions
        return [
            "What is CPMS?",
            "How can I login to CPMS portal?",
            "When is pension credited?",
            "What is the helpline number?",
            "How to lodge a grievance?"
        ]

    def handle_suggestion_click(self, suggestion: str, history: List[List[str]]) -> Tuple[str, List[List[str]]]:
        """Handle clicking on a suggested question"""
        return self.chat_response(suggestion, history)

    def clear_chat(self) -> List[List[str]]:
        """Clear chat history"""
        return []

    def get_system_info(self) -> str:
        """Get system information from backend"""
        try:
            response = requests.get(f"{self.api_url}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()

                info = f"""
## System Information (API Mode)

**Backend API:** {self.api_url}
**Status:** {health_data['status']}
**Model Type:** {health_data['system_info'].get('model_type', 'Unknown')}
**Documents:** {health_data['system_info'].get('total_documents', 'Unknown')}
**Optimizations Active:** {health_data['system_info'].get('optimizations_active', 0)}

## Contact Information
- **Helpline:** {config.HELPLINE_NUMBER}
- **Email:** {config.HELPDESK_EMAIL}
- **Technical Support:** {config.TECHNICAL_EMAIL}

## Health Check
"""
                issues = health_data['system_info'].get('issues', [])
                if issues:
                    info += "**Issues Found:**\n"
                    for issue in issues:
                        info += f"- {issue}\n"
                else:
                    info += "✅ All systems operational"

                return info
            else:
                return f"❌ Cannot get system info. Backend API returned status {response.status_code}"

        except Exception as e:
            return f"❌ Cannot connect to backend API: {str(e)}"

    def create_interface(self):
        """Create the Gradio interface"""
        # Use the same CSS as the original interface
        css = """
        /* Enhanced CSS for CPMS Interface */
        .gradio-container {
            max-width: 1400px !important;
            margin: 0 auto !important;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
        }

        .api-status {
            background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
            color: white !important;
            padding: 0.75rem 1rem !important;
            border-radius: 0.5rem !important;
            margin-bottom: 1rem !important;
            text-align: center !important;
            font-weight: 600 !important;
        }
        """

        with gr.Blocks(
            css=css,
            title="CPMS - Comprehensive Pension Management System (API Mode)",
            theme=gr.themes.Soft(),
            analytics_enabled=False
        ) as interface:

            # Header
            gr.HTML(f"""
            <div style="text-align: center; padding: 2rem; background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%); color: white; border-radius: 1rem; margin-bottom: 2rem;">
                <h1 style="margin: 0; font-size: 2.5rem;">🏛️ CPMS - API Mode</h1>
                <p style="margin: 0.5rem 0 0 0; font-size: 1.1rem; opacity: 0.9;">
                    Frontend client connecting to backend API at {self.api_url}
                </p>
            </div>
            """)

            # API Status indicator
            gr.HTML("""
            <div class="api-status">
                🔌 Connected to Backend API - Real-time communication enabled
            </div>
            """)

            with gr.Tabs():
                with gr.TabItem("💬 Chat"):
                    with gr.Row():
                        with gr.Column(scale=3):
                            chatbot = gr.Chatbot(
                                height=600,
                                show_label=False,
                                container=True,
                                bubble_full_width=False
                            )

                            with gr.Row():
                                msg = gr.Textbox(
                                    placeholder="Ask me anything about CPMS...",
                                    show_label=False,
                                    scale=4,
                                    container=False
                                )
                                submit_btn = gr.Button("Send", variant="primary", scale=1)
                                clear_btn = gr.Button("Clear", variant="secondary", scale=1)

                        with gr.Column(scale=1):
                            gr.Markdown("### 💡 Suggested Questions")
                            suggestions = self.get_suggested_questions()

                            suggestion_buttons = []
                            for suggestion in suggestions[:8]:
                                btn = gr.Button(suggestion, variant="secondary", size="sm")
                                suggestion_buttons.append(btn)

                with gr.TabItem("📊 System Info"):
                    system_info = gr.Markdown(self.get_system_info())
                    refresh_btn = gr.Button("🔄 Refresh Info", variant="primary")

            # Event handlers
            def submit_message(message, history):
                return self.chat_response(message, history)

            # Submit events
            submit_btn.click(submit_message, [msg, chatbot], [msg, chatbot])
            msg.submit(submit_message, [msg, chatbot], [msg, chatbot])

            # Clear event
            clear_btn.click(self.clear_chat, outputs=[chatbot])

            # Suggestion button events
            for btn in suggestion_buttons:
                btn.click(
                    lambda suggestion=btn.value: self.handle_suggestion_click(suggestion, []),
                    outputs=[msg, chatbot]
                )

            # Refresh system info
            refresh_btn.click(lambda: self.get_system_info(), outputs=[system_info])

        return interface

    def launch(self, share: bool = False, debug: bool = False):
        """Launch the web interface"""
        interface = self.create_interface()

        print(f"🌐 Launching CPMS Frontend Client...")
        print(f"🔌 Backend API: {self.api_url}")
        print(f"🌍 Frontend URL: http://localhost:{config.GRADIO_PORT}")
        print("💡 Press Ctrl+C to stop the frontend")

        interface.launch(
            server_name="0.0.0.0",
            server_port=config.GRADIO_PORT,
            share=share,
            debug=debug,
            show_error=True
        )

def main():
    """Main function to launch the frontend client"""
    import argparse

    parser = argparse.ArgumentParser(description="CPMS Chatbot Frontend Client")
    parser.add_argument(
        "--api-url",
        default=getattr(config, 'BACKEND_API_BASE_URL', f"http://localhost:{config.API_PORT}"),
        help="Backend API URL"
    )
    parser.add_argument("--share", action="store_true", help="Create a public link")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")

    args = parser.parse_args()

    print("🎨 CPMS Frontend Client (API Mode)")
    print("=" * 50)

    # Create and launch client
    client = CPMSWebClient(api_url=args.api_url)
    client.launch(share=args.share, debug=args.debug)

if __name__ == "__main__":
    main()
