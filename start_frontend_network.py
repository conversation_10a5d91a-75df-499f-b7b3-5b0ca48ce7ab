#!/usr/bin/env python3
"""
Start CPMS Frontend accessible from network
"""
import os
import sys
from pathlib import Path

def main():
    """Start frontend server accessible from network"""
    print("🌐 Starting CPMS Frontend (Network Accessible)")
    print("=" * 50)
    
    # Add workspace root to Python path
    workspace_root = Path(__file__).parent
    sys.path.insert(0, str(workspace_root))
    
    try:
        # Import the frontend server
        from frontend.server import CPMSFrontendServer
        
        # Start server bound to all interfaces
        print("🚀 Starting frontend server...")
        print("   Host: 0.0.0.0 (accessible from network)")
        print("   Port: 7861")
        print("   Backend: http://*************:8001")
        print("")
        print("🌐 Frontend will be accessible at:")
        print("   - http://*************:7861/index.html")
        print("   - http://*************:7861/enhanced_index.html")
        print("=" * 50)
        
        # Create and start server
        server = CPMSFrontendServer(host="0.0.0.0", port=7861)
        server.start(open_browser=False)  # Don't open browser on server
        
    except KeyboardInterrupt:
        print("\n👋 Frontend stopped by user")
    except Exception as e:
        print(f"❌ Error starting frontend: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Check if port 7861 is available")
        print("2. Verify backend is running at *************:8001")
        print("3. Check firewall settings")
        print("4. Ensure static files exist in frontend/static/")
        sys.exit(1)

if __name__ == "__main__":
    main()
