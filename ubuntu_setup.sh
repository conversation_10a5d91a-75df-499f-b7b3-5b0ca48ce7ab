#!/bin/bash

echo "🐧 CPMS Backend Setup for Ubuntu Server"
echo "======================================="

# Check Python version
echo "🐍 Checking Python version..."
python3 --version

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install system dependencies
echo "📦 Installing system dependencies..."
sudo apt update
sudo apt install -y build-essential cmake libpq-dev

# Fix python-jose issue
echo "🔧 Fixing python-jose compatibility..."
pip uninstall python-jose -y 2>/dev/null || true

# Install dependencies
echo "📦 Installing Python dependencies..."
pip install -r backend/requirements.txt

# Test JWT import
echo "✅ Testing JWT functionality..."
python3 -c "
try:
    from jose import jwt
    print('✅ python-jose works')
except ImportError:
    try:
        import jwt
        print('✅ PyJWT works as fallback')
    except ImportError:
        print('❌ Both JWT libraries failed')
        exit(1)
"

# Initialize database
echo "🗄️ Initializing database..."
cd backend
python3 -c "
from database.database import create_tables, init_database
create_tables()
init_database()
print('✅ Database initialized')
"

# Create sample data
echo "👥 Creating sample pensioner data..."
python3 scripts/populate_sample_data.py

echo ""
echo "✅ Setup Complete!"
echo "==================="
echo ""
echo "To start the backend server:"
echo "  cd backend"
echo "  python3 run_backend.py"
echo ""
echo "The server will be available at:"
echo "  - API: http://your-server-ip:8001"
echo "  - Docs: http://your-server-ip:8001/docs"
