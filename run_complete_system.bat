@echo off
echo 🏛️ CPMS Complete System Launcher
echo ================================

echo.
echo 📋 Starting Backend API Server...
echo ⏳ Please wait for backend to initialize...
start "CPMS Backend" cmd /k "cd backend && python run_backend.py"

echo.
echo ⏱️ Waiting 10 seconds for backend to start...
timeout /t 10 /nobreak > nul

echo.
echo 🎨 Starting Frontend Server...
echo 🌐 Frontend will be available at: http://localhost:7861
start "CPMS Frontend" cmd /k "cd frontend && python run_frontend.py"

echo.
echo ✅ System Launch Complete!
echo.
echo 📋 Access URLs:
echo    • Enhanced Portal: http://localhost:7861/enhanced_index.html
echo    • Basic Portal:    http://localhost:7861/index.html
echo    • API Docs:        http://localhost:8001/docs
echo.
echo 👥 Test Pensioner Accounts:
echo    • EPPO: DOT12345678, Mobile: ********** (<PERSON><PERSON>)
echo    • EPPO: DOT87654321, Mobile: ********** (<PERSON><PERSON>)
echo    • EPPO: DOT11223344, Mobile: ********** (<PERSON><PERSON>)
echo.
echo 📱 OTP codes will be shown in backend console for testing
echo.
pause
